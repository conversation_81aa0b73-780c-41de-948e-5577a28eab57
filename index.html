<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="stylesheet" href="./js/openlayer/ol.css" />
  <script type="text/javascript" src="js/openlayer/ol.js"></script>
  <script type="text/javascript" src="./js/proj4.js"></script>
  <script type="text/javascript" src="js/jquery.min.js"></script>
  <style>
    #icon {
      width: 45px;
      height: 58px;
      background: url('https://obs.tuoyupt.com/miniprogram/parentindex/map1.png') no-repeat center;
      background-size: contain;
    }

    .ol-zoom {
      display: none;
    }
  </style>
  <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vConsole/3.15.1/vconsole.min.js"></script>
  <script>
    new VConsole();
  </script> -->
  <script>
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
      CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') + '" />')
  </script>
  <title></title>
  <!--preload-links-->
  <!--app-context-->
</head>

<body>
  <div id="app"><!--app-html--></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>