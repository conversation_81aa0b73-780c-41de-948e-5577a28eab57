{"pages": [{"path": "pages_work/index/index", "style": {"enablePullDownRefresh": true}}, {"path": "pages_work/login/main"}, {"path": "pages_work/notice/index", "style": {"disableScroll": true}}, {"path": "pages_work/notice/notice"}, {"path": "pages_work/notice/push", "style": {"app-plus": {"softinputMode": "adjustResize"}}}, {"path": "pages_work/teach/index"}, {"path": "pages_work/mine/change"}, {"path": "pages_work/mine/child"}, {"path": "pages_work/mine/main"}, {"path": "pages_work/mine/setup"}, {"path": "pages_work/mine/profile"}, {"path": "pages_work/mine/feedback"}, {"path": "pages_work/mine/about"}, {"path": "pages_work/mine/service"}, {"path": "pages_work/login/login"}, {"path": "pages_work/login/forget"}, {"path": "pages_work/login/agreement"}, {"path": "pages_work/poster/index"}, {"path": "pages_work/poster/detail"}, {"path": "pages/webview/index"}, {"path": "pages/webview/lindex", "style": {"pageOrientation": "landscape"}}, {"path": "pages_work/visit/index"}, {"path": "pages_work/visit/detail"}, {"path": "pages_work/visit/form"}, {"path": "pages_work/wenwen/index"}, {"path": "pages_work/educationplan/educationplan", "style": {"enablePullDownRefresh": false, "app-plus": {"bounce": "none"}, "disableScroll": false}}, {"path": "pages_work/educationplan/eduplandetails"}, {"path": "pages_work/message/index"}, {"path": "pages_work/message/official_notice", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/interactive_class", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/nursery_msg", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/news_page", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/msg_details", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/notice_statistics", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/class_list", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/message/webview", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages_work/attendance/index"}, {"path": "pages_work/attendance/attendtrack-detail"}, {"path": "pages_work/attendance/attendtrack"}, {"path": "pages_work/attendance/add-attend"}, {"path": "pages_work/inform/addInform"}, {"path": "pages_work/inform/inform_list", "style": {"enablePullDownRefresh": true}}, {"path": "pages_work/inform/inform_detail"}, {"path": "pages_work/invite/detail"}, {"path": "pages_work/invite/detail_family"}, {"path": "pages_work/invite/index"}, {"path": "pages_work/invite/check_child"}, {"path": "pages_work/classdaily/daily", "style": {"enablePullDownRefresh": false, "app-plus": {"bounce": "none"}, "disableScroll": true}}, {"path": "pages_work/selfcard/detail", "style": {"enablePullDownRefresh": false, "app-plus": {"bounce": "none"}, "disableScroll": true}}, {"path": "pages_work/selfcard/introduce"}], "subPackages": [{"root": "pages/index", "pages": [{"path": "index", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "album", "style": {"disableScroll": true}}, {"path": "news", "style": {"disableScroll": true}}, {"path": "news2", "style": {"disableScroll": true}}, {"path": "newsdetail"}, {"path": "cookbook", "style": {"navigationBarTitleText": "营养食谱", "navigationBarBackgroundColor": "#FFFFFF", "enablePullDownRefresh": true, "navigationStyle": "default"}}, {"path": "day_check"}, {"path": "examination", "style": {"navigationBarTitleText": "体检记录", "navigationBarBackgroundColor": "#FAFAFA", "enablePullDownRefresh": true, "navigationStyle": "default"}}, {"path": "playground", "style": {"pageOrientation": "landscape"}}]}, {"root": "pages/pack", "pages": [{"path": "lifereport/report"}, {"path": "lifereport/index"}, {"path": "face/index"}, {"path": "face/face"}, {"path": "face/result"}, {"path": "liferecord/cloth_chart"}, {"path": "liferecord/drink_chart"}, {"path": "liferecord/eat_chart"}, {"path": "liferecord/sleep_chart"}, {"path": "liferecord/student_detail"}, {"path": "liferecord/toilet_chart"}, {"path": "observation-records/index"}, {"path": "observation-records/report"}, {"path": "observation-records/single-report"}, {"path": "children/index"}, {"path": "children/add"}, {"path": "children/profile"}, {"path": "survey/index"}, {"path": "media-library/index"}, {"path": "media-library/index2"}, {"path": "media-library/index3"}, {"path": "media-library/play"}, {"path": "media-library/scene"}, {"path": "media-library/vscene"}, {"path": "media-library/vplay"}, {"path": "media-library/collect"}]}, {"root": "pages/pack1", "pages": [{"path": "school/index"}, {"path": "school/map"}, {"path": "school/detail"}, {"path": "school/evaluate"}, {"path": "school/form"}, {"path": "physical/index"}, {"path": "physical/map"}, {"path": "physical/detail"}, {"path": "physical/search"}, {"path": "physical/book"}, {"path": "physical/confirm"}, {"path": "physical/list"}, {"path": "physical/report"}, {"path": "physical/book-list"}, {"path": "physical/history"}, {"path": "physical/sign"}, {"path": "physical/his-list"}, {"path": "protection/index"}, {"path": "protection/map"}, {"path": "protection/detail"}, {"path": "protection/search"}, {"path": "protection/book"}, {"path": "protection/confirm"}, {"path": "protection/list"}, {"path": "protection/book-list"}, {"path": "protection/history"}, {"path": "protection/his-list"}, {"path": "protection/record"}, {"path": "protection/record-deatil"}, {"path": "vaccine/index"}, {"path": "vaccine/refer"}, {"path": "vaccine/flow"}, {"path": "vaccine/notice"}, {"path": "vaccine/attention"}, {"path": "vaccine/search"}, {"path": "vaccine/detail"}, {"path": "vaccine/book"}, {"path": "vaccine/confirm"}, {"path": "vaccine/history"}, {"path": "vaccine/book-list"}, {"path": "vaccine/map"}, {"path": "vaccine/record"}, {"path": "childcare-subsidies/index"}, {"path": "childcare-subsidies/school"}, {"path": "childcare-subsidies/form"}, {"path": "childcare-subsidies/form1"}, {"path": "childcare-subsidies/form2"}, {"path": "childcare-subsidies/card"}, {"path": "childcare-subsidies/upload"}, {"path": "childcare-subsidies/success"}, {"path": "childcare-subsidies/list"}, {"path": "childcare-subsidies/project-list"}]}], "globalStyle": {"navigationStyle": "custom", "navigationBarTextStyle": "black", "navigationBarTitleText": "泸州", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "preloadRule": {"pages_work/login/main": {"network": "all", "packages": ["pages/index"]}}}