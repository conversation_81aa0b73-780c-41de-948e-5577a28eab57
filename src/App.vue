<script>
export default {
  globalData: {
    isFormGZH: false
  },
  onLaunch: function () {
    uni.loadFontFace({
      family: '<PERSON><PERSON><PERSON>-Deco',
      global: true,
      source: 'url("https://obs.tuoyupt.com/miniprogram/fonts/R<PERSON><PERSON>-Deco.ttf")',
      success() {
        console.log('loadfont s')
      },
      fail() {
        console.log('loadfont f')
      },
    })
    uni.loadFontFace({
      family: 'MengQu',
      global: true,
      source: 'url("https://obs.tuoyupt.com/miniprogram/fonts/zixiaohunmengquhuanleti.ttf")',
      success() {
        console.log('loadfont s')
      },
      fail() {
        console.log('loadfont f')
      },
    })
  },
  onShow: function (res) {
    if (res.scene == 1035) {
      this.globalData.isFormGZH = true;
    }
  },
};
</script>

<style lang="scss">
@import "./uni_modules/vk-uview-ui/index.scss";

.flex-mode {
  display: flex;
  align-items: center;
  justify-content: center;
}

button::after {
  border: none;
}

.cell-hover-class {
  background-color: rgb(235, 237, 238);
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20%;
  flex-direction: column;
  background: #fff;

  image {
    width: 320rpx;
    margin-bottom: -60rpx;
  }

  .t1 {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #262937;
    margin-bottom: 20rpx;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 400;
    color: #aeb0bc;
    line-height: 44rpx;
  }

  .btn {
    width: 240rpx;
    height: 80rpx;
    background: #ff7373;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 48rpx;
    margin-bottom: 100rpx;
  }
}

.default-btn {
  height: 80rpx;
  background: #ff7373;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  letter-spacing: 1px;
  padding: 0 52rpx;
  line-height: 80rpx;
  margin: 40rpx auto;
  width: max-content;
}

.tip-des {
  width: 500rpx;
  font-size: 34rpx;
  color: #bbb;
  line-height: 48rpx;
  text-align: center;
  margin: 0 auto 64rpx;
}

.tip-image {
  width: 544rpx;
  margin: 32rpx 48rpx;
}

.blur-bg {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100%;
  height: 620rpx;
  background: url("https://obs.tuoyupt.com/miniprogram/client/images/img_share.png") no-repeat;
  background-size: 100% 100%;
  backdrop-filter: blur(5px);
}

.u-close--top-left {
  padding: 20rpx;
}
</style>
