// 引入配置文件 引入policy编码计算方法 引入签名计算方法
import {
  getPolicyEncode,
  getSignature,
  Configuration as config,
} from "./Configuration.js";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);

export const OBSupload = function (filePath, fileType, fileName) {
  let expiration = dayjs().add(3, "minute").utc().format();
  let fullFilePath =
    "miniprogram/manage/" +
    fileType +
    "/" +
    new Date().getTime() +
    "_" +
    fileName;
  if (!filePath) {
    wx.showToast({
      title: "Invalid filePath",
      icon: "Please re-select path",
    });
  } else {
    const OBSPolicy = {
      // 设定policy内容，policy规则定义可参考步骤3中的超链接签名计算规则文档
      expiration,
      conditions: [
        { bucket: config.Bucket }, // 桶名要和配置文件中endpoint中的桶名保持一致
        // { "x-obs-security-token": config.SecurityToken } // 如果是临时访问秘钥鉴权，必须设置该值
        { key: fullFilePath },
      ],
    };

    const policyEncoded = getPolicyEncode(OBSPolicy); // 计算base64编码后的policy
    const signature = getSignature(policyEncoded, config.SecretKey); // 计算signature
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: config.EndPoint,
        filePath: filePath,
        name: "file",
        // #ifdef MP-WEIXIN
        header: {
          "content-type": "multipart/form-data; boundary=-9431149156168",
        },
        // #endif
        formData: {
          // 从配置文件中获取到的AK信息、计算得到的编码后policy及signature信息
          AccessKeyID: config.AccessKeyId,
          policy: policyEncoded,
          signature: signature,
          key: fullFilePath,
          // "x-obs-security-token": config.SecurityToken, // 如果是临时访问秘钥鉴权，必须设置该值
        },

        success: function (res) {
          console.log(res)
          if (res.statusCode == "204") {
            let url = 'https://obs.tuoyupt.com'
            resolve(url + "/" + fullFilePath);
          } else {
            reject(res)
          }
        },
        fail: function (e) {
          reject(e)
        },
      });
    });
  }
};

function getDateFormat() {
  let date = new Date();
  let year;
  return "yyyy-MM-dd'T'HH:mm:ss'Z'";
}
