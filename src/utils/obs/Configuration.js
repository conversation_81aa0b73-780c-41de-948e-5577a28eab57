import { Base64 } from "js-base64";
import Crypto from "crypto-js";

export function getSignature(policyEncoded, SecretKey) {
  // 利用SK对Base64编码后的policy结果进行HMAC-SHA1签名计算
  const bytes = Crypto.HmacSHA1(policyEncoded, SecretKey);
  // 对计算结果进行Base64编码，得到最终的签名信息
  const signature = Crypto.enc.Base64.stringify(bytes);
  return signature;
}

export const Configuration = {
  AccessKeyId: "9V5DYF5Y2KVIKCODXCHJ", //AK
  SecretKey: "jwiuAaQBRetr6kglPcfhPklrznBIW8j0qCNeRRhP", //SK
  EndPoint: "https://obs.tuoyupt.com",
  Bucket: "zhihuituoyupt", //完整的桶访问域名
};

export function getPolicyEncode(policy) {
  // 传入表单上传的policy字段，对policy进行Base64编码
  const encodedPolicy = Base64.encode(JSON.stringify(policy));
  return encodedPolicy;
}
