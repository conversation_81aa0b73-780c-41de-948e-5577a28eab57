export function id2name(id, arr, key = 'name') {
  if (!(id && arr)) {
    return '';
  }
  let data = arr.filter((e) => {
    return e.id == id;
  });
  if(data.length) {
    return data[0][key];
  } else {
    return ''
  }
}

export function str2arr(str, isNum = false) {
  str = str + '';
  if (!str) {
    return '';
  }
  if (isNum) {
    return str.split(",").map((e) => {
      return e * 1;
    });
  } else {
    return str.split(",");
  }
}

export const operate_condition = [
  {
    id: 0,
    name: '永久关门'
  },
  {
    id: 1,
    name: '暂停营业（当前）'
  },
  {
    id: 2,
    name: '筹建中'
  },
  {
    id: 3,
    name: '正常营业'
  }
]

export const is_record = [
  {
    id: 0,
    name: "未备案",
  },
  {
    id: 1,
    name: "已备案",
  },
];
export const school_class = [
  {
    id: 1,
    name: "公办",
  },
  {
    id: 2,
    name: "公办民营",
  },
  {
    id: 3,
    name: "民办",
  },
  {
    id: 4,
    name: "民办公助",
  },
];
export const free_type = [
  {
    id: 1,
    name: "非营利性",
  },
  {
    id: 2,
    name: "营利性",
  },
];
export const server_type = [
  {
    id: 1,
    name: "全日托",
  },
  {
    id: 2,
    name: "半日托",
  },
  {
    id: 3,
    name: "计时托",
  },
  {
    id: 4,
    name: "临时托",
  },
];
export const facility_type = [
  {
    id: 1,
    name: "独立",
  },
  {
    id: 2,
    name: "合建",
  },
];
export const place_type = [
  {
    id: 1,
    name: "自有",
  },
  {
    id: 2,
    name: "租赁",
  },
];
export const feed_type = [
  {
    id: 1,
    name: "自行",
  },
  {
    id: 2,
    name: "第三方",
  },
];
export const school_nature_ids = [
  {
    id: 1,
    name: "综合服务中心",
  },
  {
    id: 2,
    name: "普惠托育机构",
  },
  {
    id: 3,
    name: "示范托育机构",
  },
  {
    id: 4,
    name: "用人单位办托",
  },
  {
    id: 5,
    name: "省级示范托育机构",
  },
  {
    id: 6,
    name: "市级示范托育机构",
  },
  {
    id: 7,
    name: "托班（幼儿园）",
  },
  {
    id: 8,
    name: "社区托育服务中心",
  },
  {
    id: 9,
    name: "家庭托育点",
  },
  {
    id: 10,
    name: "其他",
  },
];
export const other_facilitys = [
  {
    id: 1,
    name: "监控设备",
  },
  {
    id: 2,
    name: "校车接送",
  },
  {
    id: 3,
    name: "保健室",
  },
  {
    id: 4,
    name: "盥洗室配套",
  },
];

export const cat_id = [
  {
    id: '',
    name: '全部'
  },
  {
    id: 1,
    name: '云看托'
  }, {
    id: 2,
    name: '机构活动'
  }, {
    id: 3,
    name: '婴幼儿照护'
  }, {
    id: 4,
    name: '卫生保健'
  }, {
    id: 5,
    name: '安全管理'
  }, {
    id: 6,
    name: '托育师风采'
  }, {
    id: 7,
    name: '中国文化'
  }, {
    id: 8,
    name: '其他'
  }
]
