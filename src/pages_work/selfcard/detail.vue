<template>
  <u-navbar title="个人名片" ref="navbar" :background="navBackground" :border-bottom="false"></u-navbar>
  <view class="root">
    <image class="iv_bg_card" mode="widthFix" :src="img" />

    <view class="share_save_content" v-if="isWeixinBrowser">
      <view class="save_content">
        <view class="txt_type_four">长按保存</view>
      </view>
    </view>
    <view class="share_save_content" v-else>
      <view class="save_content" @click="save">
        <image class="iv_share_save" src="https://obs.tuoyupt.com/miniprogram/client/images/xia.png" />
        <view class="txt_type_four">存到相册</view>
      </view>
    </view>
    <canvas class="canvas" canvas-id="save-picture" force-use-old-canvas="true" :disable-scroll="true" :style="{
      width: calc(750) + 'px',
      height: calc(1524) + 'px',
      background: '#fff',
    }" type="2d" id="myCanvas"></canvas>
  </view>
</template>

<script setup>
import { reactive, ref } from "vue";
import request from "@/request.js";
import { onLoad } from "@dcloudio/uni-app";
const isWeixinBrowser = /(MicroMessenger)/ig.test(navigator.userAgent);
const selfCardObj = reactive({
  user_name: "", //用户名
  school_name: "", //学校名
  serve_type: "", //全日托,半日托,计时托,临时托
  pos_number: "0", //托位数量
  user_url: "", //用户头像url
  user_code: "", //用户二维码
});
let canvas = null;
const distanceX = 0; //canvas偏移量 X轴
const distanceY = 0; //canvas偏移量 Y轴

const navBackground = {
  backgroundColor: "#FFEBE3",
};

import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();

onLoad(() => {
  reqSelfCard();
});

/**
 * 请求个人名片信息
 */

const reqSelfCard = () => {
  //个人名片数据
  let params = {
    school_id: userInfo.school_id,
    student_id: userInfo.student_id,
  };

  request({
    method: "GET",
    url: "/api/auth/personalcard",
    data: params,
  }).then(async (res) => {
    if (res != null && res.length != 0) {
      selfCardObj.user_name = res.parent.parent_name;
      selfCardObj.user_code = res.mini_code;
      selfCardObj.user_url = res.parent.img_url;
      selfCardObj.school_name = res.school.wechatname;
      selfCardObj.serve_type = res.school.serve_type;
      selfCardObj.pos_number = res.school.service.build_children_num;
      handleCanvas(2)
    } else {

    }
  });
};

/**
 * 保存到本地
 */
const save = () => {
  // 创建下载链接
  let downloadLink = document.createElement("a");
  downloadLink.download = "selfcard.jpg";
  downloadLink.href = img.value;
  // 触发点击事件以开始下载
  downloadLink.click();

};

function handleCanvas() {
  canvas = document.querySelector("canvas");
  const ctx = canvas.getContext("2d");

  //   绘制渐变背景
  const grd = ctx.createLinearGradient(0, 0, 0, calc(750), calc(1524)); // 创建一个渐变色对象
  grd.addColorStop(0, "#FFEBE3");
  grd.addColorStop(1, "#CED6F7");
  ctx.fillStyle = grd;
  //   ctx.fillStyle = "white"; //设置填充颜色
  ctx.fillRect(0, 0, calc(750), calc(1524));

  let imageTop = new Image(); //顶部小人图
  imageTop.crossOrigin = "anonymous";
  let ivBgCard = new Image();//绘制白色卡片
  ivBgCard.crossOrigin = "anonymous";
  let logo = new Image(); // // 绘制logo
  logo.crossOrigin = "anonymous";
  let userphoto = new Image();// // 绘制头像
  userphoto.crossOrigin = "anonymous";
  let code = new Image(); // 绘制二维码
  code.crossOrigin = "anonymous";
  let iconService = new Image();
  iconService.crossOrigin = "anonymous";
  let iconPosition = new Image(); // 建设托位数
  iconPosition.crossOrigin = "anonymous";
  let tips = new Image(); //提示图片
  tips.crossOrigin = "anonymous";
  let ok = new Image(); //对勾
  ok.crossOrigin = "anonymous";
  imageTop.src =
    "https://obs.tuoyupt.com/miniprogram/manage/images/poster_bg1.png"; // 顶部小人图
  imageTop.onload = function () {
    ctx.drawImage(imageTop, 0, 0, calc(750), calc(466));
    ivBgCard.src =
      "https://obs.tuoyupt.com/miniprogram/manage/images/poster_bg4.png"; // 白色背景
    ivBgCard.onload = function () {
      logo.src =
        "https://obs.tuoyupt.com/miniprogram/manage/images/icon_logo.png"; // 引入本地图片
      ctx.drawImage(
        ivBgCard,
        (calc(750) - calc(592)) / 2,
        calc(322),
        calc(592),
        calc(858)
      );

      logo.onload = function () {
        iconService.src = "https://obs.tuoyupt.com/nanjing/pstac/mine/icon_service_type.png"; // 引入本地图片
        drawCircleImage(
          ctx,
          logo,
          calc(distanceX + 138),
          calc(distanceY + 360),
          calc(20)
        );

        iconService.onload = function () {
          iconPosition.src = "https://obs.tuoyupt.com/nanjing/pstac/mine/icon_pos_num.png"; // 引入本地图片
          drawCircleImage(
            ctx,
            iconService,
            calc(distanceX + 138),
            calc(distanceY + 880),
            calc(11)
          );

          iconPosition.onload = function () {
            userphoto.src = selfCardObj.user_url.replace('tuoyufuwu.obs.cn-north-4.myhuaweicloud.com', 'obs.tuoyupt.com').replace('zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com', 'obs.tuoyupt.com');
            drawCircleImage(
              ctx,
              iconPosition,
              calc(distanceX + 138),
              calc(distanceY + 923),
              calc(11)
            );
            userphoto.onload = function () {
              ok.src = "https://obs.tuoyupt.com/nanjing/pstac/mine/icon_ok.png"; // 引入本地图片
              console.log("b:", calc(userphoto.width));
              console.log("b:", calc(userphoto.height));
              if (userphoto.width == userphoto.height) {
                ctx.save();
                handleBorderRect(
                  ctx,
                  calc(distanceX + 138),
                  calc(distanceY + 445),
                  calc(226),
                  calc(226),
                  calc(20)
                );
                ctx.clip();
                ctx.drawImage(
                  userphoto,
                  calc(distanceX + 138),
                  calc(distanceY + 445),
                  calc(226),
                  calc(226)
                );

                // 恢复之前保存的绘图上下文
                ctx.restore();
              } else {
                // 图片的x坐标
                let bg_x = calc(distanceX + 138);
                // 图片的y坐标
                let bg_y = calc(distanceY + 445);
                // 图片宽度
                let bg_w = calc(226);
                // 图片高度
                let bg_h = calc(226);
                // 图片圆角
                let bg_r = 10;

                // 绘制海报背景图片圆角
                ctx.save();
                ctx.beginPath();
                ctx.arc(
                  bg_x + bg_r,
                  bg_y + bg_r,
                  bg_r,
                  Math.PI,
                  Math.PI * 1.5
                );
                ctx.arc(
                  bg_x + bg_w - bg_r,
                  bg_y + bg_r,
                  bg_r,
                  Math.PI * 1.5,
                  Math.PI * 2
                );
                ctx.arc(
                  bg_x + bg_w - bg_r,
                  bg_y + bg_h - bg_r,
                  bg_r,
                  0,
                  Math.PI * 0.5
                );
                ctx.arc(
                  bg_x + bg_r,
                  bg_y + bg_h - bg_r,
                  bg_r,
                  Math.PI * 0.5,
                  Math.PI
                );
                ctx.clip();
                //获取等比例图
                drawImage1(
                  ctx,
                  bg_w,
                  bg_h,
                  userphoto,
                  calc(userphoto.width),
                  calc(userphoto.width),
                  bg_x,
                  bg_y
                );

                // 恢复之前保存的绘图上下文
                ctx.restore();
              }
            };

            ok.onload = function () {
              code.src = selfCardObj.user_code;
              drawCircleImage(
                ctx,
                ok,
                calc(distanceX + 340),
                calc(distanceY + 650),
                calc(24)
              );

              code.onload = function () {
                tips.src =
                  "https://obs.tuoyupt.com/aituoyu/imgs/self.png"; // 底部提示

                ctx.drawImage(
                  code,
                  calc(distanceX + 470),
                  calc(distanceY + 980),
                  calc(160),
                  calc(160)
                );
                tips.onload = function () {
                  ctx.drawImage(
                    tips,
                    (calc(750) - calc(264)) / 2,
                    calc(1203),
                    calc(264),
                    calc(70)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `bold ${calc(20)}px 黑体`;
                  ctx.fillStyle = "#262937";
                  ctx.fillText(
                    selfCardObj.user_name + "邀请您加入",
                    calc(distanceX + 190),
                    calc(distanceY + 375)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(20)}px 黑体`;
                  ctx.fillStyle = "#262937";
                  ctx.fillText(
                    selfCardObj.school_name,
                    calc(distanceX + 190),
                    calc(distanceY + 405)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `bold ${calc(40)}px 黑体`;
                  ctx.fillStyle = "#000";
                  ctx.fillText(
                    selfCardObj.user_name,
                    calc(distanceX + 138),
                    calc(distanceY + 757)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(22)}px 黑体`;
                  ctx.fillStyle = "#787C8D";
                  ctx.fillText(
                    selfCardObj.school_name,
                    calc(distanceX + 138),
                    calc(distanceY + 820)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(22)}px 黑体`;
                  ctx.fillStyle = "#787C8D";
                  ctx.fillText(
                    "这里可以让您的宝宝获得最优质的呵护",
                    calc(distanceX + 138),
                    calc(distanceY + 850)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `bold ${calc(20)}px 黑体`;
                  ctx.fillStyle = "#262937";
                  ctx.fillText(
                    "服务类型：",
                    calc(distanceX + 179),
                    calc(distanceY + 899)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `bold ${calc(20)}px 黑体`;
                  ctx.fillStyle = "#262937";
                  ctx.fillText(
                    "建设托位数：",
                    calc(distanceX + 179),
                    calc(distanceY + 940)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(20)}px 黑体`;
                  ctx.fillStyle = "#262937";
                  ctx.fillText(
                    selfCardObj.serve_type,
                    calc(distanceX + 280),
                    calc(distanceY + 899)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(20)}px 黑体`;
                  ctx.fillStyle = "#262937";
                  ctx.fillText(
                    selfCardObj.pos_number,
                    calc(distanceX + 300),
                    calc(distanceY + 940)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(22)}px 黑体`;
                  ctx.fillStyle = "#AEB0BC";
                  ctx.fillText(
                    "扫描二维码查看",
                    calc(distanceX + 138),
                    calc(distanceY + 1100)
                  );

                  ctx.textAlign = "left";
                  ctx.font = `${calc(22)}px 黑体`;
                  ctx.fillStyle = "#AEB0BC";
                  ctx.fillText(
                    "此二维码长期有效哦～",
                    calc(distanceX + 138),
                    calc(distanceY + 1135)
                  );
                };
                // 保存;
                setTimeout(() => {
                  drawAfterfunction(canvas);
                }, 100);
              };
            };
          };
        };
      };
    };
  };
}
const img = ref("")
//保存图片到本地
function drawAfterfunction(canvas) {
  img.value = canvas.toDataURL("image/jpeg", 1);
}

function drawCircleImage(ctx, img, x, y, r) {
  // 如果在绘制图片之后还有需要绘制别的元素，需启动 save() 、restore() 方法，否则 clip() 方法会导致之后元素都不可见
  //  save()：保存当前 Canvas 画布状态
  // restore()：恢复到保存时的状态
  ctx.save();
  ctx.beginPath();
  let d = r * 2;
  let cx = x + r;
  let cy = y + r;
  ctx.arc(cx, cy, r, 0, 2 * Math.PI, true);
  ctx.strokeStyle = "#FFFFFF"; // 设置绘制圆形边框的颜色
  ctx.stroke(); // 绘制出圆形，默认为黑色，可通过 ctx.strokeStyle = '#FFFFFF'， 设置想要的颜色
  ctx.clip();
  ctx.drawImage(img, x, y, d, d);
  ctx.restore();
  ctx.closePath();
}

/** 等比例图片方法
    ctx  画布
    bg_w  图片绘制区域的宽
    bg_h  图片绘制区域的高
    imgPath  图片路径
    imgWidth  图片的原始宽度
    imgPath  图片的原始高度
    x  图片绘制的左上角x坐标
    y  图片绘制的左上角y坐标
  **/
function drawImage1(ctx, bg_w, bg_h, imgPath, imgWidth, imgHeight, x, y) {
  let dWidth = bg_w / imgWidth; // canvas与图片的宽度比例
  let dHeight = bg_h / imgHeight; // canvas与图片的高度比例
  if (
    (imgWidth > bg_w && imgHeight > bg_h) ||
    (imgWidth < bg_w && imgHeight < bg_h)
  ) {
    if (dWidth > dHeight) {
      ctx.drawImage(
        imgPath,
        calc(100),
        calc(100) + (imgHeight - bg_h / dWidth) / 2,
        imgWidth,
        bg_h / dWidth,
        x,
        y,
        bg_w,
        bg_h
      );
    } else {
      ctx.drawImage(
        imgPath,
        calc(100) + (imgWidth - bg_w / dHeight) / 2,
        calc(100),
        bg_w / dHeight,
        imgHeight,
        x,
        y,
        bg_w,
        bg_h
      );
    }
  } else {
    if (imgWidth < bg_w) {
      ctx.drawImage(
        imgPath,
        calc(100),
        calc(100) + (imgHeight - bg_h / dWidth) / 2,
        imgWidth,
        bg_h / dWidth,
        x,
        y,
        bg_w,
        bg_h
      );
    } else {
      ctx.drawImage(
        imgPath,
        calc(100) + (imgWidth - bg_w / dHeight) / 2,
        calc(100),
        bg_w / dHeight,
        imgHeight,
        x,
        y,
        bg_w,
        bg_h
      );
    }
  }
}

/**
 * 图片圆角设置
 * @param string x x轴位置
 * @param string y y轴位置
 * @param string w 图片宽
 * @param string y 图片高
 * @param string r 圆角值
 */
function handleBorderRect(ctx, x, y, w, h, r) {
  ctx.beginPath();
  // 左上角
  ctx.arc(x + r, y + r, r, Math.PI, 1.5 * Math.PI);
  ctx.moveTo(x + r, y);
  ctx.lineTo(x + w - r, y);
  ctx.lineTo(x + w, y + r);
  // 右上角
  ctx.arc(x + w - r, y + r, r, 1.5 * Math.PI, 2 * Math.PI);
  ctx.lineTo(x + w, y + h - r);
  ctx.lineTo(x + w - r, y + h);
  // 右下角
  ctx.arc(x + w - r, y + h - r, r, 0, 0.5 * Math.PI);
  ctx.lineTo(x + r, y + h);
  ctx.lineTo(x, y + h - r);
  // 左下角
  ctx.arc(x + r, y + h - r, r, 0.5 * Math.PI, Math.PI);
  ctx.lineTo(x, y + r);
  ctx.lineTo(x + r, y);

  ctx.fill();
  ctx.closePath();
}

function calc(num) {
  const width = uni.getSystemInfoSync().screenWidth;
  return (num / 750) * width;
}

</script>
<style>
page {
  background: linear-gradient(180deg, #ffebe3 0%, #ced6f7 100%);
  height: 100%;
}
</style>

<style scoped>
.root {
  position: relative;
}

.iv_bg_top {
  width: 100%;
  transform: translateY(-39%);
}

.v_main {
  top: 30%;
  width: 592rpx;
  height: 858rpx;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.v_main_content {
  top: 30%;
  width: 592rpx;
  height: 1258rpx;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  z-index: 10;
}

.iv_bg_card {
  width: 100vw;
  height: 100%;
}

/* 卡片 标题栏 */
.v_main_title_c {
  margin-left: 60rpx;
  margin-top: 40rpx;
  display: flex;
}

.iv_photo {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}

.iv_user_photo {
  width: 226rpx;
  height: 226rpx;
  border-radius: 10%;
  margin-left: 60rpx;
  margin-top: 44rpx;
}

.v_person_name {
  width: auto;
  height: 28rpx;
  font-size: 20rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #262937;
  margin-left: 8rpx;
  line-height: 28rpx;
}

.v_school_name {
  width: 200rpx;
  height: 28rpx;
  margin-top: 2rpx;
  font-size: 20rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  margin-left: 8rpx;
  color: #262937;
  line-height: 28rpx;
}

.iv_user_name {
  width: auto;
  height: 40rpx;
  margin-top: 40rpx;
  font-size: 40rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 40rpx;
  margin-left: 60rpx;
}

.iv_user_school {
  width: auto;
  height: 32rpx;
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  margin-top: 25rpx;
  color: #787c8d;
  line-height: 32rpx;
  margin-left: 60rpx;
}

.iv_tips {
  width: auto;
  height: auto;
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #787c8d;
  margin-top: 2rpx;
  line-height: 32rpx;
  margin-left: 60rpx;
}

.iv_service_cotent {
  display: flex;
  flex-direction: row;
  width: auto;
  height: auto;
  align-items: center;

  margin-top: 20rpx;
  margin-left: 60rpx;
}

.iv_pos_cotent {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: auto;
  height: auto;
  margin-top: 5rpx;
  margin-left: 60rpx;
}

.icon {
  width: 20rpx;
  height: 20rpx;
}

.txt_type_one {
  width: auto;
  height: auto;
  font-size: 21rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 550;
  color: #262937;
  margin-left: 15rpx;
  line-height: 36rpx;
}

.txt_type_two {
  width: auto;
  height: auto;
  font-size: 21rpx;
  margin-left: 10rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 350;
  color: #262937;
  line-height: 36rpx;
}

.code_cotent {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: auto;
  margin-top: 10rpx;
  margin-left: 60rpx;
}

.txt_tips_cotent {
  width: auto;
  height: auto;
}

.iv_code {
  width: 160rpx;
  margin-left: 100rpx;
  height: 160rpx;
}

.txt_type_three_a {
  width: auto;
  height: auto;
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 300;
  margin-top: 100rpx;
  color: #aeb0bc;
  line-height: 32rpx;
}

.txt_type_three_b {
  width: auto;
  height: auto;
  font-size: 22rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 200;
  color: #aeb0bc;
  line-height: 32rpx;
}

.iv_card_tips {
  width: 264rpx;
  height: 80rpx;
  top: 71%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 13;
}

.share_save_content {
  top: 88%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 11;
  display: flex;
  flex-direction: row;
}

.iv_share_save {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.share_content {
  width: auto;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.save_content {
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.txt_type_four {
  width: 110rpx;
  height: auto;
  text-align: center;
  margin-top: 10rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 300;
  color: #787c8d;
}

.canvas {
  top: 0;
  position: absolute;
  left: 100%;
  /* background: #000000; */
  /* transform: translateX(-50%); */
  z-index: 13;
  display: flex;
  flex-direction: row;
  opacity: 1;
}

.user_photo_container {
  position: relative;
}

.iv_ok {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  left: 43%;
  top: 86%;
}
</style>
