<template>
  <view class="pages safe-area-inset-bottom">
    <u-navbar title="我的转介绍" ref="navbar" :background="{ background: '#FFFFFF' }" :border-bottom="false"></u-navbar>
    <scroll-view scroll-y class="scroll" @scrolltolower="scrolltolower" :refresher-triggered="triggered"
      :refresher-enabled="true" @refresherrefresh="refresh">
      <image class="top" src="https://obs.tuoyupt.com/aituoyu/imgs/header.png"></image>
      <view class="body">
        <image class="i2" src="https://obs.tuoyupt.com/miniprogram/invite/in2.png"></image>
        <view class="list">
          <view class="tit">我的邀请</view>
          <template v-if="list.length">
            <view class="item" v-for="item in list" :key="item.id">
              <view class="avter">
                <image class="img" :src="item.img_url" v-if="item.img_url"></image>
                <view v-else class="ic">
                  {{ item.name.charAt(0) }}
                </view>
              </view>
              <view class="info">
                <view class="name">{{ item.name }}</view>
                <view class="time">{{ item.phone.replace(/^(\d{3})\d{4}(\d{4})$/, "$1****$2") }}</view>
              </view>
              <view class="btn">
                {{ visitStatus(item.visit_status) }}
              </view>
            </view>
          </template>
          <view v-else class="empty">
            <u-empty src="https://obs.tuoyupt.com/miniprogram/invite/gift.png" text="暂无邀请记录"></u-empty>
          </view>
        </view>
        <view class="zhanwei"></view>
      </view>
    </scroll-view>
    <view class="footer">
      <view class="btn" @click="onFace">面对面邀请</view>
      <view class="btn" @click="onShare">邀请好友</view>
    </view>
  </view>
  <view v-if="inviteTemp" class="cleara">
    <l-painter :board="inviteTemp" isCanvasToTempFilePath @success="onImgOK" />
  </view>
  <u-popup v-model="yshow" mode="center">
    <view class="ysce">
      <image class="img" :src="imgurl"></image>
      <view class="yfooter">
        <view class="btns" v-if="isWeixinBrowser">
          <view class="down">
            <text class="ti">长按图片存到相册</text>
          </view>
        </view>
        <view class="btns" v-else>
          <view class="down" @click="down">
            <image class="icon" src="https://obs.tuoyupt.com/miniprogram/client/images/xia.png">
            </image>
            <text class="ti">存到相册</text>
          </view>
        </view>
        <view class="quxiao" @click="yshow = false">取消</view>
      </view>
    </view>
  </u-popup>
  <u-popup v-model="fshow" mode="center">
    <view class="face">
      <view class="avter">
        <image class="img" :src="parent.img_url" v-if="parent.img_url"></image>
        <view v-else class="ic">
          {{ parent.name.charAt(0) }}
        </view>
      </view>
      <view class="info">
        <view class="name">{{ parent.name }}</view>
        <view class="phone">{{ userInfo.phone }}</view>
      </view>
      <view class="csection">
        <image :src="code" class="code"></image>
        <view class="text">我的邀请码</view>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { ref } from "vue";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js"
import { onLoad } from "@dcloudio/uni-app";
const userInfo = useUserStore();
import templte from "./invite.json";
const isWeixinBrowser = /(MicroMessenger)/ig.test(navigator.userAgent);
onLoad(() => {
  getData();
  reqSelfCard();
})
const code = ref('')
let parent = ref({
  name: ""
})
let school = {}
function reqSelfCard() {
  uni.showLoading({
    mask: true
  })
  //个人名片数据
  request({
    method: "GET",
    url: "/api/auth/personalcard",
    data: {
      school_id: userInfo.school_id,
      student_id: userInfo.student_id,
    }
  }).then(async (res) => {
    uni.hideLoading();
    code.value = res.mini_code;
    parent.value = res.parent;
    school = res.school;
  });
};
let per_page = 10;
let page = 1;
const list = ref([]);
const triggered = ref(false);
async function refresh() {
  page = 1;
  canMore = true;
  triggered.value = true;
  await getData();
  triggered.value = false;
}
let canMore = true;
function scrolltolower() {
  if (canMore) {
    page++;
    getData(2);
  }
}
function getData(isMore = 1) {
  return request({
    url: "/api/parent/potential/invite",
    data: {
      school_id: userInfo.school_id,
      per_page,
      page
    }
  }).then(res => {
    if (isMore == 1) {
      list.value = res;
    } else {
      list.value.push(...res);
    }
    if (res.length < per_page) {
      canMore = false;
    }
  })
}
function visitStatus(status) {
  let str = "";
  switch (status) {
    case 0:
      str = "未邀约";
      break;
    case 1:
      str = "已邀约";
      break;
    case 2:
      str = "已到访";
      break;
  }
  return str
}
const fshow = ref(false)
function onFace() {
  fshow.value = true;
}
const inviteTemp = ref("");
const yshow = ref(false)
function onShare() {
  if (imgurl.value == '') {
    uni.showLoading()
    let newtemp = templte;
    newtemp.views[1].src = school.brand_logo.replace('tuoyufuwu.obs.cn-north-4.myhuaweicloud.com', 'obs.tuoyupt.com').replace('zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com', 'obs.tuoyupt.com');
    newtemp.views[2].text = school.wechatname;
    newtemp.views[3].text = school.serve_str;
    newtemp.views[4].src = parent.value.img_url.replace('tuoyufuwu.obs.cn-north-4.myhuaweicloud.com', 'obs.tuoyupt.com').replace('zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com', 'obs.tuoyupt.com');
    newtemp.views[5].text = parent.value.name;
    newtemp.views[6].src = code.value;
    inviteTemp.value = JSON.parse(JSON.stringify(newtemp))
  } else {
    yshow.value = true;
  }
}
const imgurl = ref('')
function onImgOK(e) {
  imgurl.value = e;
  uni.hideLoading();
  yshow.value = true;
}
function down() {
  let arr = imgurl.value.split(',');
  let bytes = atob(arr[1]);
  let ab = new ArrayBuffer(bytes.length);
  let ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  let blob = new Blob([ab], { type: 'application/octet-stream' });
  let url = URL.createObjectURL(blob);

  let downloadLink = document.createElement("a");
  downloadLink.download = "introduce.jpg";
  downloadLink.href = url;
  // 触发点击事件以开始下载
  downloadLink.click();

}

</script>
<style scoped lang="scss">
.cleara {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
}

.pages {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;

  .scroll {
    flex: 1;
    min-height: 1rpx;

    .top {
      width: 750rpx;
      height: 860rpx;
      display: block;
    }

    .body {
      width: 750rpx;
      min-height: 588rpx;
      background: #5EFC5B;
      display: flex;
      flex-direction: column;
      align-items: center;

      .i2 {
        width: 690rpx;
        height: 242rpx;
        margin: 0 auto;
      }

      .list {
        width: 690rpx;
        min-height: 410rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        position: relative;
        overflow: hidden;
        margin-top: 24rpx;

        .empty {
          height: 410rpx;
        }

        .item {
          width: 100%;
          display: flex;
          flex-direction: row;
          margin-bottom: 60rpx;
          padding: 0 30rpx;
          box-sizing: border-box;
          align-items: center;

          .avter {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            overflow: hidden;

            .ic {
              width: 80rpx;
              height: 80rpx;
              background: #787C8D;
              border-radius: 50%;
              text-align: center;
              line-height: 80rpx;
              color: #FFFFFF;
              font-size: 32rpx;
            }

            .img {
              width: 80rpx;
              height: 80rpx;
            }
          }

          .info {
            flex: 1;
            margin: 0 26rpx;

            .name {
              font-size: 28rpx;
              font-weight: 500;
              color: #262937;
            }

            .time {
              font-size: 24rpx;
              font-weight: 400;
              color: #AEB0BC;
              margin-top: 8rpx;
            }

          }

          .btn {
            width: 120rpx;
            height: 48rpx;
            text-align: center;
            line-height: 48rpx;
            border-radius: 24rpx;
            background: #EEEEEE;
            font-size: 24rpx;
            font-weight: 400;
            color: #262937;
          }
        }

        .tit {
          width: 154rpx;
          height: 54rpx;
          font-size: 28rpx;
          text-align: center;
          font-weight: 600;
          color: #000000;
          line-height: 54rpx;
          background-image: url(https://obs.tuoyupt.com/miniprogram/invite/in3.png);
          background-size: contain;
          background-repeat: no-repeat;
          margin-bottom: 24rpx;
        }
      }

      .zhanwei {
        width: 100%;
        height: 40rpx;
      }
    }
  }

  .footer {
    width: 750rpx;
    height: 100rpx;
    background: #FFFFFF;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;

    .btn {
      width: 330rpx;
      height: 80rpx;
      background: #000000;
      border-radius: 16rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 80rpx;
    }
  }
}

.face {
  width: 690rpx;
  height: 972rpx;
  background-image: url(https://obs.tuoyupt.com/miniprogram/invite/in4.png);
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;

  .csection {
    width: 570rpx;
    height: 640rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    text-align: center;
    padding-top: 20rpx;
    left: 62rpx;
    top: 272rpx;
    position: absolute;

    .text {
      font-size: 28rpx;
      font-weight: 600;
      color: #000000;
      margin-top: 20rpx;
    }

    .code {
      width: 530rpx;
      height: 530rpx;
    }
  }

  .info {
    width: 256rpx;
    height: 80rpx;
    left: 130rpx;
    top: 132rpx;
    position: absolute;

    .name {
      font-size: 28rpx;
      font-weight: 600;
      color: #262937;
      line-height: 40rpx;
    }

    .phone {
      font-size: 24rpx;
      font-weight: 400;
      color: #262937;
      line-height: 34rpx;
    }
  }

  .avter {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    left: 40rpx;
    top: 132rpx;
    position: absolute;

    .ic {
      width: 80rpx;
      height: 80rpx;
      background: #787C8D;
      border-radius: 50%;
      text-align: center;
      line-height: 80rpx;
      color: #FFFFFF;
      font-size: 32rpx;
    }

    .img {
      width: 80rpx;
      height: 80rpx;

    }
  }
}

::v-deep.u-mode-center-box {
  background-color: rgba(255, 255, 255, 0) !important;
}

.ysce {
  .img {
    width: 690rpx;
    height: 880rpx;
    border-radius: 24rpx;
  }

  .yfooter {
    width: 690rpx;
    height: 262rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin-top: 24rpx;

    .btns {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      height: 172rpx;
      align-items: center;

      .icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }

      .ti {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
        margin-top: 4rpx;
      }

      .weixin {
        width: 200rpx;
        align-items: center;
        display: flex;
        flex-direction: column;

        .btn {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          opacity: 0;
        }
      }

      .down {
        display: flex;
        flex-direction: column;
        width: 200rpx;
        align-items: center;
      }
    }

    .quxiao {
      width: 690rpx;
      height: 90rpx;
      background: #F6F6F6;
      border-radius: 0rpx 0rpx 24rpx 24rpx;
      text-align: center;
      font-size: 28rpx;
      font-weight: 400;
      color: #787C8D;
      line-height: 90rpx;
    }
  }
}
</style>