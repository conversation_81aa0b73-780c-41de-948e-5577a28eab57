<template>
  <u-navbar title="" ref="navbar" :background="navBackground" :border-bottom="false"></u-navbar>

  <view class="content" :style="{ height: `calc(100vh - 30rpx - ${navbarHeight}px)` }">
    <view class="select_class">
      <view class="change_class_container">
        <!-- 当前选中班级 -->
        <text class="currentClassName">{{ curentClassObj.class_name }}</text>
      </view>
    </view>
    <view class="container-top">
      <!-- 默认流程 -->
      <view class="view_current_plan">
        <text class="currentPlanName">{{ curentClassObj.plan_name }}</text>
      </view>
      <view class="nodata" v-if="dailyData.length == 0">
        暂无内容
      </view>
      <scroll-view :style="{ height: `calc(100vh - 280rpx - ${navbarHeight}px)` }" class="scroll-box" :scroll-y="true"
        :show-scrollbar="false" :refresher-enabled="false" :refresher-triggered="triggered" :refresher-threshold="100"
        refresher-background="#F6F6F6" @refresherrefresh="onRefresh" @refresherrestore="onRestore"
        @scrolltolower="onReachBottom" v-else>
        <view class="daily_list">
          <!-- 每一个活动 -->
          <view v-for="(dailyItem, index) in dailyData" :key="index">
            <view class="daily_item">
              <view class="daily_item_left">
                <view class="daily_start_time">
                  {{ dailyItem.start_time }}
                </view>

                <view class="daily_end_time">
                  {{ dailyItem.end_time }}
                </view>
              </view>

              <view class="daily_item_middle">
                <view class="daliy_item_line"></view>
                <view class="daliy_item_curent_show" v-if="dailyItem.is_curtime">੦</view>

                <view class="daliy_item_curent_hidden" v-else>੦</view>
              </view>

              <view class="daily_item_right">
                <view class="daily_name">
                  {{ dailyItem.name }}
                </view>

                <view class="daily_during"> {{ dailyItem.time }}分钟 </view>
              </view>
            </view>
          </view>
        </view>

        <view class="scrollSpase"></view>
      </scroll-view>

      <!-- <view class="blank"></view> -->
    </view>

    <!-- <footer class="footer" v-show="false" @click="onShare">分享</footer> -->
  </view>
</template>


<script setup>
import { ref, reactive } from "vue";
import request from "@/request";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const curentClassObj = reactive({
  class_id: "0", //当前班级ID
  class_name: "--班", //当前班级名称
  plan_name: "一日流程", //当前计划类型
});

//一日流程数据集合
const dailyData = ref([]);

let navbarHeight = ref(0);
const navBackground = {
  backgroundColor: "#FFEBE3",
};

onLoad(() => {
  //获取用户身份类型 0其他1行政2教师3员工4超级管理员
  curentClassObj.class_id = userInfo.class_id;
  curentClassObj.class_name = userInfo.class_name;

  //请求一日流程计划
  reqDailyData(curentClassObj.class_id);
});


/**
 * 分享
 */
const onShare = () => {
  console.log("分享");
};


/**
 * 请求一日流程数据
 */
const reqDailyData = (class_id) => {
  let dailyDataParams = {
    class_id: class_id,
  };

  request({
    method: "GET",
    url: "/api/daily/list",
    data: dailyDataParams,
  }).then(async (res) => {

    if (res != null && res.length != 0) {
      dailyData.value = [];
      dailyData.value = res.list;

    } else {

      dailyData.value = [];
    }

    //处理当前流程时间段状态

    //获取当前系统时间
    let date = new Date();
    var h = date.getHours();
    var m = date.getMinutes();
    var curTime = h + ":" + m;
    for (var i = 0; i < dailyData.value.length; i++) {
      var startTime = dailyData.value[i].start_time;
      var endime = dailyData.value[i].end_time;
      if (curTime >= startTime && curTime <= endime) {
        console.log("当前时间范围内 ");
        dailyData.value[i].is_curtime = true;
      } else {
        dailyData.value[i].is_curtime = false;
      }
    }
    //同时开启定时任务
    updataUITimer(dailyData);
    //设置背景渐变色
    // page_bg.value=" linear-gradient(180deg, #ffebe3 0%, #ced6f7 100%)"
  });
};
/**
 * 每一分钟刷新一次页面状态
 */
onUnload(() => {
  timer && clearInterval(timer)
})
let timer = null;
const updataUITimer = (dailyData) => {
  timer = setInterval(() => {
    // 业务逻辑
    //获取当前系统时间
    let date = new Date();
    var h = date.getHours();
    var m = date.getMinutes();
    var curTime = h + ":" + m;
    for (var i = 0; i < dailyData.value.length; i++) {
      var startTime = dailyData.value[i].start_time;
      var endime = dailyData.value[i].end_time;
      if (curTime >= startTime && curTime <= endime) {
        console.log("当前时间范围内 ");
        dailyData.value[i].is_curtime = true;
      } else {
        dailyData.value[i].is_curtime = false;
      }
    }
  }, 60 * 1000);
};
</script>

<style>
page {
  background: linear-gradient(180deg, #ffebe3 0%, #ced6f7 100%);
}
</style>
<style scoped>
.content {
  padding-top: 30rpx;
  margin-left: 0rpx;
  /* display: flex; */
  /* flex-direction: column; */
  position: relative;
  /* background: linear-gradient(180deg, #ffebe3 0%, #ced6f7 100%); */
  /* padding-bottom: 150rpx; */
}

.container-top {
  height: 220rpx;
}

.select_class {
  /* margin-top: 40rpx; */
  width: auto;
  height: auto;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;

  align-items: center;
  justify-content: space-between;
  position: relative;
}

.select_class text {
  color: #000;
}

.select_class image {
  width: 40rpx;
  height: 40rpx;
}

.change_class_container {
  display: flex;
  align-items: center;
}

.currentClassName {
  color: #111111;
  font-size: 48rpx;
  font-weight: 500;
  vertical-align: middle;
}

.change_show {
  margin-left: 20rpx;
  width: 104rpx;
  height: 48rpx;
  border-radius: 32rpx;
  color: #262937;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
}

.change_hidden {
  margin-left: 20rpx;
  width: 104rpx;
  height: 48rpx;
  border-radius: 32rpx;
  color: #262937;
  opacity: 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view_current_plan {
  margin-top: 15rpx;
  padding: 0 22rpx;
}

.currentPlanName {
  width: auto;
  height: auto;
  color: #787c8d;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 28rpx;
}

.daily_list {
  padding: 0rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: left;
  /* justify-content: center; */
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  margin-left: 25rpx;
  margin-right: 25rpx;
  margin-bottom: 30rpx;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.daily_item {
  width: auto;
  height: 190rpx;
  display: flex;
  flex-direction: row;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  /* padding: 20rpx 20rpx 20rpx 20rpx; */
}

.daily_item_left {
  width: 145rpx;
  height: auto;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  /* padding: 20rpx 20rpx 20rpx 20rpx; */
}

.daily_item_middle {
  width: 120rpx;
  /* margin-left: 60rpx; */
  position: relative;
}

.daliy_item_line {
  width: 1rpx;
  height: 100%;
  background: #eeeeee;
  /* background: #000; */
  position: absolute;
  margin: auto;
  left: 50%;
  transform: translateX(-50%);
}

.daliy_item_curent_show {
  margin-top: 0rpx;
  opacity: 1;
  font-size: 30rpx;
  position: absolute;
  margin: auto;
  top: 22%;
  left: 50%;
  transform: translateX(-50%);
}

.daliy_item_curent_hidden {
  margin-top: 0rpx;
  opacity: 0;
  font-size: 30rpx;
  position: absolute;
  margin: auto;
  top: 22%;
  left: 50%;
  transform: translateX(-50%);
}

.daily_item_right {
  margin-left: 10rpx;
  width: 100%;
  height: auto;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  /* padding: 20rpx 20rpx 20rpx 20rpx; */
}

.daily_start_time {
  margin-top: 50rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262937;
  line-height: 32rpx;
}

.daily_end_time {
  margin-top: 25rpx;
  font-size: 24rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #aeb0bc;
  line-height: 24rpx;
}

.daily_name {
  margin-top: 47rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262937;
  line-height: 36rpx;
}

.daily_during {
  margin-top: 25rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #787c8d;
  line-height: 28rpx;
}

.footer {
  height: 80rpx;
  line-height: 60rpx;
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  bottom: 40rpx;
  color: white;
  border-radius: 50rpx;
  left: 0;
  right: 0;
  margin: auto;
  width: 300rpx;
  background: #2b93f5;
}

.nodata {
  width: 100%;
  height: 100%;
  display: flex;
  margin-top: 60%;
  align-items: center;
  justify-content: center;
  color: #a3b0bc;
  font-size: 28rpx;
}

.scrollSpase {
  width: 100%;
  height: 50rpx;
  background: transparent;
}

.scroll-box {
  background: transparent;
  overflow-y: scroll;
  overflow-x: hidden;
  z-index: 1;
  margin-top: 30rpx;

  padding-bottom: 30rpx;
}

::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
</style>
