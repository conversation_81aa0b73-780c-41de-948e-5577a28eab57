<template>
  <template v-if="showBack">
    <u-navbar ref="navbar" :is-back="true" :border-bottom="false" title="家长海报">
    </u-navbar>
  </template>
  <template v-else>
    <u-navbar ref="navbar" :is-back="false" :border-bottom="false" title="家长海报">
      <view class="slot-wrap">
        <User />
      </view>
    </u-navbar>
  </template>
  <u-tabs-swiper class="tabs" name="title" :height="100" active-color="#111" :bold="true" inactive-color="#AEB0BC"
    font-size="32" :list="list" :current="current" @change="tabsChange" :is-scroll="false"></u-tabs-swiper>
  <swiper ref="swiper" class="swiper" :style="{ height: `calc(100vh - ${navbarHeight}px - 104rpx)`, width: '100%' }"
    :current="swiperCurrent" @change="swiperChange">
    <swiper-item class="swiper-item" v-for="(item, index) in list" :key="index">
      <scroll-view :refresher-triggered="item.triggered" @refresherrestore="onRestore" :refresher-enabled="true"
        @refresherrefresh="refresh" style="height: 100%;overflow:hidden;box-sizing: border-box;" scroll-y="true"
        @scrolltolower="getList(index)">
        <template v-if="item.imgs.length">
          <image @click=" intoDetail(img.id)" class="poster_item" v-for="(img, i) in item.imgs" :key="i"
            :src="img.img_url_thumb" mode="aspectFill" lazy-load="true" />
          <u-loadmore margin-top="20" :status="item.status" :load-text="loadText" />
        </template>
        <u-empty v-else mode="list"></u-empty>
        <view style="height: 40rpx"></view>
      </scroll-view>
    </swiper-item>
  </swiper>
  <privacyPopup></privacyPopup>
</template>

<script setup>
import { ref, toRefs } from 'vue';
import { onLoad, onReady, onShow } from '@dcloudio/uni-app';
import privacyPopup from "@/components/privacyPopup.vue";
import request from "@/request.js"
import User from "@/components/user.vue"
const showBack = ref(false);
const list = ref()
let swiperCurrent = ref(0);
let current = ref(0);
let navbarHeight = ref(0);
let navbar = ref(null);
let loadText = {
  loadmore: '轻轻上拉',
  loading: '努力加载中',
  nomore: '海报每日更新，明天再来哦~'
}
const per_page = 15;
function swiperChange(e) {
  let idx = e.detail.current;
  swiperCurrent.value = idx;
  current.value = idx;
  getList(idx)
}

function tabsChange(e) {
  let idx = e;
  swiperCurrent.value = idx;
  current.value = idx;
  getList(idx)
}

function intoDetail(id) {
  if (id) {
    uni.navigateTo({
      url: '/pages_work/poster/detail?id=' + id,
      success: (result) => { },
      fail: () => { },
      complete: () => { }
    });
  }
}

async function refresh() {
  let { status, page, triggered } = toRefs(list.value[current.value])
  if (triggered.value) return;
  triggered.value = true;
  status.value = 'loadmore';
  page.value = 0;
  getList(current.value);
}

async function onRestore() {
  let { triggered } = toRefs(list.value[current.value])
  triggered.value = false; // 需要重置
};

function getList(idx) {
  let { imgs, status, page, id, triggered } = toRefs(list.value[idx])
  if (status.value == 'nomore' || status.value == 'loading') {
    return;
  }
  page.value += 1;
  status.value == 'loading'
  request({
    url: '/api/poster/list',
    data: {
      page: page.value,
      cat_id: id ? id.value : '',
      per_page,
      channel: 2
    }
  }).then(res => {
    if (page.value === 1) {
      imgs.value = [];
    }
    imgs.value = [...imgs.value, ...res];
    triggered.value = false;
    status.value = res.length < per_page ? 'nomore' : 'loadmore';
  })
}
let systemInfo = uni.getSystemInfoSync();
let statusBarHeight = systemInfo.statusBarHeight;
let navHeight = systemInfo.platform == 'ios' ? 44 : 48;
navbarHeight.value = statusBarHeight + navHeight;
onReady(async () => {
  uni.showLoading()
  request({
    url: '/api/poster/category',
  }).then(res => {
    res.unshift({
      title: '全部海报'
    })
    list.value = res.map(e => {
      e.imgs = [];
      e.page = 0;
      e.status = 'loadmore';
      e.triggered = false;
      return e
    });
    uni.hideLoading();
    getList(0);
  });
})
let isMock = false;
onLoad((option) => {
  if (option.mgzh == 2) {
    showBack.value = false;
    isMock = true;
  }
})
onShow(() => {
  if (!isMock) {
    let app = getApp();
    showBack.value = !app.globalData.isFormGZH;
  }
})
</script>

<style lang="scss" scoped>
.slot-wrap {
  display: flex;
  align-items: center;
  flex: 1;
}

.tabs {
  width: 100%;
  background: #fff;
}

.swiper {
  .swiper-item {
    overflow-y: auto;

    .poster_item {
      width: 212rpx;
      // height: 372rpx;
      margin: 30rpx 0 0 30rpx;
      background: #F6F6F6;
      border-radius: 16rpx;
      background: #F6F6F6 url('https://obs.tuoyupt.com/miniprogram/client/images/img_load.png') no-repeat center;
      background-size: 80rpx 80rpx;
    }
  }
}
</style>
