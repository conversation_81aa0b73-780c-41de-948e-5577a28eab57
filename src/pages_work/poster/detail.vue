<template>
  <u-navbar ref="navbar" :title="poster ? poster.name : '海报'"></u-navbar>
  <view class="content">
    <image :src="img" class="img" :style="{
      width: calc(480) + 'px',
      height: calc(1040) + 'px',
    }"></image>
    <canvas :style="{
      width: calc(480) + 'px',
      height: calc(1040) + 'px',
    }" class="canvas" type="2d" id="myCanvas"></canvas>
    <view v-if="isWeixinBrowser">
      长按保存
    </view>
    <u-button v-else style="width: 80%" size="medium" :ripple="true" @click="onSave" class="download" type="error"
      :custom-style="customStyle">保存海报</u-button>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request.js";
const isWeixinBrowser = /(MicroMessenger)/ig.test(navigator.userAgent);
let poster = ref(null);
let canvas = null;
let ctx = null;

let navbar = ref(null);
let customStyle = ref({
  backgroundColor: "#FF7373",
});
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();

let schoolInfo = {}
let id = ''
onLoad(async (options) => {
  id = options.id;
  let par = {}
  if (userInfo.school_id !== 0) {
    par = {
      identity: 0,
      school_id: userInfo.school_id
    }
  }
  request({
    url: "/api/poster/detail/" + options.id,
    data: par
  }).then((res) => {
    poster.value = res;
    if (userInfo.school_id && res.school_info) {
      schoolInfo = res.school_info;
    }
    drawPoster();
  });
});

const img = ref('')
function onSave() {
  // 创建下载链接
  let downloadLink = document.createElement("a");
  downloadLink.download = "poster.jpg";
  downloadLink.href = img.value;

  // 触发点击事件以开始下载
  downloadLink.click();
  uni.hideLoading();
}


async function drawPoster() {
  uni.showLoading({
    title: "海报生成中...",
  });
  canvas = document.querySelector("canvas");
  ctx = canvas.getContext("2d");
  ctx.fillStyle = '#fff';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  const main = new Image();
  main.crossOrigin = "anonymous";
  const logo = new Image();
  logo.crossOrigin = "anonymous";
  const qr = new Image();
  qr.crossOrigin = "anonymous";
  main.src = poster.value.img_url.replace('tuoyufuwu.obs.cn-north-4.myhuaweicloud.com', 'obs.tuoyupt.com').replace('zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com', 'obs.tuoyupt.com');;
  main.onload = () => {
    handleBorderRect(
      ctx,
      0,
      0,
      canvas.width,
      canvas.height,
      calc(16),
      "#fff"
    );
    ctx.clip();
    ctx.drawImage(main, 0, 0, calc(480), calc(1040));
    if (schoolInfo && schoolInfo.brand_logo) {
      logo.src = schoolInfo.brand_logo.replace('tuoyufuwu.obs.cn-north-4.myhuaweicloud.com', 'obs.tuoyupt.com').replace('zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com', 'obs.tuoyupt.com');;
    } else {
      logo.src =
        "https://obs.tuoyupt.com/aituoyu/logo/jiazhang.png";
    }
  };
  logo.onload = () => {
    if (schoolInfo && schoolInfo.schoolcode) {
      qr.src = schoolInfo.schoolcode;
    } else {
      qr.src =
        "https://obs.tuoyupt.com/aituoyu/logo/gzh.jpg";
    }
    drawCircleImage(ctx, logo, calc(16), calc(30), calc(24));
  };
  qr.onload = () => {
    ctx.textAlign = "left";
    ctx.font = `bold ${calc(24)}px 黑体`;
    ctx.fillStyle = poster.value.qrcode_font_color || '#000';
    let textleftOff = 0;
    if (schoolInfo && schoolInfo.wechatname) {
      let text = schoolInfo.wechatname;
      let textWidth = ctx.measureText(text).width;
      let leftOff = (calc(canvas.width) - textWidth - calc(352)) / 2;
      textleftOff = leftOff + calc(116);
      ctx.fillText(schoolInfo.wechatname, textleftOff, calc(948));
      ctx.drawImage(qr, leftOff, calc(908), calc(106), calc(106));
    } else {
      ctx.fillText("泸州市智慧托育服务系统", calc(160), calc(948));
      ctx.drawImage(qr, calc(16), calc(896), calc(128), calc(128));
    }
    ctx.font = `bold ${calc(22)}px 黑体`;
    ctx.fillStyle = poster.value.font_color;
    if (schoolInfo && schoolInfo.wechatname) {
      ctx.fillText(schoolInfo.wechatname, calc(72), calc(62));
    } else {
      ctx.fillText("泸州市智慧托育服务系统", calc(72), calc(62));
    }
    ctx.font = `${calc(16)}px 黑体`;
    ctx.fillStyle = poster.value.qrcode_font_color || '#000';
    if (schoolInfo && schoolInfo.service) {
      let phones = schoolInfo.service.phones.split(',')
      if (phones.length) {
        let t = `招生电话:${phones[0]}`;
        ctx.fillText(t, textleftOff, calc(980));
        let tWidth = ctx.measureText('招生电话:').width;
        for (let index = 1; index < phones.length; index++) {
          let p = phones[index];
          ctx.fillText(`${p}`, textleftOff + tWidth, calc(980 + index * 20));
        }
      }
    } else {
      ctx.fillText(poster.value.slogan || '托育 托起明天的希望 育出未来的力量', calc(160), calc(980));
    }
    uni.hideLoading();
    // 登录重绘后刷新
    img.value = canvas.toDataURL("image/jpeg", 1);
  };
}

function calc(num) {
  const width = uni.getSystemInfoSync().screenWidth;
  return (num / 750) * width;
}
function drawCircleImage(ctx, img, x, y, r) {
  // 如果在绘制图片之后还有需要绘制别的元素，需启动 save() 、restore() 方法，否则 clip() 方法会导致之后元素都不可见
  //  save()：保存当前 Canvas 画布状态
  // restore()：恢复到保存时的状态
  ctx.save();
  ctx.beginPath();
  let d = r * 2;
  let cx = x + r;
  let cy = y + r;
  ctx.arc(cx, cy, r, 0, 2 * Math.PI, true);
  ctx.strokeStyle = '#FFFFFF'; // 设置绘制圆形边框的颜色
  ctx.stroke(); // 绘制出圆形，默认为黑色，可通过 ctx.strokeStyle = '#FFFFFF'， 设置想要的颜色
  ctx.clip();
  ctx.drawImage(img, x, y, d, d);
  ctx.restore();
  ctx.closePath();
}
function handleBorderRect(ctx, x, y, w, h, r, color) {
  ctx.beginPath();
  // 左上角
  ctx.arc(x + r, y + r, r, Math.PI, 1.5 * Math.PI);
  ctx.moveTo(x + r, y);
  ctx.lineTo(x + w - r, y);
  ctx.lineTo(x + w, y + r);
  // 右上角
  ctx.arc(x + w - r, y + r, r, 1.5 * Math.PI, 2 * Math.PI);
  ctx.lineTo(x + w, y + h - r);
  ctx.lineTo(x + w - r, y + h);
  // 右下角
  ctx.arc(x + w - r, y + h - r, r, 0, 0.5 * Math.PI);
  ctx.lineTo(x + r, y + h);
  ctx.lineTo(x, y + h - r);
  // 左下角
  ctx.arc(x + r, y + h - r, r, 0.5 * Math.PI, Math.PI);
  ctx.lineTo(x, y + r);
  ctx.lineTo(x + r, y);

  ctx.fillStyle = color;
  ctx.fill();
  ctx.closePath();
}

</script>

<style lang="scss" scoped>
.content {
  position: relative;
  background: #f6f6f6;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  #myCanvas {
    margin: 40px auto;
  }

  .poster {
    width: 200px;
    height: 200px;
    background: #fff;
    // position: absolute;
    // top: 200vh;
    // transform: scale(0.5);
  }

  .download {
    height: 64rpx;
    font-size: 24rpx;
    font-weight: 500;
    color: #ffffff;
    display: block;
    margin: 40rpx auto;
    text-align: center;
    line-height: 64rpx;
  }
}

.img {
  margin: 40rpx;
}

.fixed {
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  left: 50%;
  bottom: 0;
  height: 200rpx;
  transform: translateX(-50%);
}



.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.title {
  font-size: 30rpx;
  font-weight: bold;
  padding: 0 40rpx;
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 80rpx;
  padding: 0 60rpx;
}

.canvas {
  background: #fff url("https://obs.tuoyupt.com/miniprogram/client/images/img_load.png") no-repeat center;
  background-size: "80rpx 80rpx";
  position: fixed;
  left: -999%;
  top: -999%;
}
</style>
