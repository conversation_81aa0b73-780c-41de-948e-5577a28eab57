<template>
    <u-navbar :title="navTitle" :background="navBackground" :border-bottom="false"></u-navbar>
    <view class="login">
        <view class="login-title">修改密码</view>
    </view>
    <view class="login-box">
        <view class="login-label">账号/手机号</view>
        <u-input v-model="account" type="text" placeholder="请输入" :border="false" :custom-style="inputStyle" placeholder-style="font-size: 32rpx; color: #787C8D;" />
        <view class="login-label" style="margin-top: 24rpx">验证码</view>
        <u-input v-model="verifyNum" type="number" placeholder="请输入" :password-icon="false" :border="false" :custom-style="inputStyle" placeholder-style="font-size: 32rpx; color: #787C8D;" />
        <text @click="getCode" class="login-verify">{{tips}}</text>
        <view class="login-label" style="margin-top: 24rpx">新密码</view>
        <u-input v-model="password" type="password" placeholder="请输入" :password-icon="false" :border="false" :custom-style="inputStyle" placeholder-style="font-size: 32rpx; color: #787C8D;" />
        <view class="login-label" style="margin-top: 24rpx">确认新密码</view>
        <u-input v-model="rePassword" type="password" placeholder="请输入" :password-icon="false" :border="false" :custom-style="inputStyle" placeholder-style="font-size: 32rpx; color: #787C8D;" />
        <u-toast ref="uToast1"></u-toast>
        <u-verification-code :seconds="seconds" @end="end" @start="start" ref="uCode1"
                             @change="codeChange" change-text="Xs"></u-verification-code>
    </view>
    <view class="login-box" style="align-items: center">
        <u-button type="primary" :custom-style="loginButton" @click="reset">确认修改</u-button>
    </view>

</template>

<script setup>
import {ref, getCurrentInstance} from "vue";
import request from "@/request";
const { proxy }  = getCurrentInstance();
const tips = ref('')
const seconds = ref(60)
const navBackground = {
    backgroundColor:"#ffffff"
}
const verify = {
    border: "none"
}
const inputStyle = {
    borderBottom: "1px solid #EEEEEE",
    fontSize: "32rpx",
    color: "#000000",
    fontWeight: "500"
}
const loginButton = {
    width: "240rpx",
    backgroundColor: "#5F7CFC"
}
const navTitle = ref('账号登录')
// 0 为密码登录，1 为验证码
const pageType = ref(0)
const checkValue = ref(false)
const account = ref('')
const verifyNum = ref('')
const password = ref('')
const rePassword = ref('')
const getUserInfo = (detail) => {
    console.log(detail)
}
const changePage = () => {
    if(pageType.value === 0) {
        navTitle.value = '验证码登录'
        pageType.value = 1
    } else {
        navTitle.value = '账号登录'
        pageType.value = 0
    }
}
const codeChange = (text) => {
    tips.value = text;
}
const getCode = () => {
    console.log('can')

    if(proxy.$refs.uCode1.canGetCode) {
        // 模拟向后端请求验证码
        // 这里此提示会被this.start()方法中的提示覆盖
        proxy.$u.toast('验证码已发送');
        // 通知验证码组件内部开始倒计时
        if(account.value.length !== 11) {
            proxy.$u.toast('请输入正确的手机号');
        } else {
            proxy.$refs.uCode1.start();
        }
    } else {
        proxy.$u.toast('倒计时结束后再发送');
    }
}
const end = () => {
    // proxy.$u.toast('倒计时结束');
}
const start = async () => {
    // console.log(account.value.length)
    if(account.value.length !== 11) {
        proxy.$u.toast('请输入正确的手机号');
    } else {
        let params = {
            phone: account.value
        }
        // proxy.$u.toast('倒计时开始');
        await request({
            method: 'POST',
            url: `/api/teach/auth/getphonecode`,
            data: params
        }).then( async (res) => {
            console.log(res)
        })
    }
}
const toForget = () => {
    uni.navigateTo({
        url: "/pages_work/login/forget",
    })
}
const reset = async () => {
    // console.log(password.value.length,rePassword.value.length)
    if(account.value.length !== 11) {
        proxy.$u.toast('请输入正确的手机号');
    }  else {
        if(password.value.length === 0 && rePassword.value.length === 0) {
            proxy.$u.toast('请输入密码');
        }
        else {
            if(password.value !== rePassword.value) {
                proxy.$u.toast('密码前后不一致');
            } else {
                let query = {
                    phone : account.value,
                    code : verifyNum.value,
                    password : password.value,
                    re_password : rePassword.value,
                }
                await request({
                    method: 'POST',
                    url: `/api/teach/auth/reset`,
                    data: query
                }).then((result) => {
                    console.log(result)
                    proxy.$u.toast('更改成功');
                    uni.navigateBack()
                })
            }
        }
    }
}

</script>

<style scoped>
.login {
    box-sizing: border-box;
    padding: 0 112rpx;
    margin-top: 80rpx;
    display: flex;
    justify-content: center;
    align-items: baseline;
}
.login-title {
    margin-right: auto;
    font-size: 48rpx;
    font-weight: 500;
    color: #000000;
    line-height: 48rpx;
}
.login-subtitle {
    font-size: 28rpx;
    font-weight: 500;
    color: #787C8D;
    line-height: 48rpx;
}
.login-verify {
    font-size: 28rpx;
    font-weight: 500;
    color: #5F7CFC;
    line-height: 40rpx;
}
.login-box {
    padding: 0 112rpx;
    margin-top:80rpx;
    display: flex;
    flex-direction: column;
}
.login-label {
    font-size: 24rpx;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 24rpx;
}
.login-forget {
    margin-top: 40rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #787C8D;
    line-height: 40rpx;
}
</style>