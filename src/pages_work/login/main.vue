<template>
    <view class="main">
        <image class="main-image" src="https://obs.tuoyupt.com/miniprogram/manage/images/background.png"></image>
        <view class="main-center">
            <view class="main-title">
                欢迎登录
                <view class="main-signal">家长使用端</view>
            </view>
            <view class="main-subtitle">泸州市智慧托育服务系统</view>
            <!-- <u-button type="default" :custom-style="primaryStyle" :hair-line="false" hover-class="main-btn"
                @click="showVerify" v-if="!checkValue">快捷登录</u-button>
            <u-button type="default" :custom-style="primaryStyle" :hair-line="false" hover-class="main-btn"
                open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" v-else>快捷登录</u-button> -->
            <u-button type="default" :custom-style="primaryStyle" :hair-line="false" hover-class="main-btn"
                @click="toLogin">手机号登录</u-button>
            <view class="flex-mode">
                <u-checkbox v-model="checkValue" shape="circle" active-color="#FF7373" icon-size="32">
                    <view class="main-check flex-mode">
                        登录即为同意 <view class="link" style="margin-left: 10rpx;" @click="toAgree(1)">用户协议</view>、<view
                            class="link" @click="toAgree(2)">隐私政策</view>
                    </view>
                </u-checkbox>
            </view>
        </view>
        <u-toast ref="uToast" />
        <privacyPopup></privacyPopup>
    </view>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import request from "@/request";
import privacyPopup from "@/components/privacyPopup.vue";
const { proxy } = getCurrentInstance();
import { useUserStore } from "@/stores/userInfo.js"
import { onLoad } from "@dcloudio/uni-app";
const userInfo = useUserStore();
const primaryStyle = {
    border: 'none',
    color: "#FFFFFF",
    backgroundColor: "#FF7373",
    marginTop: "62rpx",
    marginBottom: "40rpx",
    width: "690rpx",
    height: "80rpx",
}
const phoneStyle = {
    color: "#FF7373",
    marginTop: "24rpx",
    marginBottom: "104rpx",
    width: "690rpx",
    height: "80rpx",
}
const checkValue = ref(false)
const toAgree = (type) => {
    uni.navigateTo({
        url: `/pages_work/login/agreement?type=${type}`
    })
}
let redirectUrl = null;
let isGZH = 0;
onLoad(option => {
    if (option.redirect) {
        redirectUrl = option.redirect;
    }
    if (option.isGZH) {
        isGZH = option.isGZH;
    }
})
const toLogin = () => {
    if (checkValue.value) {
        console.log(1)
        uni.navigateTo({
            url: "/pages_work/login/login",
        })
    }
    else {
        proxy.$refs.uToast.show({
            title: '请先阅读并勾选协议'
        })
    }
}
const getAuth = async () => {
    if (uni.getStorageSync('token')) {
        await request({
            method: 'POST',
            url: `/api/auth/refreshlogin`,
        }).then((result) => {
            if (result.token) {
                uni.hideLoading();
                uni.setStorageSync("token", res3.token);
                uni.setStorageSync("user_info", res3.user);
                let user = res3.user;
                userInfo.$patch(user)
                uni.redirectTo({
                    url: "/pages_work/index/index",
                });
            }
        })
    } else {
        console.log('暂未登录')
    }
}
onMounted(() => {
    getAuth()
})

</script>

<style scoped>
.main {
    position: relative;
    height: 100vh;
    width: 100vw;
}

.main-image {
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
}

.main-center {
    position: absolute;
    top: 700rpx;
    left: 31rpx;
}

.main-logo {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
}

.main-title {
    display: flex;
    align-items: center;
    margin-top: 32rpx;
    font-size: 48rpx;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 66rpx;
}

.main-signal {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8rpx;
    height: 48rpx;
    background: #FFFFFF;
    border-radius: 16rpx 16rpx 16rpx 0rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #FF7373;
    line-height: 44rpx;
    padding: 0 16rpx;
}

.main-subtitle {
    margin-top: 16rpx;
    font-size: 24rpx;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 36rpx;
}

.main-check {
    font-size: 24rpx;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 36rpx;
}

.main-btn {
    background-color: #FF7373;
}

.check-box {
    display: flex;
    justify-content: center;
    align-items: center;
}

.link {
    font-size: 24rpx;
    color: #FF7373;
}

.flex-mode {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>