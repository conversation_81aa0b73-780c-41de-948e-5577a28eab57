<template>
    <u-navbar :title="navTitle" :background="navBackground" :border-bottom="false"></u-navbar>
    <view class="login">
        <view class="login-title">验证码登录</view>
        <!--      <view class="login-subtitle" @click="changePage"> {{pageType === 0 ? '验证码登录' : '密码登录'}}</view>-->
    </view>
    <view class="login-box">
        <view class="login-label">账号/手机号</view>
        <u-input v-model="account" type="text" maxlength="11" placeholder="请输入" :border="false"
            :custom-style="inputStyle" placeholder-style="font-size: 32rpx; color: #787C8D;" />
        <view class="login-label" style="margin-top: 50rpx">验证码</view>
        <u-input v-model="password" type="number" placeholder="请输入" :password-icon="false" :border="false"
            :custom-style="inputStyle" placeholder-style="font-size: 32rpx; color: #787C8D;" />
        <u-toast ref="uToast1"></u-toast>
        <u-verification-code :seconds="seconds" @end="end" @start="start" ref="uCode1" @change="codeChange"
            change-text="Xs"></u-verification-code>
        <text @click="getCode" class="login-verify">{{ tips }}</text>
    </view>
    <view class="login-box" style="align-items: center">
        <u-button type="default" :custom-style="loginButton" hover-class="main-btn" @click="login">登录</u-button>
    </view>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import request from "@/request";
const { proxy } = getCurrentInstance();
import { useUserStore } from "@/stores/userInfo.js";
import { onLoad } from "@dcloudio/uni-app";
let redirectUrl = null;
let isGZH = 0;
onLoad(option => {
    if (option.redirect) {
        redirectUrl = option.redirect;
    }
    if (option.isGZH) {
        isGZH = option.isGZH;
    }
})
const userInfo = useUserStore();
const tips = ref('')
const seconds = ref(60)
const navBackground = {
    backgroundColor: "#ffffff"
}
const inputStyle = {
    borderBottom: "1px solid #EEEEEE",
    fontSize: "32rpx",
    color: "#000000",
    fontWeight: "500"
}
const loginButton = {
    color: "#FFFFFF",
    width: "240rpx",
    backgroundColor: "#FF7373"
}
const navTitle = ref('账号登录')
// 0 为密码登录，1 为验证码

const account = ref('')
const password = ref('')

const codeChange = (text) => {
    tips.value = text;
}
const getCode = () => {
    if (proxy.$refs.uCode1.canGetCode) {
        // 通知验证码组件内部开始倒计时
        if (!proxy.$u.test.mobile(account.value)) {
            proxy.$u.toast('请输入正确的手机号');
        } else {
            proxy.$refs.uCode1.start();
        }
    } else {
        proxy.$u.toast('倒计时结束后再发送');
    }
}
const end = () => {
    // proxy.$u.toast('倒计时结束');
}
const start = async () => {
    if (!proxy.$u.test.mobile(account.value)) {
        proxy.$u.toast('请输入正确的手机号');
    } else {
        let params = {
            phone: account.value
        }
        await request({
            method: 'POST',
            url: `/api/auth/getphonecode`,
            data: params
        }).then(async (res) => {
            proxy.$u.toast('验证码已发送');
        })
    }
}
const login = async () => {
    if (password.value == '') {
        proxy.$u.toast('请输入验证码');
        return
    }
    if (!proxy.$u.test.mobile(account.value)) {
        proxy.$u.toast('请输入正确的手机号');
    } else {
        uni.showLoading({
            title: '加载中',
        });
        let codephone = account.value;
        let login_type = 1;
        let checkcode = password.value;
        request({
            url: "/api/auth/browseLogin",
            method: 'post',
            data: {
                codephone,
                login_type,
                checkcode,
            }
        }).then(async (res) => {
            uni.hideLoading();
            if (res.token) {
                uni.setStorageSync("token", res.token);
                uni.setStorageSync("user_info", res.user);
                let user = res.user;
                userInfo.$patch(user)
                uni.showToast({
                    title: "登录成功",
                    icon: "success",
                    duration: 1500,
                })
                setTimeout(() => {
                    uni.redirectTo({
                        url: "/pages_work/index/index",
                    });
                }, 1500)
            }
        })
    }
}

</script>

<style scoped>
.login {
    box-sizing: border-box;
    padding: 0 112rpx;
    margin-top: 80rpx;
    display: flex;
    justify-content: center;
    align-items: baseline;
}

.login-title {
    margin-right: auto;
    font-size: 48rpx;
    font-weight: 500;
    color: #000000;
    line-height: 48rpx;
}

.login-subtitle {
    font-size: 28rpx;
    font-weight: 500;
    color: #787C8D;
    line-height: 48rpx;
}

.login-verify {
    font-size: 28rpx;
    font-weight: 500;
    color: #FF7373;
    line-height: 40rpx;
}

.login-box {
    padding: 0 112rpx;
    margin-top: 80rpx;
    display: flex;
    flex-direction: column;
}

.login-label {
    font-size: 24rpx;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 24rpx;
}

.login-forget {
    margin-top: 40rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #787C8D;
    line-height: 40rpx;
}
</style>