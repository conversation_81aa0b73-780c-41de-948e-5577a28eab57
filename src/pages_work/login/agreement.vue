<template>
    <u-navbar :title="pageType == 1 ? '用户服务协议' : '隐私协议'" :border-bottom="false"></u-navbar>
    <view class="main" v-if="pageType == 1">
        <image src="https://obs.tuoyupt.com/nanjing/fuwu/1.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/fuwu/2.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/fuwu/3.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/fuwu/4.jpg"></image>
    </view>
    <view class="main" v-else>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/1.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/2.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/3.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/4.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/5.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/6.jpg"></image>
        <image src="https://obs.tuoyupt.com/nanjing/yinsi/7.jpg"></image>
    </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad } from '@dcloudio/uni-app';
const pageType = ref(1)
onLoad((option) => {
    pageType.value = option.type
})
</script>
<style lang="scss" scoped>
.main {

    image {
        width: 750rpx;
        height: 1204rpx;
        display: block;
    }
}
</style>