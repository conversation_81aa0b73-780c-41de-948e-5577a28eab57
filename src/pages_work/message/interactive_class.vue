<template>
	<view class="page">
		<u-navbar title="互动消息" :background="navBackground" :border-bottom="false"></u-navbar>
		<view class="scroll-box">
			<scroll-view :scroll-y="true" class="scroll" :refresher-triggered="triggered" :refresher-enabled="true"
				@refresherrefresh="refresh" @scrolltolower="loadMore">
				<view v-if="msgList.length > 0" class="msg_container">
					<view class="msg_list" v-for="(item, index) in msgList" :key="index" @click="gotoPage(item)">
						<view class="item_icon">
							<img class="item_img" src="https://obs.tuoyupt.com/miniprogram/msg/interactive_msg.svg" />
							<view class="item_tip" v-if="item.read_state == 0">
							</view>
						</view>
						<view class="item_left">
							<view class="item_tit">
								{{ item.account_name }}
							</view>
							<view class="item_detail flex-mode">
								<view class="detail_left">
									<view class="left_t">
										<view class="t_type">
											{{ item.message }}
										</view>
										<view class="t_time">
											{{ formateDate(item.created_at) }}
										</view>
									</view>
									<view class="left_b" v-if="item.type === 'comment' && item.data.content">
										<view class="b_detail">
											{{ item.data.content.content ? item.data.content.content : '' }}
										</view>
									</view>
								</view>
								<view class="detail_right">
									<img style="width: 88rpx;height: 88rpx;border-radius: 8rpx;" v-if="item.data.type_class == 2"
										:src="item.data.image.url + '?vframe/jpeg/offset/1'" alt="" srcset="" />
									<img style="width: 88rpx;height: 88rpx;border-radius: 8rpx;" v-else-if="item.data.type_class == 1"
										:src="item.data.image.url" alt="" srcset="" />
									<view class="detail_right_text" v-else-if="item.data.type_class == 4">{{ item.data.title
										}}</view>
									<img class="play_btn" src="https://obs.tuoyupt.com/nanjing/pstac/notice/play.svg" v-if="item.data.type_class == 2" />
								</view>
							</view>
						</view>
					</view>
					<u-loadmore :status="loadStatus" v-if="msgList.length > 0" :load-text="loadText" />
				</view>
				<view class="no_data" v-else>
					<u-empty text="暂无消息" mode="list"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import {
	ref,
	reactive,
	watch,
	getCurrentInstance
} from "vue";
import request from "@/request";
import {
	onReachBottom,
	onShow,
	onLoad
} from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const msgList = ref([]);
const canShow = ref(false)
const triggered = ref(false);
const loadStatus = ref('loadmore')
const canLoadMore = ref(true)
const haveMore = ref(true)
const page = ref(1)
const per_page = ref(100)
const loadText = reactive({
	loadmore: '加载更多',
	loading: '努力加载中',
	nomore: '没有更多了',
	nomore1: '暂无更多'
})
const props = defineProps({
	msgType: {
		type: Object,
		default: false
	}
})
const getMnglist = async (type) => {
	let arr = msgList.value
	let params = {
		type_cate: 4,
		page: page.value,
		per_page: per_page.value,
	}
	await request({
		method: "GET",
		url: `/api/parent/message/list`,
		data: params
	}).then((result) => {
		if (type === 1) {
			msgList.value = result
			if (result.length < 5) {
				haveMore.value = false
				loadStatus.value = 'nomore1'
			} else if (result.length === per_page.value) {
				haveMore.value = true
				canLoadMore.value = true
			} else if (result.length < per_page.value) {
				haveMore.value = false
				canLoadMore.value = false
				loadStatus.value = 'nomore1'
			}
		} else {
			for (let i = 0; i < result.length; i++) {
				arr.push(result[i])
			}
			msgList.value = arr
			if (result.length > 0) {
				loadStatus.value = 'loadmore'
				haveMore.value = true
				canLoadMore.value = true
			} else {
				loadStatus.value = 'nomore'
				haveMore.value = false
			}
		}
		page.value = page.value + 1
		canShow.value = true
	});
}
const refresh = async () => {
	triggered.value = true;
	page.value = 1
	await getMnglist(1)
	triggered.value = false;
}
const loadMore = () => {
	if (canLoadMore.value === true) {
		canLoadMore.value = false
		if (haveMore.value) {
			loadStatus.value = 'loading'
			getMnglist(2)
		} else {
			loadStatus.value = 'nomore'
		}
	} else {
		console.log('throttle')
	}
}
onReachBottom(() => {
	if (haveMore.value) {
		loadStatus.value = 'loading'
		getMnglist(2)
	} else {
		loadStatus.value = 'nomore'
	}
})
const formateDate = (input) => {
	let date = new Date(input)
	var MM = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
	var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
	var hh = date.getHours() < 10 ? "0" + date.getHours() : date.getHours()
	var mm = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()
	return MM + '-' + DD + " " + hh + ':' + mm;
}
const gotoPage = (item) => {
	let list = msgList.value
	let params = {
		msg_id: item.id
	}
	if (item.type == 'class_notice' || item.type == 'school_notice') {
		request({
			method: "POST",
			url: `/api/parent/message/allRead`,
			data: params
		}).then(() => {
			list[index].read_state = 1
			msgList.value = list
			uni.navigateTo({
				url: `/pages_work/message/msg_details?id=${item.data_id}&page_type=3&stat_id=${item.id}&type=message`
			})
		})
	}
}
// 生命周期
onLoad((options) => {
	console.log(options);
})
onShow(() => {
	page.value = 1
	getMnglist(1)
})
</script>

<style lang="scss">
page {
	background: #FFFFFF
}

.page {
	transition: all 1s;
	width: 100vw;
	height: 100vh;
	position: relative;
	display: flex;
	flex-direction: column;

	.scroll-box {
		flex: 1;
		position: relative;
		z-index: 2;
		min-height: 1rpx;

		.scroll {
			width: 100vw;
			height: 100%;
		}

	}
}

.msg_container {
	padding: 32rpx 30rpx;

	.msg_list {
		display: flex;
		margin: 64rpx 0;

		.item_icon {
			position: relative;
			height: 104rpx;

			.item_img {
				width: 104rpx;
				height: 104rpx;
			}

			.item_tip {
				position: absolute;
				width: 12rpx;
				height: 12rpx;
				background: #FA5151;
				color: #fff;
				font-size: 22rpx;
				right: 0;
				top: 0;
				text-align: center;
				border-radius: 16rpx;
			}
		}

		.item_left {
			margin-left: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			border-bottom: 1rpx solid #EEEEEE;
			flex: 1;

			.item_tit {
				font-size: 30rpx;
				font-weight: bold;
				color: #111111;
				padding: 32rpx 0;
			}

			.item_detail {
				margin-top: 20rpx;
				margin-bottom: 48rpx;
				padding: 20rpx;
				width: 100%;
				background: #F6F6F6;
				border-radius: 16rpx;
				display: flex;

				.detail_left {
					margin-right: 20rpx;

					.left_t {
						display: flex;
						align-items: center;

						.t_type {
							margin-right: 20rpx;
							font-size: 26rpx;
							color: #787C8D;
						}

						.t_time {
							font-size: 22rpx;
							color: #AEB0BC;
						}
					}

					.left_t1 {
						display: flex;
						align-items: center;
						margin-top: 16rpx;

						.t_type {
							margin-right: 20rpx;
							font-size: 26rpx;
							color: #787C8D;
						}

						.t_time {
							font-size: 22rpx;
							color: #AEB0BC;
						}
					}

					.left_b {
						font-size: 26rpx;
						color: #262937;
						line-height: 36rpx;
						margin-top: 16rpx;

						.b_detail {
							word-break: break-all;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							/* 超出几行省略 */
							overflow: hidden;
						}
					}
				}

				.detail_right {
					width: 88rpx;
					height: 88rpx;
					margin-left: auto;
					position: relative;

					.play_btn {
						width: 40rpx;
						height: 40rpx;
						position: absolute;
						left: 0;
						top: 0;
						bottom: 0;
						right: 0;
						margin: auto;
					}

					.detail_right_text {
						width: 88rpx;
						height: 88rpx;
						background: #FFFFFF;
						border-radius: 8rpx;
						word-break: break-all;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 3;
						/* 超出几行省略 */
						overflow: hidden;
						font-size: 18rpx;
						font-weight: 400;
						color: #787C8D;
						line-height: 28rpx;
						padding: 0 5rpx;
					}
				}
			}

			.description_img {
				width: 100%;
				border-radius: 16rpx;
				margin-bottom: 16rpx;
			}
		}
	}

	.msg_list:first-child {
		margin-top: 0;
	}
}

.no_data {
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	margin: auto;
}
</style>
