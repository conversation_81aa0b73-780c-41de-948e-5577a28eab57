<template>
	<view class="page">
		<u-navbar title="官方通知" :background="navBackground" :border-bottom="false"></u-navbar>
		<view class="scroll-box">
			<scroll-view :scroll-y="true" class="scroll" :refresher-triggered="triggered" :refresher-enabled="true"
				@refresherrefresh="refresh" @scrolltolower="loadMore">
				<view class="msg_container" v-if="msgList.length > 0">
					<view class="msg_list" v-for="(item, index) in msgList" :key="index" @click="gotoPage(item, index)">
						<view class="item_icon">
							<img class="item_img" src="https://obs.tuoyupt.com/miniprogram/msg/official_notice.svg" />
							<view class="item_tip" v-if="item.read_state == 0">
							</view>
						</view>
						<view class="item_left">
							<view class="item_tit">
								{{ item.message }}
							</view>
							<view class="item_description">
								<view class="msg">
									{{ item.data && item.data.description }}
								</view>
							</view>
							<img class="description_img" v-if="item.data && item.data.img_url" :src="item.data.img_url" mode="aspectFill" />
							<view class="item_is_out_link" v-if="item.data && item.data.is_out_link === 1">
								<view class="link_description">查看详情</view>
								<u-icon name="arrow-right" style="margin-top: 4rpx;"></u-icon>
							</view>
							<view class="item_time">
								{{ item.created_at }}
							</view>
						</view>
					</view>
					<u-loadmore :status="loadStatus" v-if="msgList.length > 0" :load-text="loadText" />
				</view>
				<view class="no_data" v-else>
					<u-empty text="暂无消息" mode="list"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import {
	ref,
	reactive,
	getCurrentInstance
} from "vue";
import request from "@/request";
import {
	onReachBottom,
	onShow,
	onLoad
} from "@dcloudio/uni-app";
const msgList = ref([]);
const canShow = ref(false)
const triggered = ref(false);
const loadStatus = ref('loadmore')
const canLoadMore = ref(true)
const haveMore = ref(true)
const page = ref(1)
const per_page = ref(100)
const loadText = reactive({
	loadmore: '加载更多',
	loading: '努力加载中',
	nomore: '没有更多了',
	nomore1: '暂无更多'
})
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const getMnglist = async (type) => {
	let arr = msgList.value
	let params = {
		type_cate: 1,
		page: page.value,
		per_page: per_page.value,
	}
	await request({
		method: "GET",
		url: `/api/parent/message/list`,
		data: params
	}).then((result) => {
		if (type === 1) {
			msgList.value = result
			if (result.length < 4) {
				haveMore.value = false
				loadStatus.value = 'nomore1'
			} else if (result.length === per_page.value) {
				haveMore.value = true
				canLoadMore.value = true
			}
		} else {
			for (let i = 0; i < result.length; i++) {
				arr.push(result[i])
			}
			msgList.value = arr
			if (result.length > 0) {
				loadStatus.value = 'loadmore'
				haveMore.value = true
				canLoadMore.value = true
			} else {
				loadStatus.value = 'nomore'
				haveMore.value = false
			}
		}
		page.value = page.value + 1
		canShow.value = true
	});
}
const gotoPage = (item, index) => {
	console.log(item)
	let list = msgList.value
	let params = {
		msg_id: item.id
	}
	if (item.data.is_out_link === 1) {
		request({
			method: "POST",
			url: `/api/parent/message/allRead`,
			data: params
		}).then(() => {
			list[index].read_state = 1
			msgList.value = list
			uni.navigateTo({
				url: `/pages_work/message/webview?url=${item.data.link_url}`
			})
		});
	}
}
const refresh = async () => {
	triggered.value = true;
	page.value = 1
	await getMnglist(1)
	triggered.value = false;
}
const loadMore = () => {
	if (canLoadMore.value === true) {
		canLoadMore.value = false
		if (haveMore.value) {
			loadStatus.value = 'loading'
			getMnglist(2)
		} else {
			loadStatus.value = 'nomore'
		}
	} else {
		console.log('throttle')
	}
}
onReachBottom(() => {
	if (haveMore.value) {
		loadStatus.value = 'loading'
		getMnglist(2)
	} else {
		loadStatus.value = 'nomore'
	}
})
onShow(() => {
	page.value = 1
	getMnglist(1)
})
</script>

<style lang="scss" scoped>
page {
	background: #FFFFFF
}

.page {
	transition: all 1s;
	width: 100vw;
	height: 100vh;
	position: relative;
	display: flex;
	flex-direction: column;

	.scroll-box {
		flex: 1;
		position: relative;
		z-index: 2;
		min-height: 1rpx;

		.scroll {
			width: 100vw;
			height: 100%;
		}

	}
}

.msg_container {
	padding: 32rpx 30rpx;

	.msg_list {
		display: flex;
		margin: 64rpx 0;

		.item_icon {
			position: relative;
			height: 104rpx;

			.item_img {
				width: 104rpx;
				height: 104rpx;
			}

			.item_tip {
				position: absolute;
				width: 12rpx;
				height: 12rpx;
				background: #FA5151;
				color: #fff;
				font-size: 22rpx;
				right: 0;
				top: 0;
				text-align: center;
				border-radius: 16rpx;
			}
		}

		.item_left {
			margin-left: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			border-bottom: 1rpx solid #EEEEEE;
			flex: 1;

			.item_tit {
				font-size: 30rpx;
				font-weight: bold;
				color: #111111;
			}

			.item_description {
				display: flex;
				align-items: center;

				.msg {
					font-size: 26rpx;
					color: #787C8D;
					margin: 16rpx 0;
				}
			}

			.description_img {
				width: 560rpx;
				height: 316rpx;
				border-radius: 16rpx;
				margin-bottom: 16rpx;
			}

			.item_is_out_link {
				margin-bottom: 16rpx;
				display: flex;
				align-items: center;
			}

			.link_description {
				margin: 0 8rpx 0 0;
				font-size: 26rpx;
				line-height: 36rpx;
				color: #262937;
			}

			.item_time {
				margin-bottom: 48rpx;
				font-size: 22rpx;
				color: #AEB0BC;
			}
		}
	}

	.msg_list:first-child {
		margin-top: 0;
	}
}

.no_data {
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	margin: auto;
}
</style>