<template>
	<u-navbar title="消息详情" :background="navBackground" :border-bottom="false"></u-navbar>
	<view class="outer" :style="canShow ? 'opacity: 1' : 'opacity: 0'">

		<!-- <view v-if="page_type == 2" class="read_person" @click="gotoPage(notice_id)">
			<view>
				<view class="read_num">
					已读：{{ read.readUserCount }}
				</view>
				<view class="read_num">
					未读：{{ read.noReadUserCount}}
				</view>
			</view>
			<u-icon name="arrow-right"></u-icon>
		</view> -->
		<view class="notice_details" v-if="!isBlank">
			<view class="notice_title">{{ noticeDetail.data.title }}</view>
			<view class="notice_time">{{ noticeDetail.data.operate_time }}</view>
			<!-- <u-parse :html="noticeDetail.description" :show-with-animation="true"></u-parse> -->
			<text class="notice_content">{{ noticeDetail.data.description }}</text>
			<view class="btns">
				<!-- <view class="btn" @click="copy(noticeDetail.data.description)">
					分享好友
					<img class="btn_icon" src="https://obs.tuoyupt.com/miniprogram/msg/wechat.svg" alt="" srcset="">
				</view> -->
				<button open-type="share" class="btn" @click="copy(noticeDetail.data.description)">
					分享好友
					<image class="btn_icon" src="https://obs.tuoyupt.com/miniprogram/msg/wechat.svg" mode=""></image>
				</button>
			</view>
		</view>
		<!-- 缺省页 -->
		<view class="main" v-else>
			<u-empty text="暂无详情" mode="list" :src="nodataUrl"></u-empty>
		</view>
	</view>
</template>

<script setup>
import {
	ref,
	watch,
	getCurrentInstance
} from "vue";
import request from "@/request";
import {
	onReachBottom,
	onShow,
	onLoad
} from "@dcloudio/uni-app";
var read = ref({})
var notice_id = ref('')
const user_id = ref('')
const page_type = ref('')
const msg_type = ref('')
const canShow = ref(false)
const isBlank = ref(true)
const noticeDetail = ref({})
// 获取消息详情——type = message
const getMessageDetails = async (id) => {
	await request({
		method: "GET",
		url: `/api/parent/message/detail/${id}`,
	}).then((result) => {
		console.log(result);
		console.log(result != null)
		if (result != null) {
			noticeDetail.value = result
			isBlank.value = false
		} else {
			isBlank.value = true
		}
		canShow.value = true
	});
}
const copy = (value) => {
	uni.setClipboardData({
		data: value,
		success: () => {
			uni.showToast({
				title: '复制成功'
			})
		}
	});
}
const gotoPage = (index) => {
	uni.navigateTo({
		url: `/pages_work/message/notice_statistics?id=${index}`
	})
}
onLoad((options) => {
	let user_info = uni.getStorageSync("user_info");
	user_id.value = user_info.id
	notice_id.value = options.id
	page_type.value = options.page_type
	msg_type.value = options.type
	// page_type: 2-机构通知，3-校园通知

	// if( options.page_type == 2 ) {
	//     getNoticeStat(options.id)
	// }
	getMessageDetails(options.stat_id)
	// if(options.type == 'message') {
	// 	if(options.page_type == 2) {
	// 		getSchoolStat(options.stat_id)
	// 	} else if(options.page_type == 3) {
	// 		getClassStat(options.stat_id,options.class_id)
	// 	}
	// 	getMessageDetails(options.stat_id)
	// } else if(options.type == 'notice'){
	// 	if(options.page_type == 2) {
	// 		getSchoolStat(options.stat_id)
	// 	} else if(options.page_type == 3) {
	// 		getClassStat(options.stat_id,options.class_id)
	// 	}
	// 	getNoticeDetails(options.id)
	// }
})
</script>

<style lang="scss">
.outer {
	opacity: 0;
	transition: all .5s;
}

.main {
	transition: all .5s;
	margin-top: 50%
}

.read_person {
	height: 100rpx;
	padding: 0 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #EEEEEE;

	.read_num {
		display: inline-block;
		font-size: 26rpx;
		color: #000000;
	}

	.read_num:first-child {
		margin-right: 40rpx;
	}
}

.notice_details {
	padding: 40rpx 30rpx;

	.notice_title {
		font-size: 44rpx;
		color: #111111;
		line-height: 60rpx;
	}

	.notice_time {
		font-size: 28rpx;
		color: #AEB0BC;
		line-height: 44rpx;
		margin: 20rpx 0 40rpx 0;
	}

	.notice_content {
		font-size: 34rpx;
		color: #262937;
		line-height: 56rpx;
	}

	.btns {
		margin-top: 80rpx;
		display: flex;
		justify-content: center;
	}

	.btn {
		width: 230rpx;
		height: 64rpx;
		background: #F6F6F6;
		border-radius: 40rpx;
		font-size: 28rpx;
		color: #262937;
		line-height: 64rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.btn_icon {
			width: 40rpx;
			height: 40rpx;
			margin-left: 16rpx;
		}
	}
}
</style>