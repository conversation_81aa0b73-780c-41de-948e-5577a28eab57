<template>
	<view class="page">
		<u-navbar title="机构消息" :background="navBackground" :border-bottom="false"></u-navbar>
		<view class="scroll-box">
			<scroll-view :scroll-y="true" class="scroll" :refresher-triggered="triggered" :refresher-enabled="true"
				@refresherrefresh="refresh" @scrolltolower="loadMore">
				<view v-if="msgList.length > 0" class="msg_container">
					<view :class="item.type == 'school_news' ? 'msg_list' : 'msg_list1'" v-for="(item, index) in msgList"
						:key="index" @click="gotoPage(item, index)">
						<template v-if="item.type == 'school_news'">
							<view class="item_icon">
								<img class="item_img" :src="item.data[0] ? item.data[0].thumb : ''" mode="aspectFill" />
							</view>
							<view class="item_bottom">
								<view class="b_msg">
									{{ item.message }}
								</view>
								<view class="b_time">
									{{ item.created_at }}
									<view class="msg_read" v-if="item.read_state == 0"></view>
								</view>
							</view>
						</template>
						<template v-else>
							<view class="item_icon">
								<img class="item_img" src="https://obs.tuoyupt.com/miniprogram/msg/nursery_msg.svg" />
								<view class="item_tip" v-if="item.read_state == 0">
								</view>
							</view>
							<view class="item_left">
								<view class="item_tit">
									{{ item.message }}
								</view>
								<view class="item_content">
									<view class="msg_time">
										{{ item.created_at }}
									</view>
								</view>
							</view>
						</template>
					</view>
					<u-loadmore :status="loadStatus" v-if="msgList.length > 0" :load-text="loadText" />
				</view>
				<view class="no_data" v-else>
					<u-empty text="暂无消息" mode="list"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import {
	ref,
	watch,
	reactive,
	getCurrentInstance
} from "vue";
import request from "@/request";
import {
	onReachBottom,
	onShow,
	onLoad
} from "@dcloudio/uni-app";
const navBackground = {
	backgroundColor: '#f6f6f6'
}
const msgList = ref([]);
const canShow = ref(false)
const triggered = ref(false);
const loadStatus = ref('loadmore')
const canLoadMore = ref(true)
const haveMore = ref(true)
const page = ref(1)
const per_page = ref(100)
const loadText = reactive({
	loadmore: '加载更多',
	loading: '努力加载中',
	nomore: '没有更多了',
	nomore1: '暂无更多'
})
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const props = defineProps({
	msgType: {
		type: Object,
		default: false
	}
})
watch(
	() => props.msgType,
	() => {
		getMnglist()
	}
)
const getMnglist = async (type) => {
	let arr = msgList.value
	let params = {
		type_cate: 2,
		page: page.value,
		per_page: per_page.value,
		school_id: userInfo.school_id
	}
	await request({
		method: "GET",
		url: `/api/parent/message/list`,
		data: params
	}).then((result) => {
		console.log(result);
		if (type === 1) {
			msgList.value = result
			if (result.length < 5) {
				haveMore.value = false
				loadStatus.value = 'nomore1'
			} else if (result.length === per_page.value) {
				haveMore.value = true
				canLoadMore.value = true
			} else if (result.length < per_page.value) {
				haveMore.value = false
				canLoadMore.value = false
				loadStatus.value = 'nomore1'
			}
		} else {
			for (let i = 0; i < result.length; i++) {
				arr.push(result[i])
			}
			msgList.value = arr
			if (result.length > 0) {
				loadStatus.value = 'loadmore'
				haveMore.value = true
				canLoadMore.value = true
			} else {
				loadStatus.value = 'nomore'
				haveMore.value = false
			}
		}
		page.value = page.value + 1
		canShow.value = true
	});
}
const refresh = async () => {
	triggered.value = true;
	page.value = 1
	await getMnglist(1)
	triggered.value = false;
}
const loadMore = () => {
	if (canLoadMore.value === true) {
		canLoadMore.value = false
		if (haveMore.value) {
			loadStatus.value = 'loading'
			getMnglist(2)
		} else {
			loadStatus.value = 'nomore'
		}
	} else {
		console.log('throttle')
	}
}
onReachBottom(() => {
	if (haveMore.value) {
		loadStatus.value = 'loading'
		getMnglist(2)
	} else {
		loadStatus.value = 'nomore'
	}
})
const gotoPage = (item, index) => {
	let list = msgList.value
	let params = {
		msg_id: item.id
	}
	if (item.type == 'school_news') {
		let data = item.data[0];
		if (data.deleted_at) {
			uni.showToast({
				title: '该新闻内容已被删除',
				icon: 'none'
			})
		} else {
			request({
				method: "POST",
				url: `/api/parent/message/allRead`,
				data: params
			}).then(() => {
				list[index].read_state = 1
				msgList.value = list
				uni.navigateTo({
					url: `/pages_work/message/news_page?id=${data.id}`
				})
			})
		}
	} else {
		let data = item.data[0];
		if (data.deleted_at) {
			uni.showToast({
				title: '该新闻内容已被删除',
				icon: 'none'
			})
		} else {
			request({
				method: "POST",
				url: `/api/parent/message/allRead`,
				data: params
			}).then(() => {
				list[index].read_state = 1
				msgList.value = list
				uni.navigateTo({
					url: `/pages_work/message/msg_details?id=${item.data_id}&page_type=2&stat_id=${item.id}&type=message`
				})
			})
		}
	}
}
// 生命周期
onLoad((options) => {
	// console.log(options);
})
onShow(() => {
	page.value = 1
	getMnglist(1)
})
</script>

<style>
page {
	background: #F6F6F6
}
</style>

<style lang="scss" scoped>
page {
	background: #F6F6F6 !important;
}

.page {
	transition: all 1s;
	width: 100vw;
	height: 100vh;
	position: relative;
	display: flex;
	flex-direction: column;

	.scroll-box {
		flex: 1;
		position: relative;
		z-index: 2;
		min-height: 1rpx;

		.scroll {
			width: 100vw;
			height: 100%;
		}

	}
}

.msg_container {
	padding: 32rpx 30rpx;

	.msg_list {
		display: flex;
		flex-direction: column;
		margin: 64rpx 0;
		border-radius: 24rpx;
		background: #fff;

		.item_icon {
			width: 100%;

			.item_img {
				width: 100%;
				height: 316rpx;
				border-radius: 24rpx 24rpx 0 0;
			}
		}

		.item_bottom {
			padding: 40rpx;

			.b_msg {
				height: 88rpx;
				font-size: 30rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #262937;
				line-height: 44rpx;
			}

			.b_time {
				display: flex;
				align-items: center;
				margin-top: 16rpx;
				font-size: 22rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #AEB0BC;
				line-height: 32rpx;
			}

			.msg_read {
				margin-right: auto;
				margin-left: 20rpx;
				width: 12rpx;
				height: 12rpx;
				background: #FA5151;
				border-radius: 50%
			}
		}
	}

	.msg_list1 {
		padding: 40rpx;
		display: flex;
		margin: 64rpx 0;
		border-radius: 24rpx;
		background: #fff;

		.item_icon {
			position: relative;
			height: 104rpx;

			.item_img {
				width: 104rpx;
				height: 104rpx;
			}

			.item_tip {
				position: absolute;
				width: 12rpx;
				height: 12rpx;
				background: #FA5151;
				color: #fff;
				font-size: 22rpx;
				right: 0;
				top: 0;
				text-align: center;
				border-radius: 16rpx;
			}
		}

		.item_left {
			margin-left: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.item_tit {
				margin-bottom: 16rpx;
				font-size: 30rpx;
				font-weight: bold;
				color: #111111;
			}

			.item_content {
				display: flex;
				align-items: center;

				.msg {
					font-size: 26rpx;
					color: #787C8D;
					line-height: 44rpx;
				}

				.msg_time {
					line-height: 32rpx;
					font-size: 22rpx;
					color: #AEB0BC;
				}
			}
		}
	}

	.msg_list:first-child {
		margin-top: 0;
	}
}

.no_data {
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	margin: auto;
}
</style>