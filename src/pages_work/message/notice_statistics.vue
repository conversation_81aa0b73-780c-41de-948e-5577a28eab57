<template>
	<u-navbar title="通知统计" :background="navBackground" :border-bottom="false"></u-navbar>
	<view class="read_person">
		<view class="read_num" :class="{ 'read_active': read_active == 1 }" @click="changeActive('1')">
			已读{{ read.readUserCount }}
		</view>
		<view class="read_num" :class="{ 'read_active': read_active == 2 }" @click="changeActive('2')">
			未读{{ read.noReadUserCount }}
		</view>
	</view>
	<view class="person_list">
		<view class="person_item" v-for="(item, index) in person_list" :key="index">
			<template v-if="item != ''">
				<!-- <image :src="item.account_img" class="single-box__title__avatar" v-if="item.account_img"></image> -->
				<view class="person_avatar flex-mode">{{
					item.charAt(0) }}</view>
				<view class="person_name">
					{{ item }}
				</view>
			</template>
		</view>
	</view>
	<view class="btn" v-if="read_active == 2">
		提醒查看
	</view>
</template>

<script setup>
import {
	ref,
	watch,
	getCurrentInstance
} from "vue";
import request from "@/request";
import {
	onReachBottom,
	onShow,
	onLoad
} from "@dcloudio/uni-app";
var read = ref({})
var read_active = ref('1')
var person_list = ref([])
const getMnglist = async (index) => {
	await request({
		method: "GET",
		url: `/api/teach/schoolnotice/stat/${index}`,
	}).then((result) => {
		console.log(result);
		read.value = result
		changeActive(1)
		console.log(read);
	});
}
const changeActive = (index) => {
	console.log(index);
	read_active.value = index
	let arr = []
	let str = ''
	if (index == 1) {
		person_list.value = read.value.readUser
		if (read.value.readUser.length > 0) {
			for (let i = 0; i < read.value.readUser.length; i++) {
				str = read.value.readUser[i].account_name
				let arr1 = str.split(",");
				arr.push(arr1)
				for (let j = 0; j < arr1.length; j++) {
					if (arr1[j] != '') {
						arr.push(arr1[j])
					}
				}
			}
		}
	} else {
		person_list.value = read.value.noReadUser
		if (read.value.noReadUser.length > 0) {
			for (let i = 0; i < read.value.noReadUser.length; i++) {
				str = read.value.noReadUser[i].account_name
				let arr1 = str.split(",");
				for (let j = 0; j < arr1.length; j++) {
					if (arr1[j] != '') {
						arr.push(arr1[j])
					}
				}
			}
		}
	}
	console.log(arr)
	person_list.value = arr
}
onLoad((options) => {
	getMnglist(options.id)
})
</script>

<style lang="scss">
.read_person {
	height: 80rpx;
	margin: 0 30rpx;
	padding: 4rpx;
	background: #F6F6F6;
	border-radius: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.read_num {
		flex: 1;
		display: inline-block;
		font-size: 26rpx;
		color: #787C8D;
		text-align: center;
		line-height: 72rpx;
	}

	.read_active {
		height: 72rpx;
		background: #FFFFFF;
		border-radius: 12rpx;
		color: #111111;
	}
}

.person_list {
	padding: 0 30rpx;

	.person_item {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;

		.person_avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: 24rpx;
			background: #617EFB;
			color: #FFFFFF;
		}

		.person_ava {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}

		.person_name {
			flex: 1;
			font-size: 28rpx;
			color: #262937;
			margin-left: 24rpx;
		}
	}

	.person_item:first-child {
		margin-top: 40rpx;
	}
}

.btn {
	position: fixed;
	bottom: 78rpx;
	left: 30rpx;
	right: 30rpx;
	padding: 20rpx 0;
	background: #F6F6F6;
	border-radius: 16rpx;
	color: #617EFB;
	font-size: 28rpx;
	text-align: center;
	letter-spacing: 2rpx;
}
</style>