<template>
	<u-navbar title="消息中心" :is-back="false" :background="navBackground" :border-bottom="false"></u-navbar>
	<view class="container">
		<view class="msg_list" v-for="(item, index) in tablist" :key="index" @click="gotoPage(item.type_cate)">
			<view class="item_icon">
				<img class="item_img" :src="type_cate(item.type_cate)[1]" />
				<view class="item_tip" v-if="item.unread_count > 0">
					{{ item.unread_count > 99 ? "···" : item.unread_count }}
				</view>
			</view>
			<view class="item_left">
				<view class="item_tit">
					{{ item.title }}
				</view>
				<view class="item_content">
					<view class="msg">
						{{ item.unread_count }}{{ type_cate(item.type_cate)[0] }}
					</view>
					<view class="msg_time">
						{{ item.time }}
					</view>
				</view>
			</view>
		</view>
	</view>
	<view style="height: 120rpx;"></view>
	<tabbar :currentTab="3" :border-top="false"></tabbar>
</template>

<script setup>
import {
	ref
} from "vue";
import request from "@/request";
import tabbar from "@/components/tabbar.vue";
import { onShow } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const num = ref("");
const tablist = ref([]);
import hospistalImg from '@/static/img/message/hospital.png'

const getCate = async () => {
	await request({
		method: "GET",
		url: `/api/parent/message/cate`,
		data: {
			school_id: userInfo.school_id
		}
	}).then((result) => {
		var count = 0;
		result.forEach(el =>
			count += el.unread_count
		)
		num.value = `${count}`
		tablist.value = result
	});
}
const gotoClear = async () => {
	await request({
		method: "POST",
		url: `/api/parent/message/allRead`,
	}).then((result) => {
		console.log(result);
		getCate()
	});
}
const gotoPage = (index) => {
	if (index == 1) {
		uni.navigateTo({
			url: "/pages_work/message/official_notice"
		})
	} else if (index == 2) {
		uni.navigateTo({
			url: `/pages_work/message/nursery_msg`
		})
	} else if (index == 3) {
		uni.navigateTo({
			url: `/pages_work/message/class_list`
		})
	} else if (index == 4) {
		uni.navigateTo({
			url: `/pages_work/message/interactive_class`
		})
	} else if (index == 8) {
		uni.navigateTo({
			url: `/pages_work/message/hospital_msg`
		})
	} else if (index == 9) {
		uni.navigateTo({
			url: `/pages/pack2/activety/index`
		})
	} else {
		uni.showToast({
			title: "正在开发中……",
			duration: 2000,
			icon: "none",
		});
	}
}
const type_cate = (prm) => {
	switch (prm) {
		case 1:
			return ['条新通知', 'https://obs.tuoyupt.com/miniprogram/msg/official_notice.svg']
		case 2:
			return ['条新通知', 'https://obs.tuoyupt.com/miniprogram/msg/nursery_msg.svg']
		case 3:
			return ['条新通知', 'https://obs.tuoyupt.com/miniprogram/msg/class_notice.svg']
		case 4:
			return ['条新互动', 'https://obs.tuoyupt.com/miniprogram/msg/interactive_msg.svg']
		case 5:
			return ['条请假消息', 'https://obs.tuoyupt.com/miniprogram/msg/work_assistant.svg']
		case 6:
			return ['条新通知', 'https://obs.tuoyupt.com/miniprogram/msg/official_notice.svg']
		case 8:
			return ['医育消息', hospistalImg]
		default:
			return ['条新通知', 'https://obs.tuoyupt.com/miniprogram/msg/official_notice.svg']

	}
}
const onmyclick = () => {
	gotoClear()
}
const isTrail = ref(true)
onShow(() => {
	if (uni.getStorageSync("token")) {
		isTrail.value = false;
		getCate()
	} else {
		isTrail.value = true;
		uni.redirectTo({
			url: "/pages_work/login/main"
		})
	}
})
</script>

<style lang="scss" scoped>
page {
	background: #FFFFFF
}

.container {
	padding: 32rpx 30rpx;

	.msg_list {
		display: flex;
		margin: 64rpx 0;

		.item_icon {
			position: relative;
			height: 104rpx;

			.item_img {
				width: 104rpx;
				height: 104rpx;
			}

			.item_tip {
				position: absolute;
				width: 32rpx;
				height: 32rpx;
				background: #FA5151;
				color: #fff;
				font-size: 22rpx;
				right: 0;
				top: 0;
				text-align: center;
				border-radius: 16rpx;
			}
		}

		.item_left {
			margin-left: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.item_tit {
				margin-bottom: 6rpx;
				font-size: 30rpx;
				font-weight: bold;
				color: #111111;
			}

			.item_content {
				display: flex;
				align-items: center;

				.msg {
					font-size: 26rpx;
					color: #787C8D;
					line-height: 44rpx;
				}

				.msg_time {
					line-height: 32rpx;
					margin-left: 16rpx;
					font-size: 22rpx;
					color: #AEB0BC;
				}
			}
		}
	}

	.msg_list:first-child {
		margin-top: 0;
	}
}
</style>