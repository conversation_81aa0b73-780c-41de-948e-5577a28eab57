<template name="skeleton">
  <view class="sk-container">
    <view is="pages_work/index/components/header" class="data-v-66595b85">
      <view class="header header--header data-v-08b50336 header--data-v-08b50336" :style="{ marginTop: statusBarHeight + 'px' }">
        <view is="uni_modules/vk-uview-ui/components/u-navbar/u-navbar" class="data-v-08b50336 header--data-v-08b50336">
          <view class="data-v-a7de1ae4 navbar--data-v-a7de1ae4">
            <view class="u-navbar navbar--u-navbar data-v-a7de1ae4 navbar--data-v-a7de1ae4"
              style="z-index:980;">
              <view class="u-status-bar navbar--u-status-bar data-v-a7de1ae4 navbar--data-v-a7de1ae4" style="height:20px">
              </view>
              <view class="u-navbar-inner navbar--u-navbar-inner data-v-a7de1ae4 navbar--data-v-a7de1ae4"
                style="height:48px;margin-right:94px;">
                <view class="u-slot-content navbar--u-slot-content data-v-a7de1ae4 navbar--data-v-a7de1ae4">
                  <view class="slot-wrap header--slot-wrap data-v-08b50336 header--data-v-08b50336">
                    <view
                      class="title header--title data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-814 sk-text">
                      首页 </view>
                    <view
                      class="select header--select data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-734 sk-text">
                      爱尔福单业务机构</view>
                    <view class="ai header--ai data-v-08b50336 header--data-v-08b50336">
                      <image class="cion header--cion data-v-08b50336 header--data-v-08b50336 sk-image"></image>
                    </view>
                  </view>
                </view>
                <view class="u-slot-right navbar--u-slot-right data-v-a7de1ae4 navbar--data-v-a7de1ae4"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="header-center header--header-center data-v-08b50336 header--data-v-08b50336">
          <view class="info header--info data-v-08b50336 header--data-v-08b50336">
            <view
              class="name header--name data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-4444-611 sk-text">
              林小小</view>
            <view
              class="age header--age data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-7059-81 sk-text">
              2岁0个月17天</view>
            <view class="body header--body data-v-08b50336 header--data-v-08b50336">
              <view class="item header--item data-v-08b50336 header--data-v-08b50336">
                <view
                  class="label header--label data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-618 sk-text">
                  身高 </view>
                <view
                  class="value header--value data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-7-8947-292 sk-text">
                  86</view>
              </view>
              <view class="item header--item data-v-08b50336 header--data-v-08b50336">
                <view
                  class="label header--label data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-213 sk-text">
                  体重 </view>
                <view
                  class="value header--value data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-7-8947-300 sk-text">
                  18</view>
              </view>
              <view class="item header--item data-v-08b50336 header--data-v-08b50336">
                <view
                  class="label header--label data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-990 sk-text">
                  BMI </view>
                <view
                  class="value header--value data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-7-8947-300 sk-text">
                  24.3</view>
              </view>
              <view class="arrow header--arrow data-v-08b50336 header--data-v-08b50336">
                <image class="icon header--icon data-v-08b50336 header--data-v-08b50336 sk-image"></image>
              </view>
            </view>
          </view>
        </view>
        <view class="nav header--nav data-v-08b50336 header--data-v-08b50336"
          ></view>
        <scroll-view enable-flex="true" class="scroll header--scroll data-v-08b50336 header--data-v-08b50336"
          scroll-x="true">
          <view class="item header--item data-v-08b50336 header--data-v-08b50336">
            <image class="back header--back data-v-08b50336 header--data-v-08b50336 sk-image"></image>
            <view
              class="title header--title data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-839 sk-text">
              生活记录</view>
            <view
              class="des header--des data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-872 sk-text">
              0条</view>
          </view>
          <view class="item header--item data-v-08b50336 header--data-v-08b50336">
            <image class="back header--back data-v-08b50336 header--data-v-08b50336 sk-image"></image>
            <view
              class="title header--title data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-509 sk-text">
              我的相册</view>
            <view
              class="des header--des data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-468 sk-text">
              0张</view>
          </view>
          <view class="item header--item data-v-08b50336 header--data-v-08b50336">
            <image class="back header--back data-v-08b50336 header--data-v-08b50336 sk-image"></image>
            <view
              class="title header--title data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-394 sk-text">
              测评报告</view>
            <view
              class="des header--des data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-530 sk-text">
              0条</view>
          </view>
          <view class="item header--item data-v-08b50336 header--data-v-08b50336">
            <image class="back header--back data-v-08b50336 header--data-v-08b50336 sk-image"></image>
            <view
              class="title header--title data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-245 sk-text">
              成长报告</view>
            <view
              class="des header--des data-v-08b50336 header--data-v-08b50336 sk-transparent sk-text-14-2857-887 sk-text">
              0条</view>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="pages data-v-66595b85">
      <view is="pages_work/index/components/nav" class="data-v-66595b85">
        <view class="content nav--content data-v-3ae15576 nav--data-v-3ae15576">
          <view class="nav nav--nav data-v-3ae15576 nav--data-v-3ae15576">
            <image class="icon nav--icon data-v-3ae15576 nav--data-v-3ae15576 sk-image"></image>
            <text
              class="name nav--name data-v-3ae15576 nav--data-v-3ae15576 sk-transparent sk-text-14-2857-809 sk-text">家长海报</text>
          </view>
          <view class="nav nav--nav data-v-3ae15576 nav--data-v-3ae15576">
            <image class="icon nav--icon data-v-3ae15576 nav--data-v-3ae15576 sk-image"></image>
            <text
              class="name nav--name data-v-3ae15576 nav--data-v-3ae15576 sk-transparent sk-text-14-2857-740 sk-text">机构网站</text>
          </view>
          <view class="nav nav--nav data-v-3ae15576 nav--data-v-3ae15576">
            <image class="icon nav--icon data-v-3ae15576 nav--data-v-3ae15576 sk-image"></image>
            <text
              class="name nav--name data-v-3ae15576 nav--data-v-3ae15576 sk-transparent sk-text-14-2857-69 sk-text">家长嘱托</text>
          </view>
          <view class="nav nav--nav data-v-3ae15576 nav--data-v-3ae15576">
            <image class="icon nav--icon data-v-3ae15576 nav--data-v-3ae15576 sk-image"></image>
            <text
              class="name nav--name data-v-3ae15576 nav--data-v-3ae15576 sk-transparent sk-text-14-2857-739 sk-text">视频监控</text>
          </view>
        </view>
      </view>
      <view is="pages_work/index/components/amusement_park" class="data-v-66595b85">
        <view class="content park--content data-v-3a08a54f park--data-v-3a08a54f" style="background:#FF7373">
          <view class="top park--top data-v-3a08a54f park--data-v-3a08a54f">
            <view
              class="title park--title data-v-3a08a54f park--data-v-3a08a54f sk-transparent sk-text-14-2857-970 sk-text">
              宝宝乐园</view>
            <image class="dota park--dota data-v-3a08a54f park--data-v-3a08a54f sk-image"></image>
            <image class="lena park--lena data-v-3a08a54f park--data-v-3a08a54f sk-image"></image>
          </view>
          <scroll-view enable-flex="true" class="scroll park--scroll data-v-3a08a54f park--data-v-3a08a54f"
            scroll-x="true">
            <view class="item park--item data-v-3a08a54f park--data-v-3a08a54f">
              <image class="ic park--ic data-v-3a08a54f park--data-v-3a08a54f sk-image"></image>
              <view class="info park--info data-v-3a08a54f park--data-v-3a08a54f">
                <view
                  class="t1 park--t1 data-v-3a08a54f park--data-v-3a08a54f sk-transparent sk-text-14-2857-218 sk-text">
                  音乐小精灵</view>
                <view
                  class="t2 park--t2 data-v-3a08a54f park--data-v-3a08a54f sk-transparent sk-text-14-2857-443 sk-text">
                  时刻了解孩子在园情况</view>
              </view>
            </view>
            <view class="item park--item data-v-3a08a54f park--data-v-3a08a54f">
              <image class="ic park--ic data-v-3a08a54f park--data-v-3a08a54f sk-image"></image>
            </view>
          </scroll-view>
        </view>
      </view>
      <view is="pages_work/index/components/banner" class="data-v-66595b85"></view>
      <view is="pages_work/index/components/attendance" class="data-v-66595b85">
        <view class="content attendance--content data-v-5f99fa7e attendance--data-v-5f99fa7e">
          <image class="back attendance--back data-v-5f99fa7e attendance--data-v-5f99fa7e sk-image"></image>
          <view class="top attendance--top data-v-5f99fa7e attendance--data-v-5f99fa7e">
            <view
              class="title attendance--title data-v-5f99fa7e attendance--data-v-5f99fa7e sk-transparent sk-text-14-2857-431 sk-text">
              考勤日历</view>
            <view
              class="time attendance--time data-v-5f99fa7e attendance--data-v-5f99fa7e sk-transparent sk-text-14-2857-211 sk-text">
              截至2023-09-18</view>
            <view
              class="btn attendance--btn data-v-5f99fa7e attendance--data-v-5f99fa7e sk-transparent sk-text-25-0000-115 sk-text"
              style="background-position-x: 50%;">查看详情</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { nextTick, ref, provide } from "vue";
// 获取系统状态栏的高度
let systemInfo = uni.getSystemInfoSync();
let statusBarHeight = ref(systemInfo.statusBarHeight);
</script>
<style lang="scss" scoped>
.sk-transparent {
  color: transparent !important;
}

.sk-text-14-2857-814 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 56.0000rpx;
  position: relative !important;
}

.sk-text {
  background-origin: content-box !important;
  background-clip: content-box !important;
  background-color: transparent !important;
  color: transparent !important;
  background-repeat: repeat-y !important;
}

.sk-text-14-2857-734 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 33.6000rpx;
  position: relative !important;
}

.sk-text-14-4444-611 {
  background-image: linear-gradient(transparent 14.4444%, #EEEEEE 0%, #EEEEEE 85.5556%, transparent 0%) !important;
  background-size: 100% 90.0000rpx;
  position: relative !important;
}

.sk-text-14-7059-81 {
  background-image: linear-gradient(transparent 14.7059%, #EEEEEE 0%, #EEEEEE 85.2941%, transparent 0%) !important;
  background-size: 100% 34.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-618 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-7-8947-292 {
  background-image: linear-gradient(transparent 7.8947%, #EEEEEE 0%, #EEEEEE 92.1053%, transparent 0%) !important;
  background-size: 100% 38.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-213 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-7-8947-300 {
  background-image: linear-gradient(transparent 7.8947%, #EEEEEE 0%, #EEEEEE 92.1053%, transparent 0%) !important;
  background-size: 100% 38.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-990 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-839 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 39.2000rpx;
  position: relative !important;
}

.sk-text-14-2857-872 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-509 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 39.2000rpx;
  position: relative !important;
}

.sk-text-14-2857-468 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-394 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 39.2000rpx;
  position: relative !important;
}

.sk-text-14-2857-530 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-245 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 39.2000rpx;
  position: relative !important;
}

.sk-text-14-2857-887 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-809 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 33.6000rpx;
  position: relative !important;
}

.sk-text-14-2857-740 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 33.6000rpx;
  position: relative !important;
}

.sk-text-14-2857-69 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 33.6000rpx;
  position: relative !important;
}

.sk-text-14-2857-739 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 33.6000rpx;
  position: relative !important;
}

.sk-text-14-2857-970 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 44.8000rpx;
  position: absolute !important;
}

.sk-text-14-2857-218 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 44.8000rpx;
  position: relative !important;
}

.sk-text-14-2857-443 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-14-2857-431 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 44.8000rpx;
  position: relative !important;
}

.sk-text-14-2857-211 {
  background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
  background-size: 100% 33.6000rpx;
  position: relative !important;
}

.sk-text-25-0000-115 {
  background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
  background-size: 100% 40.0000rpx;
  position: relative !important;
}

.sk-image {
  background: #EFEFEF !important;
}

.sk-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  z-index: 999;
}

.header.data-v-08b50336 {
  position: relative;
}

.header .avatar.data-v-08b50336 {
  height: 420rpx;
  position: absolute;
  z-index: 2;
  top: 200rpx;
  right: 62rpx;
}

.header .nav.data-v-08b50336 {
  position: relative;
  z-index: 1;
  width: 750rpx;
  height: 256rpx;
}

.header .scroll.data-v-08b50336 {
  width: 750rpx;
  height: 200rpx;
  display: flex;
  flex-direction: row;
  position: absolute;
  z-index: 3;
  bottom: 30rpx;
  left: 0;
}

.header .scroll .zhanwei.data-v-08b50336 {
  flex-shrink: 0;
  width: 30rpx;
}

.header .scroll .item.data-v-08b50336 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 24rpx;
  margin-left: 30rpx;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  padding: 24rpx;
}

.header .scroll .item .back.data-v-08b50336 {
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  left: 0;
  top: 0;
  z-index: -1;
}

.header .scroll .item .title.data-v-08b50336 {
  font-size: 28rpx;
  font-weight: 600;
  color: #262937;
}

.header .scroll .item .des.data-v-08b50336 {
  font-size: 20rpx;
  font-weight: 400;
  color: #787C8D;
}

.header-center.data-v-08b50336 {
  width: 100%;
  height: 316rpx;
  position: relative;
  z-index: 1;
}

.header-center .backbaby.data-v-08b50336 {
  position: absolute;
  z-index: 2;
  width: 690rpx;
  height: 232rpx;
  bottom: 40rpx;
  left: 30rpx;
}

.header-center .info.data-v-08b50336 {
  padding-left: 70rpx;
  padding-top: 70rpx;
  position: relative;
  z-index: 3;
  width: 100%;
  box-sizing: border-box;
}

.header-center .info .name.data-v-08b50336 {
  font-size: 64rpx;
  font-weight: 600;
  color: #262937;
  line-height: 90rpx;
}

.header-center .info .age.data-v-08b50336 {
  font-size: 24rpx;
  font-weight: 400;
  color: #AEB0BC;
  line-height: 34rpx;
}

.header-center .info .body.data-v-08b50336 {
  display: flex;
  flex-direction: row;
  margin-top: 32rpx;
}

.header-center .info .body .arrow.data-v-08b50336 {
  width: 32rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header-center .info .body .arrow .icon.data-v-08b50336 {
  width: 32rpx;
  height: 32rpx;
}

.header-center .info .body .item.data-v-08b50336 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 48rpx;
}

.header-center .info .body .item .label.data-v-08b50336 {
  font-size: 20rpx;
  font-weight: 400;
  color: #787C8D;
  line-height: 28rpx;
}

.header-center .info .body .item .value.data-v-08b50336 {
  font-size: 32rpx;
  font-family: RousseauDeco;
  color: #262937;
  line-height: 38rpx;
}

.slot-wrap.data-v-08b50336 {
  width: 100%;
  display: flex;
  flex-direction: row;
  height: 100%;
  align-items: center;
  box-sizing: border-box;
  padding: 0 20rpx 0 30rpx;
}

.slot-wrap .title.data-v-08b50336 {
  font-size: 40rpx;
  font-weight: 600;
  color: #000000;
}

.slot-wrap .select.data-v-08b50336 {
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
  margin-left: 8rpx;
}

.slot-wrap .ai.data-v-08b50336 {
  margin-left: auto;
  position: relative;
}

.slot-wrap .ai .tips.data-v-08b50336 {
  width: 690rpx;
  height: 80rpx;
  background: rgba(38, 41, 55, 0.8);
  border-radius: 40rpx;
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  padding: 0 38rpx;
  transform: translateX(-440rpx);
  top: 84rpx;
  z-index: 4;
}

.slot-wrap .ai .tips .text.data-v-08b50336 {
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 40rpx;
}

.slot-wrap .ai .tips .btn.data-v-08b50336 {
  width: 112rpx;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  text-align: center;
  font-size: 24rpx;
  color: #262937;
  line-height: 48rpx;
  margin-left: auto;
  margin-right: 34rpx;
}

.slot-wrap .ai .cion.data-v-08b50336 {
  width: 64rpx;
  border-radius: 50%;
  height: 64rpx;
}

.slot-wrap.data-v-c3bb0e2a {
  width: 100%;
  display: flex;
  flex-direction: row;
  height: 100%;
  align-items: center;
  box-sizing: border-box;
  padding: 0 20rpx 0 30rpx;
}

.slot-wrap .title.data-v-c3bb0e2a {
  font-size: 40rpx;
  font-weight: 600;
  color: #000000;
}

.slot-wrap .select.data-v-c3bb0e2a {
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
  margin-left: 8rpx;
}

.slot-wrap .ai.data-v-c3bb0e2a {
  margin-left: auto;
  position: relative;
}

.slot-wrap .ai .tips.data-v-c3bb0e2a {
  width: 690rpx;
  height: 80rpx;
  background: rgba(38, 41, 55, 0.8);
  border-radius: 40rpx;
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  padding: 0 38rpx;
  transform: translateX(-440rpx);
  top: 84rpx;
  z-index: 4;
}

.slot-wrap .ai .tips .text.data-v-c3bb0e2a {
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 40rpx;
}

.slot-wrap .ai .tips .btn.data-v-c3bb0e2a {
  width: 112rpx;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  text-align: center;
  font-size: 24rpx;
  color: #262937;
  line-height: 48rpx;
  margin-left: auto;
  margin-right: 34rpx;
}

.slot-wrap .ai .cion.data-v-c3bb0e2a {
  width: 64rpx;
  border-radius: 50%;
  height: 64rpx;
}

.content.data-v-3ae15576 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx 10rpx;
}

.content .nav.data-v-3ae15576 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.content .nav .icon.data-v-3ae15576 {
  width: 64rpx;
  height: 64rpx;
}

.content .nav .name.data-v-3ae15576 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  margin-top: 20rpx;
}

.content.data-v-3a08a54f {
  width: 690rpx;
  height: 320rpx;
  background: #617EFB;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 24rpx;
  margin: 0 auto;
  overflow: hidden;
}

.content .top.data-v-3a08a54f {
  height: 100rpx;
  position: relative;
}

.content .top .lena.data-v-3a08a54f {
  height: 64rpx;
  width: 370rpx;
  position: absolute;
  right: 0rpx;
  bottom: 0;
}

.content .top .dota.data-v-3a08a54f {
  height: 34rpx;
  width: 96rpx;
  position: absolute;
  left: 210rpx;
  top: 34rpx;
}

.content .top .title.data-v-3a08a54f {
  top: 34rpx;
  left: 30rpx;
  position: absolute;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}

.content .scroll.data-v-3a08a54f {
  width: 100%;
  display: flex;
  flex-direction: row;
  background: #fff;
  height: 220rpx;
  padding-top: 30rpx;
}

.content .scroll .zhanwei.data-v-3a08a54f {
  width: 30rpx;
  flex-shrink: 0;
}

.content .scroll .item.data-v-3a08a54f {
  width: 508rpx;
  height: 160rpx;
  background: #F6F6F6;
  border-radius: 16rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  margin-left: 30rpx;
}

.content .scroll .item .ic.data-v-3a08a54f {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.content .scroll .item .info.data-v-3a08a54f {
  padding-left: 28rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.content .scroll .item .info .t1.data-v-3a08a54f {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.content .scroll .item .info .t2.data-v-3a08a54f {
  font-size: 20rpx;
  color: #B3B3B3;
  margin-top: 10rpx;
}

.banner.data-v-19afc848 {
  width: 690rpx;
  height: 128rpx;
  background: #FF87BC;
  border-radius: 24rpx;
  margin: 40rpx auto;
}

.content.data-v-b1f6e244 {
  width: 690rpx;
  height: 494rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx auto 0;
  box-sizing: border-box;
  padding: 30rpx;
}

.content .imgs.data-v-b1f6e244 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 40rpx;
}

.content .imgs .imgs1.data-v-b1f6e244 {
  width: 198rpx;
  height: 312rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.content .imgs .imgs1 .img.data-v-b1f6e244 {
  width: 198rpx;
}

.content .top.data-v-b1f6e244 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
}

.content .top .left .t1.data-v-b1f6e244 {
  font-size: 32rpx;
  font-weight: 500;
  color: #787C8D;
  letter-spacing: 1px;
}

.content .top .left .t2.data-v-b1f6e244 {
  font-size: 20rpx;
  font-weight: 400;
  color: #AEB0BC;
}

.content .top .more.data-v-b1f6e244 {
  font-size: 24rpx;
  font-weight: 400;
  color: #AEB0BC;
}

.content.data-v-5f99fa7e {
  width: 690rpx;
  height: 486rpx;
  background: #E4E9FF;
  border-radius: 24rpx;
  margin: 24rpx auto;
  position: relative;
}

.content .back.data-v-5f99fa7e {
  width: 690rpx;
  height: 286rpx;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}

.content .top.data-v-5f99fa7e {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx 30rpx 0;
  position: relative;
  z-index: 2;
}

.content .top .title.data-v-5f99fa7e {
  font-size: 32rpx;
  font-weight: 500;
  color: #262937;
}

.content .top .time.data-v-5f99fa7e {
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
  margin-left: 20rpx;
}

.content .top .btn.data-v-5f99fa7e {
  width: 112rpx;
  height: 40rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #000000;
  line-height: 40rpx;
  text-align: center;
  margin-left: auto;
}

.content .table.data-v-5f99fa7e {
  width: 658rpx;
  height: 338rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  position: relative;
  z-index: 2;
  margin: 40rpx auto 0;
  padding-top: 72rpx;
}

.content .table .back1.data-v-5f99fa7e {
  position: absolute;
  width: 658rpx;
  height: 82rpx;
  left: 0rpx;
  top: -30rpx;
}

.content .table .nums.data-v-5f99fa7e {
  width: 598rpx;
  height: 90rpx;
  background: #F6F6F6;
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin: 0 auto 0;
  box-sizing: border-box;
  padding: 16rpx 20rpx;
}

.content .table .nums .t1.data-v-5f99fa7e {
  font-size: 48rpx;
  font-family: RousseauDeco;
  color: #262937;
}

.content .table .nums .t2.data-v-5f99fa7e {
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
}

.content .table .nums .t3.data-v-5f99fa7e {
  margin-left: auto;
}

.content .table .nums .t4.data-v-5f99fa7e {
  margin-left: 20rpx;
}

.content .table .hr.data-v-5f99fa7e {
  width: 598rpx;
  margin: 20rpx auto 40rpx;
  border-top: 1rpx dashed #bbb;
}

.content .table .dots.data-v-5f99fa7e {
  display: flex;
  flex-direction: row;
  width: 610rpx;
  flex-wrap: wrap;
  margin-left: 30rpx;
}

.content .table .dots .dot.data-v-5f99fa7e {
  width: 28rpx;
  height: 28rpx;
  background: #F6F6F6;
  border-radius: 8rpx;
  margin-right: 13rpx;
  margin-bottom: 20rpx;
}

.content .table .dots .dot1.data-v-5f99fa7e {
  background: #40E08F;
}

.content .table .dots .dot2.data-v-5f99fa7e {
  background: #FF7373;
}

.content.data-v-049f7f6e {
  width: 750rpx;
  border-radius: 24rpx;
  margin: 20rpx auto 0;
  padding: 0rpx 30rpx;
  box-sizing: border-box;
}

.content .top.data-v-049f7f6e {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.content .top .title.data-v-049f7f6e {
  font-size: 32rpx;
  font-weight: 500;
  color: #787C8D;
}

.content .top .arrow.data-v-049f7f6e {
  font-size: 24rpx;
  font-weight: 400;
  color: #AEB0BC;
}

.content .body.data-v-049f7f6e {
  width: 690rpx;
  height: 180rpx;
  background: #F6F6F6;
  border-radius: 24rpx;
  position: relative;
  padding-left: 30rpx;
  box-sizing: border-box;
  padding-top: 20rpx;
  margin-top: 24rpx;
}

.content .body .back.data-v-049f7f6e {
  width: 190rpx;
  height: 160rpx;
  bottom: 1rpx;
  position: absolute;
  right: 20rpx;
}

.content .body .title1.data-v-049f7f6e {
  font-size: 32rpx;
  font-weight: 500;
  color: #262937;
  line-height: 44rpx;
}

.content .body .date.data-v-049f7f6e {
  font-size: 20rpx;
  font-weight: 400;
  color: #AEB0BC;
  line-height: 28rpx;
  margin-top: 20rpx;
}

.content .body .des.data-v-049f7f6e {
  margin-top: 32rpx;
  display: flex;
  flex-direction: row;
  font-size: 24rpx;
  font-weight: 500;
  color: #787C8D;
  line-height: 34rpx;
}

.content .body .des .detail1.data-v-049f7f6e {
  margin-left: 20rpx;
}

.content .body .add.data-v-049f7f6e {
  margin-top: 48rpx;
  width: 136rpx;
  height: 40rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  text-align: center;
  line-height: 40rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
}

.content.data-v-b3e12b5c {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 24rpx auto;
  width: 690rpx;
}

.content .scroll.data-v-b3e12b5c {
  width: 100%;
  height: 218rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0 24rpx;
  box-sizing: border-box;
  z-index: 1;
  position: relative;
}

.content .right.data-v-b3e12b5c {
  background: #FFEBE3;
  color: #FF7373;
}

.content .right .mask.data-v-b3e12b5c {
  position: absolute;
  width: 334rpx;
  height: 104rpx;
  background: linear-gradient(180deg, rgba(255, 235, 227, 0) 0%, #FFEBE3 100%);
  border-radius: 0rpx 0rpx 24rpx 24rpx;
  left: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

.content .right .empty.data-v-b3e12b5c {
  font-size: 28rpx;
}

.content .right .item.data-v-b3e12b5c {
  padding: 0 20rpx;
  max-width: 286rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 400;
  text-align: center;
  color: #262937;
  line-height: 48rpx;
  box-sizing: border-box;
  flex-shrink: 0;
  margin-bottom: 16rpx;
}

.content .left.data-v-b3e12b5c {
  background: #EBF6FF;
  color: #617EFB;
}

.content .left .empty.data-v-b3e12b5c {
  font-size: 28rpx;
}

.content .left .mask.data-v-b3e12b5c {
  position: absolute;
  width: 334rpx;
  height: 104rpx;
  background: linear-gradient(180deg, rgba(235, 246, 255, 0) 0%, #EBF6FF 100%);
  border-radius: 0rpx 0rpx 24rpx 24rpx;
  left: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

.content .left .item.hasLine.data-v-b3e12b5c::after {
  content: " ";
  position: absolute;
  width: 1rpx;
  height: 100rpx;
  left: 43rpx;
  border-left: 1rpx dashed #ccc;
  top: 34rpx;
  z-index: 2;
}

.content .left .item.data-v-b3e12b5c {
  width: 286rpx;
  height: 104rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  padding-top: 16rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.content .left .item .dot.data-v-b3e12b5c {
  width: 16rpx;
  height: 16rpx;
  background: #FFFFFF;
  border: 2rpx solid #617EFB;
  border-radius: 50%;
  margin-left: 36rpx;
  position: relative;
  top: 8rpx;
}

.content .left .item .info.data-v-b3e12b5c {
  padding-left: 16rpx;
}

.content .left .item .info .time.data-v-b3e12b5c {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #AEB0BC;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content .left .item .info .text.data-v-b3e12b5c {
  font-size: 28rpx;
  font-weight: 500;
  color: #262937;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content .right.data-v-b3e12b5c,
.content .left.data-v-b3e12b5c {
  width: 334rpx;
  height: 320rpx;
  border-radius: 24rpx;
  box-sizing: border-box;
  position: relative;
}

.content .right .title.data-v-b3e12b5c,
.content .left .title.data-v-b3e12b5c {
  padding: 40rpx 30rpx 0;
  font-size: 32rpx;
  font-weight: 500;
}

.list.data-v-1ed4a3b6 {
  overflow: auto;
  width: 100%;
  min-height: 50vh;
  overflow: hidden;
  box-sizing: border-box;
  background: #f6f6f6;
  box-sizing: border-box;
}

.list .item.data-v-1ed4a3b6 {
  height: 240rpx;
  display: flex;
  margin-bottom: 20rpx;
  background: #fff;
  margin: 20rpx auto;
  width: 690rpx;
  border-radius: 24rpx;
}

.list .item .msg.data-v-1ed4a3b6 {
  margin-left: 16rpx;
  flex: auto;
  flex: 1;
  min-width: 1rpx;
}

.list .item .msg .name.data-v-1ed4a3b6 {
  height: 28rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #262937;
  margin: 12rpx 0;
  line-height: 28rpx;
}

.list .item .msg .address.data-v-1ed4a3b6 {
  font-size: 24rpx;
  color: #787C8D;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list .item .msg .address .distance.data-v-1ed4a3b6 {
  color: #262937;
}

.list .item .msg .tag.data-v-1ed4a3b6 {
  display: flex;
  flex-wrap: wrap;
  height: 88rpx;
  overflow: hidden;
  margin-top: 14rpx;
  margin-bottom: 14rpx;
}

.list .item .msg .tag text.data-v-1ed4a3b6 {
  font-size: 20rpx;
  color: #262937;
  padding: 0 16rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
  background: #F6F6F6;
  border-radius: 8rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
}

.list .item .msg .num.data-v-1ed4a3b6 {
  font-size: 24rpx;
  color: #FF7373;
}</style>