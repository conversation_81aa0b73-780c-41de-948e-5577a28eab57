<template name="skeleton">
  <view class="sk-container">
    <view class="opacity_box data-v-077dfeb2" style="opacity: 1">
      <view is="uni_modules/vk-uview-ui/components/u-navbar/u-navbar" class="data-v-077dfeb2">
        <view class="data-v-a7de1ae4 navbar--data-v-a7de1ae4">
          <view
            class="u-navbar navbar--u-navbar data-v-a7de1ae4 navbar--data-v-a7de1ae4 u-navbar-fixed navbar--u-navbar-fixed"
            style="z-index:980;background-color:#FFFFFF;">
            <view class="u-status-bar navbar--u-status-bar data-v-a7de1ae4 navbar--data-v-a7de1ae4" style="height:20px">
            </view>
            <view class="u-navbar-inner navbar--u-navbar-inner data-v-a7de1ae4 navbar--data-v-a7de1ae4"
              style="height:48px;margin-right:94px;">
              <view class="u-slot-content navbar--u-slot-content data-v-a7de1ae4 navbar--data-v-a7de1ae4">
                <view class="slot-wrap data-v-077dfeb2 sk-transparent sk-text-8-3333-460 sk-text"> 动态 </view>
              </view>
              <view class="u-slot-right navbar--u-slot-right data-v-a7de1ae4 navbar--data-v-a7de1ae4"></view>
            </view>
          </view>
          <view class="u-navbar-placeholder navbar--u-navbar-placeholder data-v-a7de1ae4 navbar--data-v-a7de1ae4"
            style="width:100%;height:68px"></view>
        </view>
      </view>
      <view class="scroll-box data-v-077dfeb2">
        <scroll-view class="scroll data-v-077dfeb2" refresher-enabled="true" scroll-y="true" style="opacity: 1">
          <view class="single-box data-v-077dfeb2">
            <view class="single-box__title flex-mode data-v-077dfeb2">
              <image class="single-box__title__avatar data-v-077dfeb2 sk-image"></image>
              <view class="single-box__title__text flex-mode data-v-077dfeb2">
                <view class="single-box__title__text__1 data-v-077dfeb2 sk-transparent sk-text-0-0000-805 sk-text">高园长
                </view>
                <view class="single-box__title__text__2 data-v-077dfeb2 sk-transparent sk-text-0-0000-578 sk-text">园长
                </view>
              </view>
            </view>
            <view class="single-box__content data-v-077dfeb2">
              <view class="single-box__content__article data-v-077dfeb2 sk-transparent sk-text-16-6667-568 sk-text">描述啥？
              </view>
              <view class="flex-mode single-box__content__img1 data-v-077dfeb2">
                <image class="data-v-077dfeb2 sk-image" mode="aspectFill"></image>
              </view>
            </view>
            <view class="flex-mode single-box__bottom data-v-077dfeb2">
              <view class="single-box__bottom__time data-v-077dfeb2 sk-transparent sk-text-14-7059-849 sk-text"
                style="true">09月13日</view>
              <view class="single-box__bottom__icon data-v-077dfeb2">
                <image class="data-v-077dfeb2 sk-image"></image>
              </view>
              <view class="single-box__bottom__count data-v-077dfeb2 sk-transparent sk-text-14-7059-734 sk-text">0</view>
              <view class="single-box__bottom__icon data-v-077dfeb2" style="margin-left:0">
                <image class="data-v-077dfeb2 sk-image"></image>
              </view>
              <view class="single-box__bottom__count data-v-077dfeb2 sk-transparent sk-text-14-7059-275 sk-text">0</view>
            </view>
            <view class="single-box__divider data-v-077dfeb2"></view>
          </view>
          <view class="single-box data-v-077dfeb2">
            <view class="single-box__title flex-mode data-v-077dfeb2">
              <image class="single-box__title__avatar data-v-077dfeb2 sk-image"></image>
              <view class="single-box__title__text flex-mode data-v-077dfeb2">
                <view class="single-box__title__text__1 data-v-077dfeb2 sk-transparent sk-text-0-0000-840 sk-text">高园长
                </view>
                <view class="single-box__title__text__2 data-v-077dfeb2 sk-transparent sk-text-0-0000-574 sk-text">园长
                </view>
              </view>
              <image class="single-box__title__work data-v-077dfeb2 sk-image"></image>
            </view>
            <view class="single-box__content data-v-077dfeb2">
              <view class="single-box__content__article data-v-077dfeb2"></view>
              <view class="flex-mode single-box__content__img1 data-v-077dfeb2">
                <image class="data-v-077dfeb2 sk-image" mode="aspectFill"></image>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="add-btn data-v-077dfeb2">
      <image class="data-v-077dfeb2 sk-image"></image>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.sk-transparent {
  color: transparent !important;
}

.sk-text-8-3333-460 {
  background-image: linear-gradient(transparent 8.3333%, #EEEEEE 0%, #EEEEEE 91.6667%, transparent 0%) !important;
  background-size: 100% 48.0000rpx;
  position: relative !important;
}

.sk-text {
  background-origin: content-box !important;
  background-clip: content-box !important;
  background-color: transparent !important;
  color: transparent !important;
  background-repeat: repeat-y !important;
}

.sk-text-0-0000-805 {
  background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-0-0000-578 {
  background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
  background-size: 100% 24.0000rpx;
  position: relative !important;
}

.sk-text-16-6667-568 {
  background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
  background-size: 100% 48.0000rpx;
  position: relative !important;
}

.sk-text-14-7059-849 {
  background-image: linear-gradient(transparent 14.7059%, #EEEEEE 0%, #EEEEEE 85.2941%, transparent 0%) !important;
  background-size: 100% 34.0000rpx;
  position: relative !important;
}

.sk-text-14-7059-734 {
  background-image: linear-gradient(transparent 14.7059%, #EEEEEE 0%, #EEEEEE 85.2941%, transparent 0%) !important;
  background-size: 100% 34.0000rpx;
  position: relative !important;
}

.sk-text-14-7059-275 {
  background-image: linear-gradient(transparent 14.7059%, #EEEEEE 0%, #EEEEEE 85.2941%, transparent 0%) !important;
  background-size: 100% 34.0000rpx;
  position: relative !important;
}

.sk-text-0-0000-840 {
  background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
  background-size: 100% 28.0000rpx;
  position: relative !important;
}

.sk-text-0-0000-574 {
  background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
  background-size: 100% 24.0000rpx;
  position: relative !important;
}

.sk-image {
  background: #EFEFEF !important;
}

.sk-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  z-index: 999;
}

.flex-mode.data-v-077dfeb2 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-btn.data-v-077dfeb2 {
  z-index: 999;
  position: fixed;
  bottom: 200rpx;
  right: 40rpx;
}

.add-btn image.data-v-077dfeb2 {
  width: 112rpx;
  height: 112rpx;
}

.opacity_box.data-v-077dfeb2 {
  transition: all 1s;
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.opacity_box .scroll-box.data-v-077dfeb2 {
  flex: 1;
  position: relative;
  z-index: 2;
  min-height: 1rpx;
  padding-bottom: 180rpx;
}

.opacity_box .scroll-box .scroll.data-v-077dfeb2 {
  width: 100vw;
  height: 100%;
}

.main.data-v-077dfeb2 {
  transition: all 0.5s;
  margin-top: 50%;
}

.single-box.data-v-077dfeb2 {
  transition: all 0.5s;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 40rpx 30rpx 0 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
}

.single-box__title.data-v-077dfeb2 {
  width: 100%;
}

.single-box__title__avatar.data-v-077dfeb2 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #FF7373;
  color: #FFFFFF;
}

.single-box__title__work.data-v-077dfeb2 {
  width: 140rpx;
  height: 64rpx;
}

.single-box__title__text.data-v-077dfeb2 {
  flex-direction: column;
  margin-right: auto;
}

.single-box__title__text__1.data-v-077dfeb2 {
  margin-right: auto;
  font-size: 28rpx;
  font-weight: 500;
  color: #111111;
  line-height: 28rpx;
}

.single-box__title__text__2.data-v-077dfeb2 {
  margin-right: auto;
  margin-top: 8rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #AEB0BC;
  line-height: 24rpx;
}

.single-box__content.data-v-077dfeb2 {
  padding-left: 68rpx;
  width: 100%;
}

.single-box__content__article.data-v-077dfeb2 {
  margin-top: 16rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #262937;
  line-height: 48rpx;
}

.single-box__content__show.data-v-077dfeb2 {
  font-size: 32rpx;
  font-weight: 400;
  color: #617EFB;
  line-height: 48rpx;
}

.single-box__content__img.data-v-077dfeb2 {
  margin-top: 16rpx;
  width: 556rpx;
  height: 180rpx;
  background: #FF7373;
  border-radius: 24rpx;
}

.single-box__content__img image.data-v-077dfeb2 {
  width: 556rpx;
  height: 180rpx;
  border-radius: 24rpx;
}

.single-box__content__img1.data-v-077dfeb2 {
  margin-top: 16rpx;
  width: 556rpx;
  height: 368rpx;
  border-radius: 24rpx;
}

.single-box__content__img1 image.data-v-077dfeb2 {
  width: 556rpx;
  height: 368rpx;
  border-radius: 24rpx;
}

.single-box__content__img1 video.data-v-077dfeb2 {
  width: 556rpx;
  height: 368rpx;
  border-radius: 24rpx;
}

.single-box__content__img2.data-v-077dfeb2 {
  margin-top: 16rpx;
  width: 556rpx;
  height: 274rpx;
  justify-content: space-between;
  border-radius: 24rpx;
}

.single-box__content__img2 image.data-v-077dfeb2 {
  width: 274rpx;
  height: 274rpx;
}

.single-box__content__img2 image.data-v-077dfeb2:nth-child(1) {
  border-top-left-radius: 24rpx;
  border-bottom-left-radius: 24rpx;
}

.single-box__content__img2 image.data-v-077dfeb2:nth-child(2) {
  border-top-right-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}

.single-box__content__img3.data-v-077dfeb2 {
  margin-top: 16rpx;
  width: 556rpx;
  height: 368rpx;
  justify-content: space-between;
  border-radius: 24rpx;
}

.single-box__content__img3 .single.data-v-077dfeb2 {
  width: 368rpx;
  height: 368rpx;
  border-top-left-radius: 24rpx;
  border-bottom-left-radius: 24rpx;
}

.single-box__content__img3 .multi.data-v-077dfeb2 {
  flex-direction: column;
  justify-content: space-between;
  width: 180rpx;
  height: 100%;
}

.single-box__content__img3 .multi image.data-v-077dfeb2:nth-child(1) {
  width: 180rpx;
  height: 180rpx;
  border-top-right-radius: 24rpx;
}

.single-box__content__img3 .multi image.data-v-077dfeb2:nth-child(2) {
  width: 180rpx;
  height: 180rpx;
  border-bottom-right-radius: 24rpx;
}

.single-box__content__img4.data-v-077dfeb2 {
  margin-top: 16rpx;
  width: 556rpx;
  height: 556rpx;
  justify-content: space-between;
  flex-direction: column;
  border-radius: 24rpx;
}

.single-box__content__img4 .multi1.data-v-077dfeb2 {
  flex-direction: row;
  justify-content: space-between;
  width: 556rpx;
  height: 274rpx;
}

.single-box__content__img4 .multi1 image.data-v-077dfeb2:nth-child(1) {
  width: 274rpx;
  height: 274rpx;
  border-top-left-radius: 24rpx;
}

.single-box__content__img4 .multi1 image.data-v-077dfeb2:nth-child(2) {
  width: 274rpx;
  height: 274rpx;
  border-top-right-radius: 24rpx;
}

.single-box__content__img4 .multi2.data-v-077dfeb2 {
  flex-direction: row;
  justify-content: space-between;
  width: 556rpx;
  height: 274rpx;
}

.single-box__content__img4 .multi2 image.data-v-077dfeb2:nth-child(1) {
  width: 274rpx;
  height: 274rpx;
  border-bottom-left-radius: 24rpx;
}

.single-box__content__img4 .multi2 image.data-v-077dfeb2:nth-child(2) {
  width: 274rpx;
  height: 274rpx;
  border-bottom-right-radius: 24rpx;
}

.single-box__content__img5.data-v-077dfeb2 {
  margin-top: 16rpx;
  width: 556rpx;
  height: 462rpx;
  justify-content: space-between;
  flex-direction: column;
  border-radius: 24rpx;
}

.single-box__content__img5 .multi3.data-v-077dfeb2 {
  flex-direction: row;
  justify-content: space-between;
  width: 556rpx;
  height: 274rpx;
}

.single-box__content__img5 .multi3 image.data-v-077dfeb2:nth-child(1) {
  width: 274rpx;
  height: 274rpx;
  border-top-left-radius: 24rpx;
}

.single-box__content__img5 .multi3 image.data-v-077dfeb2:nth-child(2) {
  width: 274rpx;
  height: 274rpx;
  border-top-right-radius: 24rpx;
}

.single-box__content__img5 .multi4.data-v-077dfeb2 {
  position: relative;
  flex-direction: row;
  justify-content: space-between;
  width: 556rpx;
  height: 180rpx;
}

.single-box__content__img5 .multi4 image.data-v-077dfeb2:nth-child(1) {
  width: 180rpx;
  height: 180rpx;
  border-bottom-left-radius: 24rpx;
}

.single-box__content__img5 .multi4 image.data-v-077dfeb2:nth-child(2) {
  width: 180rpx;
  height: 180rpx;
}

.single-box__content__img5 .multi4.data-v-077dfeb2 :nth-child(3) {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-bottom-right-radius: 24rpx;
}

.single-box__content__img5 .multi4 .cover.data-v-077dfeb2 {
  right: 0;
  bottom: 0;
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  border-bottom-right-radius: 24rpx;
  font-size: 48rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 66rpx;
  background: rgba(0, 0, 0, 0.5);
}

.single-box__bottom.data-v-077dfeb2 {
  margin-top: 32rpx;
  width: 100%;
  flex-direction: row;
  padding-left: 68rpx;
}

.single-box__bottom__time.data-v-077dfeb2 {
  margin-right: auto;
  font-size: 24rpx;
  font-weight: 400;
  color: #AEB0BC;
  line-height: 34rpx;
}

.single-box__bottom__del.data-v-077dfeb2 {
  margin-right: auto;
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
  line-height: 34rpx;
}

.single-box__bottom__icon.data-v-077dfeb2 {
  margin-left: 68rpx;
}

.single-box__bottom__icon image.data-v-077dfeb2 {
  width: 48rpx;
  height: 48rpx;
}

.single-box__bottom__count.data-v-077dfeb2 {
  width: 60rpx;
  margin-left: 14rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #AEB0BC;
  line-height: 34rpx;
}

.single-box__divider.data-v-077dfeb2 {
  width: 100%;
  height: 2rpx;
  color: #F6F6F6;
  margin: 32rpx 0 0 0;
}

.pop-title.data-v-077dfeb2 {
  position: absolute;
  top: 30rpx;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #262937;
  line-height: 44rpx;
}

.comment-content.data-v-077dfeb2 {
  height: 100%;
  padding: 100rpx 30rpx 130rpx;
  text-align: center;
}

.comment-content .input-box.data-v-077dfeb2 {
  width: 100%;
  box-sizing: border-box;
  padding: 18rpx 30rpx;
  position: fixed;
  left: 0;
  bottom: 30rpx;
}

.comment-content .input-box__inner.data-v-077dfeb2 {
  flex: 1;
  text-align: left;
  width: 100%;
  padding: 18rpx 30rpx;
  background: #F6F6F6;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #262937;
  line-height: 28rpx;
}

.comment-content .input-box__placeholder.data-v-077dfeb2 {
  font-size: 28rpx;
  font-weight: 400;
  color: #AEB0BC;
  line-height: 28rpx;
}

.comment-content .input-box__send.data-v-077dfeb2 {
  margin-left: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #262937;
  line-height: 40rpx;
}

.comment-content .comment-single.data-v-077dfeb2 {
  padding-top: 40rpx;
}

.comment-content .comment-single image.data-v-077dfeb2 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-bottom: auto;
}

.comment-content .comment-single__avatar.data-v-077dfeb2 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-bottom: auto;
  background: #FF7373;
  color: #FFFFFF;
}

.comment-content .comment-single__right.data-v-077dfeb2 {
  flex: 1;
  margin-left: 24rpx;
  width: 100%;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #EEEEEE;
  text-align: left;
}

.comment-content .comment-single__right__name.data-v-077dfeb2 {
  font-size: 28rpx;
  font-weight: 500;
  color: #111111;
  line-height: 28rpx;
}

.comment-content .comment-single__right__content.data-v-077dfeb2 {
  margin-top: 24rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #262937;
  line-height: 28rpx;
}

.comment-content .comment-single__right__date.data-v-077dfeb2 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #AEB0BC;
  line-height: 34rpx;
}

.comment-content .comment-single__right__date text.data-v-077dfeb2 {
  margin-left: 16rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #787C8D;
  line-height: 34rpx;
}

.slot-wrap.data-v-077dfeb2 {
  margin-left: 30rpx;
  display: flex;
  align-items: center;
  flex: 1;
  font-size: 40rpx;
  font-weight: 500;
  color: #000000;
  line-height: 48rpx;
  letter-spacing: 1px;
}</style>