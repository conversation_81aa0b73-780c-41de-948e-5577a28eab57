<template>
    <skeleton v-if="showCom" />
    <view v-else class="opacity_box">
        <view class="top-box flex-mode" :style="{ paddingTop: statusBarHeight + 'px' }">
            <image class="top-box__backimg" src="https://obs.tuoyupt.com/nanjing/pstac/notice/back.svg"></image>
            <view class="slot-wrap">
                <view :class="[userInfo.circle_type == 0 ? 'title_sel' : 'title']" @click="changeTitle(0)">
                    成长时光
                </view>
                <view :class="[userInfo.circle_type == 1 ? 'title title2 title_sel' : 'title title2']"
                    @click="changeTitle(1)">
                    班级动态
                </view>
                <view class="slot-wrap__right" @tap="toTop"></view>
            </view>
            <!--泸州托育隐藏 <view class="top-box__ai">
                <view class="top-box__ai__top">HI，我是宝宝成长AI助手</view>
                <view class="top-box__ai__mid">宝宝成长问题尽管问我</view>
                <image @click="toWenwen" class="top-box__ai__img"
                    src="https://obs.tuoyupt.com/miniprogram/client/ai.gif">
                </image>
                <NoticeBar :notices="wenwenList"></NoticeBar>
            </view> -->
        </view>
        <swiper class="scroll-box" :current="userInfo.circle_type" @animationfinish="animationfinish">
            <swiper-item class="">
                <scroll-view :scroll-y="true" :scroll-top="scrollTopGrow" class="scroll"
                    :refresher-triggered="triggered" :refresher-enabled="true" @refresherrefresh="refresh"
                    @scrolltolower="loadMore" @scroll="scrollGrow">
                    <!-- 缺省页 -->
                    <view class="main" v-if="isGrowBlank" :style="showList">
                        <u-empty text="这里静悄悄，还没有动态" mode="list" :src="nodataUrl"></u-empty>
                    </view>
                    <!-- 列表 -->
                    <template v-else>
                        <!-- <ListSingle :cur-detail="item" @sendrefresh="getList" @changeshow="changeShow" v-if="showCom"></ListSingle> -->
                        <view class="single-box" v-for="(item, index) in growList" :key="item.id">
                            <!-- 顶部title -->
                            <view class="single-box__title flex-mode">
                                <view class="single-box__title__timebox flex-mode">
                                    <view class="single-box__title__timebox__1">{{ item.time1 }}</view>
                                    <view class="single-box__title__timebox__inner flex-mode">
                                        <view class="single-box__title__timebox__inner__left">{{ item.time2 }}</view>
                                        <view class="single-box__title__timebox__inner__right">{{ item.student.age }}
                                        </view>
                                    </view>
                                </view>
                                <!-- <image :src="item.account_img" class="single-box__title__avatar" v-if="item.account_img">
                        </image>
                        <view class="single-box__title__avatar flex-mode" v-else>{{
                            item.account_name.charAt(0) }}</view>
                        <view class="single-box__title__text flex-mode">
                            <view class="single-box__title__text__1">{{ item.account_name }}</view>
                            <view class="single-box__title__text__2">{{ item.job_name }}
                            </view>
                        </view> -->
                                <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/works.png"
                                    class="single-box__title__work" v-if="item.type == 3"></image>
                            </view>
                            <!-- 中部内容区域 -->
                            <view class="single-box__content u-p-b-30">
                                <!-- 时间线 开始 -->
                                <view class="single-box__content__timeline">
                                    <view
                                        class="single-box__content__timeline__dot single-box__content__timeline__begin">
                                    </view>
                                    <view class="single-box__content__timeline__dot single-box__content__timeline__end">
                                    </view>
                                </view>
                                <!-- 时间线 结束 -->
                                <view class="single-box__content__article" v-if="item.type != 9">{{ item.tempContent }}
                                </view>
                                <!-- 初始消息 开始 -->
                                <view class="single-box__content__default flex-mode" v-else>
                                    <view class="single-box__content__default__top">
                                        <image src="https://obs.tuoyupt.com/miniprogram/client/hello.png">
                                        </image>
                                    </view>
                                    <view class="single-box__content__default__title">{{ item.title1 }}</view>
                                    <view class="single-box__content__default__content">{{ item.title2 }}</view>
                                </view>
                                <!-- 初始消息 结束 -->
                                <!-- <view class="single-box__content__article">{{ tempContent }}</view> -->
                                <view v-if="item.tempContent !== null && item.tempContent.length > limFont">
                                    <text class="single-box__content__show" v-if="item.isShowAllContent"
                                        @click="toggleDescription1(index)">
                                        展开
                                    </text>
                                    <text class="single-box__content__show" v-else @click="toggleDescription1(index)">
                                        收起
                                    </text>
                                </view>
                                <view class="flex-mode single-box__content__img" @click="toExam" v-if="item.type == 6">
                                    <text class="single-box__content__img__title">体检报告</text>
                                    <text class="single-box__content__img__time">{{ item.time1 }}</text>
                                    <image src="https://obs.tuoyupt.com/miniprogram/client/notice/exam.png">
                                    </image>
                                </view>
                                <!-- 图片展示区域 开始 -->
                                <template v-if="item.type_class == 1">
                                    <!-- 一张图 -->
                                    <view class="flex-mode single-box__content__img1 u-m-b-40"
                                        v-if="item.image.length === 1 && item.image[0].url != null">
                                        <image @click="concatImg(item.image, 0)" :src="item.image[0].url"
                                            mode="aspectFill">
                                        </image>
                                    </view>
                                    <!-- 两张图 -->
                                    <view class="flex-mode single-box__content__img2 u-m-b-40"
                                        v-if="item.image.length === 2">
                                        <image @click="concatImg(item.image, 0)" :src="item.image[0].url"
                                            mode="aspectFill">
                                        </image>
                                        <image @click="concatImg(item.image, 1)" :src="item.image[1].url"
                                            mode="aspectFill">
                                        </image>
                                    </view>
                                    <!-- 三张图 -->
                                    <view class="flex-mode single-box__content__img3 u-m-b-40"
                                        v-if="item.image.length === 3">
                                        <image @click="concatImg(item.image, 0)" class="single" :src="item.image[0].url"
                                            mode="aspectFill"></image>
                                        <view class="flex-mode multi">
                                            <image @click="concatImg(item.image, 1)" :src="item.image[1].url"
                                                mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.image, 2)" :src="item.image[2].url"
                                                mode="aspectFill">
                                            </image>
                                        </view>
                                    </view>
                                    <!-- 四张图 -->
                                    <view class="flex-mode single-box__content__img4 u-m-b-40"
                                        v-if="item.image.length === 4">
                                        <view class="flex-mode multi1">
                                            <image @click="concatImg(item.image, 0)" :src="item.image[0].url"
                                                mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.image, 1)" :src="item.image[1].url"
                                                mode="aspectFill">
                                            </image>
                                        </view>
                                        <view class="flex-mode multi2">
                                            <image @click="concatImg(item.image, 2)" :src="item.image[2].url"
                                                mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.image, 3)" :src="item.image[3].url"
                                                mode="aspectFill">
                                            </image>
                                        </view>
                                    </view>
                                    <!-- 五张图及以上 -->
                                    <view class="flex-mode single-box__content__img5 u-m-b-40"
                                        v-if="item.image.length >= 5">
                                        <view class="flex-mode multi3">
                                            <image @click="concatImg(item.image, 0)" :src="item.image[0].url"
                                                mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.image, 1)" :src="item.image[1].url"
                                                mode="aspectFill">
                                            </image>
                                        </view>
                                        <view class="flex-mode multi4">
                                            <image @click="concatImg(item.image, 2)" :src="item.image[2].url"
                                                mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.image, 3)" :src="item.image[3].url"
                                                mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.image, 4)" :src="item.image[4].url"
                                                mode="aspectFill">
                                            </image>
                                            <view @click="concatImg(item.image, 4)" class="flex-mode cover"
                                                v-if="item.image.length > 5">+{{
                                                    item.image.length - 5 }}</view>
                                        </view>
                                    </view>
                                </template>
                                <!-- 图片展示区域 结束 -->
                                <!-- 视频展示区域 开始 -->
                                <template v-else-if="item.type_class == 2">
                                    <!-- <view class="flex-mode single-box__content__img1 u-m-b-40">
                                        <video :src="item.image[0].url" object-fit="cover" :show-progress="false"
                                            :poster="item.image[0].url + '?vframe/jpeg/offset/1'" :id="index"
                                            @play="saveplay(index)" @loadedmetadata="loadedmetadata"></video>
                                    </view> -->
                                    <view class="flex-mode single-box__content__img1 u-m-b-40">
                                        <player :url="item.image[0].url" :id="index"></player>
                                    </view>
                                </template>
                                <!-- 视频展示区域 结束 -->
                                <!-- 底部信息 开始 -->
                                <view class="single-box__content__bottom flex-mode">
                                    <view class="single-box__content__bottom__time">{{ item.time3 }}</view>
                                    <view class="single-box__content__bottom__time u-m-l-10" v-if="item.can_delete == 1"
                                        @click="delGrowth(item)">删除
                                    </view>
                                    <view class="single-box__content__bottom__time single-box__content__bottom__time1">
                                        {{ item.account_name }}</view>
                                </view>
                                <!-- 底部信息 结束 -->
                            </view>
                            <view class="single-box__divider"></view>
                        </view>
                    </template>
                    <u-loadmore :status="loadStatus" v-if="!isGrowBlank" />
                </scroll-view>
            </swiper-item>
            <swiper-item class="">
                <scroll-view :scroll-y="true" :scroll-top="scrollTopCircle" class="scroll"
                    :refresher-triggered="triggered" :refresher-enabled="true" @refresherrefresh="refresh"
                    @scrolltolower="loadMore" @scroll="scrollCircle">
                    <!-- 缺省页 -->
                    <view class="main" v-if="isBlank" :style="showList">
                        <u-empty text="这里静悄悄，还没有动态" mode="list" :src="nodataUrl"></u-empty>
                    </view>
                    <!-- 列表 -->
                    <template v-else>
                        <!-- <ListSingle :cur-detail="item" @sendrefresh="getList" @changeshow="changeShow" v-if="showCom"></ListSingle> -->
                        <view class="single-box" v-for="(item, index) in circleList" :key="item.id">
                            <!-- 顶部title -->
                            <view class="single-box__title flex-mode">
                                <image :src="item.account_img" class="single-box__title__avatar"
                                    v-if="item.account_img">
                                </image>
                                <view class="single-box__title__avatar flex-mode" v-else>{{
                                    item.account_name.charAt(0) }}</view>
                                <view class="single-box__title__text flex-mode">
                                    <view class="single-box__title__text__1">{{ item.account_name }}</view>
                                    <view class="single-box__title__text__2">{{ item.job_name }}
                                    </view>
                                </view>
                                <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/works.png"
                                    class="single-box__title__work" v-if="item.type == 3"></image>
                            </view>
                            <!-- 中部内容区域 -->
                            <view class="single-box__content">
                                <view class="single-box__content__article">{{ item.tempContent }}</view>
                                <!-- <view class="single-box__content__article">{{ tempContent }}</view> -->
                                <view v-if="item.tempContent !== null && item.tempContent.length > limFont">
                                    <text class="single-box__content__show" v-if="item.isShowAllContent"
                                        @click="toggleDescription(index)">
                                        展开
                                    </text>
                                    <text class="single-box__content__show" v-else @click="toggleDescription(index)">
                                        收起
                                    </text>
                                </view>
                                <!-- 图片展示区域 开始 -->
                                <template v-if="item.type_class == 1">
                                    <!-- 教学计划 -->
                                    <view class="flex-mode single-box__content__img" v-if="item.type_name == 5">
                                        <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/eduplan.png"></image>
                                    </view>
                                    <!-- 食谱 -->
                                    <view class="flex-mode single-box__content__img" v-if="item.type_name == 6">
                                        <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/recipe.png"></image>
                                    </view>
                                    <!-- 一张图 -->
                                    <view class="flex-mode single-box__content__img1"
                                        v-if="item.data_media.medias.length === 1 && (item.data_media.type == 1 || item.data_media.type == 3)">
                                        <image @click="concatImg(item.data_media.medias, 0)"
                                            :src="item.data_media.medias[0].url" mode="aspectFill"></image>
                                    </view>
                                    <!-- 两张图 -->
                                    <view class="flex-mode single-box__content__img2"
                                        v-if="item.data_media.medias.length === 2">
                                        <image @click="concatImg(item.data_media.medias, 0)"
                                            :src="item.data_media.medias[0].url" mode="aspectFill"></image>
                                        <image @click="concatImg(item.data_media.medias, 1)"
                                            :src="item.data_media.medias[1].url" mode="aspectFill"></image>
                                    </view>
                                    <!-- 三张图 -->
                                    <view class="flex-mode single-box__content__img3"
                                        v-if="item.data_media.medias.length === 3">
                                        <image @click="concatImg(item.data_media.medias, 0)" class="single"
                                            :src="item.data_media.medias[0].url" mode="aspectFill"></image>
                                        <view class="flex-mode multi">
                                            <image @click="concatImg(item.data_media.medias, 1)"
                                                :src="item.data_media.medias[1].url" mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.data_media.medias, 2)"
                                                :src="item.data_media.medias[2].url" mode="aspectFill">
                                            </image>
                                        </view>
                                    </view>
                                    <!-- 四张图 -->
                                    <view class="flex-mode single-box__content__img4"
                                        v-if="item.data_media.medias.length === 4">
                                        <view class="flex-mode multi1">
                                            <image @click="concatImg(item.data_media.medias, 0)"
                                                :src="item.data_media.medias[0].url" mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.data_media.medias, 1)"
                                                :src="item.data_media.medias[1].url" mode="aspectFill">
                                            </image>
                                        </view>
                                        <view class="flex-mode multi2">
                                            <image @click="concatImg(item.data_media.medias, 2)"
                                                :src="item.data_media.medias[2].url" mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.data_media.medias, 3)"
                                                :src="item.data_media.medias[3].url" mode="aspectFill">
                                            </image>
                                        </view>
                                    </view>
                                    <!-- 五张图及以上 -->
                                    <view class="flex-mode single-box__content__img5"
                                        v-if="item.data_media.medias.length >= 5">
                                        <view class="flex-mode multi3">
                                            <image @click="concatImg(item.data_media.medias, 0)"
                                                :src="item.data_media.medias[0].url" mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.data_media.medias, 1)"
                                                :src="item.data_media.medias[1].url" mode="aspectFill">
                                            </image>
                                        </view>
                                        <view class="flex-mode multi4">
                                            <image @click="concatImg(item.data_media.medias, 2)"
                                                :src="item.data_media.medias[2].url" mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.data_media.medias, 3)"
                                                :src="item.data_media.medias[3].url" mode="aspectFill">
                                            </image>
                                            <image @click="concatImg(item.data_media.medias, 4)"
                                                :src="item.data_media.medias[4].url" mode="aspectFill">
                                            </image>
                                            <view @click="concatImg(item.data_media.medias, 4)" class="flex-mode cover"
                                                v-if="item.data_media.medias.length > 5">+{{
                                                    item.data_media.medias.length - 5 }}</view>
                                        </view>
                                    </view>
                                </template>
                                <!-- 图片展示区域 结束 -->
                                <!-- 视频展示区域 开始 -->
                                <template v-else-if="item.type_class == 2">
                                    <!-- 视频 -->
                                    <view class="flex-mode single-box__content__img1">
                                        <player :url="item.data_media.medias[0].url" :id="index"></player>
                                    </view>
                                </template>
                                <!-- 视频展示区域 结束 -->
                            </view>
                            <!-- 底部统计区域 -->
                            <view class="flex-mode single-box__bottom">
                                <view class="single-box__bottom__time"
                                    :style="item.user_id == userId ? 'margin-right: 16rpx' : ''">{{
                                        item.time }}</view>
                                <view class="single-box__bottom__del" v-if="item.user_id == userId"
                                    @click="delMy(item)">删除
                                </view>
                                <view class="single-box__bottom__icon" @click="changePop(item, index)">
                                    <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/comment.svg"></image>
                                </view>
                                <view class="single-box__bottom__count">{{ item.comment_count > 999 ? '999+' :
                                    item.comment_count }}</view>
                                <view class="single-box__bottom__icon" style="margin-left: 0;"
                                    @click="changeLike(item, index)">
                                    <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/like.svg"
                                        v-if="item.is_zan != 1"></image>
                                    <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/like1.svg" v-else></image>
                                </view>
                                <view class="single-box__bottom__count">{{ item.zan_count > 999 ? '999+' :
                                    item.zan_count }}
                                </view>
                            </view>
                            <view class="single-box__divider"></view>
                        </view>
                    </template>
                    <u-loadmore :status="loadStatus" v-if="!isBlank" />
                </scroll-view>
            </swiper-item>
        </swiper>
        <!-- 评论区弹窗 -->
        <u-popup v-model="showPop" mode="bottom" height="90%" z-index="9999" closeable close-icon-pos="top-left"
            close-icon-color="#262937" close-icon-size="24" :mask="false" :custom-style="popStyle" negative-top="100"
            @close="popClose">
            <view class="pop-title">评论区</view>
            <view class="comment-content">
                <!-- 缺省页 -->
                <view style="height: 100%" v-if="commentList.length < 1">
                    <u-empty text="暂无评论，说点什么吧" mode="list" :src="nodataUrl2"></u-empty>
                </view>
                <scroll-view :scroll-top="scrollTop" scroll-y="true" style="height: 100%;" v-else>
                    <view :id="'scroll' + index" v-for="(item, index) in commentList" :key="index"
                        :scroll-with-animation="true" :scroll-into-view="scrollToView">
                        <view class="comment-single flex-mode" @click="showInput(item)">
                            <image :src="item.img" v-if="item.img"></image>
                            <view class="comment-single__avatar flex-mode" v-else>{{
                                item.account_name.charAt(0) }}</view>
                            <view class="comment-single__right">
                                <view class="comment-single__right__name">{{ item.account_name }}</view>
                                <view class="comment-single__right__content">{{ item.content }}</view>
                                <view class="comment-single__right__date">{{ item.created_at }}<text
                                        v-if="item.isMy == 1" @click="delSingle(item.id)">删除</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view id='scrollSignal' style='height:1px;width:100%'></view>
                </scroll-view>
                <view class="input-box flex-mode">
                    <input class="input-box__inner" v-model="inputValue" :placeholder="placeholder"
                        placeholder-style="input-box__placeholder" :focus="inputFocus" :always-embed="true"
                        :cursor-spacing="100" />
                    <view class="input-box__send" @click="sendComment">发送</view>
                </view>
            </view>
        </u-popup>
        <!-- 删除弹窗 -->
        <u-modal v-model="delShow" :content="delContent" show-cancel-button @confirm="confirmDel"></u-modal>
    </view>
    <view class="add-btn" @click="addCircle">
        <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/add.svg"></image>
    </view>
    <tabbar :currentTab="1" :border-top="false">
    </tabbar>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs";
import skeleton from "@/pages_work/components/skeleton/notice.vue";

import tabbar from "@/components/tabbar.vue";
import player from "./components/player.vue";
import request from "@/request";
import { onReachBottom, onShow, onLoad, onUnload, onHide } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
// 变量
const statusBarHeight = ref(0)
const isBlank = ref(true)
const isGrowBlank = ref(true)
const loadStatus = ref('loadmore')
const pageCircle = ref(1)
const pageGrow = ref(1)
const perPage = ref(10)
const circleDetail = ref([])
const circleList = ref([])
const growList = ref([])
const nodataUrl = ref('https://obs.tuoyupt.com/nanjing/pstac/notice/nodata1.svg')
const wenwenList = ref([])
const inputStyle = {
    width: "100%"
}
const showList = ref({
    opacity: 0
})
const showCom = ref(true)
const haveMore = ref(true)
const triggered = ref(false);

const userId = ref('')
const scrollTop = ref(0)
const scrollTopGrow = ref(-1)
const scrollTopCircle = ref(-1)
const scrollToView = ref('')
const curId = ref('')
const studentId = ref('')
const limFont = 75
const showPop = ref(false)
const popStyle = ref({ paddingTop: '30rpx' })
const placeholder = ref('参与讨论')
const delShow = ref(false)
const delContent = ref('确认要删除当前评论吗？')
const curImgList = ref([])
const commentList = ref([])
const curCommentId = ref('')
const inputFocus = ref(false)
const inputValue = ref('')
const nodataUrl2 = ref('https://obs.tuoyupt.com/nanjing/pstac/notice/nodata2.svg')
const curCommentIndex = ref(0)
const canLoadMore = ref(true)
const studentInfo = ref('')

// 方法
const toWenwen = () => {
    uni.navigateTo({
        url: "/pages_work/wenwen/index"
    })
}
const toggleDescription1 = (index) => {
    if (growList.value[index].isShowAllContent) {
        growList.value[index].isShowAllContent = false
        growList.value[index].tempContent = growList.value[index].title
    } else {
        growList.value[index].isShowAllContent = true
        growList.value[index].tempContent = growList.value[index].title.substring(0, limFont) + "..."
    }
}
// listSingle方法
const toggleDescription = (index) => {
    if (circleList.value[index].isShowAllContent) {
        circleList.value[index].isShowAllContent = false
        circleList.value[index].tempContent = circleList.value[index].title
    } else {
        circleList.value[index].isShowAllContent = true
        circleList.value[index].tempContent = circleList.value[index].title.substring(0, limFont) + "..."
    }
}
// 显示评论列表弹出
const changePop = (item, index) => {
    curCommentIndex.value = index
    curId.value = item.id
    commentList.value = item.data_comment
    showPop.value = true
}
// 删除评论弹窗
const delSingle = (id) => {
    delShow.value = true
    curCommentId.value = id
}

// 跳转新建页面
const addCircle = () => {
    uni.navigateTo({
        url: "/pages_work/notice/push"
    })
}

// 删除评论
const confirmDel = async () => {
    await request({
        method: "POST",
        url: `/api/parent/circle/delComment/${curCommentId.value}`,
    }).then((result) => {
        // console.log(result);
        let arr = commentList.value
        for (let i = 0; i < arr.length; i++) {
            if (curCommentId.value == arr[i].id) {
                arr.splice(i, 1)
                break
            }
        }
        commentList.value = arr
        circleList.value[curCommentIndex.value].comment_count = circleList.value[curCommentIndex.value].comment_count - 1
        // commentCount.value = commentCount.value - 1
    });
}

// 修改点赞状态
const changeLike = async (item, index) => {
    console.log(circleList.value[index].zan_count, index)
    if (item.is_zan == 1) {
        circleList.value[index].zan_count = circleList.value[index].zan_count - 1
        circleList.value[index].is_zan = 0
        // zanCount.value = zanCount.value - 1
        // isZan.value = 0
        await request({
            method: 'POST',
            url: `/api/parent/circle/zan/${item.id}`
        }).then(res => {
            // console.log(res)
        })
    } else {
        circleList.value[index].zan_count = circleList.value[index].zan_count + 1
        circleList.value[index].is_zan = 1
        // zanCount.value = zanCount.value + 1
        // isZan.value = 1
        await request({
            method: 'POST',
            url: `/api/parent/circle/zan/${item.id}`
        }).then(res => {
            // console.log(res)
        })
    }
}

// 图片拼接展示
const concatImg = (list, index) => {
    // console.log(list)
    let imgList = []
    for (let i = 0; i < list.length; i++) {
        imgList.push(list[i].url)
    }
    curImgList.value = imgList
    // console.log(curImgList.value)
    uni.previewImage({
        current: index,
        urls: curImgList.value,
        indicator: "number",
        longPressActions: {
            itemList: ['发送给朋友', '保存图片', '收藏'],
            success: function (data) {
                // console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
            },
            fail: function (err) {
                // console.log(err.errMsg);
            }
        }
    });
}

// 点击评论弹出评论框
const showInput = (item) => {
    // console.log(item)
    inputFocus.value = true
}
// 发送评论
const sendComment = throttle(async () => {
    let params = {
        content: inputValue.value
    }
    uni.hideKeyboard()
    uni.showLoading({
        mask: true
    })
    await request({
        method: "POST",
        url: `/api/parent/circle/comment/${curId.value}`,
        data: params
    }).then((result) => {
        uni.hideLoading();
        let arr = commentList.value
        let resComment = result.comment_object
        resComment.isMy = 1
        arr.push(resComment)
        commentList.value = arr
        circleList.value[curCommentIndex.value].comment_count = circleList.value[curCommentIndex.value].comment_count + 1
        scrollToView.value = 'scroll' + commentList.length - 1
        inputValue.value = ''
    });
}, 1000)
// 关闭弹窗
const popClose = () => {
    // emit('sendrefresh')
    // emit('changeshow')
}
const delCirle = async (item) => {
    await request({
        method: "POST",
        url: `/api/parent/circle/del/${item.id}`,
    }).then((result) => {
        uni.showToast({
            title: '删除成功',
            icon: 'none',
            duration: 500
        });
        setTimeout(() => {
            getList(1)
        }, 500)
    });
}
const delGrow = async (item) => {
    await request({
        method: "POST",
        url: `/api/parent/time/del/${item.id}`,
    }).then((result) => {
        uni.showToast({
            title: '删除成功',
            icon: 'none',
            duration: 500
        });
        setTimeout(() => {
            getGrow(1)
        }, 500)
    });
}
//删除本人动态
const delMy = (item) => {
    uni.showModal({
        title: '删除该动态？',
        confirmText: '删除',
        cancelText: '取消',
        success: function (res) {
            if (res.confirm) {
                pageCircle.value = 1
                delCirle(item)
            } else if (res.cancel) {
                console.log('用户点击取消')
            }
        },
    })
}

const delGrowth = (item) => {
    uni.showModal({
        title: '删除该成长时光？',
        confirmText: '删除',
        cancelText: '取消',
        success: function (res) {
            if (res.confirm) {
                pageGrow.value = 1
                delGrow(item)
            } else if (res.cancel) {
                console.log('用户点击取消')
            }
        },
    })
}


// 方法
const refresh = async () => {
    let user_info = uni.getStorageSync('user_info')
    let class_list = uni.getStorageSync('class_list')
    if (user_info.teacher_type == 3 && class_list.length == 0) {
        triggered.value = false;
    } else {
        triggered.value = true;
        if (userInfo.circle_type == 0) {
            pageGrow.value = 1
            await getGrow(1)
        } else {
            pageCircle.value = 1
            await getList(1)
        }
        triggered.value = false;
    }
}
const changeTitle = async (index) => {
    userInfo.circle_type = index
    if (userInfo.circle_type == 0) {
        pageGrow.value = 1
        await getGrow(1)
    } else {
        pageCircle.value = 1
        await getList(1)
    }
}
watch(() => {
    return userInfo.student_id
}, () => {
    getGrow(1)
})
// 获取班级动态
const getList = async (type) => {
    // type :1 更新tab  2 触底加载
    let arr = circleList.value || []
    let params = {
        per_page: perPage.value,
        page: pageCircle.value,
        system: 'weChat',
        student_id: userInfo.student_id
    }
    await request({
        method: "GET",
        url: `/api/parent/circle/list`,
        data: params
    }).then((res) => {
        // console.log('circlelist',res);
        if (res.list.length > 3) {
            haveMore.value = true
            isBlank.value = false
            loadStatus.value = 'loadmore'
        } else if (res.list.length == 0 && pageGrow.value == 1) {
            isBlank.value = true
        } else {
            isBlank.value = false
            haveMore.value = false
            loadStatus.value = 'nomore'
        }
        showList.value = {
            opacity: 1,
        }
        circleDetail.value = res
        for (let j = 0; j < res.list.length; j++) {
            if (res.list[j].title.length > limFont) {
                res.list[j].isShowAllContent = true
                res.list[j].tempContent = res.list[j].title.substring(0, limFont) + "..."
            } else {
                res.list[j].isShowAllContent = false
                res.list[j].tempContent = res.list[j].title
            }
        }
        if (type === 1) {
            circleList.value = res.list
        } else {
            for (let i = 0; i < res.list.length; i++) {
                arr.push(res.list[i])
            }
            circleList.value = arr
        }
        showCom.value = false;
        pageCircle.value = pageCircle.value + 1
        canLoadMore.value = true;
    });
}
// 获取成长时光
const getGrow = async (type) => {
    // type :1 更新tab  2 触底加载
    let arr = growList.value || []
    let params = {
        per_page: perPage.value,
        page: pageGrow.value,
        system: 'weChat',
        school_id: userInfo.school_id,
        student_id: userInfo.student_id
    }
    await request({
        method: "GET",
        url: `/api/parent/time/list`,
        data: params
    }).then((res) => {
        showCom.value = false;
        if (res.length > 3) {
            haveMore.value = true
            isGrowBlank.value = false
            loadStatus.value = 'loadmore'
        } else if (res.length == 0 && pageGrow.value == 1) {
            isGrowBlank.value = true
        } else {
            isGrowBlank.value = false
            haveMore.value = false
            loadStatus.value = 'nomore'
        }
        showList.value = {
            opacity: 1,
        }
        for (let j = 0; j < res.length; j++) {
            let date = dayjs(res[j].created_at)
            res[j].time1 = date.format("YYYY年MM月");
            res[j].time2 = date.format("DD");
            res[j].time3 = date.format("HH:mm");
            if (res[j].title.length > limFont) {
                res[j].isShowAllContent = true
                res[j].tempContent = res[j].title.substring(0, limFont) + "..."
            } else {
                res[j].isShowAllContent = false
                res[j].tempContent = res[j].title
            }
            // 入园处理
            if (res[j].type == 9) {
                let str = res[j].title
                let arr = str.split('##')
                res[j].title1 = arr[0]
                res[j].title2 = arr[1]
            }
        }
        // 处理文字
        // txtContent.value = props.curDetail.title
        // let txtCntIndex = txtContent.value.length
        // if (txtCntIndex > limFont) {
        //     isShowAllContent.value = true
        //     tempContent.value = txtContent.value.substring(0, limFont) + "..."
        // } else {
        //     isShowAllContent.value = false
        //     tempContent.value = txtContent.value
        // }
        if (type === 1 || '') {
            growList.value = res
        } else {
            for (let i = 0; i < res.length; i++) {
                arr.push(res[i])
            }
            growList.value = arr
        }
        pageGrow.value = pageGrow.value + 1
        canLoadMore.value = true
    });
}

const getAi = async () => {
    let params = {
        student_id: userInfo.student_id
    }
    await request({
        method: "GET",
        url: `/api/parent/moblab/list`,
        data: params
    }).then((res) => {
        wenwenList.value = res.hot_question
    })
}
// 跳转体检
const toExam = () => {
    uni.navigateTo({
        url: `/pages/index/examination`
    })
}
// 切换结束
const animationfinish = (e) => {
    let current = e.detail.current;
    userInfo.circle_type = current

    if (circleList.value.length == 0) {
        if (current == 0) {
            pageGrow.value = 1
            getGrow(1)
        } else {
            pageCircle.value = 1
            getList(1)
        }
    }
    // this.$refs.uTabs.setFinishCurrent(current);
    // this.swiperCurrent = current;
    // this.current = current;
}
const scrollGrow = (e) => {
    scrollTopGrow.value = -1
}
const scrollCircle = (e) => {
    scrollTopCircle.value = -1
}
// 返回顶部
const toTop = () => {
    if (userInfo.circle_type == 0) {
        scrollTopGrow.value = 0
    } else {
        scrollTopCircle.value = 0
    }
}
onMounted(() => {
    uni.$on('refresh', () => {
        if (userInfo.circle_type == 0) {
            pageGrow.value = 1
            scrollTopGrow.value = 0
            getGrow(1)
        } else {
            pageCircle.value = 1
            scrollTopCircle.value = 0
            getList(1)
        }
    })
})

onLoad(() => {
    statusBarHeight.value = uni.getSystemInfoSync().statusBarHeight;
    let user_info = uni.getStorageSync('user_info')
    let cur_child = uni.getStorageSync('cur_child')
    studentId.value = cur_child.id
    getAi()
    // let class_list = uni.getStorageSync('class_list')
    // let current_class = uni.getStorageSync('cur_class')
    // // 机构教师类型0其他，1行政，2教师，3员工 4超级管理员
    // let user_info = uni.getStorageSync('user_info')
    // if(user_info.teacher_type == 4) {
    //     let school_id = current_class.id
    //     getClass(school_id)
    // } else {
    //     class_list.unshift({
    //         id: 0,
    //         name: '全部'
    //     })
    //     classList.value = class_list
    // }
})
onShow(() => {
    userId.value = uni.getStorageSync('user_info').id
    studentInfo.value = uni.getStorageSync('cur_child')
    // showCom.value = true
    if (circleList.value.length == 0) {
        if (userInfo.circle_type == 0) {
            pageGrow.value = 1
            getGrow(1)
        } else {
            pageCircle.value = 1
            getList(1)
        }
    }
})
const loadMore = () => {
    if (canLoadMore.value === true) {
        canLoadMore.value = false
        if (haveMore.value) {
            loadStatus.value = 'loading'
            if (userInfo.circle_type == 0) {
                getGrow(2)
            } else {
                getList(2)
            }
        } else {
            loadStatus.value = 'nomore'
        }
    } else {
        console.log('throttle')
    }
}
// 节流
function throttle(fn, delay = 300) {
    let timer = null
    return function (...args) {
        if (timer == null) {
            timer = setTimeout(() => {
                fn.call(this, ...args)

                clearTimeout(timer)
                timer = null
            }, delay);
        }
    }
}
onHide(() => {
    // showCom.value = false
})
onUnload(() => {
    uni.$off('refresh');
})
onReachBottom(() => {
    if (haveMore.value) {
        loadStatus.value = 'loading'
        if (userInfo.circle_type == 0) {
            getGrow(2)
        } else {
            getList(2)
        }
    } else {
        loadStatus.value = 'nomore'
    }
    // if(this.page >= 3) return ;
    // this.status = 'loading';
    // this.page = ++ this.page;
    // setTimeout(() => {
    //     this.list += 10;
    //     if(this.page >= 3) this.status = 'nomore';
    //     else this.status = 'loading';
    // }, 2000)
})


</script>


<style lang="scss" scoped>
.flex-mode {
    display: flex;
    justify-content: center;
    align-items: center;
}

.add-btn {
    z-index: 999;
    position: fixed;
    bottom: 200rpx;
    right: 40rpx;

    image {
        width: 112rpx;
        height: 112rpx;
    }
}

.opacity_box {
    transition: all 1s;
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    touch-action: none;

    .scroll-box {

        flex: 1;
        position: relative;
        z-index: 2;
        min-height: 1rpx;
        padding-bottom: 180rpx;

        .scroll {
            width: 100vw;
            height: 100%;
        }
    }

    .top-box {
        height: 180rpx;
        // height: 550rpx; 不隐藏ai
        flex-direction: column;

        .top-box__backimg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 550rpx;
            z-index: -1;
        }

        &__ai {
            position: relative;
            margin: 20rpx auto 40rpx;
            width: 690rpx;
            height: 314rpx;
            background: #FFFFFF;
            box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.04);
            border-radius: 0rpx 40rpx 40rpx 40rpx;

            &__top {
                margin: 60rpx 0 8rpx 42rpx;
                font-size: 40rpx;
                font-weight: 600;
                color: #111111;
                line-height: 56rpx;
            }

            &__img {
                position: absolute;
                right: 0;
                top: 0rpx;
                width: 204rpx;
                height: 204rpx;
            }

            &__mid {
                margin-left: 42rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #787C8D;
                line-height: 40rpx;
            }

            &__bottom {
                margin: 40rpx 24rpx;
                height: 72rpx;
                background: #EBF6FF;
                border-radius: 36rpx;

                &__img {
                    background: #FF7373;
                    margin-left: 12rpx;
                    width: 48rpx;
                    height: 48rpx;
                    border-radius: 50%;
                }

                &__name {
                    margin-left: 16rpx;
                    font-size: 30rpx;
                    font-weight: 500;
                    color: #262937;
                    line-height: 42rpx;
                }

                &__content {
                    margin-right: auto;
                    font-size: 30rpx;
                    font-weight: 400;
                    color: #262937;
                    line-height: 42rpx;
                }
            }
        }
    }
}

.main {
    transition: all .5s;
    margin-top: 50%
}



.single-box {
    // opacity: 0;
    transition: all .5s;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 40rpx 30rpx 0 30rpx;
    box-sizing: border-box;
    background-color: #FFFFFF;


    &__title {
        width: 100%;

        // 成长时光开始
        &__timebox {
            margin-right: auto;
            flex-direction: column;

            &__1 {
                margin-right: auto;
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 24rpx;
            }

            &__inner {
                margin-top: 14rpx;

                &__left {
                    margin-right: 16rpx;
                    font-size: 48rpx;
                    color: #262937;
                    line-height: 40rpx;
                }

                &__right {
                    margin-right: auto;
                    width: 214rpx;
                    height: 40rpx;
                    background: linear-gradient(90deg, #FFEBE3 0%, rgba(255, 235, 227, 0) 100%);
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #262937;
                    line-height: 40rpx;
                }
            }
        }

        // 成长时光结束

        &__avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 24rpx;
            background: #FF7373;
            color: #FFFFFF;
        }

        &__work {
            width: 140rpx;
            height: 64rpx;
        }

        &__text {
            flex-direction: column;
            margin-right: auto;

            &__1 {
                margin-right: auto;
                font-size: 28rpx;
                font-weight: 500;
                color: #111111;
                line-height: 28rpx;
            }

            &__2 {
                margin-right: auto;
                margin-top: 8rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 24rpx;
            }
        }
    }

    &__content {
        position: relative;
        padding-left: 68rpx;
        width: 100%;

        //新增 开始
        &__timeline {
            position: absolute;
            top: 14rpx;
            left: 25rpx;
            height: 100%;
            width: 2rpx;
            border: 2rpx dashed #EEEEEE;

            &__dot {
                position: absolute;
                width: 6rpx;
                height: 6rpx;
                background: #111111;
            }

            &__begin {
                top: 0;
                left: -2rpx;
            }

            &__end {
                bottom: 0;
                left: -2rpx;
            }
        }

        &__bottom {
            width: 620rpx;
            position: absolute;
            bottom: -25rpx;

            &__time {
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 34rpx;
            }

            &__time1 {
                margin-left: auto;
            }
        }

        &__default {
            margin-top: 40rpx;
            position: relative;
            padding: 40rpx;
            flex-direction: column;
            width: 556rpx;
            height: 512rpx;
            background: #F6F6F6;
            border-radius: 24rpx;

            &__top {
                top: 0;
                left: 0;
                position: absolute;
                width: 556rpx;
                height: 254rpx;
                background: #617EFB;
                border-radius: 24rpx 24rpx 0rpx 0rpx;

                image {
                    width: 100%;
                    height: 100%;
                    border-radius: 24rpx 24rpx 0rpx 0rpx;
                }
            }

            &__title {
                margin: 230rpx auto 0 0;
                margin-right: auto;
                font-size: 32rpx;
                font-weight: 500;
                color: #262937;
                line-height: 48rpx;
            }

            &__content {
                margin-top: 22rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #787C8D;
                line-height: 44rpx;
            }
        }

        //新增 结束

        &__article {
            margin-top: 16rpx;
            font-size: 32rpx;
            font-weight: 400;
            color: #262937;
            line-height: 48rpx;
        }

        &__show {
            font-size: 32rpx;
            font-weight: 400;
            color: #617EFB;
            line-height: 48rpx;
        }

        &__img {
            margin-top: 16rpx;
            width: 556rpx;
            height: 180rpx;
            background: #FF7373;
            border-radius: 24rpx;
            position: relative;

            &__title {
                top: 20rpx;
                left: 40rpx;
                position: absolute;
                font-size: 32rpx;
                font-weight: 600;
                color: #FFFFFF;
                line-height: 44rpx;
                letter-spacing: 1px;
            }

            &__time {
                bottom: 20rpx;
                left: 40rpx;
                position: absolute;
                font-size: 20rpx;
                font-weight: 500;
                color: #FFFFFF;
                line-height: 28rpx;
            }

            image {
                width: 556rpx;
                height: 180rpx;
                border-radius: 24rpx;
                // background-color: #00C777;
            }
        }

        &__img1 {
            margin-top: 16rpx;
            width: 556rpx;
            height: 368rpx;
            border-radius: 24rpx;

            image {
                width: 556rpx;
                height: 368rpx;
                border-radius: 24rpx;
                // background-color: #00C777;
            }

            video {
                width: 556rpx;
                height: 368rpx;
                border-radius: 24rpx;
            }
        }

        &__img2 {
            margin-top: 16rpx;
            width: 556rpx;
            height: 274rpx;
            justify-content: space-between;
            border-radius: 24rpx;

            image {
                width: 274rpx;
                height: 274rpx;
                // background-color: #617EFB;
            }

            image:nth-child(1) {
                border-top-left-radius: 24rpx;
                border-bottom-left-radius: 24rpx;
            }

            image:nth-child(2) {
                border-top-right-radius: 24rpx;
                border-bottom-right-radius: 24rpx;
            }
        }

        &__img3 {
            margin-top: 16rpx;
            width: 556rpx;
            height: 368rpx;
            justify-content: space-between;
            border-radius: 24rpx;

            image {
                // background-color: #AEB0BC;
            }

            .single {
                width: 368rpx;
                height: 368rpx;
                border-top-left-radius: 24rpx;
                border-bottom-left-radius: 24rpx;
            }

            .multi {
                flex-direction: column;
                justify-content: space-between;
                width: 180rpx;
                height: 100%;

                image:nth-child(1) {
                    width: 180rpx;
                    height: 180rpx;
                    border-top-right-radius: 24rpx;
                }

                image:nth-child(2) {
                    width: 180rpx;
                    height: 180rpx;
                    border-bottom-right-radius: 24rpx;
                }
            }
        }

        &__img4 {
            margin-top: 16rpx;
            width: 556rpx;
            height: 556rpx;
            justify-content: space-between;
            flex-direction: column;
            border-radius: 24rpx;

            .multi1 {
                flex-direction: row;
                justify-content: space-between;
                width: 556rpx;
                height: 274rpx;

                image {
                    // background-color: #AEB0BC;
                }

                image:nth-child(1) {
                    width: 274rpx;
                    height: 274rpx;
                    border-top-left-radius: 24rpx;
                }

                image:nth-child(2) {
                    width: 274rpx;
                    height: 274rpx;
                    border-top-right-radius: 24rpx;
                }
            }

            .multi2 {
                flex-direction: row;
                justify-content: space-between;
                width: 556rpx;
                height: 274rpx;

                image:nth-child(1) {
                    width: 274rpx;
                    height: 274rpx;
                    border-bottom-left-radius: 24rpx;
                }

                image:nth-child(2) {
                    width: 274rpx;
                    height: 274rpx;
                    border-bottom-right-radius: 24rpx;
                }
            }
        }

        &__img5 {
            margin-top: 16rpx;
            width: 556rpx;
            height: 462rpx;
            justify-content: space-between;
            flex-direction: column;
            border-radius: 24rpx;

            image {
                // background-color: #AEB0BC;
            }

            .multi3 {
                flex-direction: row;
                justify-content: space-between;
                width: 556rpx;
                height: 274rpx;

                image:nth-child(1) {
                    width: 274rpx;
                    height: 274rpx;
                    border-top-left-radius: 24rpx;
                }

                image:nth-child(2) {
                    width: 274rpx;
                    height: 274rpx;
                    border-top-right-radius: 24rpx;
                }
            }

            .multi4 {
                position: relative;
                flex-direction: row;
                justify-content: space-between;
                width: 556rpx;
                height: 180rpx;

                image:nth-child(1) {
                    width: 180rpx;
                    height: 180rpx;
                    border-bottom-left-radius: 24rpx;
                }

                image:nth-child(2) {
                    width: 180rpx;
                    height: 180rpx;
                }

                :nth-child(3) {
                    position: relative;
                    width: 180rpx;
                    height: 180rpx;
                    border-bottom-right-radius: 24rpx;
                }

                .cover {
                    right: 0;
                    bottom: 0;
                    position: absolute;
                    width: 180rpx;
                    height: 180rpx;
                    border-bottom-right-radius: 24rpx;
                    font-size: 48rpx;
                    font-weight: 500;
                    color: #FFFFFF;
                    line-height: 66rpx;
                    background: rgba(0, 0, 0, 0.5);
                }
            }
        }
    }

    &__bottom {
        margin-top: 32rpx;
        width: 100%;
        flex-direction: row;
        padding-left: 68rpx;

        &__time {
            margin-right: auto;
            font-size: 24rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 34rpx;
        }

        &__del {
            margin-right: auto;
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
            line-height: 34rpx;
        }

        &__icon {
            margin-left: 68rpx;

            image {
                width: 48rpx;
                height: 48rpx;
            }
        }

        &__count {
            width: 60rpx;
            margin-left: 14rpx;
            font-size: 24rpx;
            font-weight: 500;
            color: #AEB0BC;
            line-height: 34rpx;
        }
    }

    &__divider {
        width: 100%;
        height: 2rpx;
        color: #F6F6F6;
        margin: 32rpx 0 0 0;
    }
}



.pop-title {
    position: absolute;
    top: 30rpx;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #262937;
    line-height: 44rpx;
}

.comment-content {
    height: 100%;
    padding: 100rpx 30rpx 130rpx;
    text-align: center;

    .input-box {
        width: 100%;
        box-sizing: border-box;
        padding: 18rpx 30rpx;
        position: fixed;
        left: 0;
        bottom: 30rpx;

        &__inner {
            flex: 1;
            text-align: left;
            width: 100%;
            padding: 18rpx 30rpx;
            background: #F6F6F6;
            border-radius: 32rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #262937;
            line-height: 28rpx;
        }

        &__placeholder {
            font-size: 28rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 28rpx;
        }

        &__send {
            margin-left: 24rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
        }
    }

    .comment-single {
        padding-top: 40rpx;

        image {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-bottom: auto;
        }

        &__avatar {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-bottom: auto;
            background: #FF7373;
            color: #FFFFFF;
        }

        &__right {
            flex: 1;
            margin-left: 24rpx;
            width: 100%;
            padding-bottom: 24rpx;
            border-bottom: 2rpx solid #EEEEEE;
            text-align: left;

            &__name {
                font-size: 28rpx;
                font-weight: 500;
                color: #111111;
                line-height: 28rpx;
            }

            &__content {
                margin-top: 24rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #262937;
                line-height: 28rpx;
            }

            &__date {
                margin-top: 20rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 34rpx;

                text {
                    margin-left: 16rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #787C8D;
                    line-height: 34rpx;
                }
            }
        }
    }
}

.slot-wrap {
    position: relative;
    height: 44px;
    margin-right: auto;
    margin-left: 30rpx;
    display: flex;
    align-items: center;
    flex: 1;
    font-size: 40rpx;
    font-weight: 500;
    color: #000000;
    line-height: 48rpx;
    letter-spacing: 1px;

    &__right {
        position: absolute;
        right: -100%;
        height: 44px;
        width: 100%;
    }

    .title {
        transition: all .5s;
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
    }

    .title2 {
        margin-left: 32rpx;
    }

    .title_sel {
        transition: all .5s;
        font-size: 40rpx;
        font-weight: 600;
        color: #000000;
    }
}
</style>