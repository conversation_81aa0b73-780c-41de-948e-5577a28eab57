<template>
    <u-navbar :background="navBackground" :border-bottom="false" :custom-back="back"></u-navbar>
    <view class="push-box">
        <u-field v-model="curCont" label-width="0" type="textarea" placeholder="随手记录宝宝点滴成长时光吧…" maxlength="-1"
            :border-bottom="false" :placeholder-style="textStyle" :clearable='false' :field-style="fieldStyle"
            :trim="true">
        </u-field>
        <!-- <u-upload :max-size="10 * 1024 * 1024" max-count="15" upload-text="" del-bg-color="#0C1418"></u-upload> -->

        <view class="upload-box u-flex u-flex-wrap">
            <!-- 准备上传的图片列表 -->
            <view class="pre-item u-rela" v-for="(item, index) in imageList" :key="item"
                :style="{ marginRight: (index + 1) % 4 == 0 ? '0' : '16rpx' }">
                <image ref="uUpload" class="pre-item-image" :src="item" mode="aspectFill" @tap="previewImage(item)">
                </image>
                <view class="u-delete-icon u-abso u-flex u-row-center" @tap="delect(index)">
                    <u-icon name="close" size="12" color="#ffffff"></u-icon>
                </view>
                <!-- <u-line-progress v-if="item.progress > 0 && !item.error && item.progress != 100" :show-percent="false"
                    height="16" class="u-progress" :percent="item.progress">
                </u-line-progress> -->
            </view>
            <!-- 准备上传的视频列表 -->
            <view class="pre-item1 u-rela" v-for="(item, index) in srcVideo" :key="item.tempFilePath">
                <video ref="uUpload" :src="item.tempFilePath" :style="videoStyle" object-fit="cover"
                    :show-progress="false"></video>
                <view class="u-delete-icon1 u-abso u-flex u-row-center" @tap="delectVideo()">
                    <u-icon name="close" size="12" color="#ffffff"></u-icon>
                </view>
                <!-- <u-line-progress :show-percent="false" height="16" class="u-progress" :percent="item.progress">
                </u-line-progress> -->
            </view>
            <view class="pre-item u-flex u-row-center" @tap="chooseVideoImage"
                v-if="(imageList.length < 15 && form.type_class == 1) || (srcVideo.length == 0 && form.type_class == 2)">
                <u-icon name="plus" size="30" color="#787C8D"></u-icon>
            </view>
        </view>
        <view class="push-box__divider" style="margin-top: 40rpx;"></view>
        <view class="flex-mode push-box__single" @click="showSync" v-if="!disSecret">
            <!-- <view class="push-box__single__left">同步成长时光</view> -->
            <view class="push-box__single__left">同步到班级动态</view>
            <u-switch v-model="isSecret" active-color="#000000" inactive-color="#AEB0BC"></u-switch>
        </view>
        <view class="push-box__divider"></view>
        <view class="flex-mode push-box__single">
            <view class="push-box__single__left">标记为作品</view>
            <u-switch v-model="isWork" active-color="#000000" inactive-color="#AEB0BC"></u-switch>
        </view>
    </view>
    <u-toast ref="uToast" />
    <view class="bottom-box">
        <u-button type="primary" :custom-style="sendButton" @click="send" throttle-time="1000">发布</u-button>
    </view>
</template>

<script setup>
import {
    ref,
    reactive,
    computed,
    getCurrentInstance
} from "vue";
import request from "@/request";
import {
    onReady
} from '@dcloudio/uni-app';
import videoUpload from '@/lib/videosdk/index.js';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const disSecret = computed(() => {
    let current = userInfo.student_arr.find(it => it.id == userInfo.student_id);
    if (current) {
        if (current.status == 1) {
            return false;
        } else {
            return true;
        }
    } else {
        return true;
    }
})
/**
 *  数据
 */
const {
    proxy
} = getCurrentInstance();
const navBackground = {
    backgroundColor: "#ffffff"
}
const sendButton = {
    width: "690rpx",
    height: "80rpx",
    backgroundColor: '#FF7373',
}
const textStyle = {
    fontSize: '32rpx',
    fontWeight: '400',
    color: '#AEB0BC',
    lineHeight: '48rpx'
}
const fieldStyle = {
    minHeight: '288rpx',
    fontSize: '32rpx',
    color: '#262937',
    lineHeight: '48rpx'
}
const curCont = ref('')
const isWork = ref(false)
const isSecret = ref(false)
const showPop = ref(false)
const popType = ref(0)
const popTitle = ref('')
const class_id = ref('')
const student_ids = ref('')
const imageList = ref([])
const srcVideo = ref([])
const videoStyle = reactive({
    width: '100%',
    height: '300rpx',
    borderRadius: '15rpx'
})
const form = reactive({
    in_circle: 1,
    title: curCont.value,
    type: isWork ? 3 : 2,
    img_type: 0,
    student_ids: student_ids.value,
    img_url: '',
    class_id: class_id.value,
    type_class: 1,
})
const isSending = ref(false)

/**
 *  方法
 */
const send = () => {
    isSending.value = true
    sendCircle()
}
const sendCircle = async () => {
    // 图片
    if (form.type_class == 1) {
        if (curCont.value.length == 0 && imageList.value.length == 0) {
            proxy.$refs.uToast.show({
                title: '请输入正文或选择照片'
            })
        } else if (curCont.value.length != 0 && imageList.value.length == 0) {
            uni.showLoading({
                title: '正在发布中……'
            })
            form.type_class = 4
            let params = {
                in_circle: 1,
                title: curCont.value,
                type: isWork.value ? 3 : 2,
                img_type: 0,
                img_url: '',
                is_private: isSecret.value ? 0 : 1,
                type_class: form.type_class,
            }
            // console.log("上传", params)
            request({
                method: "POST",
                url: `/api/parent/circle/add`,
                data: params
            }).then((result) => {
                // console.log(result);
                uni.hideLoading()
                isSending.value = false
                uni.showToast({
                    title: "发布成功",
                    icon: "none",
                    mask: true,
                    duration: 1500
                })
                setTimeout(() => {
                    userInfo.circle_type = 0
                    uni.$emit('refresh', { refresh: true });
                    uni.redirectTo({
                        url: '/pages_work/notice/index'
                    })
                }, 1500);
            });
        } else {
            uni.showLoading({
                title: '正在发布中……'
            })
            let requestArr = []
            let imgs = []
            imageList.value.forEach(item => {
                let fileName = item.substring(item.lastIndexOf("/") + 1);
                let fileExtension = 'classcircle'
                requestArr.push(OBSupload(item, fileExtension, fileName))
            })
            imgs = await Promise.all(requestArr);
            let params = {
                in_circle: 1,
                title: curCont.value,
                type: isWork.value ? 3 : 2,
                img_type: 1,
                img_url: imgs,
                is_private: isSecret.value ? 0 : 1,
                type_class: form.type_class,
            }
            // console.log("上传", params)
            request({
                method: "POST",
                url: `/api/parent/circle/add`,
                data: params
            }).then((result) => {
                // console.log(result);
                isSending.value = false
                uni.hideLoading()
                uni.showToast({
                    title: "发布成功",
                    icon: "none",
                    mask: true,
                    duration: 1500
                })
                setTimeout(() => {
                    userInfo.circle_type = 0
                    uni.$emit('refresh', { refresh: true });
                    uni.redirectTo({
                        url: '/pages_work/notice/index'
                    })
                }, 1500);
            });
        }
    } else if (form.type_class == 2) {
        if (curCont.value.length == 0 && srcVideo.value.length == 0) {
            proxy.$refs.uToast.show({
                title: '请输入正文或选择视频'
            })
        } else {
            uni.showLoading({
                title: '正在发布中……'
            })
            let { tempFilePath, name } = srcVideo.value[0];
            let res = await videoUpload(tempFilePath, name)
            let params = {
                in_circle: 1,
                title: curCont.value,
                type: isWork.value ? 3 : 2,
                img_type: 2,
                img_url: res,
                is_private: isSecret.value ? 0 : 1,
                type_class: form.type_class,
            }
            // console.log("上传", params)
            request({
                method: "POST",
                url: `/api/parent/circle/add`,
                data: params
            }).then((result) => {
                // console.log(result);
                isSending.value = false
                uni.hideLoading()
                uni.showToast({
                    title: "发布成功",
                    icon: "none",
                    mask: true,
                    duration: 1500
                })
                setTimeout(() => {
                    userInfo.circle_type = 0
                    uni.$emit('refresh', { refresh: true });
                    uni.redirectTo({
                        url: '/pages_work/notice/index'
                    })
                }, 1500);
            });
        }
    }
}
const showSee = () => {
    showPop.value = true
    popTitle.value = '谁可以看'
    popType.value = 0
}
const showSync = () => {
    showPop.value = true
    popTitle.value = '同步成长时光'
    popType.value = 1
}

// 点击上传图片或视频
const chooseVideoImage = () => {
    uni.showActionSheet({
        title: '选择上传类型',
        itemList: ['图片', '视频'],
        success: res => {
            console.log(res);
            if (res.tapIndex == 0) {
                form.type_class = 1
                chooseImages(); //选择上传图片
            } else {
                if (imageList.value.length > 0) {
                    uni.showToast({
                        title: "已选择图片，无法选择视频",
                        icon: "none",
                        mask: true,
                        duration: 1500
                    })
                } else {
                    form.type_class = 2
                    chooseVideos(); //选择上传视频
                }
            }
        }
    });
}
// 上传图片
const chooseImages = () => {
    let imgList = imageList.value
    uni.chooseImage({
        count: 15,  //总限制5张
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], //从相册选择
        success: res => {
            console.log(res);
            if (imgList.length + res.tempFilePaths.length > 15) {
                proxy.$refs.uToast.show({
                    title: '图片数量已超过15张，请重新选择'
                })
            } else {
                res.tempFilePaths.forEach(element => {
                    imgList.push(element)
                })
                imageList.value = imgList
            }
            // let arr = res.tempFilePaths.map(async (item, index) => {
            // 	let imgs = [];
            // 	let requestArr = []
            // 	let fileName = res.tempFilePaths[index].substring(res.tempFilePaths[index].lastIndexOf("/") + 1);
            // 	let fileExtension = 'classcircle'
            // 	requestArr.push(OBSupload(res.tempFilePaths[index], fileExtension, fileName))
            // 	imgs = await Promise.all(requestArr)
            // 	return imgs
            // })
            // console.log('arr',arr)
        },
    })
}
// 上传视频
const chooseVideos = () => {
    uni.chooseVideo({
        count: 1,
        mediaType: ['video'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,  //时长限制10分钟
        success(res) {
            srcVideo.value = [res]
        },
        fail() {
            form.type_class = 1
        }
    })
}

// 预览图片
const previewImage = (e) => {
    // console.log('预览图片', e)
    var current = e;
    uni.previewImage({
        current: current,
        urls: imageList.value
    });
}
// 删除图片
const delect = (index) => {
    let imgList = imageList.value
    uni.showModal({
        title: '提示',
        content: '是否要删除该图片',
        success: res => {
            if (res.confirm) {
                imgList.splice(index, 1);
                imageList.value = imgList
                // console.log(imgList)
            }
        }
    });
}
// 预览视频
const previewVideo = (e) => {
    console.log(e)
    var current = e;
    uni.previewImage({
        current: current,
        urls: this.srcVideo
    });
}
// 删除视频
const delectVideo = (index) => {
    uni.showModal({
        title: '提示',
        content: '是否要删除该视频',
        success: res => {
            if (res.confirm) {
                srcVideo.value = []
                form.type_class = 1
            }
        }
    });
}
// 返回上级
const back = () => {
    console.log(isSending.value)
    if (isSending.value) {
        uni.hideLoading()
        uni.showModal({
            title: '放弃本次发布吗？',
            confirmText: '放弃',
            cancelText: '继续发布',
            success: function (res) {
                if (res.confirm) {
                    uni.navigateBack()
                } else if (res.cancel) {
                    uni.showLoading({
                        title: '正在发布中……'
                    })
                    console.log('用户点击取消')
                }
            },
        })
    } else {
        if (curCont.value.length != 0 || srcVideo.value.length != 0 || imageList.value.length != 0) {
            uni.showModal({
                title: '退出此次编辑？',
                confirmText: '退出',
                cancelText: '取消',
                success: function (res) {
                    if (res.confirm) {
                        uni.navigateBack()
                    } else if (res.cancel) {
                        console.log('用户点击取消')
                    }
                },
            })
        } else {
            uni.navigateBack()
        }
    }
}
/**
 *  生命周期
 */
onReady(() => {
    // getClass()
})
</script>

<style lang="scss" scoped>
.choose_content {
    font-size: 28rpx;
    color: #262937;
    line-height: 40rpx;
    margin-bottom: 32rpx;
}

.flex-mode {
    display: flex;
    justify-content: center;
    align-items: center;
}

.push-box :deep(.u-field) {
    padding: 0;
    margin: 0;
}

.push-box {
    padding: 40rpx 30rpx 180rpx;
    width: 100%;
    height: 100%;

    .upload-box {
        display: flex;

        .pre-item {
            margin-top: 16rpx;
            width: 156rpx;
            height: 156rpx;
            background-color: #F6F6F6;

            &-image {
                width: 156rpx;
                height: 156rpx;
            }

            .u-delete-icon {
                top: 12rpx;
                right: 12rpx;
                width: 24rpx;
                height: 24rpx;
                background: rgba(0, 0, 0, 0.7);
                border-radius: 50%;
            }
        }

        .pre-item1 {
            margin-top: 16rpx;
            width: 556rpx;
            height: auto;
            background-color: #F6F6F6;

            .u-delete-icon1 {
                top: 12rpx;
                right: 12rpx;
                width: 24rpx;
                height: 24rpx;
                background: rgba(0, 0, 0, 0.7);
                border-radius: 50%;
            }
        }
    }

    &__divider {
        background: #EEEEEE;
        width: 100%;
        height: 2rpx;
    }

    &__single {
        width: 100%;
        height: 108rpx;
        font-size: 32rpx;
        font-weight: 400;
        color: #AEB0BC;
        line-height: 44rpx;

        &__left {
            margin-right: auto;
        }
    }
}

.pop-title {
    position: absolute;
    top: 30rpx;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #262937;
    line-height: 44rpx;
}

.content {
    height: 100%;
    padding: 100rpx 30rpx 130rpx;
    text-align: center;

    .input-box {
        width: 100%;
        box-sizing: border-box;
        padding: 18rpx 0 36rpx;

        &__inner {
            flex: 1;
            text-align: left;
            width: 100%;
            padding: 18rpx 30rpx;
            background: #F6F6F6;
            border-radius: 32rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 28rpx;
        }

        &__placeholder {
            font-size: 28rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 28rpx;
        }

        &__send {
            margin-left: 24rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
        }
    }

    .content-title {
        text-align: left;
        font-size: 24rpx;
        font-weight: 500;
        color: #111111;
        line-height: 34rpx;
    }

    .content-box {
        padding: 8rpx 0;
        flex-wrap: wrap;
        justify-content: space-between;

        &__single {
            padding: 12rpx 22rpx;
            margin-top: 28rpx;
            width: 212rpx;
            height: 60rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            margin-left: 28rpx;
            margin-right: auto;
        }

        &__single:nth-of-type(3n+1) {
            margin-left: 0rpx;
            margin-right: 0;
        }

        .is-active {
            background: #E0E5FF;
            border-radius: 8rpx;
            border: 2rpx solid #617EFB;
        }
    }

    .comment-single {
        padding-top: 40rpx;

        image {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-bottom: auto;
        }

        &__right {
            margin-left: 24rpx;
            width: 100%;
            padding-bottom: 24rpx;
            border-bottom: 2rpx solid #EEEEEE;
            text-align: left;

            &__name {
                font-size: 28rpx;
                font-weight: 500;
                color: #111111;
                line-height: 28rpx;
            }

            &__content {
                margin-top: 24rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #262937;
                line-height: 28rpx;
            }

            &__date {
                margin-top: 20rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 34rpx;

                text {
                    margin-left: 16rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #787C8D;
                    line-height: 34rpx;
                }
            }
        }
    }
}

.bottom-box {
    position: fixed;
    width: 100%;
    height: 140rpx;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-top: 20rpx;
    background: #fff;
    z-index: 2;
}
</style>