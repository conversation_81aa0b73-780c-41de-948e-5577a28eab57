<template>
    <view class="notice">
        <view :animation="animationData" class="notice-content">
            <view class="notice-item flex-mode" v-for="(item, index) in noticeList" :key="index"
                @tap="handleClickNotice(item)">
                <image :src="item.img_url" class="notice-item__img"></image>
                <view class="notice-item__name">{{ item.nickname }}：</view>
                <view class="notice-item__content">{{ item.question }}</view>
            </view>
        </view>
    </view>
</template>
  
<script>
export default {
    name: "notice-bar",
    data() {
        return {
            noticeList: [], // 通知列表
            animation: null, // 动画对象
            animationData: {},
            timer: null, // 定时器
            interval: 3000, // 滚动时间间隔
            scrollTop: 0, // 滚动距离
            currentIndex: 0, // 当前通知索引
            myrpx: 0,
        };
    },
    props: {
        notices: {
            // 外部传入的通知列表
            type: Array,
            default: [],
        },
    },
    watch: {
        // 实现可动态传入通知列表
        notices() {
            this.initNoticeList();
        },
    },
    mounted() {
        this.initNoticeList();
        const _this = this;
        uni.getSystemInfo({
            success(res) {
                _this.myrpx = 750 * 1 / res.screenWidth
            }
        });
        _this.animation = uni.createAnimation({
            duration: 500,
            timingFunction: "ease-out",
        });
    },
    methods: {
        // 初始化通知列表
        initNoticeList() {
            const _this = this;
            _this.noticeList = _this.notices;
            if (_this.noticeList.length > 1) {
                _this.timer = setInterval(() => {
                    _this.handleScrollNotice();
                }, _this.interval);
            }
        },
        // 点击通知时触发
        handleClickNotice(item) {
            this.$emit("click", item);
        },
        // 滚动通知
        handleScrollNotice() {
            const len = this.noticeList.length;
            if (this.currentIndex === len - 1) {
                this.currentIndex = 0;
            } else {
                this.currentIndex++;
            }
            this.animateScroll();
        },
        // 动画滚动
        animateScroll() {
            const _this = this;
            const noticeHeight = Math.floor(72 / this.myrpx); // 通知高度，根据实际情况调整
            const scrollTop = _this.currentIndex * noticeHeight;

            if (scrollTop === 0) {
                _this.animation.translateY(-scrollTop).step({
                    duration: 0,
                });
            } else {
                _this.animation.translateY(-scrollTop).step();
            }
            _this.animationData = _this.animation.export();
        },
    },
    destroyed() {
        if (this.timer) {
            clearInterval(this.timer);
        }
        if (this.animation) {
            this.animation = null;
        }
    },
};
</script>
  
<style lang="scss" scoped>
.flex-mode {
    display: flex;
    justify-content: center;
    align-items: center;
}

.notice {
    margin: 40rpx 24rpx;
    height: 72rpx;
    background: #EBF6FF;
    border-radius: 36rpx;
    /* 组件高度，根据实际情况调整 */
    height: 72rpx;
    overflow: hidden;
}

.notice-scroll {
    width: 100%;
    height: 100%;
}

.notice-content {
    display: flex;
    flex-direction: column;
}

.notice-item {
    /* 通知高度，根据实际情况调整 */
    height: 72rpx;
    /* 通知行高，根据实际情况调整 */
    line-height: 72rpx;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &__img {
        background: #FF7373;
        margin-left: 12rpx;
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
    }

    &__name {
        margin-left: 16rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: #262937;
        line-height: 42rpx;
    }

    &__content {
        width: 390rpx;
        margin-right: auto;
        font-size: 30rpx;
        font-weight: 400;
        color: #262937;
        line-height: 42rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}</style>