<template>
  <view class="video-box">
    <video :poster="poster" :src="playerUrl" :id="`player${id}`" class="cover" object-fit="cover" :show-progress="false"
      :loop="false" :controls="true" :showPlayBtn="true" :showCenterPlayBtn="true"></video>
  </view>
</template>
<script setup>
import request from "@/request";
import { ref, onMounted } from 'vue';
const props = defineProps({
  url: {
    type: String,
    default() {
      return ''
    }
  },
  id: {
    type: Number,
    default() {
      return 0
    }
  },
})
const playerUrl = ref('');
const poster = ref('');
function getDeatil(asset_id) {
  return request({
    url: "/api/getdetail",
    data: {
      asset_id,
      categories: "base_info,transcode_info&thumbnail_info,review_info"
    },
    method: "post"
  })
}
onMounted(() => {
  const regExp = /[\?&]asset_id=([^&#]+)/;
  const match = regExp.exec(props.url);
  const asset_id = match && decodeURIComponent(match[1]);
  if (asset_id) {
    getDeatil(asset_id)
      .then(res => {
        if (res.transcode_info.transcode_status == 'TRANSCODE_SUCCEED') {
          playerUrl.value = res.transcode_info.output[0].url;
          poster.value = res.base_info.cover_info_array[0].cover_url;
        } else {
          playerUrl.value = res.base_info.video_url;
        }
      })
  } else {
    playerUrl.value = props.url;
    poster.value = props.url + '?vframe/jpeg/offset/1'
  }
})
</script>

<style lang="scss" scoped>
.video-box {
  width: 556rpx;
  height: 368rpx;
  border-radius: 24rpx;
  position: reactive;

  .cover {
    width: 556rpx;
    height: 368rpx;
    border-radius: 24rpx;
  }

  .play-btn {
    position: absolute;
    width: 96rpx;
    height: 96rpx;
    left: 0;
    right: 0;
    top: 200rpx;
    bottom: 0;
    margin: auto;
  }
}
</style>