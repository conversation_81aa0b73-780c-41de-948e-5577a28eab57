<template>
    <view class="box">
        <!-- <view class="ai">
            <view class="ai__top">HI，我是宝宝成长AI助手</view>
            <view class="ai__mid">宝宝成长问题尽管问我</view>
            <image @click="toWenwen" class="ai__img" src="https://obs.tuoyupt.com/nanjing/pstac/notice/ai.png">
            </image>
        </view> -->
    </view>
</template>

<script>
/**
 * navbar 自定义导航栏
 * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uniapp自带的导航栏。
 * @tutorial https://www.uviewui.com/components/navbar.html
 * @property {String Number} height 导航栏高度(不包括状态栏高度在内，内部自动加上)，注意这里的单位是px（默认44）
 * @property {String} back-icon-color 左边返回图标的颜色（默认#606266）
 * @property {String} back-icon-name 左边返回图标的名称，只能为uView自带的图标（默认arrow-left）
 * @property {String Number} back-icon-size 左边返回图标的大小，单位rpx（默认30）
 * @property {String} back-text 返回图标右边的辅助提示文字
 * @property {Object} back-text-style 返回图标右边的辅助提示文字的样式，对象形式（默认{ color: '#606266' }）
 * @property {String} title 导航栏标题，如设置为空字符，将会隐藏标题占位区域
 * @property {String Number} title-width 导航栏标题的最大宽度，内容超出会以省略号隐藏，单位rpx（默认250）
 * @property {String} title-color 标题的颜色（默认#606266）
 * @property {String Number} title-size 导航栏标题字体大小，单位rpx（默认32）
 * @property {Function} custom-back 自定义返回逻辑方法
 * @property {String Number} z-index 固定在顶部时的z-index值（默认980）
 * @property {Boolean} is-back 是否显示导航栏左边返回图标和辅助文字（默认true）
 * @property {Object} background 导航栏背景设置，见官网说明（默认{ background: '#ffffff' }）
 * @property {Boolean} is-fixed 导航栏是否固定在顶部（默认true）
 * @property {Boolean} immersive 沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效（默认false）
 * @property {Boolean} border-bottom 导航栏底部是否显示下边框，如定义了较深的背景颜色，可取消此值（默认true）
 * @example <u-navbar back-text="返回" title="剑未配妥，出门已是江湖"></u-navbar>
 */
export default {
    name: "ai-box",
    props: {
    },
    data() {
    },
    computed: {
    },
    created() { },
    methods: {
        goBack() {
            // 如果自定义了点击返回按钮的函数，则执行，否则执行返回逻辑
            if (typeof this.customBack === 'function') {
                // 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this
                // 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文
                this.customBack.bind(this.$u.$parent.call(this))();
            } else {
                uni.navigateBack({
                    fail(error) {
                        uni.navigateTo({
                            url: '/pages/index/index',
                        })
                    }
                });
            }
        }
    }
};
</script>

<style scoped lang="scss">
.box {
    overflow: hidden;
    padding: 20rpx 0 0;

    .ai {
        position: relative;
        margin-top: 20rpx;
        width: 690rpx;
        height: 314rpx;
        background: #FFFFFF;
        box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.04);
        border-radius: 0rpx 40rpx 40rpx 40rpx;

        &__top {
            margin: 60rpx 0 8rpx 42rpx;
            font-size: 40rpx;
            font-weight: 600;
            color: #111111;
            line-height: 56rpx;
        }

        &__img {
            padding: 50rpx;
            position: absolute;
            right: 0;
            top: 0;
            width: 154rpx;
            height: 154rpx;
        }

        &__mid {
            margin-left: 42rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #787C8D;
            line-height: 40rpx;
        }
    }
}
.box::after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}
</style>
