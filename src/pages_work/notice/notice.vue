<template>
    <u-navbar :title="navTitle" :background="navBackground" :border-bottom="false"></u-navbar>
    <view class="notice-box">
        <input v-model="curTitle" placeholder="编辑标题" :style="titleStyle" :placeholder-style="titleStyle" />
        <textarea v-model="curCont" placeholder="在这里输入正文" :style="textStyle" :placeholder-style="textStyle1" />
        <view class="bottom-box">
            <u-button type="primary" :custom-style="sendButton" hover-class @click="send" throttle-time="1000">发布</u-button>
        </view>
        <template v-if="pageType == 1">
            <view class="notice-box__divider" style="margin-top: 40rpx;"></view>
            <view class="flex-mode notice-box__single" @click="showSee">
                <view class="notice-box__single__left">通知班级</view>
                <view><u-icon name="arrow-right" color="#AEB0BC" size="28"></u-icon></view>
            </view>
            <view class="choose_content" v-if="class_name">
                {{ class_name }}
            </view>
        </template>
    </view>

    <u-popup v-model="showPop" mode="bottom" height="90%" z-index="9999" closeable close-icon-pos="top-left"
        close-icon-color="#262937" close-icon-size="24" :mask="false" :custom-style="popStyle" safe-area-inset-bottom
        negative-top="50">
        <view class="pop-title">{{ popTitle }}</view>
        <view class="content">
            <scroll-view scroll-y="true" style="height: 100%;">
                <view>
                    <view class="content-title">
                        选择可见班级
                    </view>
                    <view class="content-box flex-mode">
                        <template v-for="(item, index) in allClassList" :key="index">
                            <view class="content-box__single flex-mode" @click="selectClass(index)"
                                :class="item.is_selected ? 'is-active' : ''">{{ item.name }}</view>
                        </template>
                    </view>
                </view>
            </scroll-view>
            <view class="input-box flex-mode">
                <u-button type="primary" :custom-style="sendButton" hover-class @click="confirm">确定</u-button>
            </view>
        </view>
    </u-popup>
</template>

<script setup>
import { ref, reactive } from "vue";
import request from "@/request";
import { onShow, onLoad, onUnload } from "@dcloudio/uni-app";
// 变量
const navTitle = ref('')
const pageType = ref(1)
const navBackground = {
    backgroundColor: '#FFFFFF'
}
const showList = ref({
    opacity: 0
})
const sendButton = {
    width: "690rpx",
    height: "80rpx",
    backgroundColor: '#617EFB',
}
const titleStyle = "font-size: 44rpx; font-weight: bold; color: #111111; line-height: 60rpx"
const textStyle = "margin-top: 40rpx; font-size: 30rpx; font-weight: 400; color: #262937; line-height: 48rpx"
const textStyle1 = "margin-top: 40rpx; font-size: 30rpx; font-weight: 400; color: #AEB0BC; line-height: 48rpx"
const curTitle = ref('')
const curCont = ref('')
const showPop = ref(false)
const popType = ref(0)
const popTitle = ref('')
const popStyle = ref({
    paddingTop: '30rpx'
})
const allClassList = ref([])
const class_id = ref('')
const class_name = ref('')
// 方法
const showSee = () => {
    showPop.value = true
    popTitle.value = '通知班级'
    popType.value = 0
}
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
// 获取班级列表
const getClass = async () => {
    let school_id = userInfo.school_id;
    uni.showLoading({
        title: "正在加载中…"
    })
    await request({
        method: "GET",
        url: `/api/teach/circle/classes?school_id=${school_id}`,
    }).then((result) => {
        // console.log(result);
        let list = []
        for (let i = 0; i < result.length; i++) {
            for (let j = 0; j < result[i].class_list.length; j++) {
                result[i].class_list[j].is_selected = false
                list.push(result[i].class_list[j])
            }
        }
        // console.log(list)
        allClassList.value = list
        uni.hideLoading()
    });
}
// 选择班级
const selectClass = (index) => {
    allClassList.value[index].is_selected = !allClassList.value[index].is_selected
}
// 确认选择
const confirm = async () => {
    var arr = []
    var arr_name = []
    allClassList.value.forEach(el => {
        if (el.is_selected) {
            // console.log(el);
            arr.push(el.id)
            arr_name.push(el.name)
        }
    })
    arr = arr.join(',')
    arr_name = arr_name.join('、')
    // console.log("arr", arr, "arr_name", arr_name);
    class_id.value = arr
    class_name.value = arr_name
    showPop.value = false
    popTitle.value = '通知班级'
    popType.value = 0
}
const send = async () => {
    let school_id = userInfo.school_id;
    let params = {
        title: curTitle.value,
        description: curCont.value,
        type: pageType.value == 1 ? 4 : 3,
        class_ids: class_id.value,
        submit_status: 1
    }
    await request({
        method: "POST",
        url: `/api/teach/schoolnotice/add/${school_id}`,
        data: params
    }).then((res) => {
        console.log('send', res);
        uni.showToast({
            title: '发布成功',
            icon: 'none',
            duration: 1000
        });
        setTimeout(() => {
            uni.navigateBack()
        }, 1000)
    });
}
onLoad((option) => {
    console.log(option)
    if (option.type == 1) {
        pageType.value = 1
        navTitle.value = '发布班级通知'
        getClass()
    } else {
        pageType.value = 2
        navTitle.value = '发布机构通知'
    }
})
onShow(() => {
    console.log('refresh')
    // getClass()
    uni.$on('refresh', (data) => {
        if (data.refresh) {
            // getList(2)
        }
    })
})
onUnload(() => {
    console.log('unload')
    uni.$off('refresh');
})


</script>


<style lang="scss" scoped>
.flex-mode {
    display: flex;
    justify-content: center;
    align-items: center;
}

.notice-box {
    padding: 40rpx 30rpx;

    .bottom-box {
        position: fixed;
        width: 100%;
        height: 140rpx;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0 auto;
        padding-top: 20rpx;
        background: #fff;
        z-index: 2;
    }

    &__divider {
        background: #EEEEEE;
        width: 100%;
        height: 2rpx;
    }

    &__single {
        width: 100%;
        height: 108rpx;
        font-size: 32rpx;
        font-weight: 400;
        color: #AEB0BC;
        line-height: 44rpx;

        &__left {
            margin-right: auto;
        }
    }
}

.choose_content {
    font-size: 28rpx;
    color: #262937;
    line-height: 40rpx;
    margin-bottom: 32rpx;
}

.pop-title {
    position: absolute;
    top: 30rpx;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #262937;
    line-height: 44rpx;
}

.content {
    height: 100%;
    padding: 100rpx 30rpx 130rpx;
    text-align: center;

    .input-box {
        width: 100%;
        box-sizing: border-box;
        padding: 18rpx 0 36rpx;

        &__inner {
            flex: 1;
            text-align: left;
            width: 100%;
            padding: 18rpx 30rpx;
            background: #F6F6F6;
            border-radius: 32rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 28rpx;
        }

        &__placeholder {
            font-size: 28rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 28rpx;
        }

        &__send {
            margin-left: 24rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
        }
    }

    .content-title {
        text-align: left;
        font-size: 24rpx;
        font-weight: 500;
        color: #111111;
        line-height: 34rpx;
    }

    .content-box {
        padding: 8rpx 0;
        flex-wrap: wrap;
        justify-content: space-between;

        &__single {
            padding: 12rpx 22rpx;
            margin-top: 28rpx;
            width: 212rpx;
            height: 60rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            margin-left: 28rpx;
            margin-right: auto;
        }

        &__single:nth-of-type(3n+1) {
            margin-left: 0rpx;
            margin-right: 0;
        }

        .is-active {
            background: #E0E5FF;
            border-radius: 8rpx;
            border: 2rpx solid #617EFB;
        }
    }

    .comment-single {
        padding-top: 40rpx;

        image {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-bottom: auto;
        }

        &__right {
            margin-left: 24rpx;
            width: 100%;
            padding-bottom: 24rpx;
            border-bottom: 2rpx solid #EEEEEE;
            text-align: left;

            &__name {
                font-size: 28rpx;
                font-weight: 500;
                color: #111111;
                line-height: 28rpx;
            }

            &__content {
                margin-top: 24rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #262937;
                line-height: 28rpx;
            }

            &__date {
                margin-top: 20rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 34rpx;

                text {
                    margin-left: 16rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #787C8D;
                    line-height: 34rpx;
                }
            }
        }
    }
}
</style>