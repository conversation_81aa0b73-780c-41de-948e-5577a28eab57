<template>
  <u-navbar title="" ref="navbar" :background="navBackground" :border-bottom="false"></u-navbar>

  <view class="content" :style="{ height: `calc(100vh - 30rpx - ${navbarHeight}px)` }">
    <!-- 下拉选择框 -->
    <view class="container-top">
      <view class="select_class">
        <view class="change_class_container">
          <!-- 当前选中班级 -->
          <text class="currentClassName">{{ curentClassObj.class_name }}</text>
        </view>
      </view>
      <!-- 默认计划 -->
      <view class="view_current_plan">
        <text class="currentPlanName">{{ curentClassObj.plan_name }}</text>
      </view>

      <!-- 班级的计划种类 -->
      <view class="plan_category">
        <!-- 每一个计划类型 -->
        <view class="plan_category_content" v-for="(category, index) in planCategorys" :key="index"
          @click="onPlanCategory(category.name, category.category_id, index)">
          <view :class="current_category_idx == index
            ? 'category_detail'
            : 'category_detail_unselect'
            ">{{ category.name }}</view>

          <view :class="current_category_idx == index ? 'plan_type_line' : ''"></view>
        </view>
      </view>
    </view>

    <!-- 无数据 -->

    <view class="nodata" v-show="isShowNodata" v-if="planList.length == 0">
      暂无内容
    </view>
    <!-- 计划列表 -->
    <scroll-view :style="{ height: `calc(100vh - 280rpx - ${navbarHeight}px)` }" class="scroll-box" :scroll-y="true"
      :refresher-enabled="true" :refresher-triggered="triggered" :refresher-threshold="100"
      refresher-background="#F6F6F6" @refresherrefresh="onRefresh" @scrolltolower="onReachBottom" v-else>
      <view class="plan_list">
        <!-- 每一个计划类型  -->
        <view v-for="(planItem, index) in planList" :key="index" @click="onPlanItem(planItem.url)">
          <view class="plan_item">
            <view class="plan_type_name">
              <view class="item_plan_category_name_week" v-if="planItem.category_id == 1 || planItem.category_id == 5">
                {{
                  planItem.category_name }}
              </view>
              <view class="item_plan_category_name_month" v-else-if="planItem.category_id == 2 || planItem.category_id == 6
              ">{{ planItem.category_name }}</view>
              <view class="item_plan_category_name_halfyear" v-else-if="planItem.category_id == 3 || planItem.category_id == 7
              ">{{ planItem.category_name }}</view>

              <view class="item_plan_category_name_year" v-else-if="planItem.category_id == 4 || planItem.category_id == 8
              ">{{ planItem.category_name }}</view>
              <view class="item_plan_category_name_year" v-else>{{
                planItem.category_name
              }}</view>

              <view class="item_plan_name">{{ planItem.name }}</view>
            </view>

            <view class="item_plan_time">{{ planItem.start_time }}~{{ planItem.end_time }}</view>
          </view>
        </view>
      </view>

      <!--加载更多-->

      <u-loadmore v-show="isShowLoadmore" :status="status" margin-top="30" margin-bottom="30" :load-text="loadText" />
    </scroll-view>
    <!-- </mescroll-body> -->
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import request from "@/request";
import { onLoad } from "@dcloudio/uni-app";

const isShowNodata = ref(false); //是否显示暂无数据
const isShowLoadmore = ref(false); //是否显示加载更多组件
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const curentClassObj = reactive({
  class_id: "0", //当前班级ID
  class_name: "--班", //当前班级名称
  student_id: "0",
  plan_name: "保育计划", //当前计划类型
  category_id: "0", //当前计划类型ID
});
const bussinessType = ref(1); //机构业务类型
let navbarHeight = ref(0);
let navbar = ref(null);
// const class_id = ref(""); //当前班级ID
const planList = ref([]); //计划列表
// 班级的计划种类
const planCategorys = ref();
// 当前班级计划种类下标
const current_category_idx = ref(0);
//当前请求页面页码page
const page = ref(1);
//当前请求页面条数
const size = ref(10);
//声明加载更多状态
const status = ref("loadmore");
//是否是上拉加载更多
const isLoadmore = ref(false);
//声明加载状态
const loadText = reactive({
  loadmore: "加载中",
  loading: "加载中",
  nomore: "没有更多了",
});
//是否显示 切换底部班级滚轮
const isShowClassSelect = ref(false);
//班级滚轮默认选中位置
const classSelectIndex = ref([0]);
//自定义导航栏颜色
const navBackground = {
  backgroundColor: "#F8F8F8",
};

onLoad((option) => {
  setTimeout(() => {
    navbarHeight.value =
      navbar.value.statusBarHeight + navbar.value.navbarHeight;
  }, 0);
  curentClassObj.class_id = userInfo.class_id;
  curentClassObj.class_name = userInfo.class_name;
  curentClassObj.student_id = userInfo.student_id;

  //请求保教计划分类
  reqCategory(curentClassObj.class_id);

});
const triggered = ref(false)
/*下拉刷新的回调 */
const onRefresh = () => {
  triggered.value = true;
  //重置页面
  page.value = 1;
  //重置=计划列表数据
  planList.value = [];
  isShowNodata.value = false;
  //请求保教计划列表
  reqPlanList(
    curentClassObj.class_id,
    curentClassObj.category_id,
    page.value,
    size.value
  );
  triggered.value = false;
};
/*上拉加载的回调 */
const onReachBottom = () => {
  console.log("上拉加载");
  // isLoadmore.value = true;

  // if(status.value == "nomore"){
  //   //NOmare 无需加载
  //   return;
  // }
  isShowLoadmore.value = true;
  status.value = "loadmore";
  page.value = page.value + 1;
  //请求保教计划列表
  reqPlanList(
    curentClassObj.class_id,
    curentClassObj.category_id,
    page.value,
    size.value
  );
};

/**
 * 提交班级滚轮选择器
 */
const confirm = (e) => {
  console.log("提交班级滚轮选择器:", e[0]);
  console.log("e.detail[0]", e[0].value);
  console.log("e.detail[0]", e[0].label);
  console.log("e.detail[0]", e[0].extra);
  console.log("classSelectIndex：", classSelectIndex.value);

  console.log(
    "切换钱班级---",
    curentClassObj.class_id,
    curentClassObj.class_name
  );
  //更新当前选中班级
  curentClassObj.class_id = e[0].value;
  curentClassObj.class_name = e[0].label;
  classSelectIndex.value = [e[0].extra];
  // selectIsClass();
  //重新保教计划分类
  reqCategory(curentClassObj.class_id);
  console.log(
    "切换后班级---",
    curentClassObj.class_id,
    curentClassObj.class_name
  );
};

const selectIsClass = () => {
  isShowClassSelect.value = true;

  // if (isXiala.value == 0) {
  //   isXiala.value = 1;
  // } else if (isXiala.value == 1) {
  //   isXiala.value = 2;
  // } else if (isXiala.value == 2) {
  //   isXiala.value = 1;
  // }
};
/**
 * 切换班级
 */
const changeClass = (id, name) => {
  // //更新当前选中班级
  // curentClassObj.class_id = id;
  // curentClassObj.class_name = name;
  // selectIsClass();
  // //重新保教计划分类
  // reqCategory(curentClassObj.class_id);
  // console.log(
  //   "切换后班级---",
  //   curentClassObj.class_id,
  //   curentClassObj.class_name
  // );
};
/**
 * 点击计划item
 */
const onPlanItem = (url) => {
  //跳转到计划详情
  openDoc(url);
};

//请求保教计划分类
const reqCategory = (class_id) => {
  let planCategoryParams = {
    class_id: class_id,
  };

  request({
    method: "GET",
    url: "/api/plan/category",
    data: planCategoryParams,
  }).then(async (res) => {
    if (res != null && res.length != 0) {
      planCategorys.value = [];
      planCategorys.value = res;
      console.log("保教计划分类:" + res);
      //重置页面
      page.value = 1;
      //重置=计划列表数据
      planList.value = [];
      isShowNodata.value = false;
      //默认选中第一个，请求计划列表数据
      console.log(
        "班级id,计划分类id:" +
        curentClassObj.class_id +
        "," +
        planCategorys.value[0].category_id
      );
      console.log("planCategorys.length:" + planCategorys.value.length);
      if (planCategorys.value.length >= 0) {
        //默认选中第一个下标
        current_category_idx.value = 0;
        reqPlanList(
          curentClassObj.class_id,
          planCategorys.value[0].category_id,
          page.value,
          size.value
        );
      }
    } else {
      console.log("数据异常");
    }
  });
};
/**
 * 点击保教计划分类
 */
const onPlanCategory = (category_name, category_id, index) => {
  //更新当前选中的计划种类下标
  current_category_idx.value = index;
  console.log("current_category_idx:" + current_category_idx.value);

  //重置页面
  page.value = 1;
  //重置=计划列表数据
  planList.value = [];
  isShowNodata.value = false;
  //请求保教计划列表
  reqPlanList(curentClassObj.class_id, category_id, page.value, size.value);
};

//请求计划列表
const reqPlanList = (class_id, category_id, page, size) => {
  //保存当前选中班级类型id
  curentClassObj.category_id = category_id;

  let planListParams = {
    class_id: class_id,
    category_id: category_id,
    student_id: curentClassObj.student_id,
    page: page,
    size: size,
  };

  request({
    method: "GET",
    url: "/api/parent/plan/list",
    data: planListParams,
  }).then((res) => {
    console.log(res);
    //处理加载更多显示文本
    if (res.length == "") {
      status.value = "nomore";
    } else if (res.length < 10) {
      status.value = "nomore";
      // this.suo = false;
    }

    planList.value = [...planList.value, ...res];
    if (planList.value.length == 0) isShowNodata.value = true;
    else isShowNodata.value = false;

    // if ((isLoadmore.value = true)) {

    //   //是上拉加载更多
    //   isLoadmore.value = false; //重置为false
    // }

    if (planList.value.length > 7) {
      //显示没有loadmore组件
      isShowLoadmore.value = true;
    } else {
      isShowLoadmore.value = false;
    }
    uni.stopPullDownRefresh();
    uni.hideLoading();
  });
};

/**
 * 打开pdf类文件
 */
const openDoc = (url) => {
  window.open(
    url
  )
};

// /* mescroll 属性配置 */

// const upOption = () => ({
//   use: false, // 是否启用上拉加载; 默认tru
//   auto: false, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true
// });

// const downOption = () => ({
//   use: false, // 是否启用下拉刷新; 默认true
//   auto: false, // 是否在初始化完毕之后自动执行下拉刷新的回调; 默认true
//   textLoading: "加载中 ...", // 加载中的提示文本
// });
</script>

<style>
page {
  background: #f6f6f6;
}

.content {
  margin-top: 30rpx;
  margin-left: 0rpx;
  display: flex;
  flex-direction: column;
}

.select_class {
  /* margin-top: 40rpx; */
  width: auto;
  height: auto;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;

  align-items: center;
  justify-content: space-between;
  position: relative;
}

.change_class_container {
  display: flex;
  align-items: center;
}

.currentClassName {
  color: #111111;
  font-size: 48rpx;
  font-weight: 500;
  vertical-align: middle;
}

.change {
  margin-left: 20rpx;
  width: 104rpx;
  height: 48rpx;
  border-radius: 32rpx;
  color: #262937;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view_current_plan {
  margin-top: 15rpx;
  padding: 0 22rpx;
}

.currentPlanName {
  width: auto;
  height: auto;
  color: #787c8d;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 28rpx;
}

.select_class text {
  color: #000;
}

.select_class image {
  width: 40rpx;
  height: 40rpx;
}

/* 
.xiala {
  position: absolute;
  bottom: -240rpx;
  left: 0;
  height: 230rpx;
  width: 200rpx;
  z-index: 2;
}

.xiala-xuan {
  height: auto;
  width: 200rpx;
  background-color: #efefef;
  box-sizing: border-box;
  overflow: hidden;
  z-index: 2;
}

.xiala-hang {
  height: 80rpx;
  width: 100%;
  border-bottom: 1px solid #efefef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #262937;
  z-index: 2;
}

.xiala-hang:last-child {
  border: 0px;
  z-index: 2;
} */

/* 显示或关闭动画*/
.open {
  animation: slideContentUp 0.3s linear both;
}

.close {
  animation: slideContentDown 0.3s linear both;
}

/* 动态设置高度 */
@keyframes slideContentUp {
  from {
    height: 0;
  }

  to {
    height: -230rpx;
  }
}

@keyframes slideContentDown {
  from {
    height: -230rpx;
  }

  to {
    height: 0;
  }
}

.plan_category {
  margin-top: 40rpx;
  display: flex;
  /* align-items: left; */
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-around;
  flex-direction: row;
  flex-wrap: nowrap;
}

.category_detail {
  /* margin-left: 10rpx; */
  width: auto;
  height: auto;
  font-size: 32rpx;
  line-height: 44rpx;
  letter-spacing: 1rpx;
  font-weight: 500;
  color: #111111;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  /* padding: 0 20rpx; */
}

.category_detail_unselect {
  width: auto;
  height: auto;
  font-size: 32rpx;
  line-height: 44rpx;
  letter-spacing: 1rpx;
  font-weight: 500;
  color: #aeb0bc;
}

.plan_list {
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: left;
  /* justify-content: center; */
  background: #ffffff;
  border-radius: 16rpx;
  margin: 30rpx;
}

.plan_item {
  margin: 45rpx 20rpx;
  width: auto;
  height: auto;
  /* border-radius: 1rpx; */
  /* background-color:rgba(36, 44, 61, 1); */
  /* border: 1px solid #000; */
  /* padding: 20rpx 20rpx 20rpx 20rpx; */
}

.plan_type_name {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.class_select {
  color: #40a9ff;
}

.class_unselect {
  color: #262937;
}

.plan_category_content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.plan_type_line {
  margin-top: 12rpx;
  width: 46rpx;
  height: 4rpx;
  background: #262937;
}

/* 默认计划 */
.item_plan_category_name_default {
  background: rebeccapurple;
  width: auto;
  height: auto;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
}

/* 周计划 */
.item_plan_category_name_week {
  background: #f759ab;
  width: auto;
  height: auto;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12rpx;
  padding-right: 12rpx;
  padding-top: 6rpx;
  padding-bottom: 5rpx;
  border-radius: 10rpx;
}

/* 月计划 */
.item_plan_category_name_month {
  background: #9254de;
  width: auto;
  height: auto;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12rpx;
  padding-right: 12rpx;
  padding-top: 6rpx;
  padding-bottom: 5rpx;
  border-radius: 10rpx;
}

/* 半年计划 */
.item_plan_category_name_halfyear {
  background: #40a9ff;
  width: auto;
  height: auto;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12rpx;
  padding-right: 12rpx;
  padding-top: 6rpx;
  padding-bottom: 5rpx;
  border-radius: 10rpx;
}

/* 年计划 */
.item_plan_category_name_year {
  background: #36cfc9;
  width: auto;
  height: auto;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12rpx;
  padding-right: 12rpx;
  padding-top: 6rpx;
  padding-bottom: 5rpx;
  border-radius: 10rpx;
}

.item_plan_name {
  margin-left: 15rpx;
  width: auto;
  height: auto;
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262937;
  line-height: 32rpx;
}

.item_plan_time {
  margin-top: 20rpx;
  width: auto;
  height: auto;
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #aeb0bc;
  line-height: 24rpx;
}

.nodata {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a3b0bc;
  font-size: 28rpx;
}

.scroll-box {
  background: #f6f6f6;
  overflow-y: scroll;
  overflow-x: hidden;
  z-index: 1;
}

.container-top {
  height: 220rpx;
}

::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
</style>