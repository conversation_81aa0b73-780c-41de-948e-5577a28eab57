  <template>
  <u-navbar
    :title="bartitle"
    :background="navBackground"
    :border-bottom="false"
  ></u-navbar>

  <el-container class="resPreview">
    <div ref="refPdf" />
  </el-container>
</template>


 <script setup>
import { ref, getCurrentInstance, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";


const state = reactive({
  pdfh5: null,
});
const bartitle = ref("保教计划详情");

onLoad((option) => {
  //获取上个页面携带额参数
  var data = JSON.parse(decodeURIComponent(option.obj));
  bartitle.value = data.plan_name;
  openDoc(data.url);
});

const openDoc = (url) => {
  uni.showToast({
    title: "打开中…",
    icon: "loading",
    duration: 1000,
  });
  //延迟两秒打开
  setTimeout(() => {
    uni.downloadFile({
      url: url, //要预览的PDF的地址
      success: function (res) {
        console.log(res);
        if (res.statusCode === 200) {
          //成功
          var Path = res.tempFilePath; //返回的文件临时地址，用于后面打开本地预览所用
          uni.openDocument({
            filePath: Path, //要打开的文件路径
            success: function (res) {
              console.log("打开PDF成功");
              //打开成功 返回上个页面
              setTimeout(() => {
                // 返回上一页
                uni.navigateBack({});
                console.log("返回上个页面");
              }, 100);
            },
            fail: function () {
              uni.showToast({
                title: "暂不支持此类型",
                duration: 2000,
                icon: "none",
              });
            },
          });
        }
      },
      fail: function (res) {
        console.log(res); //失败
      },
    });
  }, 1000);
};
</script>
