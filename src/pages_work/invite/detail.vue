<template>
  <view class="pages safe-area-inset-bottom">
    <scroll-view class="scroll" :scroll-y="true">
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/invite/ab1.png">
      </image>
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/invite/b2b2.png">
      </image>
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/invite/b5bb.png">
      </image>
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/invite/ab4.png">
      </image>
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/invite/5b2.png">
      </image>
      <view class="msg">{{ teacher_name }}老师邀请您加入{{ school_name }}{{ class_name }}啦～</view>
    </scroll-view>
    <view class="footer">
      <view class="btn" v-if="hasLogin" @click="checkBind">加入班级</view>
      <view class="btn" v-else @click="loginShow = true">加入班级 </view>
    </view>
  </view>
  <u-popup v-model="loginShow" mode="center" border-radius="24" closeable>
    <view class="popview">
      <view class="btn" v-if="checkValue">手机号登录
        <button class="getPhoneNumber" @click="gotoLogin">getPhoneNumber</button>
      </view>
      <view class="btn" v-else @click="showMsg">手机号登录
      </view>
      <view class="check-box">
        <u-checkbox active-color="#FF7373" v-model="checkValue" shape="circle" icon-size="25">
          <view class="main-check flex-mode">
            登录即为同意 <view class="link" style="margin-left: 10rpx;" @click="toAgree(1)">泸州托育用户协议</view>、<view class="link"
              @click="toAgree(2)">泸州托育隐私</view>
          </view>
        </u-checkbox>
      </view>
    </view>
  </u-popup>
  <u-popup v-model="errShow" mode="center" border-radius="24" :closeable="false">
    <view class="popview">
      <view class="errText"> 很抱歉，您当前手机账号暂未绑定任何班级，请联系班级老师！</view>
      <view class="btn">
        确定
        <navigator target="miniProgram" class="getPhoneNumber" open-type="exit">
        </navigator>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
import { ref } from 'vue';
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
let school_id = "";
let class_id = "";
let teacher_uid = "";
onLoad(option => {
  school_id = option.school_id;
  class_id = option.class_id;
  teacher_uid = option.teacher_uid;
  getDetail();
  getAuth();
})
function gotoLogin() {
  uni.redirectTo({
    url: "/pages_work/login/main"
  })
}
let student_info = [];
const school_name = ref("");
const class_name = ref("");
const teacher_name = ref("");
const hasLogin = ref(false)
const getAuth = async () => {
  if (uni.getStorageSync('token')) {
    await userInfo.refrsh();
    if (userInfo.id !== 0) {
      hasLogin.value = true;
    }
  }
}
function getDetail() {
  uni.showLoading()
  request({
    url: "/api/auth/invitebindinfo",
    method: 'post',
    data: {
      school_id,
      class_id,
      teacher_uid
    }
  }).then(res => {
    uni.hideLoading()
    school_name.value = res.school_name;
    class_name.value = res.class_name;
    teacher_name.value = res.teacher_name;
  })
}
function getStudentInfo() {
  return request({
    url: "/api/auth/invitebind",
    method: 'post',
    data: {
      school_id,
      class_id,
      teacher_uid
    }
  }).then(res => {
    student_info = res.student_info;
  })
}
const errShow = ref(false);
async function checkBind() {
  await getStudentInfo();
  // todo
  if (student_info.length) {
    let infoArr = [];
    for (let index = 0; index < student_info.length; index++) {
      const element = student_info[index];
      if (element.class_id == Number(class_id)) {
        infoArr.push(element)
      }
    }
    if (infoArr.length == 0) {
      errShow.value = true;
    } else if (infoArr.length == 1) {
      checkChange(infoArr[0])
    } else {
      uni.setStorageSync("childarr", infoArr)
      uni.redirectTo({
        url: `/pages_work/invite/check_child?class_id=${class_id}`
      })
    }
  } else {
    errShow.value = true;
  }
}
function checkChange(stu) {
  return request({
    method: "GET",
    url: "/api/auth/me",
  }).then((res) => {
    if (res.parent.student_id == stu.student_id) {
      uni.redirectTo({
        url: '/pages_work/mine/child?noback=no'
      })
    } else {
      let parent_id = res.parent.id;
      let params = {
        student_id: stu.student_id,
        parent_id: parent_id
      }
      request({
        method: 'POST',
        url: '/api/auth/changestudent',
        data: params
      }).then(() => {
        uni.redirectTo({
          url: '/pages_work/mine/child?noback=no'
        })
      })
    }
  });
}
const loginShow = ref(false);
const checkValue = ref(false)
function showMsg() {
  uni.showToast({
    title: "请阅读并同意协议",
    icon: 'none'
  })
}
const toAgree = (type) => {
  uni.navigateTo({
    url: `/pages_work/login/agreement?type=${type}`
  })
}
const getPhoneNumber = (e) => {
  if (e.detail.code) {
    // 展示加载框
    uni.showLoading({
      title: '加载中',
    });
    uni.login({
      provider: 'weixin',
      success: (res) => {
        let code = res.code;
        if (res.errMsg == 'login:ok') {
          request({
            url: `/api/auth/firstlogin?code=${code}`,
          }).then(async (res2) => {
            let openid = res2.openid;
            let codephone = e.detail.code;
            let login_type = 2;
            let checkcode = 0;
            request({
              url: "/api/auth/secondlogin",
              method: 'post',
              data: {
                openid,
                codephone,
                login_type,
                checkcode,
              }
            }).then(res => {
              uni.hideLoading();
              if (res.token) {
                uni.setStorageSync("token", res.token);
                uni.setStorageSync("user_info", res.user);
                let user = res.user;
                userInfo.$patch(user)
                hasLogin.value = true;
                checkBind();
              } else {
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  confirmText: '确认',
                  cancelText: '取消',
                  success: function (resc) {
                    if (resc.confirm) {
                      uni.showLoading();
                      request({
                        url: "/api/auth/loginbindsure",
                        method: 'post',
                        data: {
                          openid: res.data.mini_openid,
                          uid: res.data.uid,
                        }
                      }).then(res3 => {
                        uni.hideLoading();
                        uni.setStorageSync("token", res3.token);
                        uni.setStorageSync("user_info", res3.user);
                        let user = res3.user;
                        userInfo.$patch(user)
                        hasLogin.value = true;
                        checkBind();
                      })
                    }
                  },
                })
              }
            })
          });
        }
        uni.hideLoading()
      },
    });
  }
}
</script>
<style lang="scss" scoped>
.pages {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;

  .scroll {
    flex: 1;
    min-height: 1rpx;
    position: relative;

    .msg {
      position: absolute;
      top: 290rpx;
      left: 30rpx;
      width: 686rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #787C8D;
      line-height: 40rpx;
    }

    .back {
      width: 100%;
      height: 746rpx;
      display: block;
    }
  }

  .footer {
    width: 750rpx;
    height: 112rpx;
    background: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;

    .btn {
      width: 690rpx;
      height: 80rpx;
      background: #FF7373;
      border-radius: 16rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 80rpx;
      position: relative;

      .getPhoneNumber {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
      }
    }
  }
}

.popview {
  width: 690rpx;
  padding: 60rpx 30rpx 30rpx;
  box-sizing: border-box;

  .errText {
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .link {
    font-size: 24rpx;
    color: #FF7373;
  }

  .main-check {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .check-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
  }

  .btn {
    width: 600rpx;
    height: 80rpx;
    background: #FF7373;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    line-height: 80rpx;
    position: relative;
    margin: 0 auto;

    .getPhoneNumber {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
    }
  }
}
</style>