<template>
  <view class="pages safe-area-inset-bottom">
    <scroll-view class="scroll" :scroll-y="true">
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/client/family/mys.png">
      </image>
      <view class="msg">{{ invite_str }}邀请您共同关注宝宝的成长～</view>
      <view class="select">
        <view class="item" v-for="item in relation" :key="item.id" @click="select_id = item.id">
          <image class="head" :src="getImg(item.id)">
          </image>
          <view v-if="select_id == item.id" class="check">
            <view class="name">
              {{ item.text }}
            </view>
            <view class="icon">
              <u-icon name="checkbox-mark" size="24"></u-icon>
            </view>
          </view>
          <view class="text" v-else>{{ item.text }}</view>
        </view>
      </view>
    </scroll-view>
    <view class="footer">
      <view class="btn" v-if="hasLogin" @click="checkBind">确定绑定</view>
      <view class="btn" v-else @click="loginShow = true">确定绑定
      </view>
    </view>
  </view>
  <u-popup v-model="loginShow" mode="center" border-radius="24" closeable>
    <view class="popview">
      <view class="btn" v-if="checkValue">手机号登录
        <button class="getPhoneNumber" @click="gotoLogin">getPhoneNumber</button>
      </view>
      <view class="btn" v-else @click="showMsg">手机号登录
      </view>
      <view class="check-box">
        <u-checkbox active-color="#FF7373" v-model="checkValue" shape="circle" icon-size="25">
          <view class="main-check flex-mode">
            登录即为同意 <view class="link" style="margin-left: 10rpx;" @click="toAgree(1)">泸州托育用户协议</view>、<view class="link"
              @click="toAgree(2)">泸州托育隐私</view>
          </view>
        </u-checkbox>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
import { ref } from 'vue';
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
let student_id = "";
let inviteparent_id = "";
onLoad(option => {
  student_id = option.student_id;
  inviteparent_id = option.inviteparent_id;
  getDetail();
  getAuth();
})
function gotoLogin() {
  uni.redirectTo({
    url: "/pages_work/login/main"
  })
}
let relation = ref([]);
const invite_str = ref("");
const select_id = ref("");
const hasLogin = ref(false)
const getAuth = async () => {
  if (uni.getStorageSync('token')) {
    await userInfo.refrsh();
    if (userInfo.id !== 0) {
      hasLogin.value = true;
    }
  }
}
const toAgree = (type) => {
  uni.navigateTo({
    url: `/pages_work/login/agreement?type=${type}`
  })
}
function getDetail() {
  uni.showLoading()
  request({
    url: "/api/auth/inviteselect",
    method: 'get',
    data: {
      inviteparent_id,
      student_id
    }
  }).then(res => {
    uni.hideLoading()
    let keys = Object.keys(res.relation_str);
    relation.value = keys.map(item => {
      return {
        id: item,
        text: res.relation_str[item]
      }
    });
    invite_str.value = res.invite_str;
  })
}

async function checkBind() {
  request({
    url: "/api/auth/familybind",
    method: "post",
    data: {
      student_id,
      relationship: select_id.value ?? 17
    }
  }).then(res => {
    uni.showToast({
      title: '绑定成功',
      icon: "success"
    })
    uni.reLaunch({
      url: "/pages_work/index/index"
    })
  })
}
const loginShow = ref(false);
const checkValue = ref(false)
function showMsg() {
  uni.showToast({
    title: "请阅读并同意协议",
    icon: 'none'
  })
}
const getPhoneNumber = (e) => {
  if (e.detail.code) {
    // 展示加载框
    uni.showLoading({
      title: '加载中',
    });
    uni.login({
      provider: 'weixin',
      success: (res) => {
        let code = res.code;
        if (res.errMsg == 'login:ok') {
          request({
            url: `/api/auth/firstlogin?code=${code}`,
          }).then(async (res2) => {
            let openid = res2.openid;
            let codephone = e.detail.code;
            let login_type = 2;
            let checkcode = 0;
            request({
              url: "/api/auth/secondlogin",
              method: 'post',
              data: {
                openid,
                codephone,
                login_type,
                checkcode,
              }
            }).then(res => {
              uni.hideLoading();
              if (res.token) {
                uni.setStorageSync("token", res.token);
                uni.setStorageSync("user_info", res.user);
                let user = res.user;
                userInfo.$patch(user)
                hasLogin.value = true;
                checkBind();
              } else {
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  confirmText: '确认',
                  cancelText: '取消',
                  success: function (resc) {
                    if (resc.confirm) {
                      uni.showLoading();
                      request({
                        url: "/api/auth/loginbindsure",
                        method: 'post',
                        data: {
                          openid: res.data.mini_openid,
                          uid: res.data.uid,
                        }
                      }).then(res3 => {
                        uni.hideLoading();
                        uni.setStorageSync("token", res3.token);
                        uni.setStorageSync("user_info", res3.user);
                        let user = res3.user;
                        userInfo.$patch(user)
                        hasLogin.value = true;
                        checkBind();
                      })
                    }
                  },
                })
              }
            })
          });
        }
        uni.hideLoading()
      },
    });
  }
}
function getImg(id) {
  return `https://obs.tuoyupt.com/miniprogram/client/family/i${id}.png`
}
</script>
<style lang="scss" scoped>
.pages {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;

  .scroll {
    flex: 1;
    min-height: 1rpx;
    position: relative;

    background: linear-gradient(180deg, #FFEBE3 0%, #CED6F7 100%);

    .msg {
      position: absolute;
      top: 290rpx;
      left: 30rpx;
      width: 686rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #787C8D;
      line-height: 40rpx;
    }

    .select {
      position: absolute;
      top: 526rpx;
      left: 70rpx;
      width: 610rpx;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 50%;
        margin-bottom: 40rpx;

        .check {
          padding: 0 12rpx;
          height: 52rpx;
          background: #FF7373;
          border-radius: 26rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 28rpx;
          font-weight: 500;
          color: #FFFFFF;
          margin-left: 32rpx;

          .name {
            max-width: 80rpx;
            white-space: nowrap;
            overflow: hidden;
          }

          .icon {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            width: 28rpx;
            height: 28rpx;
            background: #FFFFFF;
            border-radius: 50%;
            color: #FF7373;
            margin-left: 8rpx;
          }
        }
      }

      .head {
        width: 80rpx;
        height: 80rpx;
      }

      .text {
        font-size: 28rpx;
        font-weight: 500;
        color: #262937;
        margin-left: 32rpx;
      }
    }

    .back {
      width: 100%;
      height: 1436rpx;
    }
  }

  .footer {
    width: 750rpx;
    height: 112rpx;
    background: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;

    .btn {
      width: 690rpx;
      height: 80rpx;
      background: #FF7373;
      border-radius: 16rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 80rpx;
      position: relative;

      .getPhoneNumber {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
      }
    }
  }
}

.popview {
  width: 690rpx;
  padding: 60rpx 30rpx 30rpx;
  box-sizing: border-box;

  .errText {
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .check-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
  }

  .btn {
    width: 600rpx;
    height: 80rpx;
    background: #FF7373;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    line-height: 80rpx;
    position: relative;
    margin: 0 auto;

    .getPhoneNumber {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
    }
  }
}
</style>