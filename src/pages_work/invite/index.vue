<template>
  <view class="pages">
    <u-navbar title="邀请家人" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <image class="poster" :src="imgurl"></image>
    <view class="down" v-if="isWeixinBrowser">
      <text class="ti">长按图片存到相册</text>
    </view>
    <view class="down" @click="down" v-else>
      <image class="icon" src="https://obs.tuoyupt.com/miniprogram/client/images/xia.png">
      </image>
      <text class="ti">存到相册</text>
    </view>
    <view v-if="inviteTemp" class="cleara">
      <l-painter :board="inviteTemp" isCanvasToTempFilePath @success="onImgOK" />
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import templte from "./poster/invite.json";
import templte2 from "./poster/invite2.json";
import request from '@/request';
import { useUserStore } from "@/stores/userInfo.js";
const imgurl = ref("");
const inviteTemp = ref("");
const isWeixinBrowser = /(MicroMessenger)/ig.test(navigator.userAgent);
const userInfo = useUserStore();
onLoad(() => {
  getCode();
});
function getCode() {
  uni.showLoading({
    mask: true
  });
  let newtemp;
  request({
    url: '/api/auth/familyinvite',
    data: {
      student_id: userInfo.student_id
    }
  }).then(res => {
    if (res.img_url) {
      newtemp = templte;
      newtemp.views[1].src = res.img_url.replace('tuoyufuwu.obs.cn-north-4.myhuaweicloud.com', 'obs.tuoyupt.com').replace('zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com', 'obs.tuoyupt.com');
      newtemp.views[2].src = res.wechat_code_url;
      newtemp.views[3].text = res.relation_str;
    } else {
      newtemp = templte2;
      newtemp.views[2].text = res.parent_name.charAt(0);
      newtemp.views[3].src = res.wechat_code_url;
      newtemp.views[4].text = res.relation_str;
    }
    inviteTemp.value = JSON.parse(JSON.stringify(newtemp))
  })
}

function onImgOK(e) {
  imgurl.value = e;
  uni.hideLoading();
}
function down() {

  var arr = imgurl.value.split(',');
  var bytes = atob(arr[1]);
  let ab = new ArrayBuffer(bytes.length);
  let ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  var blob = new Blob([ab], { type: 'application/octet-stream' });
  var url = URL.createObjectURL(blob);

  let downloadLink = document.createElement("a");
  downloadLink.download = "invite.jpg";
  downloadLink.href = url;
  // 触发点击事件以开始下载
  downloadLink.click();

}
function shareWexin() {
  uni.showShareImageMenu({
    path: imgurl.value,
    success(res) {
      uni.showToast({ icon: "none", title: "分享成功" });
    },
  });
}
</script>
<style lang="scss" scoped>
.cleara {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
}

.pages {
  width: 100vw;
  height: 100vh;
  background: #f6f6f6;
  position: relative;

  .poster {
    width: 690rpx;
    height: 838rpx;
    background: #FFFFFF;
    position: absolute;
    top: 180rpx;
    left: 30rpx;
  }

  .icon {
    width: 80rpx;
    height: 80rpx;
  }

  .ti {
    font-size: 24rpx;
    font-weight: 400;
    color: #787C8D;
    margin-top: 20rpx;
  }

  .down {
    width: 96rpx;
    text-align: center;
    position: absolute;
    left: 338rpx;
    top: 1100rpx;
  }
}
</style>