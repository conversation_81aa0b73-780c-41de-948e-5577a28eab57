<template>
  <view class="pages">
    <u-navbar title="选择孩子" ref="navbar" :background="{ background: '#FFFFFF' }" :border-bottom="false"></u-navbar>
    <view class="title">检测到多个孩子</view>
    <view class="msg">您在该班级有{{ list.length }}个宝宝，请选择需要展示的宝宝</view>
    <scroll-view class="scroll">
      <u-radio-group v-model="stu">
        <view class="item" v-for="item in list" :key="item.id">
          <view class="avter">
            <image class="img" :src="item.img_url" v-if="item.img_url"></image>
            <view v-else class="ic">
              {{ item.name.charAt(0) }}
            </view>
          </view>
          <view class="left">
            <view class="name">{{ item.name }}</view>
          </view>
          <view class="check">
            <u-radio shape="square" :name="item.id"></u-radio>
          </view>
        </view>
      </u-radio-group>
    </scroll-view>
    <view class="footer safe-area-inset-bottom">
      <view class="btn" @click="goEdit">
        确定
      </view>
    </view>
  </view>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import request from '@/request';
let parent_id = ""
onLoad((option) => {
  request({
    method: "GET",
    url: "/api/auth/me",
  }).then((res) => {
    parent_id = res.parent.id;
    for (let i = 0; i < res.students.length; i++) {
      if (res.students[i].class_id == option.class_id) {
        list.value.push(res.students[i])
      }
    }
  });
})
const stu = ref("");
const list = ref([]);
function goEdit() {
  if (stu.value == "") {
    uni.showToast({
      title: "请选择一个孩子",
      icon: "none"
    })
  }
  let params = {
    student_id: stu.value,
    parent_id: parent_id
  }
  request({
    method: 'POST',
    url: '/api/auth/changestudent',
    data: params
  }).then((res) => {
    uni.redirectTo({
      url: '/pages_work/mine/child?noback=no'
    })
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  background: #FFFFFF;
  position: relative;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 48rpx;
    font-weight: 500;
    color: #111111;
    padding: 30rpx;
  }

  .msg {
    font-size: 28rpx;
    font-weight: 400;
    color: #787C8D;
    padding: 0 30rpx;
  }

  .scroll {
    flex: 1;
    min-height: 1rpx;

    .item {
      width: 690rpx;
      margin: 0 auto;
      height: 128rpx;
      background: #FFFFFF;
      display: flex;
      flex-direction: row;
      box-sizing: border-box;
      align-items: center;
      border-bottom: 1rpx solid #eee;

      .avter {
        width: 80rpx;
        height: 80rpx;

        .img {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
        }

        .ic {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          background: #617EFB;
          text-align: center;
          line-height: 80rpx;
          color: #FFFFFF;
          font-size: 28rpx;
        }
      }


      .left {
        flex: 1;
        padding-left: 26rpx;

        .name {
          font-size: 28rpx;
          font-weight: 500;
          color: #333333;
        }
      }

      .status {
        width: 80rpx;
        height: 48rpx;
        background: #E5FFF8;
        border-radius: 8rpx;
        font-size: 24rpx;
        text-align: center;
        font-weight: 500;
        color: #0ED5A2;
        line-height: 48rpx;
      }

      .status1 {
        background: #FFF5F5;
        color: #FF7373;
      }

      .status2 {
        background: #FFF5E5;
        color: #FAB13B;
      }
    }
  }

  .footer {
    width: 750rpx;
    background: #FFFFFF;

    .btn {
      margin: 20rpx auto;
      width: 690rpx;
      height: 80rpx;
      background: #FF7373;
      border-radius: 16rpx;
      font-size: 28rpx;
      text-align: center;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 80rpx;
    }
  }
}
</style>