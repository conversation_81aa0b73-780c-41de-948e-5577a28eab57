<template>
  <view>
    <web-view :src="url" :bindmessage="close"></web-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const url = ref('')

function close() {
  uni.navigateBack();
}

onMounted(() => {
  request({
    url: `/api/parent/moblab/list?student_id=${userInfo.student_id ?? ''}`,
  }).then(async res => {
    url.value = res.wen_url
  })
})


</script>


<style lang="scss" scoped></style>