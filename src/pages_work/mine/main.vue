<template>
  <view class="u-page" :style="showPage">
    <view class="top-box">
      <view class="top-box__left">
        <view class="top-box__title">{{
          userInfo.nickname
          }}</view>
        <view class="top-box__subtitle">ID：{{ userInfo.phone }}</view>
      </view>
      <view class="top-box__right" @click="toProfile">
        <view class="top-box__right--box">
          <image class="top-box__right--avatar" style="overflow: visible" :src="userInfo.parent.img_url
            ? userInfo.parent.img_url
            : 'https://tuoyufuwu.obs.cn-north-4.myhuaweicloud.com/miniprogram/client/images/default.png'
            "></image>
        </view>
        <view class="top-box__right--icon">
          <image style="position: absolute" src="https://obs.tuoyupt.com/nanjing/pstac/mine/changeImg.png"></image>
        </view>
      </view>
    </view>
    <view class="switch-box" v-if="curIdentity != 1">
      <view class="switch-box__inner" @click="toChild">
        {{ haveStudent ? curStudent.name : "暂无宝宝信息" }}
        <u-icon name="arrow-right" color="#262937" v-if="curStudent.alert_edit == 1"></u-icon>
        <view class="switch-box__notice" v-if="curStudent.alert_edit == 1">完善宝宝信息</view>
      </view>
      <view class="switch-box__inner1" v-if="haveStudent"> 当前孩子 </view>
      <view class="switch-box__inner2" @click="changeKids" v-if="haveStudent">
        切换
      </view>
    </view>
    <view class="menu-box">
      <view class="muen-item" @click="goMsg">
        <view class="icon">
          <view class="count" v-if="userInfo.msg_count">{{ userInfo.msg_count }}</view>
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jcion6.png">
          </image>
        </view>
        <view class="name">消息中心</view>
      </view>
      <view class="muen-item" @click="goSelfCard">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jcion5.png"></image>
        </view>
        <view class="name">个人名片</view>
      </view>
      <view class="muen-item" @click="goZhuan">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jicon4.png"></image>
        </view>
        <view class="name">我的转介绍</view>
      </view>
      <view class="muen-item" @click="goInvite">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jcion3.png"></image>
        </view>
        <view class="name">邀请家人</view>
      </view>
      <!-- <view class="muen-item">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jcion2.png"></image>
        </view>
        <view class="name">托育券</view>
      </view> -->
      <view class="muen-item" @click="goVisit" v-if="curIdentity == -1">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jcion1.png"></image>
        </view>
        <view class="name">找托育</view>
      </view>
      <view class="muen-item" @click="goFace">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/face/facep.svg"></image>
        </view>
        <view class="name">人脸管理</view>
      </view>
      <view class="muen-item" @click="goChild" v-if="curIdentity == -1">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/index/caicon1.png"></image>
        </view>
        <view class="name">绑定婴幼儿</view>
      </view>
      <view class="muen-item" @click="goZZ" v-if="curIdentity == -1">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/menu/jcion1.png"></image>
        </view>
        <view class="name">找机构新</view>
      </view>
      <view class="muen-item" @click="goTTj" v-if="curIdentity == -1">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/index/caicon2.png"></image>
        </view>
        <view class="name">入托体检</view>
      </view>
      <view class="muen-item" @click="goBjsa" v-if="curIdentity == -1">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/index/caicon3.png"></image>
        </view>
        <view class="name">儿童保健</view>
      </view>
      <view class="muen-item" @click="goYmyy" v-if="curIdentity == -1">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/index/caicon4.png"></image>
        </view>
        <view class="name">疫苗预约</view>
      </view>
      <view class="muen-item" @click="goRtbt" v-if="curIdentity == 2">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/aituoyu/imgs/rutuo.png"></image>
        </view>
        <view class="name">入托补贴</view>
      </view>
      <view class="muen-item" @click="goFp">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/index/caicon4.png"></image>
        </view>
        <view class="name">我的发票</view>
      </view>
      <view class="muen-item" @click="goSmkq">
        <view class="icon">
          <image class="img" src="https://obs.tuoyupt.com/nanjing/icon/kqm.png"></image>
        </view>
        <view class="name">扫码考勤</view>
      </view>
      <view class="muen-item" @click="goApply">
        <view class="icon">
          <view class="count" v-if="userInfo.leave_school === 1">{{ userInfo.leave_school }}</view>
          <image class="img" src="https://doc.ycswjw.com.cn/upload/2025/06/19/u61YaFwpMwhn7e94BrJHmgcIeCUnmkcWGbAAWxsz.png">
          </image>
        </view>
        <view class="name">一键申请</view>
      </view>
      <view class="muen-item" @click="goJob">
        <view class="icon">
          <image class="img"
            src="https://obs.tuoyupt.com/miniprogram/manage/png/1753695625975_求职招才.png">
          </image>
        </view>
        <view class="name">招聘入职</view>
      </view>
      <view class="muen-item" @click="goConsult" v-if="curIdentity == 2">
        <view class="icon">
          <image class="img"
            src="https://doc.ycswjw.com.cn/upload/2025/06/19/u61YaFwpMwhn7e94BrJHmgcIeCUnmkcWGbAAWxsz.png">
          </image>
        </view>
        <view class="name">在线咨询</view>
      </view>
    </view>
    <view class="list-box">
      <view class="list-box__single" @click="toSetup">
        <image class="list-box__icon1" src="https://obs.tuoyupt.com/nanjing/pstac/mine/setup.svg"></image>
        <view class="list-box__name">设置</view>
        <view class="list-box__badge" v-if="unreadcount" style="color: #aeb0bc">
          <view class="dot"></view>问卷调查
        </view>
        <u-icon name="arrow-right"></u-icon>
      </view>
      <view class="list-box__single" @click="toAbout">
        <image class="list-box__icon1" src="https://obs.tuoyupt.com/nanjing/pstac/mine/about.svg"></image>
        <view class="list-box__name">关于</view>
        <view class="list-box__badge" style="color: #aeb0bc">泸州托育</view>
        <u-icon name="arrow-right"></u-icon>
      </view>
      <view class="list-box__single" @click="toService">
        <image class="list-box__icon1" src="https://obs.tuoyupt.com/nanjing/pstac/mine/service.svg"></image>
        <view class="list-box__name">客服</view>
        <!--                <view class="list-box__badge">24</view>-->
        <u-icon name="arrow-right"></u-icon>
      </view>
    </view>
  </view>
  <tabbar :currentTab="3" :border-top="false"></tabbar>
</template>

<script setup>
import { ref } from "vue";
import request from "@/request";
import { onLoad, onShow } from "@dcloudio/uni-app";
import tabbar from "@/components/tabbar.vue";
import { useUserStore } from "@/stores/userInfo.js"
const userInfoStore = useUserStore();
const curStudent = ref("");
const haveStudent = ref(true);
const showEdit = ref(false);

const userInfo = ref({
  nickname: "",
  parent: {
    img_url: ""
  }
});

const curIdentity = ref(1);
const showPage = ref({
  opacity: 0,
});
const modelOpacity = ref({
  opacity: 0,
  zIndex: -1,
});
function goChild() {
  uni.navigateTo({
    url: "/pages/pack/children/index",
  });
}
function goZZ() {
  uni.navigateTo({
    url: "/pages/pack1/school/index",
  });
}
const toProfile = () => {
  uni.navigateTo({
    url: "/pages_work/mine/profile",
  });
};
const toSetup = () => {
  uni.navigateTo({
    url: "/pages_work/mine/setup",
  });
};
const toChild = () => {
  uni.navigateTo({
    url: "/pages_work/mine/child",
  });
};
function goTTj() {
  uni.navigateTo({
    url: "/pages/pack1/physical/index",
  });
}
function goRtbt() {
  uni.navigateTo({
    url: "/pages/pack1/childcare-subsidies/index",
  });
}
function goYmyy() {
  uni.navigateTo({
    url: "/pages/pack1/vaccine/index",
  });
}
function goBjsa() {
  uni.navigateTo({
    url: "/pages/pack1/protection/index",
  });
}
function goSmkq() {
  if (userInfoStore.student_id > 0) {
    //如果有小孩子 判断小孩是否有绑定班级
    if (userInfoStore.class_id > 0) {
      window.location.assign(`/h5/attend/parent.html?student_id=${userInfoStore.student_id}&school_id=${userInfoStore.school_id}&parent_id=${userInfoStore.parent_id}`);
    } else {
      uni.showToast({
        title: "暂无班级信息,请添加后查看。",
        duration: 2000,
        icon: "none",
      });
    }
  } else {
    uni.showToast({
      title: "宝宝暂无班级信息,请添加后查看。",
      duration: 2000,
      icon: "none",
    });
  }
}
function goApply() {
  uni.navigateTo({
    url: "/pages/pack1/apply/index",
  });
}
function goJob() {
  uni.navigateTo({
    url: "/pages/job/index",
  });
}
function goConsult() {
  uni.navigateTo({
    url: "/pages/consult/index",
  });
}
function goFp() {
  if (userInfoStore.student_id > 0) {
    //如果有小孩子 判断小孩是否有绑定班级
    if (userInfoStore.class_id > 0) {
      uni.navigateTo({
        url: "/pages/index/invoice",
      });
    } else {
      uni.showToast({
        title: "暂无班级信息,请添加后查看。",
        duration: 2000,
        icon: "none",
      });
    }
  } else {
    uni.showToast({
      title: "宝宝暂无班级信息,请添加后查看。",
      duration: 2000,
      icon: "none",
    });
  }
}
function goZhuan() {
  if (userInfoStore.student_id > 0) {
    //如果有小孩子 判断小孩是否有绑定班级
    if (userInfoStore.class_id > 0) {
      uni.navigateTo({
        url: "/pages_work/selfcard/introduce",
      });
    } else {
      uni.showToast({
        title: "暂无班级信息,请添加后查看。",
        duration: 2000,
        icon: "none",
      });
    }
  } else {
    uni.showToast({
      title: "宝宝暂无班级信息,请添加后查看。",
      duration: 2000,
      icon: "none",
    });
  }
}
function goFace() {
  if (userInfoStore.student_id > 0) {
    //如果有小孩子 判断小孩是否有绑定班级
    if (userInfoStore.class_id > 0) {
      uni.navigateTo({
        url: "/pages/pack/face/index",
      });
    } else {
      uni.showToast({
        title: "暂无班级信息,请添加后查看。",
        duration: 2000,
        icon: "none",
      });

    }
  } else {
    uni.showToast({
      title: "宝宝暂无班级信息,请添加后查看。",
      duration: 2000,
      icon: "none",
    });
  }
}
function goInvite() {
  if (userInfoStore.student_id > 0) {
    //如果有小孩子 判断小孩是否有绑定班级
    if (userInfoStore.class_id > 0) {
      uni.navigateTo({
        url: "/pages_work/invite/index",
      });
    } else {
      uni.showToast({
        title: "暂无班级信息,请添加后查看。",
        duration: 2000,
        icon: "none",
      });

    }
  } else {
    uni.showToast({
      title: "宝宝暂无班级信息,请添加后查看。",
      duration: 2000,
      icon: "none",
    });
  }
}

const getUser = () => {
  if (!uni.getStorageSync("token")) {
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
    return
  }
  uni.showLoading({
    mask: true,
    title: "加载中",
  });
  userInfoStore.refrsh(true);
  request({
    method: "GET",
    url: "/api/auth/me",
    goLogin: true,
  }).then((res) => {
    curIdentity.value = res.identity;
    uni.setStorageSync("identity", res.identity);
    if (res.students.length > 0) {
      haveStudent.value = true;
      for (let i = 0; i < res.students.length; i++) {
        if (res.students[i].id == res.parent.student_id) {
          curStudent.value = res.students[i];
          // console.log(res.students[i])
          uni.setStorageSync("cur_child", res.students[i]);
          if (res.students[i].alert_edit == 1) {
            showEdit.value = true;
          }
        }
      }
    } else {
      haveStudent.value = false;
      showEdit.value = false;
    }
    uni.setStorageSync("user_info", res);
    userInfo.value = res;
    showPage.value = {
      opacity: 1,
    };
    uni.hideLoading();
  }).catch((error) => {
    uni.hideLoading();
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
  });
};

function goSelfCard() {
  if (userInfoStore.student_id > 0) {
    //如果有小孩子 判断小孩是否有绑定班级
    if (userInfoStore.class_id > 0) {
      uni.navigateTo({
        url: "/pages_work/selfcard/detail"
      });
    } else {
      uni.showToast({
        title: "宝宝暂无班级信息,请添加后查看。",
        duration: 2000,
        icon: "none",
      });
    }
  } else {
    uni.showToast({
      title: "宝宝暂无班级信息,请添加后查看。",
      duration: 2000,
      icon: "none",
    });
  }
}
function goMsg() {
  uni.redirectTo({
    url: "/pages_work/message/index",
  })
}
function goVisit() {
  uni.navigateTo({
    url: '/pages_work/visit/index?type=2'
  })
}
function toAbout() {
  uni.navigateTo({
    url: "/pages_work/mine/about",
  })
}
function toService() {
  uni.navigateTo({
    url: "/pages_work/mine/service",
  })
}
const changeKids = () => {
  uni.navigateTo({
    url: "/pages_work/mine/change",
  });
  modelOpacity.value = {
    opacity: 1,
    zIndex: 100,
  };
};
onLoad(() => {
  getcount()
})
onShow(() => {
  getUser();
});
let unreadcount = ref(0)
function getcount() {
  if (userInfo.school_id) {
    request({
      url: `/api/parent/jyform/unreadcount`,
      data: {
        school_id: userInfo.school_id
      }
    }).then(res => {
      unreadcount.value = res;
      uni.setStorageSync("unreadcount", res)
    })
  }
}

</script>

<style scoped lang="scss">
.u-page {
  opacity: 0;
  transition: 0.5s all;
  background: #f6f6f6;
  padding-top: 40rpx;
  min-height: calc(100vh - 120rpx - constant(safe-area-inset-bottom));
  min-height: calc(100vh - 120rpx - env(safe-area-inset-bottom));
}

.top-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 60rpx;
}

.top-box__left {
  display: flex;
  flex-direction: column;
}

.top-box__title {
  font-size: 56rpx;
  font-weight: 500;
  color: #000000;
  line-height: 56rpx;
}

.top-box__subtitle {
  margin-top: 30rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #aeb0bc;
  line-height: 28rpx;
}

.top-box__right {
  position: relative;
}

.top-box__right--box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 144rpx;
  height: 144rpx;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.top-box__right--avatar {
  width: 144rpx;
  height: 144rpx;
}

.top-box__right--icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background: #ffffff;
  box-shadow: 0rpx 12rpx 16rpx -4rpx rgba(0, 0, 0, 0.16);
  border-radius: 50%;
}

.top-box__right--icon image {
  width: 24rpx;
  height: 24rpx;
}

.switch-box {
  position: relative;
  margin: 60rpx 30rpx 0;
  width: 690rpx;
  height: 160rpx;
  border-radius: 24rpx;
  background: linear-gradient(270deg, #FBF1E1 0%, #FFDCBF 100%);
}

.switch-box__inner {
  display: flex;
  align-items: center;
  position: absolute;
  top: 38rpx;
  left: 30rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: #262937;
  line-height: 36rpx;
  letter-spacing: 1rpx;
}

.switch-box__inner1 {
  position: absolute;
  top: 84rpx;
  left: 30rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #787C8D;
  line-height: 20rpx;
}

.switch-box__inner2 {
  position: absolute;
  top: 48rpx;
  right: 30rpx;
  text-align: center;
  width: 96rpx;
  height: 48rpx;
  background: #ffffff;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #ff7373;
  line-height: 48rpx;
}

.switch-box__notice {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  width: 148rpx;
  height: 36rpx;
  background: #35180B;
  border-radius: 16rpx 16rpx 16rpx 0rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 400;
  color: #FFE5D0;
  line-height: 36rpx;
}

.menu-box {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 40rpx auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .muen-item {
    width: 25%;
    height: 172rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      position: relative;
      width: 56rpx;
      height: 56rpx;

      .count {
        position: absolute;
        width: 28rpx;
        height: 28rpx;
        font-size: 20rpx;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 28rpx;
        text-align: center;
        right: -14rpx;
        top: -14rpx;
        background: #FF0000;
        border-radius: 50%;
      }

      .img {
        width: 56rpx;
        height: 56rpx;
      }
    }

    .name {
      font-size: 24rpx;
      font-weight: 500;
      color: #262937;
      margin-top: 18rpx;
    }
  }
}

.list-box {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 40rpx auto;
}

.list-box__single {
  padding: 0 30rpx;
  height: 100rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-box__name {
  margin: 0 auto 0 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #262937;
  line-height: 40rpx;
}

.list-box__badge {
  margin-right: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ff7373;
  line-height: 40rpx;
  position: relative;

  .dot {
    position: absolute;
    left: -18rpx;
    top: 22rpx;
    width: 10rpx;
    height: 10rpx;
    border-radius: 50%;
    background: #ff7373;
  }
}

.list-box__icon {
  width: 48rpx;
  height: 48rpx;
}

.list-box__icon1 {
  width: 48rpx;
  height: 48rpx;
}
</style>