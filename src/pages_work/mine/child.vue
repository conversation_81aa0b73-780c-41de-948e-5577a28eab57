<template>
    <view class="pages">
        <u-navbar title="编辑信息" ref="navbar" :is-back="isBack" :background="{ background: '#F6F6F6' }"
            :border-bottom="false"></u-navbar>
        <view class="formse">
            <scroll-view :scroll-y="true" class="form">
                <u-form class="form" :border-bottom="false" :model="info" ref="formRef" label-position="top">
                    <view class="section">
                        <view class="img_url">
                            <view class="lable">头像</view>
                            <view class="isection">
                                <image class="img" v-if="info.img_url" :src="info.img_url"></image>
                                <image class="img" v-else
                                    src="https://obs.tuoyupt.com/miniprogram/client/images/default.png">
                                </image>
                                <button class="btn" @click="chooseavatar">chooseavatar</button>
                            </view>
                        </view>
                        <u-form-item :border-bottom="false" label="姓名">
                            <text class="disable">{{ info.name }}</text>
                        </u-form-item>
                        <u-form-item :border-bottom="false" label="性别" right-icon="arrow-right">
                            <text class="disable">{{ info.sexName }}</text>
                        </u-form-item>
                        <u-form-item :border-bottom="false" label="出生日期" right-icon="arrow-right">
                            <text class="disable">{{ info.birth }}</text>
                        </u-form-item>
                        <u-form-item :border-bottom="false" label="身份证号">
                            <text class="disable" v-if="disIdCode">{{ info.id_code }}</text>
                            <u-input placeholder="请输入" v-else v-model="info.id_code" />
                        </u-form-item>
                    </view>
                    <view class="section">
                        <u-form-item :border-bottom="false" label="籍贯"><u-input placeholder="请输入"
                                v-model="info.native_place" /></u-form-item>
                        <u-form-item :border-bottom="false" label="家庭住址"><u-input placeholder="请输入"
                                v-model="info.address" /></u-form-item>
                        <u-form-item :border-bottom="false" label="入园日期">
                            <text class="disable"> {{ info.join_date }}</text>
                        </u-form-item>
                    </view>
                    <view class="section">
                        <u-form-item :border-bottom="false" label="身高(cm)"><u-input placeholder="请输入"
                                v-model="info.height" type="number" /></u-form-item>
                        <u-form-item :border-bottom="false" label="体重(kg)"><u-input placeholder="请输入"
                                v-model="info.weight" type="number" /></u-form-item>
                        <u-form-item :border-bottom="false" label="血型"><u-input placeholder="请输入"
                                v-model="info.blood_type" disabled @click="selectblood" /></u-form-item>
                    </view>
                    <view class="ti">健康信息</view>
                    <view class="tis">婴幼儿病史、食物禁忌、过敏源信息提交后不可更改，如需修改请联系班级老师。</view>
                    <view class="section">
                        <u-form-item :border-bottom="false" label="食物过敏" right-icon="arrow-right">
                            <text class="disable" v-if="disFood"> {{ info.allergy_food }}</text>
                            <u-input v-else placeholder="请选择" disabled @click="selectAllergy(1)"
                                v-model="info.allergy_food" />
                        </u-form-item>
                        <u-form-item :border-bottom="false" label="过敏原" right-icon="arrow-right">
                            <text class="disable" v-if="disSource"> {{ info.allergy_source }}</text>
                            <u-input v-else placeholder="请选择" disabled @click="selectAllergy(2)"
                                v-model="info.allergy_source" /></u-form-item>
                        <u-form-item :border-bottom="false" label="过敏史"><u-input placeholder="请输入"
                                v-model="info.allergy.allergy_history" maxlength="300" type="textarea" :height="70"
                                cursor-spacing="100" :auto-height="true" /></u-form-item>
                        <u-form-item :border-bottom="false" label="病史"><u-input placeholder="请输入"
                                v-model="info.allergy.medical_history" maxlength="300" type="textarea" :height="70"
                                cursor-spacing="100" :auto-height="true" /></u-form-item>
                        <u-form-item :border-bottom="false" label="家族病史"><u-input placeholder="请输入"
                                v-model="info.allergy.medical_family" maxlength="300" type="textarea" :height="70"
                                cursor-spacing="100" :auto-height="true" /></u-form-item>
                    </view>
                </u-form>
            </scroll-view>
        </view>
        <u-popup v-model="popupShow" :mask="false" mode="bottom" safe-area-inset-bottom>
            <view class="pop-view" :style="{ height: scorllHeight }">
                <view class="top">
                    <view class="cloi" @click="popupShow = false">
                        <u-icon name="close" color="#111111" size="28"></u-icon>
                    </view>
                    <view class="title">{{ popTitle }}</view>
                    <view class="btn" @click="popupConfirm">确定</view>
                </view>
                <view class="search">
                    <u-search height="80" :clearabled="true" shape="round" bg-color="#F6F6F6" placeholder="搜索"
                        :show-action="false" v-model="keyword" @clear="search" @search="search"></u-search>
                </view>
                <view class="center">
                    <scroll-view scroll-y="true" class="type">
                        <view class="titem" @click="currentOption = item"
                            :class="{ active: item.class == currentOption.class }" v-for="item in allOption"
                            :key="item.id">
                            {{ item.class_title }}
                        </view>
                    </scroll-view>
                    <scroll-view scroll-y="true" class="check">
                        <view class="citem" @click="selectItem(item)" :class="{ active: checkSelect(item) }"
                            v-for="item in currentOption.list" :key="item.id">
                            {{ item.allergy_title }}
                        </view>
                    </scroll-view>
                </view>
                <view class="footer-p">
                    <scroll-view scroll-x="true" enhanced :show-scrollbar="false" class="checked">
                        <view class="citem" v-for="item in selected" :key="item">
                            {{ item }} <u-icon color="#787C8D" @click="selectItem1(item)"
                                name="close-circle-fill"></u-icon>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </u-popup>
        <view class="footer">
            <view class="btn" @click="submit">
                确定
            </view>
        </view>
    </view>
    <u-select @confirm="confirmSelect" v-model="selectShow" :list="selectEumn"></u-select>
</template>
<script setup>
import { ref } from 'vue';
import request from '@/request';
import { onLoad } from '@dcloudio/uni-app';
import { OBSupload } from '@/utils/obs/obs-upload.js'
let systemInfo = uni.getSystemInfoSync();
let height = systemInfo.platform == 'ios' ? 44 : 48;
let statusBarHeight = systemInfo.statusBarHeight;
const scorllHeight = `calc(100vh - 80rpx - ${height}px - ${statusBarHeight}px)`;
const isBack = ref(true);
const disSource = ref(false);
const disFood = ref(false);
const disIdCode = ref(false);
// 初始化
onLoad((option) => {
    if (option.noback == 'no') {
        isBack.value = false;
    }
    uni.showLoading({
        mask: true,
        title: "加载中",
    });
    request({
        method: "GET",
        url: "/api/auth/me",
    }).then((res) => {
        for (let i = 0; i < res.students.length; i++) {
            if (res.students[i].id == res.parent.student_id) {
                info.value = res.students[i];
                info.value.sexName = res.students[i].sex == 1 ? '男' : '女';
                info.value.allergy_food = res.students[i].allergy_food ?? "";
                if (info.value.id_code) {
                    disIdCode.value = true;
                }
                if (info.value.allergy_food) {
                    disFood.value = true;
                }
                info.value.allergy_source = res.students[i].allergy_source ?? "";
                if (info.value.allergy_source) {
                    disSource.value = true;
                }
            }
        }
        uni.hideLoading();
    });
    getPrepare();
})
const info = ref({
    img_url: "",
    name: "",
    sex: "",
    sexName: "男",
    birth: "",
    native_place: "",
    address: "",
    weight: "",
    height: "",
    allergy: {
        medical_family: "",
        medical_history: "",
        allergy_history: "",
    }
})
function chooseavatar() {
    uni.chooseImage({
        count: 1, //总限制5张
        sizeType: ["original", "compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], //从相册选择
        success: (res) => {
            let path = res.tempFilePaths[0];
            let fileName = path.substring(path.lastIndexOf("/") + 1);
            let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
            OBSupload(path, fileExtension, fileName)
                .then(res => {
                    info.value.img_url = res;
                })
        }
    });
}
const selectEumn = ref([
    {
        label: "A",
        value: 'A'
    }, {
        label: "B",
        value: 'B'
    }, {
        label: "AB",
        value: 'AB'
    }, {
        label: "O",
        value: 'O'
    }, {
        label: "Rh阴性",
        value: 'Rh阴性'
    }, {
        label: "Rh阳性",
        value: 'Rh阳性'
    }
])
const selectShow = ref(false);
function confirmSelect(e) {
    info.value.blood_type = e[0].value;
}
function selectblood() {
    selectShow.value = true;
}
// 提交
function submit() {
    uni.showLoading({
        mask: true
    });
    request({
        url: `/api/auth/editstudent`,
        method: "post",
        data: {
            student_id: info.value.id,
            img_url: info.value.img_url,
            allergy: JSON.stringify(info.value.allergy),
            native_place: info.value.native_place,
            height: info.value.height,
            weight: info.value.weight,
            address: info.value.address,
            id_code: info.value.id_code,
            blood_type: info.value.blood_type,
            allergy_source: info.value.allergy_source,
            allergy_food: info.value.allergy_food,
        },
    }).then(() => {
        uni.hideLoading();
        uni.showToast({
            title: "操作成功！",
            icon: "none",
            mask: true,
            duration: 1500
        })
        if (isBack.value) {
            setTimeout(uni.navigateBack, 1500);
        } else {
            setTimeout(() => {
                uni.reLaunch({
                    url: "/pages_work/mine/main"
                })
            }, 1500);
        }
    })
}
// 获取枚举值
// 过敏原选择
let selectallergyType = 1;
function selectAllergy(type) {
    selectallergyType = type;
    if (type == 1) {
        allOption.value = allergy_food;
        selected.value = info.value.allergy_food.split(',').filter(item => item != '');
        popTitle.value = "食物禁忌";
    } else if (type == 2) {
        allOption.value = allergy_source;
        selected.value = info.value.allergy_source.split(',').filter(item => item != '');
        popTitle.value = "过敏原";
    }
    currentOption.value = JSON.parse(JSON.stringify(allOption.value[0]));
    popupShow.value = true;
}
const popupShow = ref(false);
const keyword = ref("");
function search() {
    if (keyword.value !== "") {
        let hasR = false;
        if (selectallergyType == 1) {
            for (let index = 0; index < allergy_food.length; index++) {
                const element = allergy_food[index];
                let arr = element.list.filter(it => {
                    return it.allergy_title.indexOf(keyword.value) > -1
                })
                if (arr.length > 0) {
                    currentOption.value = JSON.parse(JSON.stringify(element))
                    hasR = true;
                    break;
                }
            }
        } else {
            for (let index = 0; index < allergy_source.length; index++) {
                const element = allergy_source[index];
                let arr = element.list.filter(it => {
                    return it.allergy_title.indexOf(keyword.value) > -1
                })
                if (arr.length > 0) {
                    currentOption.value = JSON.parse(JSON.stringify(element))
                    hasR = true;
                    break;
                }
            }
        }
        if (!hasR) {
            uni.showToast({
                title: "无搜索结果",
                icon: "none"
            })
        }
    }
}
const popTitle = ref("")
function popupConfirm() {
    if (selectallergyType == 1) {
        info.value.allergy_food = selected.value.join(',');
    } else if (selectallergyType == 2) {
        info.value.allergy_source = selected.value.join(',');
    }
    popupShow.value = false;
}
const allOption = ref([]);
let allergy_food = [];
let allergy_source = []
const currentOption = ref({});
const selected = ref([]);
function checkSelect(it) {
    let index = selected.value.findIndex(item => item == it.allergy_title);
    return index > -1
}
function selectItem(it) {
    let index = selected.value.findIndex(item => item == it.allergy_title);
    if (index > -1) {
        selected.value.splice(index, 1);
    } else {
        selected.value.push(it.allergy_title)
    }
}
function selectItem1(it) {
    let index = selected.value.findIndex(item => item == it);
    selected.value.splice(index, 1);
}
function getPrepare() {
    uni.showLoading();
    let res = uni.getStorageSync("studentallergyselect");
    if (res) {
        allergy_food = res.allergy_food;
        allergy_source = res.allergy_source;
        uni.hideLoading();
    } else {
        request({
            url: `/api/auth/allergyselect`,
            method: "post",
        }).then(res => {
            allergy_food = res.allergy_food;
            allergy_source = res.allergy_source;
            uni.hideLoading();
        })
    }
}
</script>
<style lang="scss" scoped>
.pages {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f6f6f6;
    position: relative;


    .formse {
        flex: 1;
        overflow: hidden;
        padding-bottom: 20rpx;
    }

    .form {
        height: 100%;

        .ti {
            font-size: 28rpx;
            font-weight: 500;
            color: #000000;
            padding: 4rpx 30rpx;
        }

        .tis {
            font-size: 24rpx;
            font-weight: 500;
            color: #787C8D;
            padding: 4rpx 30rpx;
        }

        .section {
            width: 690rpx;
            background: #FFFFFF;
            margin: 20rpx 30rpx;
            border-radius: 24rpx;
            padding: 0rpx 30rpx;

            .disable {
                font-size: 28rpx;
                font-weight: 500;
                color: #787C8D;
            }

            .img_url {
                display: flex;
                flex-direction: row;
                height: 128rpx;
                align-items: center;
                justify-content: space-between;

                .lable {
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #AEB0BC;
                }

                .isection {
                    position: relative;
                    width: 80rpx;
                    height: 80rpx;

                    .img {
                        width: 80rpx;
                        height: 80rpx;
                        border-radius: 40rpx;
                    }

                    .btn {
                        position: absolute;
                        left: 0;
                        top: 0;
                        height: 0;
                        width: 80rpx;
                        height: 80rpx;
                        opacity: 0;
                        z-index: 2;
                    }
                }
            }

            ::v-deep.u-form-item {
                line-height: 50rpx !important;
            }
        }
    }

    .footer {
        width: 750rpx;
        height: calc(100rpx + env(safe-area-inset-bottom));
        background: #f6f6f6;

        .btn {
            width: 690rpx;
            height: 80rpx;
            background: #FF7373;
            border-radius: 16rpx;
            margin: 10rpx auto;
            font-size: 28rpx;
            text-align: center;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 80rpx;
        }
    }
}

.checkbox {
    width: 100%;
    padding: 30rpx 30rpx 0;
    box-sizing: border-box;
}

.pop-view {
    width: 100%;
    padding: 0rpx 30rpx;
    box-sizing: border-box;

    .top {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;

        .cloi {
            width: 60rpx;
        }

        .title {
            font-size: 32rpx;
            font-weight: 500;
            color: #262937;
        }

        .btn {
            font-size: 32rpx;
            font-weight: 500;
            color: #617EFB;
        }
    }

    .center {
        width: 100%;
        display: flex;
        flex-direction: row;
        height: calc(100% - 270rpx);
        padding-top: 40rpx;

        .type {
            width: 212rpx;
            height: 100%;

            .titem {
                width: 212rpx;
                height: 60rpx;
                background: #FFFFFF;
                text-align: center;
                line-height: 60rpx;
                color: #262937;
                font-size: 24rpx;
                margin-bottom: 24rpx;
            }

            .active {
                color: #FFFFFF;
                background: #262937;
                border-radius: 8rpx;
            }
        }

        .check {
            flex: 1;
            height: 100%;
            padding-left: 20rpx;

            .citem {
                display: inline-block;
                width: 212rpx;
                height: 60rpx;
                background: #F6F6F6;
                border-radius: 8rpx;
                text-align: center;
                font-size: 24rpx;
                font-weight: 400;
                color: #262937;
                line-height: 60rpx;
                margin-bottom: 24rpx;

                &:nth-child(odd) {
                    margin-right: 28rpx;
                }
            }

            .active {
                background: #E0E5FF;
                border-radius: 8rpx;
                border: 2rpx solid #617EFB;
                color: #617EFB;
            }
        }
    }

    .footer-p {
        width: 100%;
        background: #FFFFFF;
        padding-top: 30rpx;
        box-sizing: border-box;

        .checked {
            width: 100%;

            .citem {
                width: 136rpx;
                height: 60rpx;
                background: #F6F6F6;
                border-radius: 8rpx;
                text-align: center;
                font-size: 28rpx;
                font-weight: 500;
                color: #262937;
                line-height: 60rpx;
                display: inline-block;
                margin-right: 20rpx;
            }
        }
    }
}
</style>