<template>
  <view class="pages">
    <u-navbar title="客服" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="content">
      <view class="top">
        <image class="qr" src="https://obs.tuoyupt.com/aituoyu/imgs/kfqr.png">
        </image>
        <view class="t">微信扫码添加客服</view>
      </view>
      <view class="phone">
        <view>
          <view class="t1">
            客服电话
          </view>
          <view class="t2" @click="callPhone">
            010-57182596
          </view>
        </view>
        <u-icon name="arrow-right"></u-icon>
      </view>
    </view>
    <view class="yj" @click="goFK">行业信箱<u-icon name="arrow-right"></u-icon></view>
  </view>
</template>
<script setup>
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: '010-57182596'
  })
}
function goFK() {
  uni.navigateTo({
    url: "/pages_work/mine/feedback"
  })
}

</script>
<style scoped lang="scss">
.pages {
  background: #F6F6F6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content {
    width: 690rpx;
    height: 734rpx;
    background: #EEEEEE;
    border-radius: 24rpx;
    overflow: hidden;

    .top {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 594rpx;

      .qr {
        width: 360rpx;
        height: 360rpx;
      }

      .t {
        font-size: 32rpx;
        font-weight: 500;
        color: #787C8D;
        margin-top: 30rpx;
      }
    }

    .phone {
      background: #FFFFFF;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 30rpx;
      height: 140rpx;
      justify-content: space-between;

      .t1 {
        font-size: 28rpx;
        font-weight: 500;
        color: #787C8D;
      }

      .t2 {
        font-size: 32rpx;
        font-weight: 500;
        color: #262937;
      }
    }
  }

  .yj {
    font-size: 28rpx;
    font-weight: 500;
    color: #AEB0BC;
    margin-top: 320rpx;
  }
}
</style>