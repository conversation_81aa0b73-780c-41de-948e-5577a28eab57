<template>
  <u-navbar title="意见反馈" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
  <view class="form">
    <view class="section">
      <view class="label">
        反馈类型
      </view>
      <view class="btns">
        <view class="btn" :class="{ active: type === 1 }" @click="type = 1">教学类</view>
        <view class="btn btn1" :class="{ active: type === 2 }" @click="type = 2">环境类</view>
        <view class="btn btn1" :class="{ active: type === 3 }" @click="type = 3">安全类</view>
        <view class="btn btn1" :class="{ active: type === 4 }" @click="type = 4">其他类</view>
      </view>
    </view>
    <view class="section">
      <view class="label">
        反馈描述
      </view>
      <view class="textarea">
        <u-input maxlength="200" :disabled="pageReadonly" v-model="text" type="textarea" height="200rpx"
          :auto-height="true" />
      </view>
    </view>
    <view class="section">
      <view class="label">
        上传图片
      </view>
      <view class="textarea" v-if="pageReadonly">
        <img @click="previewMedia(index)" :src="item"
          style="width: 156rpx; height: 156rpx; margin: rpx; object-fit: cover; border: 8rpx;"
          v-for="(item, index) in imgList" alt="">
      </view>
      <view class="textarea" v-else>
        <u-upload width="156" max-count="6" height="156" @on-list-change="imgChange" multiple deletable
          :auto-upload="false">
        </u-upload>
      </view>
    </view>
    <view class="section" v-if="pageReadonly">
      <view class="label">
        处理状态：
        <u-button type="warning" size="mini" shape="square" v-if="status == 0">未查阅</u-button>
        <u-button type="success" size="mini" shape="square" v-if="status == 3">已知悉</u-button>
      </view>
    </view>
    <view class="section" v-if="pageReadonly">
      <view class="label">
        处理意见
      </view>
      <view class="textarea">
        {{ reply_content || '-' }}
      </view>
    </view>
  </view>
  <view class="list-box">
    <u-button class="center" @click="back" v-if="pageReadonly">
      返回
    </u-button>
    <u-button class="center" type="primary" @click="submit" v-else>
      提交
    </u-button>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { OBSupload } from '@/utils/obs/obs-upload.js'
import { onLoad, onShow } from "@dcloudio/uni-app";
import request from "@/request";
const text = ref('')
const type = ref(1)
let status = ref(0);
let imgList = ref([]);
let reply_content = ref('');
function imgChange(e) {
  imgList.value = e;
}
function previewMedia(index) {
  uni.previewImage({
    urls: imgList.value,
    showmenu: true,
    current: imgList.value[index]
  })
}
async function submit() {
  if (text.value == '') {
    uni.showToast({
      title: "请输入内容",
      icon: "none"
    })
    return
  }
  let imgs = [];
  if (imgList.value.length) {
    let requestArr = []
    imgList.value.forEach(item => {
      let fileName = item.file.name.substring(item.file.name.lastIndexOf("/") + 1);
      let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      requestArr.push(OBSupload(item.url, fileExtension, fileName))
    })
    imgs = await Promise.all(requestArr);
  }
  uni.showLoading();
  request({
    url: `/api/parent/jyfeedback/add`,
    method: "post",
    data: {
      type: type.value,
      content: text.value,
      image_url: imgs.join(',')
    },
  }).then(() => {
    uni.hideLoading();
    uni.showToast({
      title: "操作成功！",
      icon: "none",
      mask: true,
      duration: 1500
    });
    uni.navigateBack();
  })
}
const back = () => {
  uni.navigateBack();
};

const getData = async (id) => {
  uni.showLoading();
  const res = await request({
    url: `/api/parent/jyfeedback/detail?id=${id}`,
  });
  if (res) {
    imgList.value = res.image_url.split(',');
    text.value = res.content;
    reply_content.value = res.reply_content;
    status.value = res.status;
  }
  uni.hideLoading();
}

let pageId = ref('');
let pageReadonly = ref(false);
onLoad(async (options) => {
  pageId.value = options.id;
  if (options.id) {
    pageId.value = options.id;
    await getData(options.id);
    pageReadonly.value = true;
  } else {
    pageReadonly.value = false;
  }
});
</script>

<style>
page {
  background: #F6F6F6
}
</style>

<style lang="scss" scoped>
.form {
  width: 690rpx;
  margin: 0rpx auto;

  .section {
    margin-top: 40rpx;

    .label {
      height: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #787C8D;
    }

    .textarea {
      width: 690rpx;
      background: #FFFFFF;
      border-radius: 20rpx;
      margin-top: 24rpx;
      padding: 30rpx;
      min-height: 188rpx;
    }

    .btns {
      display: flex;
      flex-direction: row;
      margin-top: 24rpx;

      .btn {
        line-height: 66rpx;
        height: 66rpx;
        background: #EEEEEE;
        border-radius: 34rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
      }

      .btn1 {
        margin-left: 24rpx;
      }

      .active {
        background: #000000;
        color: #FFFFFF;
      }
    }
  }
}

.list-box {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 40rpx auto;


  .center {
    width: 690rpx;
    text-align: center;
  }
}
</style>