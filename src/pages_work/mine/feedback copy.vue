<template>
  <u-navbar title="意见反馈" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
  <view class="form">
    <view class="section">
      <view class="label">
        反馈类型
      </view>
      <view class="btns">
        <view class="btn" :class="{ active: type === 0 }" @click="type = 0">功能异常</view>
        <view class="btn btn1" :class="{ active: type === 1 }" @click="type = 1">意见与建议</view>
      </view>
    </view>
    <view class="section">
      <view class="label">
        问题描述
      </view>
      <view class="textarea">
        <u-input maxlength="200" v-model="text" type="textarea" height="200rpx" :auto-height="true" />
      </view>
    </view>
    <view class="section">
      <view class="label">
        上传问题截图
      </view>
      <view class="textarea">
        <u-upload width="156" max-count="6" height="156" @on-list-change="imgChange" multiple deletable
          :auto-upload="false">
        </u-upload>
      </view>
    </view>
  </view>
  <view class="list-box">
    <view class="center" @click="submit">
      提交
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { OBSupload } from '@/utils/obs/obs-upload.js'
import request from "@/request";
const text = ref('')
const type = ref(0)
let imgList = [];
function imgChange(e) {
  imgList = e;
}
async function submit() {
  if (text.value == '') {
    uni.showToast({
      title: "请输入内容",
      icon: "none"
    })
    return
  }
  let imgs = [];
  if (imgList.length) {
    let requestArr = []
    imgList.forEach(item => {
      let fileName = item.file.name.substring(item.file.name.lastIndexOf("/") + 1);
      let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      requestArr.push(OBSupload(item.url, fileExtension, fileName))
    })
    imgs = await Promise.all(requestArr);
  }
  uni.showLoading();
  request({
    url: `/api/auth/feedback`,
    method: "post",
    data: {
      type: type.value,
      content: text.value,
      images: imgs
    },
  }).then(() => {
    uni.hideLoading();
    uni.showToast({
      title: "操作成功！",
      icon: "none",
      mask: true,
      duration: 1500
    })
    setTimeout(uni.navigateBack, 1500);
  })
}
</script>

<style>
page {
  background: #F6F6F6
}
</style>

<style lang="scss" scoped>
.form {
  width: 690rpx;
  margin: 0rpx auto;

  .section {
    margin-top: 40rpx;

    .label {
      height: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #787C8D;
    }

    .textarea {
      width: 690rpx;
      background: #FFFFFF;
      border-radius: 20rpx;
      margin-top: 24rpx;
      padding: 30rpx;
      min-height: 188rpx;
    }

    .btns {
      display: flex;
      flex-direction: row;
      margin-top: 24rpx;

      .btn {
        line-height: 66rpx;
        height: 66rpx;
        background: #EEEEEE;
        border-radius: 34rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
      }

      .btn1 {
        margin-left: 24rpx;
      }

      .active {
        background: #000000;
        color: #FFFFFF;
      }
    }
  }
}

.list-box {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 40rpx auto;


  .center {
    width: 690rpx;
    text-align: center;
    height: 100rpx;
    line-height: 100rpx;
    font-weight: 600;
    color: #262937;
  }
}
</style>