<template>
    <view class="pages">
        <u-navbar title="关于我们" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
        <image class="main-logo" src="https://obs.tuoyupt.com/aituoyu/logo/jiazhang.png"></image>
        <view class="title">泸州市智慧托育服务系统</view>
        <view class="version">当前版本：v{{ version }}</view>
        <!-- <view class="text">中国人口学会托育服务分会（英文名称:Childcare Services of China
            PopulationAssociation)是中国人口学会的组成部分，接受中国人口学会领导，是由从事3岁以下婴幼儿照护服务与托育服务领域理论研究的单位、部门、科研工作者、实际从业者、企业机构自愿结成的全国性、非营利性、学术性、行业性社会组织。
        </view>
        <view class="btn" @click="goGuan">
            <view class="list-box__name">访问官网</view>
            <u-icon name="arrow-right"></u-icon>
        </view> -->
    </view>
</template>
<script setup>
import { ref } from "vue";
import request from "@/request";
const version = ref('')
request({
    method: 'get',
    url: '/api/auth/aboutus'
}).then((res) => {
    version.value = res.version
})
function goGuan() {
    uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent('https://tuoyupt.com/')}`
    })
}
</script>
<style scoped lang="scss">
.pages {
    background: #F6F6F6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;

    .main-logo {
        width: 160rpx;
        height: 160rpx;
        margin-top: 40rpx;
        border-radius: 50%;
    }

    .title {
        font-size: 32rpx;
        font-weight: 600;
        color: #262937;
        margin-top: 40rpx;
    }

    .version {
        font-size: 28rpx;
        font-weight: 400;
        color: #AEB0BC;
        margin-top: 8rpx;
    }

    .text {
        margin-top: 40rpx;
        width: 690rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #787C8D;
        line-height: 44rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        box-sizing: border-box;
        padding: 40rpx 30rpx;
    }

    .btn {
        width: 690rpx;
        padding: 0 30rpx;
        height: 100rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #FFFFFF;
        border-radius: 24rpx;
        margin-top: 40rpx;

        .list-box__name {
            font-size: 28rpx;
            font-weight: 600;
            color: #262937;
            line-height: 40rpx;
        }
    }
}
</style>