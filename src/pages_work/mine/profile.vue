<template>
    <u-navbar title="个人信息" :background="navBackground" :border-bottom="false"></u-navbar>
    <view class="box1" :style="showPage">
        <view class="box-single1">
            <view class="box-single__label" style="margin-top: 0">头像</view>
            <view class="isection">
                <image class="img" v-if="avatar" :src="avatar"></image>
                <view class="defaltavatar" v-else>
                    {{ nickname.charAt(0) }}
                </view>
                <button class="btn" @click="chooseavatar">chooseavatar</button>
            </view>
        </view>
        <u-divider :use-slot="false" half-width="100%" margin-top="28"></u-divider>
        <view class="box-single__label">姓名</view>
        <view class="box-single">
            <u-input v-model="nickname" placeholder="请填写" :border-bottom="false" :field-style="inputStyle"
                @blur="saveName"></u-input>
        </view>
        <u-divider :use-slot="false" half-width="100%"></u-divider>
        <view class="box-single" @click="selectSex">
            <view class="box-single__label">性别</view>
            <u-select v-model="sexShow" :list="sexList" @confirm="sexChange"></u-select>
            <!--              <view class="box-single__detail">{{ userInfo.parent.sex == 1 ? '男' : '女' }}</view>-->
            <view class="box-single1">
                <view class="box-single__detail" style="margin-top: 0">{{ selectedSex }}</view>
                <u-icon name="arrow-right"></u-icon>
            </view>
        </view>
        <u-divider :use-slot="false" half-width="100%"></u-divider>
        <view class="box-single" @click="selectRelation" v-if="curIdentity != 1">
            <view class="box-single__label">与孩子关系</view>
            <u-select v-model="relationShow" :list="relationList" @confirm="relationChange"></u-select>
            <view class="box-single1">
                <view class="box-single__detail" style="margin-top: 0">{{ selectedRelation }}</view>
                <u-icon name="arrow-right"></u-icon>
            </view>
        </view>
    </view>
    <view class="box1" :style="showPage" style="margin-top: 20rpx">
        <view class="box-single">
            <view class="box-single__label">联系电话</view>
            <view class="box-single__detail">{{ userInfo.phone ? userInfo.phone : '暂无数据' }}</view>
        </view>
        <u-divider :use-slot="false" half-width="100%" margin-top="28"></u-divider>
        <view class="box-single" @click="selectEdu">
            <view class="box-single__label">文化程度</view>
            <u-select v-model="eduShow" :list="eduList" @confirm="eduChange"></u-select>
            <view class="box-single1">
                <view class="box-single__detail" style="margin-top: 0">{{ selectedEdu }}</view>
                <u-icon name="arrow-right"></u-icon>
            </view>
        </view>
        <u-divider :use-slot="false" half-width="100%"></u-divider>
        <!--        隐藏班级展示-->
        <!--        <view class="box-single">-->
        <!--            <view class="box-single__label">负责班级</view>-->
        <!--            <view class="box-single__detail">{{ userInfo.teacher.class_id ? curClass.name : '暂无数据' }}</view>-->
        <!--        </view>-->
        <!--        <u-divider :use-slot="false" half-width="100%" margin-top="28"></u-divider>-->
        <view class="box-single">
            <view class="box-single__label">工作单位</view>
            <u-input v-model="userInfo.parent.company" placeholder="请填写" :border-bottom="false"
                :field-style="inputStyle" @blur="saveCompany"></u-input>
        </view>
        <u-toast ref="uToast" />
    </view>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, reactive } from "vue";
const { proxy } = getCurrentInstance();
import { OBSupload } from '@/utils/obs/obs-upload'
import request from "@/request";
const inputStyle = {
    width: "100%"
}
const navBackground = {
    backgroundColor: '#F6F6F6'
}
const curIdentity = ref(1)
const userInfo = ref({
    wechat: {
        avatar_url: ""
    },
    parent: {
        company: ""
    }
})
const sexList = ref([
    {
        value: '1',
        label: '男'
    },
    {
        value: '2',
        label: '女'
    }
])
const relationList = ref([
    {
        value: '1',
        label: '爸爸'
    }, {
        value: '2',
        label: '妈妈'
    }, {
        value: '3',
        label: '爷爷'
    }, {
        value: '4',
        label: '奶奶'
    }, {
        value: '5',
        label: '姥姥（外婆）'
    }, {
        value: '6',
        label: ' 姥爷（外公）'
    }, {
        value: '7',
        label: '舅舅'
    }, {
        value: '8',
        label: '舅妈'
    }, {
        value: '9',
        label: '姨夫'
    }, {
        value: '10',
        label: '姨妈'
    }, {
        value: '11',
        label: '姑父'
    }, {
        value: '12',
        label: '姑妈'
    }, {
        value: '13',
        label: '叔叔'
    }, {
        value: '14',
        label: '婶婶'
    }, {
        value: '15',
        label: '哥哥'
    }, {
        value: '16',
        label: '姐姐'
    }, {
        value: '17',
        label: '亲属'
    }
])
const canSelectEdu = ref(false)
const eduList = ref([
    {
        value: '1',
        label: '小学'
    }, {
        value: '2',
        label: '初中'
    }, {
        value: '3',
        label: '高中'
    }, {
        value: '4',
        label: '大专'
    }, {
        value: '5',
        label: '本科'
    }, {
        value: '6',
        label: '硕士'
    }, {
        value: '7',
        label: '硕士以上'
    }
])
const sexShow = ref(false)
const eduShow = ref(false)
const relationShow = ref(false)
const selectedRelation = ref('')
const selectedEdu = ref('')
const nickname = ref('')
const haveSex = ref(true)
const haveBirth = ref(true)
const selectedSex = ref('')
const imgList = reactive([{
    url: ''
}])
const showPage = ref({
    opacity: 0
})
function chooseavatar() {
    uni.chooseImage({
        count: 1, //总限制5张
        sizeType: ["original", "compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], //从相册选择
        success: (res) => {
            let path = res.tempFilePaths[0];
            let fileName = path.substring(path.lastIndexOf("/") + 1);
            let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
            OBSupload(path, fileExtension, fileName)
                .then(res => {
                    saveAvatar(res)
                })
        }
    });
}
function saveAvatar(res) {
    let params = {
        parent_id: userInfo.value.parent.id,
        student_id: userInfo.value.parent.student_id,
        img_url: res
    }
    request({
        method: 'POST',
        url: '/api/auth/editParent',
        data: params
    }).then((res) => {
        console.log(res)
        proxy.$refs.uToast.show({
            title: '修改成功'
        })
        getUser()
    })
}
const selectRelation = () => {
    relationShow.value = true;
}
const selectSex = () => {
    sexShow.value = true

}
const selectEdu = () => {
    eduShow.value = true
}
const getUser = () => {
    request({
        method: 'GET',
        url: '/api/auth/me'
    }).then((res) => {
        userInfo.value = res;
        uni.setStorageSync('user_info', res)
        nickname.value = res.nickname;
        avatar.value = res.parent.img_url;
        selectedSex.value = res.parent.sex == 0 ? '未知' : (res.parent.sex == 1 ? '男' : '女')
        for (let i = 0; i < eduList.value.length; i++) {
            if (res.parent.education == eduList.value[i].value) {
                selectedEdu.value = eduList.value[i].label
            }
        }
        for (let i = 0; i < relationList.value.length; i++) {
            if (res.parent.relationship == relationList.value[i].value) {
                selectedRelation.value = relationList.value[i].label
            }
        }
        imgList[0].url = res.wechat.avatar_url ? res.wechat.avatar_url : ''
        showPage.value = {
            opacity: 1
        }
    })
}
const sexChange = (cur) => {
    selectedSex.value = cur[0].label
    let params = {
        parent_id: userInfo.value.parent.id,
        student_id: userInfo.value.parent.student_id,
        sex: cur[0].value
    }
    request({
        method: 'POST',
        url: '/api/auth/editParent',
        data: params
    }).then((res) => {
        console.log(res)
        proxy.$refs.uToast.show({
            title: '修改成功'
        })
        getUser()
    })
}
const relationChange = (cur) => {
    selectedRelation.value = cur[0].label
    let params = {
        parent_id: userInfo.value.parent.id,
        student_id: userInfo.value.parent.student_id,
        sex: cur[0].value
    }
    request({
        method: 'POST',
        url: '/api/auth/editParent',
        data: params
    }).then((res) => {
        console.log(res)
        proxy.$refs.uToast.show({
            title: '修改成功'
        })
        getUser()
    })
}
const eduChange = (cur) => {
    selectedEdu.value = cur[0].label
    console.log(cur[0].label)
    let params = {
        parent_id: userInfo.value.parent.id,
        student_id: userInfo.value.parent.student_id,
        education: cur[0].value
    }
    request({
        method: 'POST',
        url: '/api/auth/editParent',
        data: params
    }).then((res) => {
        console.log(res)
        proxy.$refs.uToast.show({
            title: '修改成功'
        })
        getUser()
    })
}
const saveName = () => {
    if (nickname.value.length > 10) {
        proxy.$refs.uToast.show({
            title: '名称不大于10个字'
        })
    } else {
        let params = {
            key_id: userInfo.value.wechat.id,
            key_str: 'nickname',
            key_value: nickname.value,
        }
        request({
            method: 'POST',
            url: '/api/auth/changeWechat',
            data: params
        }).then((res) => {
            console.log(res)
            proxy.$refs.uToast.show({
                title: '修改成功'
            })
            getUser()
        })
    }
}
const saveCompany = () => {
    let params = {
        parent_id: userInfo.value.parent.id,
        student_id: userInfo.value.parent.student_id,
        company: userInfo.value.parent.company
    }
    request({
        method: 'POST',
        url: '/api/auth/editParent',
        data: params
    }).then((res) => {
        console.log(res)
        proxy.$refs.uToast.show({
            title: '修改成功'
        })
        getUser()
    })
}
const avatar = ref('')
onMounted(() => {
    getUser()
})


</script>

<style lang="scss">
page {
    background: #F6F6F6
}

.isection {
    position: relative;
    width: 80rpx;
    height: 80rpx;

    .img {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
    }

    .defaltavatar {
        width: 80rpx;
        height: 80rpx;
        background-color: #627efa;
        color: #FFF;
        border-radius: 50%;
        text-align: center;
        font-size: 24rpx;
        line-height: 80rpx;
    }

    .btn {
        position: absolute;
        left: 0;
        top: 0;
        height: 0;
        width: 80rpx;
        height: 80rpx;
        opacity: 0;
        z-index: 2;
    }
}
</style>

<style scoped>
.box1 {
    margin: 0 30rpx;
    opacity: 0;
    transition: .5s all;
    border-radius: 24rpx;
    margin-top: 40rpx;
    background: #FFFFFF;
    padding: 30rpx;
    box-sizing: border-box;
}

.box-single1 {
    position: relative;
    min-height: 72rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.box-single {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.box-single__label {
    margin-top: 24rpx;
    font-size: 24rpx;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 24rpx;
}

.box-single__image {
    position: absolute;
    right: 30rpx;
    top: 0;
    bottom: 0;
    margin: 0 auto;
    width: 80rpx;
    height: 80rpx;
}

.box-single__detail {
    margin-top: 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #787C8D;
    line-height: 28rpx;
}

.slot-btn {
    opacity: 0;
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgb(244, 245, 246);
    border-radius: 10rpx;
}

.slot-btn__hover {
    background-color: rgb(235, 236, 238);
}
</style>