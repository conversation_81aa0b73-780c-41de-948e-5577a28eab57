<template>
    <u-navbar :title="navTitle" :border-bottom="false"></u-navbar>
    <view class="modal-box" :style="modelOpacity">
        <u-icon name="arrow-left" :style="backClass"></u-icon>
        <view v-for="item in userInfo.students" :key="item.id">
            <view class="model-box__single" @click="changeKid(item.id)">
                <image class="model-box__avatar" :src="item.img_url" v-if="item.img_url"></image>
                <view class="model-box__avatar" v-else>{{ item.name.charAt(0) }}</view>
                <view class="model-box__name">{{ item.name }}</view>
                <view class="model-box__signal" v-if="item.id == userInfo.parent.student_id">当前</view>
                <u-icon name="arrow-right" style="margin-left: auto"></u-icon>
            </view>
        </view>
    </view>
</template>

<script setup>
import { onMounted, ref } from "vue";
import request from "@/request";
const userInfo = ref('')
const navTitle = ref('切换孩子')
import { useUserStore } from "@/stores/userInfo.js"
const userStore = useUserStore();
const modelOpacity = ref({
    opacity: 1,
    zIndex: -1,
})
const backClass = ref({
    position: 'absolute',
    top: '126rpx',
    left: '30rpx',
})

const getUser = () => {
    request({
        method: 'GET',
        url: '/api/auth/me'
    }).then((res) => {
        uni.setStorageSync('user_info', res)
        userInfo.value = res
    })
}
const changeKid = (id) => {
    let params = {
        student_id: id,
        parent_id: userInfo.value.parent.id
    }
    request({
        method: 'POST',
        url: '/api/auth/changestudent',
        data: params
    }).then((res) => {
        userStore.refrsh(true)
        uni.navigateBack()
    })
}
onMounted(() => {
    userInfo.value = uni.getStorageSync('user_info')
})

</script>

<style scoped>
.modal-box {
    background-color: #FFFFFF;
}

.model-box__single {
    padding: 30rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.model-box__avatar {
    width: 72rpx;
    height: 72rpx;
    background: #AEB0BC;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.85);
}

.model-box__name {
    margin: 0 0 0 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
}

.model-box__signal {
    margin: 0 auto 0 16rpx;
    font-size: 20rpx;
    font-weight: 400;
    color: #999999;
}
</style>