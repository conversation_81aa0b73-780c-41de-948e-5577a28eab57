<template>
    <u-navbar title="设置" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="list-box">
        <view class="list-box__single" @click="toAgree(1)">
            <view class="list-box__name">用户协议</view>
            <u-icon name="arrow-right"></u-icon>
        </view>
        <view class="list-box__single" @click="toAgree(2)">
            <view class="list-box__name">隐私政策</view>
            <u-icon name="arrow-right"></u-icon>
        </view>
        <view class="list-box__single" @click="goFK">
            <view class="list-box__name">意见反馈</view>
            <u-icon name="arrow-right"></u-icon>
        </view>
        <!-- <view class="list-box__single" @click="goFK">
            <view class="list-box__name">行业信箱</view>
            <u-icon name="arrow-right"></u-icon>
        </view> -->
        <view class="list-box__single" @click="goWJ">
            <view class="list-box__name">满意度调研</view>
            <view class='num' v-if="Number(unreadcount) > 0">{{ unreadcount }}</view>
            <u-icon name="arrow-right"></u-icon>
        </view>
    </view>
    <view class="list-box">
        <view class="center" @click="logout">
            退出登录
        </view>
    </view>
</template>

<script setup>
import { ref } from "vue";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
import request from "@/request";
const logout = () => {
    request({
        method: 'POST',
        url: '/api/auth/logout'
    }).then(() => {
        userInfo.$reset()
        uni.clearStorageSync()
        uni.reLaunch({
            url: '/pages_work/login/main'
        })
    })
}
const toAgree = (type) => {
    uni.navigateTo({
        url: `/pages_work/login/agreement?type=${type}`
    })
}
function goFK() {
    uni.navigateTo({
        url: "/pages_work/mine/feedback_list"
    })
}
let unreadcount = ref(0)
let unread = uni.getStorageSync("unreadcount");
if (unread) {
    unreadcount.value = unread;
}
function goWJ() {
    uni.navigateTo({
        url: "/pages/pack/survey/index"
    })
}
</script>

<style>
page {
    background: #F6F6F6
}
</style>

<style lang="scss" scoped>
.list-box {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    margin: 40rpx auto;

    .list-box__single {
        padding: 0 30rpx;
        height: 100rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .list-box__name {
            font-size: 28rpx;
            font-weight: 600;
            color: #262937;
            line-height: 40rpx;
        }

        .num {
            flex: 1;
            text-align: right;
            color: #ff7373;
            padding-right: 20rpx;
        }
    }

    .center {
        width: 690rpx;
        text-align: center;
        height: 100rpx;
        line-height: 100rpx;
        font-weight: 600;
        color: #262937;
    }
}
</style>