<template>
  <view class="page-complaint">
    <u-navbar :title="'意见反馈'" :background="{ background: '#FFFFFF' }" :border-bottom="true" />
    <view class="card-item" v-for="(item, index) in list" :key="index" @click="goDetail(item.id)">
      <view class="info-item">
        <text class="label">反馈日期：</text>
        <text class="value">{{ item.feed_time }}</text>
      </view>
      <view class="info-item">
        <text class="label">反馈进度：</text>
        <text class="value">
          <u-button type="warning" size="mini" shape="square" v-if="item.status == 0">未查阅</u-button>
          <u-button type="success" size="mini" shape="square" v-if="item.status == 3">已知悉</u-button>
        </text>
      </view>
      <view class="footer" style="justify-content: flex-end;">
        <text class="title">查看详情</text>
      </view>
    </view>
    <view class="page-nodata" v-if="list.length == 0">
      <u-empty text="暂无数据"></u-empty>
    </view>
    <view class="add" @click="goAdd">
      <image class="addi" src="https://obs.tuoyupt.com/aituoyu/img/enrollment/add.svg"></image>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, watch } from "vue";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);
import request from "@/request";
const type = ref(1);
const navTitle = ref('');
const list = ref([]);

function goDetail(id) {
  uni.navigateTo({
    url: `/pages_work/mine/feedback?id=${id}`,
  });
}
function goAdd() {
  uni.navigateTo({
    url: `/pages_work/mine/feedback`,
  });
}

async function getData() {
  uni.showLoading();
  const res = await request({
    url: baseUrl.value + "/list?user_id=" + userInfo.id,
  });
  console.log(res);
  uni.hideLoading();
  if (res) {
    list.value = res.data;
  }
}
let baseUrl = ref('');
onLoad((options) => {
  type.value = options.type;
  baseUrl.value = '/api/parent/jyfeedback'
});

onShow(() => {
  console.log('onshow')
  getData();
})

</script>

<style lang="scss" scoped>
.page-complaint {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: #f6f6f6;

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;

    .header {
      padding: 24rpx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }

      .u-btn {
        margin-left: 24rpx;
      }
    }

    .footer {
      padding: 24rpx 0;
      border-top: 1px solid #eee;
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #2979ff;
      line-height: 1.5;

      .title {
        font-size: 28rpx;
      }

      .status {
        flex: 0 0 280rpx;
        text-align: right;
      }
    }

    .info-item {
      display: flex;
      align-items: center;
      height: 80rpx;

      .label {
        text-align: right;
        font-size: 28rpx;
        color: #666;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #9a9a9a;
      }
    }
  }

  .add {
    position: fixed;
    bottom: 140rpx;
    right: 40rpx;
    z-index: 5;

    .addi {
      width: 112rpx;
      height: 112rpx;
    }
  }
}
</style>
