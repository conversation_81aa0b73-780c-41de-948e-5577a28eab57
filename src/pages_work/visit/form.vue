<template>
  <u-navbar ref="navbar" :custom-back="backFun" :border-bottom="false" title="预约参观"></u-navbar>
  <view class="visit-form">
    <image class="logo" :src="school.logo" mode="aspectFit" />
    <view class="name">{{ school.name }}</view>
    <u-form class="form" :border-bottom="false" :model="formData" ref="form" label-position="top"
      :label-style="labelStyle">
      <u-form-item required label="宝宝姓名"><u-input :custom-style="inputStyle" v-model="formData.name" /></u-form-item>
      <u-form-item required label="联系方式"><u-input maxlength="11" :custom-style="inputStyle"
          v-model="formData.phone" /></u-form-item>
      <u-form-item required label="亲属关系"><u-input placeholder="请选择" :custom-style="inputStyle" disabled
          @click="showSelect = true" v-model="relationshipName" /></u-form-item>
      <u-form-item required label="宝宝生日"><u-input placeholder="请选择" :custom-style="inputStyle" disabled
          @click="selectDate('birth')" v-model="formData.birth" /></u-form-item>
      <u-form-item required label="计划参观日期"><u-input placeholder="请选择" :custom-style="inputStyle" disabled
          @click="selectDate('plan_visit')" v-model="formData.plan_visit" /></u-form-item>
      <view class="default-btn" @click="submit">提交信息</view>
    </u-form>
    <u-select @confirm="confirm" v-model="showSelect" :list="relation" value-name="id" label-name="name"></u-select>
    <u-calendar v-if="show" :min-date="minDate" max-date="2051-01-01" :blur="10" :safe-area-inset-bottom="true"
      v-model="show" :mode="mode" @change="change"></u-calendar>
  </view>
</template>

<script setup>
import { onLoad, onReady } from "@dcloudio/uni-app";
import { reactive, ref } from "vue";
import request from "@/request";
let now = new Date();
const minDate = ref("")
const labelStyle = {
  fontSize: "24rpx",
  color: "#AEB0BC",
};
const relation = ref([]);
const inputStyle = {
  fontSize: "28rpx",
  color: "#000000",
  height: "40rpx",
  minHeight: "40rpx",
};
let school = ref(uni.getStorageSync("visitSchool"));
let calendarType = ref("");
let show = ref(false);
let showSelect = ref(false);
let form = ref(null);
let relationshipName = ref("");
let formData = reactive({
  name: "",
  phone: "",
  relationship: "",
  birth: "",
  plan_visit: "",
});

function selectDate(type) {
  if (type == 'plan_visit') {
    minDate.value = `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`
  } else {
    minDate.value = `1980-${now.getMonth() + 1}-${now.getDate()}`
  }
  calendarType.value = type;
  show.value = true;
}

function change(e) {
  formData[calendarType.value] = e.result;
  show.value = false;
}

function confirm(e) {
  formData.relationship = e[0].value;
  relationshipName.value = e[0].label;
  showSelect.value = false;
}

function submit(e) {
  for (const key in formData) {
    if (Object.hasOwnProperty.call(formData, key)) {
      const element = formData[key];
      console.log(checkModbile(element));
      if (key == "phone" && !checkModbile(element)) {
        uni.showToast({ icon: "none", title: "请填写正确的手机号格式" });
        return;
      }
      if (element == "") {
        uni.showToast({ icon: "none", title: "请完善信息后提交" });
        return;
      }
    }
  }
  request({
    method: "POST",
    url: `/api/parent/school/saveCustomer/${school.value.id}`,
    data: {
      ...formData,
      invit_teacher_id,
      identity,
      sourse_id
    },
  }).then((res) => {
    uni.showToast({
      title: "提交成功",
      icon: "none",
      complete: () => {
        uni.setStorageSync("visitAdd", 1);
        let prePage = getCurrentPages();
        if (prePage.length > 2) {
          uni.navigateBack();
        } else {
          uni.navigateTo({
            url: `/pages_work/visit/detail?id=${school.value.id}&back=home`
          });
        }
      },
    });
  });
}
function checkModbile(mobile) {
  var re = /^1[3,4,5,6,7,8,9][0-9]{9}$/;
  var result = re.test(mobile);
  if (!result) {
    return false;//若手机号码格式不正确则返回false
  }
  return true;
}
let school_id = "";
let invit_teacher_id = "";
let identity = '';
let sourse_id = '';
let type = 0;
onLoad((option) => {
  if (option.school_id) {
    school_id = option.school_id;
    invit_teacher_id = option.invit_teacher_id;
    identity = option.identity;
    sourse_id = option.sourse_id;
    getSchoolInfo()
  }
  if (option.type) {
    type = option.type;
  }
})
function backFun() {
  if (type == 1) {
    uni.navigateBack();
  } else {
    uni.navigateTo({
      url: `/pages_work/visit/detail?id=${school_id}`
    });
  }
}
function getSchoolInfo() {
  request({
    url: "/api/parent/school/detail/" + school_id,
  }).then((res) => {
    school.value = {
      logo: res.brand_logo,
      name: res.abbreviation,
      id: res.id,
    };
  });
}
onReady(async () => {
  request({
    url: `/api/parent/school/relation`,
  }).then((res) => {
    let arr = [];
    for (const key in res) {
      if (Object.hasOwnProperty.call(res, key)) {
        const element = res[key];
        arr.push({
          id: key,
          name: element,
        });
      }
    }
    relation.value = arr;
  });
});
</script>

<style lang="scss" scoped>
.visit-form {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .form {
    width: 528rpx;
    margin: 0 auto;
  }

  .logo {
    width: 216rpx;
    height: 216rpx;
    border-radius: 108rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    margin: 40rpx auto 0;
  }

  .name {
    font-size: 40rpx;
    font-weight: 500;
    color: #000000;
    line-height: 56rpx;
    text-align: center;
    margin: 20rpx 0 40rpx;
  }
}
</style>
