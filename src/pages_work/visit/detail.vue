<template>
  <view class="school-detail-page">
    <u-navbar :background="{ background: `rgba(255,255,255,${navBackground})` }" :immersive="true" ref="navbar"
      :is-back="navBackground ? true : false" :border-bottom="navBackground ? true : false" title="">
      <view v-if="navBackground == 1" class="slot-wrap">
        <u-tabs :list="tabList" bg-color="transparent" :show-bar="false" :active-item-style="{ color: '#000' }"
          :is-scroll="false" v-model="current" @change="tabChange"></u-tabs>
      </view>
      <view v-else class="slot-wrap">
        <image @click="backFun" class="back-icon"
          src="https://obs.tuoyupt.com/miniprogram/client/images/back_icon.png" />
      </view>
    </u-navbar>
    <div class="swiper" id="swiper">
      <swiper v-if="schoolData" class="swiper-wrap" circular :indicator-dots="swiperOption.indicatorDots"
        :autoplay="swiperOption.autoplay" :interval="swiperOption.interval" :duration="swiperOption.duration">
        <swiper-item v-for="(item, index) in swiperList" :key="index">
          <view class="swiper-item" v-if="item.type == 1">
            <video controls="false" :src="item.url" initial-time="0" autoplay="true" :muted="false"></video>
          </view>
          <view class="swiper-item" v-else>
            <image :src="item.url" mode="aspectFill" />
          </view>
        </swiper-item>
      </swiper>
    </div>
    <view class="content" v-if="schoolData">
      <view class="name-part">
        <view class="left">
          <view class="name">{{ schoolData.abbreviation }}</view>
          <view class="tag">
            <text v-if="schoolData.free_type">{{ id2name(schoolData.free_type, free_type) }}</text>
            <text v-if="schoolData.school_class">{{ id2name(schoolData.school_class, school_class) }}</text>
            <text v-if="schoolData.is_record != 0">{{ id2name(schoolData.is_record, is_record) }}</text>
            <text v-if="schoolData.school_nature_ids" v-for="(item, index) in str2arr(schoolData.school_nature_ids)"
              :key="index">{{ id2name(item,
                school_nature_ids) }}</text>
          </view>
        </view>
        <image @click="phoneShow = true" class="right"
          src="https://obs.tuoyupt.com/miniprogram/client/images/call_icon.png" mode="aspectFit" />
      </view>
      <view class="address-part">
        <view class="left" @click="infoMap">
          <view class="address">{{ schoolData.service.address }}</view>
          <view class="status">
            {{
              id2name(schoolData.service.operate_condition, operate_condition)
            }}
          </view>
        </view>
        <view class="right" @click="intoForm" v-if="isLogin">
          <view> 预约参观 </view>
          <text> {{ schoolData.service.visit_num }}人预约 </text>
        </view>
        <view class="right" @click="gotoLogin" v-else>
          <view> 预约参观 </view>
          <text> {{ schoolData.service.visit_num }}人预约 </text>
        </view>
      </view>
      <view class="intro-part" id="introduce" :class="{ 'intro-open': introOpen }">
        {{ schoolData.service.introduce }}
        <view class="close-btn" v-if="!hideOpenBtn" @click="introOpen = false;">收起</view>
        <view class="open-btn" @click="introOpen = true;">展开</view>
      </view>
      <u-line id="msg-part" color="#eee" margin="40rpx 0" />
      <view class="msg-part">
        <view class="title-1">信息</view>
        <u-collapse :accordion="false" :head-style="headStyle">
          <u-collapse-item class="title-2" title="基本信息">
            <view v-if="schoolData.service.build_tuoyu_num || schoolData.service.build_children_num" style="margin: 0"
              class="title-3">建设托位数：</view>
            <view v-if="schoolData.service.build_tuoyu_num || schoolData.service.build_children_num" class="value">{{
              schoolData.service.build_tuoyu_num ||
              schoolData.service.build_children_num
            }}</view>
            <view v-if="schoolData.service.server_type" class="title-3">服务类型：</view>
            <view v-if="schoolData.service.server_type" class="value">
              <template v-for="(item, index) in str2arr(
                schoolData.service.server_type,
                true
              )" :key="index">
                {{ id2name(item, server_type) }}；
              </template>
            </view>
            <view v-if="schoolData.service.feed_type" class="title-3">供餐情况：</view>
            <view v-if="schoolData.service.feed_type" style="margin-bottom: 40rpx" class="value">{{
              id2name(schoolData.service.feed_type, feed_type)
            }}</view>
          </u-collapse-item>
          <u-collapse-item class="title-2" title="设施信息">
            <view v-if="schoolData.service.facility_type" style="margin: 0" class="title-3">设施情况：</view>
            <view v-if="schoolData.service.facility_type" class="value">{{
              id2name(schoolData.service.facility_type, facility_type)
            }}</view>
            <view v-if="schoolData.service.place_type" class="title-3">服务场所性质：</view>
            <view v-if="schoolData.service.place_type" class="value">{{
              id2name(schoolData.service.place_type, place_type)
            }}</view>
            <view v-if="schoolData.service.struct_area || schoolData.service.out_area || schoolData.service.in_area"
              class="title-3">场地面积：</view>
            <view class="value">
              <text v-if="schoolData.service.struct_area">建筑面积{{ schoolData.service.struct_area }}㎡，</text>
              <text v-if="schoolData.service.out_area">户外场地面积{{ schoolData.service.out_area }}㎡，</text>
              <text v-if="schoolData.service.in_area">室内面积{{ schoolData.service.in_area }}㎡，</text>
            </view>
            <view v-if="schoolData.service.classroom_num" class="title-3">教室数量：</view>
            <view v-if="schoolData.service.classroom_num" class="value">{{ schoolData.service.classroom_num }}</view>
            <view v-if="schoolData.service.other_facilitys" class="title-3">其他设备：</view>
            <view v-if="schoolData.service.other_facilitys" class="value">
              <template v-for="(item, index) in str2arr(
                schoolData.service.other_facilitys,
                true
              )" :key="index">
                {{ id2name(item, other_facilitys) }}；
              </template>
            </view>
          </u-collapse-item>
        </u-collapse>
      </view>
      <u-line id="media-part" color="#eee" margin="40rpx 0" />
      <view class="media-part">
        <view class="title-1">机构动态</view>
        <view class="media-tab">
          <view class="tab" :class="{ on: catId == item.id }" v-for="(item, index) in cat_id" :key="index"
            @click="mediaTab(item.id)">{{ item.name }}
          </view>
        </view>
        <!-- <view v-if="list.length" class="media-list">
          <view class="media" id="first-media" @click="preview(item)" v-for="(item, index) in list" :key="index">
            <view class="tip" v-if="item.type == 0">
              {{ str2arr(item.image).length }}
            </view>
            <image class="tip" v-else
              src="https://obs.tuoyupt.com/miniprogram/client/images/play_icon.png" />
            <view class="cover">
              <image v-if="item.type == 0" :src="str2arr(item.image)[0]" mode="widthFix" lazy-load="true" />
              <image v-else :src="item.video_face" mode="widthFix" lazy-load="true" />
            </view>
            <view class="name">{{ item.description }}</view>
          </view>
        </view> -->
        <u-waterfall class="media-list" v-model="list" v-if="list.length">
          <template v-slot:left="{ leftList }">
            <view v-for="(item, index) in leftList" :key="index" style="margin-right: 20rpx;">
              <view class="media" @click="preview(item)">
                <view class="tip" v-if="item.type == 0">
                  {{ str2arr(item.image).length }}
                </view>
                <image class="tip" v-else src="https://obs.tuoyupt.com/miniprogram/client/images/play_icon.png" />
                <view class="cover">
                  <image v-if="item.type == 0" :src="str2arr(item.image)[0]" mode="widthFix" lazy-load="true" />
                  <image v-else :src="item.video_face" mode="widthFix" lazy-load="true" />
                </view>
                <view class="name">{{ item.description }}</view>
              </view>
            </view>
          </template>
          <template v-slot:right="{ rightList }">
            <view v-for="(item, index) in rightList" :key="index">
              <view class="media" @click="preview(item)">
                <view class="tip" v-if="item.type == 0">
                  {{ str2arr(item.image).length }}
                </view>
                <image class="tip" v-else src="https://obs.tuoyupt.com/miniprogram/client/images/play_icon.png" />
                <view class="cover">
                  <image v-if="item.type == 0" :src="str2arr(item.image)[0]" mode="widthFix" lazy-load="true" />
                  <image v-else :src="item.video_face" mode="widthFix" lazy-load="true" />
                </view>
                <view class="name">{{ item.description }}</view>
              </view>
            </view>
          </template>
        </u-waterfall>
        <view class="no-data" v-else src="">
          <image src="https://obs.tuoyupt.com/miniprogram/client/images/no_data.png" mode="aspectFit" />
          <view class="t2">该分类暂无机构动态</view>
        </view>
        <u-loadmore v-if="list.length" margin-top="40" margin-bottom="40" :status="status" :load-text="loadText" />
      </view>
    </view>
    <u-action-sheet @click="call" :list="phoneList" :safe-area-inset-bottom="true" v-model="phoneShow"></u-action-sheet>
    <Preview :previewData="previewData" />
  </view>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { onLoad, onReady, onShow, onReachBottom, onPageScroll } from "@dcloudio/uni-app";
import request from "../../request.js";
import Preview from "@/components/preview.vue";
import {
  id2name,
  str2arr,
  operate_condition,
  is_record,
  cat_id,
  school_class,
  free_type,
  place_type,
  facility_type,
  server_type,
  other_facilitys,
  school_nature_ids,
  feed_type,
} from "@/utils/dataHandle.js";
// import { OBSupload } from '../../static/js/obs/obs-upload'

// const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
let schoolData = ref(null);
let list = ref([]);
let catId = ref("");
let navBackground = ref(0);
let hideOpenBtn = ref(false);
let listLoading = ref(false);
let tabList = ref([{
  name: '机构'
}, {
  name: '信息'
}, {
  name: '动态',
}])
let isLogin = ref(false);
let current = ref(0)
let swiperOption = reactive({
  indicatorDots: false,
  autoplay: false,
  interval: 2000,
  duration: 500,
});
let status = ref("loadmore");
let loadText = {
  loadmore: "轻轻上拉",
  loading: "努力加载中",
  nomore: "已经加载全部~",
};
let lastScrollTop = 0;
let phoneShow = ref(false);
let pageData = reactive({
  page: 0,
  per_page: 6,
});
let previewData = reactive({
  show: false,
  type: "image",
  video_url: "",
  image_list: [],
});
let headStyle = {
  height: "80rpx",
  color: "#787C8D",
};

let introOpen = ref(true);
let navbar = ref(null);

const props = defineProps(["id"]);

let swiperList = computed(() => {
  if (!schoolData.value) {
    return [];
  }
  let { head_imgs, head_video } = schoolData.value.service;
  let arr = [];
  if (head_video) {
    arr.push({
      type: 1,
      url: head_video
    });
  }
  if (head_imgs) {
    let imgs = head_imgs.split(",").map(it => {
      return {
        type: 2,
        url: it
      }
    })
    arr = [...arr, ...imgs];
  }
  if (arr.length == 0) {
    arr.push({
      type: 2,
      url: 'https://obs.tuoyupt.com/miniprogram/parentindex/school.png'
    });
  }
  return arr;
});

let phoneList = computed(() => {
  if (!schoolData.value) {
    return [];
  }
  return schoolData.value.service.phones.split(",").map((e) => {
    return { text: e };
  });
});

function backFun() {
  if (backHome) {
    uni.reLaunch({
      url: '/pages_work/index/index'
    })
    return
  }
  let prePage = getCurrentPages();
  if (prePage.length >= 2) {
    let page = prePage[prePage.length - 2].$page.fullPath;
    if (page == '/pages_work/index/index' || page.includes('/pages_work/visit/index')) {
      uni.navigateBack();
    } else {
      uni.navigateTo({
        url: '/pages_work/visit/index'
      })
    }
  } else {
    uni.navigateTo({
      url: '/pages_work/visit/index'
    })
  }
}

function preview(item) {
  if (item.type == "0") {
    previewData.type = "image";
    previewData.image_list = str2arr(item.image);
    console.log(previewData);
  } else if (item.type = "1") {
    previewData.type = "video";
    previewData.video_url = item.video_url;
  }
  previewData.show = true;
}

async function mediaTab(id) {
  if (catId.value == id) {
    return;
  }
  let scrollTop = 0;
  let getSystemInfo = await uni.getSystemInfo();
  uni.createSelectorQuery().select('#first-media').boundingClientRect(function (res) {
    if (res) {
      let top = getSystemInfo.screenHeight - res.height;
      scrollTop = res.top + lastScrollTop - top
      uni.pageScrollTo({
        scrollTop,
        duration: 300,
        success: function () {
          catId.value = id;
          getMediaList(true);
        }
      })
    } else {
      catId.value = id;
      getMediaList(true);
    }
  }).exec()

}

async function tabChange(index) {
  let scrollTop = 0;
  console.log(lastScrollTop);
  if (index == 0) {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  } else if (index == 1) {
    uni.createSelectorQuery().select('#msg-part').boundingClientRect(function (res) {
      scrollTop = res.top + lastScrollTop - 68
      uni.pageScrollTo({
        scrollTop,
        duration: 300
      })
    }).exec()
  } else if (index == 2) {
    uni.createSelectorQuery().select('#media-part').boundingClientRect(function (res) {
      scrollTop = res.top + lastScrollTop - 68
      uni.pageScrollTo({
        scrollTop,
        duration: 300
      })
    }).exec()
  }
  console.log(scrollTop);

}
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
function getPhoneNumber(e) {
  if (e.detail.code) {
    // 展示加载框
    uni.showLoading({
      title: '加载中',
    });
    uni.login({
      provider: 'weixin',
      success: (res) => {
        let code = res.code;
        if (res.errMsg == 'login:ok') {
          request({
            url: `/api/auth/firstlogin?code=${code}`,
          }).then(async (res2) => {
            let openid = res2.openid;
            let codephone = e.detail.code;
            let login_type = 2;
            let checkcode = 0;
            request({
              url: "/api/auth/secondlogin",
              method: 'post',
              data: {
                openid,
                codephone,
                login_type,
                checkcode,
              }
            }).then(res => {
              uni.hideLoading();
              if (res.token) {
                uni.setStorageSync("token", res.token);
                uni.setStorageSync("user_info", res.user);
                let user = res.user;
                userInfo.$patch(user)
                loginHandle();
              } else {
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  confirmText: '确认',
                  cancelText: '取消',
                  success: function (resc) {
                    if (resc.confirm) {
                      uni.showLoading();
                      request({
                        url: "/api/auth/loginbindsure",
                        method: 'post',
                        data: {
                          openid: res.data.mini_openid,
                          uid: res.data.uid,
                        }
                      }).then(res3 => {
                        uni.hideLoading();
                        uni.setStorageSync("token", res3.token);
                        uni.setStorageSync("user_info", res3.user);
                        let user = res3.user;
                        userInfo.$patch(user)
                        loginHandle();
                      })
                    }
                  },
                })
              }
            })
          });
        }
        uni.hideLoading()
      },
    });
  }
}

async function loginHandle() {
  isLogin.value = true;
  intoForm();
}

function infoMap() {
  uni.openLocation({
    latitude: Number(schoolData.value.service.lat),
    longitude: Number(schoolData.value.service.lng),
    name: schoolData.value.abbreviation,
    address: schoolData.value.service.address
  });

}

function intoForm() {
  uni.setStorageSync("visitSchool", {
    logo: schoolData.value.brand_logo,
    name: schoolData.value.abbreviation,
    id: schoolData.value.id,
  });
  uni.navigateTo({
    url: `/pages_work/visit/form?type=1`,
  });
}

function call(index) {
  let phoneNumber = phoneList.value[index].text;
  console.log(index, phoneNumber);
  uni.makePhoneCall({
    phoneNumber, //仅为示例，并非真实的电话号码
  });
}
function gotoLogin() {
  uni.redirectTo({
    url: "/pages_work/login/main"
  })
}
function getMediaList(newlist = false) {
  if (newlist) {
    pageData.page = 0;
    list.value = [];
    status.value = "loadmore";
  }
  if (status.value == "nomore" || status.value == "loading") {
    return;
  }
  pageData.page += 1;
  status.value == "loading";
  let cat = catId.value && `&cat_id=${catId.value}`;
  listLoading.value = true;
  request({
    url: `/api/parent/school/advertise/${schoolData.value.id}?page=${pageData.page}&per_page=${pageData.per_page}${cat}`,
  }).then((res) => {
    list.value = [...list.value, ...res];
    status.value = res.length < pageData.per_page ? "nomore" : "loadmore";
    listLoading.value = false;
  });
}

onPageScroll((e) => {
  // console.log(e.scrollTop);
  lastScrollTop = e.scrollTop;
  if ((e.scrollTop > 60)) {
    navBackground.value = 1;
  } else {
    navBackground.value = 0;
  }
})
let backHome = false;
onLoad(async (options) => {
  isLogin.value = uni.getStorageSync('token');
  request({
    url: "/api/parent/school/detail/" + options.id,
  }).then((res) => {
    schoolData.value = res;
    setTimeout(() => {
      uni.createSelectorQuery().select('#introduce').boundingClientRect(function (res) {
        if (res.height > 48) {
          introOpen.value = false;
        } else {
          hideOpenBtn.value = true;
          introOpen.value = true;
        }
      }).exec()
    }, 0);
    getMediaList();
  });
  if (options.back == 'home') {
    backHome = true;
  }
});
onShow(() => {
  if (schoolData && uni.getStorageSync('visitAdd')) {
    schoolData.value.service.visit_num += 1;
    uni.removeStorageSync('visitAdd');
  }
});
onReachBottom((e) => {
  getMediaList();
});
</script>

<style>
.u-mode-center-box {
  background: transparent !important;
}

.no-data,
.u-water-list {
  min-height: 400px;
}

.u-collapse-title {
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
.school-detail-page {
  .slot-wrap {
    position: absulute;
    width: calc(100vw - 38rpx - 24px);
    margin: 0 auto;
    padding-right: calc(168rpx + 24px);
    padding-left: 130rpx;
    left: 0;
    overflow: visible;

    .back-icon {
      width: 56rpx;
      height: 56rpx;
      position: absolute;
      left: 40rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .swiper {
    height: 620rpx;
    position: relative;
  }

  .swiper-wrap {
    height: 420px;
  }

  .swiper-item {
    display: block;
    height: 420rpx;
    text-align: center;

    video,
    image {
      height: 100%;
      display: block;
      width: 100%;
    }
  }

  .content {
    margin-top: -200rpx;
    border-radius: 24rpx 24rpx 0 0;
    padding: 0 30rpx;
    position: relative;
    z-index: 9;
    background: #fff;
    min-height: calc(100vh - 420rpx);
    overflow: hidden;

    .name-part {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30rpx;

      .right {
        flex: 0 0 80rpx;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }

      .left {
        flex: auto;

        .name {
          font-size: 40rpx;
          font-weight: 500;
          color: #262937;
        }

        .tag {
          display: flex;
          margin-top: 12rpx;
          flex-wrap: wrap;

          text {
            height: 40rpx;
            background: #f6f6f6;
            border-radius: 8rpx;
            padding: 0 16rpx;
            font-size: 20rpx;
            color: #262937;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16rpx;
            margin-bottom: 8rpx;
          }
        }
      }
    }

    .address-part {
      width: 690rpx;
      height: 144rpx;
      background: #f6f6f6;
      border-radius: 16rpx;
      display: flex;
      margin-top: 30rpx;

      .left {
        flex: auto;
        font-size: 28rpx;
        color: #787c8d;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        padding: 0 20rpx;

        >view {
          margin: 7rpx 0;
        }

        .status {
          font-size: 24rpx;
          color: #08c22f;
        }
      }

      .right {
        flex: 0 0 130rpx;
        width: 130rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #ff7373;
        line-height: 44rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        padding: 0 !important;

        text {
          font-size: 24rpx;
          color: #aeb0bc;
        }
      }
    }

    .intro-part {
      margin-top: 20rpx;
      height: 88rpx;
      font-size: 28rpx;
      color: #262937;
      line-height: 44rpx;
      position: relative;
      overflow: hidden;
      word-break: break-all;

      .open-btn {
        position: absolute;
        right: 0;
        top: 44rpx;
        width: 300rpx;
        height: 44rpx;
        text-align: right;
        color: #ff7373;
        background-image: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            #ffffff 100%);
      }

      .close-btn {
        text-align: right;
        color: #ff7373;
      }
    }

    .intro-open {
      overflow: visible;
      height: auto;

      .open-btn {
        display: none;
      }
    }

    .title-1 {
      font-size: 32rpx;
      font-weight: 500;
      color: #262937;
      line-height: 32rpx;
      letter-spacing: 1px;
      margin-bottom: 20rpx;
    }

    .title-2 {
      font-size: 28rpx;
      color: #787c8d;
      line-height: 40rpx;
      margin-top: 28rpx;
    }

    .title-3 {
      font-size: 28rpx;
      font-weight: 500;
      color: #515A6E;
      line-height: 44rpx;
      margin-top: 28rpx;
    }

    .value {
      font-size: 28rpx;
      color: #262937;
      line-height: 44rpx;
      margin-top: 14rpx;
    }

    .media-tab {
      display: flex;
      flex-wrap: wrap;

      .tab {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #787c8d;
        height: 48rpx;
        background: #f6f6f6;
        border-radius: 8rpx;
        margin-right: 16rpx;
        line-height: 48rpx;
        padding: 0 20rpx;
        margin-bottom: 16rpx;
      }

      .on {
        background: #ff7373;
        color: #fff;
      }
    }

    .media-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .media {
        flex: 0 1 336rpx;
        width: 336rpx;
        margin-top: 40rpx;
        overflow: hidden;
        position: relative;

        .tip {
          width: 40rpx;
          height: 40rpx;
          position: absolute;
          right: 20rpx;
          top: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 24rpx;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
        }

        .cover {
          width: 336rpx;
          border-radius: 12rpx;
          overflow: hidden;
          background-color: #f6f6f6;
          max-height: 392rpx;

          image {
            width: 336rpx;
            display: block;
          }
        }

        .name {
          margin-top: 16rpx;
          font-size: 28rpx;
          color: #262937;
          line-height: 36rpx;
          letter-spacing: 1px;
        }
      }
    }
  }
}
</style>
