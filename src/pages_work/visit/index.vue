<template>
  <view class="pages">
    <u-navbar v-if="type == 1" :background="{ background: 'transparent' }" ref="navbar" :is-back="false"
      :border-bottom="false" title="托育机构">
      <view class="slot-wrap">
        <User />
      </view>
    </u-navbar>
    <u-navbar v-else :background="{ background: 'transparent' }" ref="navbar" :is-back="true" :border-bottom="false"
      title="托育机构">
    </u-navbar>
    <view class="visit-index">
      <view class="header">
        <view class="btns">
          <view class="btn" :class="{ active: tabIndex == 1 }" @click="tabIndex = 1">
            列表
          </view>
          <view class="btn" :class="{ active: tabIndex == 0 }" @click="tabIndex = 0">
            地图
          </view>
        </view>
        <view @click="regionShow = true" class="city">{{ locationCity.name || '泸州' }} <u-icon name="map" color="#FF7373"
            size="30"></u-icon>
        </view>
      </view>
      <view class="search">
        <view class="quyu" @click="showArea = true">
          {{ area.text }} <u-icon name="arrow-down" color="#FF7373" size="30"></u-icon>
        </view>
        <view class="input">
          <u-search style="width: 100%;" bg-color="transparent" :clearabled="false" placeholder="搜索" :show-action="true"
            action-text="取消" v-model.trim="popupData.name" @search="search" @clear="clear" @custom="clear"></u-search>
        </view>
      </view>
      <view class="tab-content" v-if="tabIndex == 0">
        <map :style="{ width: '750rpx', height: '100%' }" :latitude="latitude" :longitude="longitude" :markers="covers"
          @markertap="onMarkertap" scale="12">
        </map>
      </view>
      <view class="tab-content" v-if="tabIndex == 1">
        <view class="filter">
          <view class="filter-wrap">
            <view class="distance" :class="{ on: searchData.order == 'distance' }" @click="orderList('distance')">距离
            </view>
            <view class="popular" :class="{ on: searchData.order == 'popular' }" @click="orderList('popular')">人气</view>
          </view>
          <view class="dropdown-btn">筛选 <u-icon style="margin-left: 6rpx"
              name="https://obs.tuoyupt.com/miniprogram/client/images/filter_icon.png" color="#FF7373"
              size="28"></u-icon>
          </view>
          <u-dropdown ref="dropdown">
            <u-dropdown-item>
              <view class="slot-content" :style="{ height: `calc(100vh - ${navbarHeight}px - 130px)` }">
                <scroll-view scroll-y="true" style="height: 100%">
                  <view class="search-title">备案情况</view>
                  <view class="search-list">
                    <view @click="filterHandle('is_record', item.id)" :class="{ on: popupData.is_record === item.id }"
                      class="search-item" v-for="(item, index) in is_record" :key="index">{{ item.name }}</view>
                  </view>
                  <view class="search-title">服务类型</view>
                  <view class="search-list">
                    <view @click="filterHandle('server_type', item.id)"
                      :class="{ on: popupData.server_type == item.id }" class="search-item"
                      v-for="(item, index) in server_type" :key="index">{{ item.name }}</view>
                  </view>
                  <view class="search-title">机构性质</view>
                  <view class="search-list">
                    <view @click="filterHandle('free_type', item.id)" :class="{ on: popupData.free_type == item.id }"
                      class="search-item" v-for="(item, index) in free_type" :key="index">{{ item.name }}</view>
                  </view>
                  <view class="search-title">机构属性</view>
                  <view class="search-list">
                    <view @click="filterHandle('school_nature_ids', item.id)"
                      :class="{ on: popupData.school_nature_ids == item.id }" class="search-item"
                      v-for="(item, index) in school_nature_ids" :key="index">{{ item.name }}</view>
                  </view>
                  <view class="search-title">服务类型</view>
                  <view class="search-list">
                    <view @click="filterHandle('school_class', item.id)"
                      :class="{ on: popupData.school_class == item.id }" class="search-item"
                      v-for="(item, index) in school_class" :key="index">{{ item.name }}</view>
                  </view>
                  <view class="search-title">供餐情况</view>
                  <view class="search-list">
                    <view @click="filterHandle('feed_type', item.id)" :class="{ on: popupData.feed_type == item.id }"
                      class="search-item" v-for="(item, index) in feed_type" :key="index">{{ item.name }}</view>
                  </view>
                  <view style="height: 60rpx;"></view>
                </scroll-view>
                <view class="fixed-btn">
                  <view class="btn-wrap">
                    <view class="btn" @click="searchReset">重置</view>
                    <view class="btn confirm" @click="search">确定</view>
                  </view>
                </view>
              </view>
            </u-dropdown-item>
          </u-dropdown>
        </view>
        <scroll-view :refresher-triggered="triggered" @refresherrestore="onRestore" :refresher-enabled="true"
          @refresherrefresh="refresh" class="list" scroll-y="true" @scrolltolower="getList">
          <template v-if="list.length">
            <view class="item" @click="intoDetail(item.id)" v-for="(item, index) in list" :key="index">
              <u-image v-if="item.entrance_img" width="240rpx" height="240rpx" border-radius="16" mode="aspectFill"
                :src="item.entrance_img"></u-image>
              <u-image v-else width="240rpx" height="240rpx" border-radius="16" mode="aspectFill"
                src="https://obs.tuoyupt.com/miniprogram/parentindex/school1.png"></u-image>
              <view class="msg">
                <view class="name">{{ item.abbreviation }}</view>
                <view class="address">
                  <text style="width: 320rpx">{{ item.address }}</text>
                  <template v-if="!noLocation">
                    <text class="distance" v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2) }}km</text>
                    <text v-else class="distance">{{ item.distance * 1000 }}m</text>
                  </template>
                </view>
                <view class="tag">
                  <text v-if="item.free_type">{{ id2name(item.free_type, free_type) }}</text>
                  <text v-if="item.school_class">{{ id2name(item.school_class, school_class) }}</text>
                  <text v-if="item.is_record != 0">{{ id2name(item.is_record, is_record) }}</text>
                  <text v-if="item.school_nature_ids" v-for="(item, index) in str2arr(item.school_nature_ids)"
                    :key="index">{{
                      id2name(item,
                    school_nature_ids) }}</text>
                </view>
                <!-- <view class="num">{{ item.visit_num }}人已预约</view> -->
              </view>
            </view>
            <u-loadmore margin-top="40" :status="status" :load-text="loadText" />
          </template>
          <view class="no-data" v-else>
            <image src="https://obs.tuoyupt.com/miniprogram/client/images/no_data.png" mode="aspectFit" />
            <view class="t1">抱歉</view>
            <view class="t2">该区域暂无托育机构，托育机构申请可</view>
            <view class="t2">咨询客服：010-62030731</view>
            <view class="btn" @click="intoWebview">申请加入</view>
          </view>
        </scroll-view>
      </view>
    </view>
    <u-select @confirm="confirmArea" v-model="showArea" :current="area.code" :list="areaList" label-name="text"
      value-name="code"></u-select>
    <u-popup v-model="showInfo" border-radius="24" mode="bottom" safe-area-inset-bottom>
      <view class="pop" @click="intoDetail(info.id)">
        <view class="t1">
          <view class="area_name">{{ info.area_name }}</view>
          <view class="tag">
            <text v-if="info.free_type">{{ id2name(info.free_type, free_type) }}</text>
            <text v-if="info.school_class">{{ id2name(info.school_class, school_class) }}</text>
            <text v-if="info.is_record != 0">{{ id2name(info.is_record, is_record) }}</text>
            <text v-if="info.school_nature_ids" v-for="(info, index) in str2arr(info.school_nature_ids)" :key="index">{{
              id2name(info,
              school_nature_ids) }}</text>
          </view>
        </view>
        <view class="name">{{ info.abbreviation }}</view>
        <view class="address">
          <text style="width: 640rpx">{{ info.address
            }}</text>
          <template v-if="!noLocation">
            <text class="distance" v-if="info.distance >= 1">{{ (info.distance * 1).toFixed(2)
              }}km</text>
            <text v-else class="distance">{{ info.distance * 1000 }}m</text>
          </template>
        </view>
        <view class="daohang" @click.stop="goDaohang">
          <image src="https://obs.tuoyupt.com/nanjing/pstac/visit/daohang.svg" class="icon"></image>
          <text>导航</text>
        </view>
      </view>
    </u-popup>
    <u-picker @confirm="bindPickerChange" :params="params" v-model="regionShow" mode="region"></u-picker>
    <u-modal v-model="showTip" width="auto" :show-cancel-button="true" confirm-color="#FF7373" content="content"
      title="开启定位" confirm-text="去开启">
      <image class="tip-image" src="https://obs.tuoyupt.com/miniprogram/client/images/tip_location.png"
        mode="widthFix" />
      <view class="tip-des">需要您的地理位置，可以给您匹配距离最近的托育机构</view>
    </u-modal>
  </view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { onLoad, onReady, onShow } from '@dcloudio/uni-app';
import request from "@/request.js";
import { id2name, str2arr, is_record, school_class, free_type, server_type, school_nature_ids, feed_type } from '@/utils/dataHandle.js'
import User from "@/components/user.vue"
import { getLocation } from '@/utils/cityHandle.js'
const type = ref(1);
const navbarHeight = ref('');
let systemInfo = uni.getSystemInfoSync();
let statusBarHeight = systemInfo.statusBarHeight;
let navHeight = systemInfo.platform == 'ios' ? 44 : 48;
navbarHeight.value = statusBarHeight + navHeight;
onLoad((option) => {
  if (option.type == 2) {
    type.value = 2;
  }
})
const showInfo = ref(false);
const info = ref({});
const tabIndex = ref(1);
const latitude = ref(43.92272);
const longitude = ref(81.33054);
const covers = ref([])
async function onMarkertap(e) {
  let data = list.value.find(item => item.id == e.detail.markerId);
  info.value = data;
  await nextTick();
  showInfo.value = true;
}
let list = ref([])
let status = ref('loadmore');

let navbar = ref(null);
let dropdown = ref(null);
let locationCity = ref({});
let showTip = ref(false);
let noLocation = ref(false);
let triggered = ref(false);
const searchData = reactive({
  is_record: '',
  school_class: '',
  free_type: '',
  server_type: '',
  feed_type: '',
  school_nature_ids: [],
  name: '',
  order: 'distance',
  page: 0,
  per_page: 20,
  lat: '',
  lng: '',
});
const popupData = reactive({
  name: '',
  is_record: '',
  school_class: '',
  free_type: '',
  server_type: '',
  feed_type: '',
  school_nature_ids: [],
});
let searchPopupShow = ref(false)

let loadText = {
  loadmore: '轻轻上拉',
  loading: '努力加载中',
  nomore: '已经加载全部'
}

let toCityPage = ref(true);

// function intoCity() {
//   uni.navigateTo({
//     url: '/pages/common/city',
//     success: (result) => {
//       toCityPage.value = true;
//     }
//   });
// }

function intoDetail(id) {
  if (id) {
    uni.navigateTo({
      url: `/pages_work/visit/detail?id=${id}`,
    });
  }
}

function intoWebview() {
  let url = encodeURIComponent('https://www.tuoyupt.com/h5/register.html');
  uni.navigateTo({
    url: `/pages/webview/index?url=${url}`,
  });
}

function orderList(order) {
  if (noLocation.value && order == 'distance') {
    showTip.value = true;
    return
  }
  searchData.order = order;
  getList(true);
}

function filterHandle(type, id) {
  if (popupData[type] === id) {
    popupData[type] = ''
  } else {
    popupData[type] = id
  }
}

function searchReset() {
  popupData.name = '';
  popupData.is_record = '';
  popupData.school_class = '';
  popupData.free_type = '';
  popupData.server_type = '';
  popupData.feed_type = '';
  popupData.school_nature_ids = [];
}

function clear() {
  popupData.name = '';
  search();
}
let params = {
  province: true,
  city: true,
  area: false,
}
const regionShow = ref(false)
function bindPickerChange(e) {
  locationCity.value = {
    code: e.city.code,
    name: e.city.name,
  };
  area.value = {
    code: "",
    text: "全部"
  }
  uni.setStorageSync('locationCity', locationCity.value);
  getArea();
  getList(true);
  getJingwei();
}
function search() {
  for (const key in popupData) {
    if (Object.hasOwnProperty.call(popupData, key)) {
      const element = popupData[key];
      searchData[key] = element;
    }
  }
  searchPopupShow.value = false;
  dropdown.value.close();
  getList(true);
}

async function refresh() {
  if (triggered.value) return;
  triggered.value = true;
  getList(true);
}

async function onRestore() {
  triggered.value = false; // 需要重置
};
const area = ref({
  code: "",
  text: "全部"
})
const areaList = ref([])
function confirmArea(e) {
  area.value = {
    code: e[0].value,
    text: e[0].label,
  }
  getList(true);
  getJingwei();
}
function getJingwei() {
  request({
    url: `/api/city/getlatlng`,
    data: {
      city: locationCity.value.code,
      address: area.value.text ? area.value.text : locationCity.value.name
    }
  }).then(res => {
    let location = res?.result?.location;
    if (location) {
      latitude.value = location.lat;
      longitude.value = location.lng
      uni.setStorageSync("location", {
        lat: location.lat,
        lng: location.lng,
      });
    }
  })
}
function getList(newlist = false) {
  if (newlist === true) {
    searchData.page = 0;
    status.value = 'loadmnore';
  }
  if (status.value == 'nomore' || status.value == 'loading') {
    return;
  }
  searchData.page += 1;
  status.value == 'loading'
  let params = '';
  for (const key in searchData) {
    if (Object.hasOwnProperty.call(searchData, key)) {
      const element = searchData[key];
      if (Array.isArray(element) && element.length) {
        params += `&${key}=${element}`
      } else if (!Array.isArray(element) && element !== '') {
        params += `&${key}=${element}`
      }
    }
  }
  uni.showLoading()
  request({
    url: `/api/parent/school/list?area=${area.value.code}&city=${locationCity.value.code}${params}`,
  }).then(res => {
    if (newlist === true) {
      list.value = res;
    } else {
      list.value = [...list.value, ...res];
    }
    covers.value = list.value.map(item => {
      return {
        id: item.id,
        latitude: Number(item.lat),
        longitude: Number(item.lng),
        width: 45,
        height: 58,
        iconPath: 'https://obs.tuoyupt.com/miniprogram/parentindex/map1.png'
      }
    })
    triggered.value = false;
    status.value = res.length < searchData.per_page ? 'nomore' : 'loadmore';
    uni.hideLoading();
  })
}
const showArea = ref(false)
function getArea() {
  request({
    url: `/api/auth/area`,
    data: {
      city: locationCity.value.code
    }
  }).then(res => {
    areaList.value = [{
      code: "",
      text: "全部"
    }, ...res]
  })
}
let isFist = true;
onShow(async () => {
  // 获取定位
  if (uni.getStorageSync('location') && uni.getStorageSync('locationCity')) {
    let location = uni.getStorageSync('location');
    searchData.lng = location.lng;
    searchData.lat = location.lat;
    locationCity.value = uni.getStorageSync('locationCity');
    latitude.value = location.lat;
    longitude.value = location.lng;
    getArea();
    if (toCityPage.value) {
      getList(true);
    }
  } else {
    let success = await getLocation();
    if (success) {
      noLocation.value = false;
      let location = uni.getStorageSync('location');
      searchData.lng = location.lng;
      searchData.lat = location.lat;
      locationCity.value = uni.getStorageSync('locationCity');
      latitude.value = location.lat;
      longitude.value = location.lng;
      getArea();
      if (list.value.length == 0) {
        getList(true);
      }
    } else {
      if (isFist) {
        showTip.value = true;
        isFist = false;
      }
      noLocation.value = true;
      searchData.lng = '0';
      searchData.lat = '0';
      locationCity.value = {
        code: 654000,
        name: '伊犁'
      };
      getArea();
      if (list.value.length == 0) {
        orderList('popular')
      }
    }
  }
})
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng),
    name: info.value.abbreviation,
    address: info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
</script>

<style lang="scss">
.pages {
  background-color: #F6F6F6;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.visit-index {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 50vh;

  u-dropdown {
    flex: 1;
    position: absolute;
    width: 100%;
    // display: none;
  }

  .tab-content {
    width: 750rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 10vh;
  }

  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    height: 60rpx;

    .btns {
      width: 212rpx;
      height: 48rpx;
      background: #EEEEEE;
      border-radius: 32rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .btn {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 104rpx;
        height: 44rpx;
        border-radius: 22rpx;
        color: #AEB0BC;
        font-size: 24rpx;
      }

      .active {
        background: #FFFFFF;
        color: #262937;
        font-weight: 600;
      }
    }

  }

  .slot-content {
    background: #fff;
    padding-bottom: 160rpx;
    position: relative;

    .search-list {
      overflow: hidden;
      padding: 0 30rpx;
    }

    .search-title {
      font-size: 24rpx;
      font-weight: 500;
      color: #111111;
      line-height: 34rpx;
      margin: 40rpx auto 0;
      padding: 0 30rpx;
    }

    .search-item {
      float: left;
      width: calc((100vw - 120rpx) / 3);
      height: 60rpx;
      font-size: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #262937;
      border-radius: 8rpx;
      background: #F6F6F6;
      margin: 28rpx 28rpx 0 0;

      &:nth-of-type(3n) {
        margin-right: 0;
      }
    }

    .on {
      color: #FF7373;
      background: #FEEAEA;
      border: 2rpx solid #FF7373;
    }

    .fixed-btn {
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      z-index: 99;
      padding-top: 20rpx;
      height: 160rpx;
      background: #fff;
      display: flex;
      justify-content: center;
      box-shadow: 0 2rpx 0rpx #888992;
    }

    .btn-wrap {
      width: 690rpx;
      border-radius: 16rpx;
      overflow: hidden;
      display: flex;
      border: 2rpx solid #FF7373;
      height: 80rpx;

      .btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FF7373;
        background: #fff;
        height: 80rpx;
      }

      .confirm {
        background: #FF7373;
        color: #fff;
      }
    }
  }

  .search {
    width: 690rpx;
    height: 64rpx;
    margin: 30rpx auto 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;

    .quyu {
      width: 140rpx;
      flex-shrink: 0;
      font-size: 24rpx;
      height: 48rpx;
      color: #000;
      line-height: 48rpx;
      border: 1rpx solid #FF7373;
      border-radius: 24rpx;
      text-align: center;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .input {
      margin-left: 20rpx;
      flex: 1;
      height: 80rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      flex-direction: row;
    }
  }

  .filter {
    height: 80rpx;
    border-radius: 24rpx 24rpx 0 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
    width: 100%;
    font-size: 28rpx;
    font-weight: bold;
    color: #000;
    position: relative;

    .filter-wrap {
      height: 80rpx;
      width: 75%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: #fff;
      position: absolute;
      z-index: 9999;
      left: 0;

      >view {
        height: 100%;
        display: flex;
        align-items: center;
      }
    }

    .dropdown-btn {
      width: 25%;
      position: absolute;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .on {
      color: #FF7373;
    }
  }

  .list {
    overflow: auto;
    width: 100%;
    flex: 1;
    min-height: 1rpx;
    overflow: hidden;
    box-sizing: border-box;
    background: #fff;
  }
}

.pop {
  width: 750rpx;
  padding: 30rpx;

  .address {
    font-size: 24rpx;
    color: #787C8D;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .distance {
      color: #262937;
    }
  }

  .daohang {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    padding-top: 20rpx;
    border-top: 1rpx solid #EEEEEE;
    margin-top: 20rpx;

    .icon {
      width: 32rpx;
      height: 32rpx;
    }

    color: #1296db;
    font-size: 28rpx;
  }

  .t1 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .area_name {
      font-size: 28rpx;
      font-weight: 500;
      color: #262937;
    }

    .tag {
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;
      margin-top: 14rpx;
      margin-bottom: 14rpx;

      text {
        font-size: 20rpx;
        color: #262937;
        padding: 0 16rpx;
        margin-right: 8rpx;
        margin-bottom: 8rpx;
        background: #F6F6F6;
        border-radius: 8rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
      }
    }
  }

  .name {
    font-size: 32rpx;
    font-weight: 600;
    color: #262937;
    margin: 12rpx 0;
    line-height: 40rpx;
    width: 100%;
  }
}

.item {
  height: 240rpx;
  padding: 0 30rpx;
  display: flex;
  margin-bottom: 20rpx;
  background: #fff;
  margin-top: 10rpx;

  .msg {
    margin-left: 16rpx;
    flex: auto;

    .name {
      font-size: 28rpx;
      font-weight: 500;
      color: #262937;
      margin: 12rpx 0;
      line-height: 28rpx;
      width: 100%;
    }

    .address {
      font-size: 24rpx;
      color: #787C8D;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .distance {
        color: #262937;
      }
    }

    .tag {
      display: flex;
      flex-wrap: wrap;
      height: 88rpx;
      overflow: hidden;
      margin-top: 14rpx;
      margin-bottom: 14rpx;

      text {
        font-size: 20rpx;
        color: #262937;
        padding: 0 16rpx;
        margin-right: 8rpx;
        margin-bottom: 8rpx;
        background: #F6F6F6;
        border-radius: 8rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
      }
    }

    .num {
      font-size: 24rpx;
      color: #FF7373;
    }
  }
}
</style>
