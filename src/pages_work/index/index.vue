<template>
    <template v-if="loading">
        <skeleton />
    </template>
    <template v-else>
        <view v-if="isTrail" class="trail">
            <u-navbar :is-fixed="false" title="" :background="{ background: '#F6F6F6' }" :border-bottom="false"
                :is-back="false">
                <view class="slot-wrap">
                    <view class="title">
                        首页
                    </view>
                </view>
            </u-navbar>
            <Nav @goWebView="goWebView" :type="1" :list="nav1"></Nav>
            <Banner @goWebView="goWebView" :type="1" :list="banner1" />
            <Example :list="excellentSchools" />
            <Visit :city="locationCity" :list="nearSchool"></Visit>
            <Poster :list="playbill"></Poster>
            <!-- <AmusementParkTrail /> -->
            <News />
            <!-- <News2 :list="zcNews" /> -->
            <Gzh />
            <view class="footer">
                <!-- 京学集团 技术支持 -->
            </view>
        </view>
        <template v-else>
            <u-navbar :is-fixed="isFixed" title="" :background="{ background: topBackground }" :border-bottom="false"
                :is-back="false">
                <view class="slot-wrap">
                    <view class="title">
                        首页
                    </view>
                    <view class="select">
                        {{
                            headerInfo.school_wechatname
                        }}
                    </view>
                    <!-- <view class="ai">
                        <image @click="toWenwen" class="cion"
                            src="https://obs.tuoyupt.com/miniprogram/parentindex/wenwen.png">
                        </image>
                        <view class="tips" v-if="showTips">
                            <text class="text">hi～我是您的AI育儿助手问问。</text>
                            <view class="btn" @click="toWenwen">问一下</view>
                            <u-icon @click.stop="closeTips" name="close" color="#FFFFFF" size="32"></u-icon>
                        </view>
                    </view> -->
                </view>
            </u-navbar>
            <Header :info="headerInfo" :live="live" :styleType="styleType"></Header>
            <Nav @goWebView="goWebView" :type="2" :list="nav"></Nav>
            <!-- <AmusementPark :styleType="styleType" /> -->
            <Banner @goWebView="goWebView" :type="2" :list="banner" />
            <Attendance :info="attendall"></Attendance>
            <!-- <Air /> -->
            <Day :daily="daily" :recipes="recipes"></Day>
            <Poster :list="playbill"></Poster>
            <Plan :info="plan"></Plan>
            <News />
            <view class="footer">
                <!-- 京学集团 技术支持 -->
            </view>
        </template>
    </template>
    <SurveyPop></SurveyPop>
    <tabbar :showMsg="!isTrail" v-model="currentTab" :border-top="false">
    </tabbar>
</template>

<script setup>
import { ref, nextTick, computed, onMounted } from "vue";
import { onPageScroll, onPullDownRefresh, onShow } from "@dcloudio/uni-app";
import { getLocation } from '@/utils/cityHandle.js';
import SurveyPop from "./components/surveypop.vue";
import request from "@/request";
import skeleton from "../components/skeleton/index.vue";
import Header from "./components/header.vue";
import Nav from "./components/nav.vue";
// import AmusementPark from "./components/amusement_park.vue";
// import AmusementParkTrail from "./components/amusement_park_trail.vue"
import tabbar from "@/components/tabbar.vue";
import Banner from "./components/banner.vue";
import Poster from "./components/poster.vue"
import Attendance from "./components/attendance.vue";
import Plan from "./components/plan.vue";
import News from "./components/news.vue";
import News2 from "./components/news2.vue";
// import Gzh from "./components/gzh.vue";
// import Air from "./components/air.vue"
import Day from "./components/day.vue";
import Visit from "./components/visit.vue";
import Example from "./components/example.vue";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const loading = ref(true);
const isTrail = ref(true)

onShow(() => {
    if (uni.getStorageSync("token")) {
        getData();
    } else {
        noInit();
    }
})
const isFixed = ref(false);
const topF = ref(false)
onPageScroll((e) => {
    let scrollTop = e.scrollTop;
    if (scrollTop < 3) {
        isFixed.value = false;
    } else {
        isFixed.value = true;
    }
    if (scrollTop < 20) {
        isFixed.value = false;
        topF.value = false;
    } else {
        isFixed.value = true;
        topF.value = true;
    }
})
const theme = {
    boy: {
        background: "#E4E9FF",
        avatar: "https://obs.tuoyupt.com/miniprogram/parentindex/nbaby.png"
    },
    girl: {
        background: "#FFEBE3",
        avatar: "https://obs.tuoyupt.com/miniprogram/parentindex/nvbaby.png"
    }
}
const topBackground = computed(() => {
    return topF.value ? '#FFFFFF' : styleType.value == 2 ? theme.girl.background : theme.boy.background
})
onMounted(() => {
    let time = new Date();
    let nowStr = `${time.getFullYear()}${time.getMonth()}${time.getDate()}`;
    let isShow = uni.getStorageSync("index_ai_tips");
    let isLogin = uni.getStorageSync("isLogin");
    if (!isShow && isLogin) {
        showTips.value = true;
        setTimeout(() => {
            showTips.value = false;
        }, 2000)
        uni.setStorageSync("index_ai_tips", `${time.getFullYear()}${time.getMonth()}${time.getDate()}`)
    }
    if (isShow && isLogin) {
        if (isShow !== nowStr) {
            showTips.value = true;
            setTimeout(() => {
                showTips.value = false;
            }, 2000)
            uni.setStorageSync("index_ai_tips", `${time.getFullYear()}${time.getMonth()}${time.getDate()}`)
        }
    }
})
const showTips = ref(false);
function closeTips() {
    showTips.value = false;
}
const toWenwen = () => {
    uni.navigateTo({
        url: "/pages_work/wenwen/index"
    })
}
async function getData() {
    await userInfo.refrsh();
    if (userInfo.identity == 2) {
        isTrail.value = false;
        init();
    } else {
        noInit();
    }
}
async function init() {
    await getBanner();
    await getDay();
    await nextTick();
    loading.value = false;
}
async function noInit() {
    await getBanner1();
    await getInfo1();
    loading.value = false;
}
const nav1 = ref([])
const banner1 = ref([]);
const industryNews = ref([]);
const zcNews = ref([]);
const excellentSchools = ref([])
const nearSchool = ref([])
function getBanner1() {
    return request({
        url: "/api/auth/menubannernologin",
        data: {
            student_id: userInfo.student_id,
            equip_type: 3
        }
    }).then(res => {
        banner1.value = res.banner;
        let [e, nav2] = res.list;
        nav1.value = nav2
    })
}
const location = ref({
    "lat": 36.195499,
    "lng": 113.116000
})
const locationCity = ref({
    code: 510500,
    name: '泸州'
})
async function getInfo1() {
    // let locationS = uni.getStorageSync('location');
    // let locationCityS = uni.getStorageSync('locationCity');
    // if (locationS && locationCityS) {
    //     location.value = locationS;
    //     locationCity.value = locationCityS;
    // } else {
    //     let success = await getLocation();
    //     if (success) {
    //         locationCityS = uni.getStorageSync('locationCity');
    //         locationS = uni.getStorageSync('location');
    //         location.value = locationS;
    //         locationCity.value = locationCityS;
    //     }
    // }
    return request({
        url: "/api/parent/indexinfo/customerinfo",
        data: {
            lng: location.value.lng,
            lat: location.value.lat,
            city: locationCity.value.code,
        }
    }).then(res => {
        playbill.value = res.playbill;
        industryNews.value = res.industryNews;
        zcNews.value = res.zcNews;
        excellentSchools.value = res.excellentSchools;
        nearSchool.value = res.nearSchool;
    })
}
onPullDownRefresh(async () => {
    if (isTrail.value) {
        await getBanner1();
        await getInfo1();
    } else {
        await getBanner();
        await getDay();
    }
    uni.stopPullDownRefresh()
})
const currentTab = ref(1);
//banner info
const banner = ref([])
const titl = ref([]);
const nav = ref([])
const styleType = ref(1)
const headerInfo = ref({
    age: "",
    back_img_url: "",
    bmi: 0,
    height: 0,
    school_id: 2,
    school_wechatname: "",
    sex: 2,
    student_id: "",
    student_name: "",
    weight: 0,
})
function getBanner() {
    return request({
        url: "/api/parent/indexinfo/menubanner",
        data: {
            student_id: userInfo.student_id,
            equip_type: 3
        }
    }).then(res => {
        banner.value = res.banner;
        headerInfo.value = res.info;
        let [e, titl1, nav1] = res.list;
        titl.value = titl1;
        nav.value = nav1;
        if (res.info.sex == 2) {
            styleType.value = 2;
        } else {
            styleType.value = 1;
        }
    })
}
const plan = ref({})
const playbill = ref([])
const attendall = ref({
    list: []
})
const recipes = ref([])
const daily = ref([])
const live = ref({
    album: 0,
    deleted_at: null,
    evaluation: 0,
    examination: 0,
    id: 3,
    life_record: 0,
    life_topten: 0,
    life_week: 0,
    photo: 0,
    report: 0,
    student_id: 0,
    video: 0,
})
function goWebView(url) {
    let token = uni.getStorageSync("token");
    let surl = ''
    if (url.indexOf('?') > -1) {
        surl = `${url}&token=${token}&school_id=${headerInfo.value.school_id ?? ''}&parent_id=${userInfo.parent_id}&student_id=${userInfo.student_id}&class_id=${userInfo.class_id}`
    } else {
        surl = `${url}?token=${token}&school_id=${headerInfo.value.school_id ?? ''}&parent_id=${userInfo.parent_id}&student_id=${userInfo.student_id}&class_id=${userInfo.class_id}`
    }
    uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(surl)}`
    })
}
function getDay() {
    return request({
        url: "/api/parent/indexinfo/datainfo",
        data: {
            student_id: userInfo.student_id,
            equip_type: 3
        }
    }).then(res => {
        if (res.plan) {
            plan.value = res.plan;
        }
        playbill.value = res.playbill;
        attendall.value = res.attendall;
        recipes.value = res.recipes;
        daily.value = res.daily;
        if (!isEmpty(res.live)) {
            live.value = res.live;
        }
    }).catch(err => {
        if (err.code == 203) {
            if (uni.getStorageSync("token")) {
                isTrail.value = true;
                uni.clearStorageSync()
                noInit();
            }
        }
    })
}
function isEmpty(obj) {
    return Object.keys(obj).length === 0 && obj.constructor === Object;
}  
</script>


<style lang="scss" scoped>
.pages {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #FFF;

    .ssection {
        flex: 1;
        position: relative;
        z-index: 2;
        min-height: 1rpx;

        .scroll {
            width: 100vw;
            height: 100%;
            position: relative;
        }
    }
}

.footer {
    width: 100%;
    height: 188rpx;
    line-height: 188rpx;
    text-align: center;
    font-size: 24rpx;
    font-weight: 400;
    color: #AEB0BC;
}

.zhanwei {
    height: 140rpx;
    height: calc(constant(safe-area-inset-bottom) + 140rpx);
    height: calc(env(safe-area-inset-bottom) + 140rpx);
}

.loading {
    width: 100vw;

    height: 100vh;

    .des {
        width: 690rpx;
        height: 100rpx;
        left: 30rpx;
        margin-bottom: 40rpx;
        margin-left: 30rpx;
    }
}

.trail {
    background: #f6f6f6;
}

.slot-wrap {
    width: 100%;
    display: flex;
    flex-direction: row;
    height: 100%;
    align-items: center;
    box-sizing: border-box;
    padding: 0 20rpx 0 30rpx;

    .title {
        font-size: 40rpx;
        font-weight: 600;
        color: #000000;
    }

    .select {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
        margin-left: 8rpx;
    }

    .ai {
        margin-left: auto;
        position: relative;

        .tips {
            width: 690rpx;
            height: 80rpx;
            background: rgba(38, 41, 55, 0.8);
            border-radius: 40rpx;
            position: absolute;
            display: flex;
            flex-direction: row;
            align-items: center;
            box-sizing: border-box;
            padding: 0 38rpx;
            transform: translateX(-440rpx);
            top: 84rpx;
            z-index: 4;

            .text {
                font-size: 28rpx;
                font-weight: 400;
                color: #FFFFFF;
                line-height: 40rpx;
            }

            .btn {
                width: 112rpx;
                height: 48rpx;
                background: #FFFFFF;
                border-radius: 32rpx;
                text-align: center;
                font-size: 24rpx;
                color: #262937;
                line-height: 48rpx;
                margin-left: auto;
                margin-right: 34rpx;
            }

        }

        .cion {
            width: 64rpx;
            border-radius: 50%;
            height: 64rpx;
        }
    }
}
</style>
