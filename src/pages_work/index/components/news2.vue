<template>
  <view class="content">
    <view class="top">
      <image class="icon" src="https://obs.tuoyupt.com/miniprogram/parentindex/zhengce.png">
      </image>
      <text>政策信息</text>
    </view>
    <view class="item" v-for="item in list" :key="item.id" @click="goDetail(item)">
      {{ item.title }}
    </view>
    <view class="more" @click="goNews">
      查看更多政策信息 <u-icon name="arrow-right"></u-icon>
    </view>
  </view>
</template>
<script setup>
defineProps(['list'])
function goDetail(item) {
  let url = `https://www.tuoyupt.com/detail.html?id=${item.id}&type=article`
  uni.navigateTo({
    url: `/pages/webview/index?url=${encodeURIComponent(url)}`
  })
}
function goNews() {
  uni.navigateTo({
    url: `/pages/index/news?type=2`
  })
}
</script>
<style scoped lang="scss">
.content {
  width: 690rpx;
  min-height: 632rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx auto;

  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 130rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #787C8D;
    padding-left: 30rpx;

    .icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
  }

  .item {
    width: 690rpx;
    border-bottom: 1rpx solid #F7F7F7;
    font-size: 32rpx;
    font-weight: 600;
    color: #262937;
    line-height: 44rpx;
    padding: 30rpx;
    box-sizing: border-box;
  }

  .more {
    text-align: center;
    font-size: 28rpx;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 120rpx;
    height: 120rpx;
  }
}
</style>