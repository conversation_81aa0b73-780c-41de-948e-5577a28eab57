<template>
  <view class="banner" :style="{ height: type == 1 ? '228rpx' : '128rpx' }" v-if="list.length">
    <u-swiper :height="type == 1 ? 228 : 128" border-radius="20" name="banner_url" @click="onClick" :list="list"
      mode="rect"></u-swiper>
  </view>
</template>
<script setup>
import { inject } from "vue";
const emit = defineEmits(['goWebView'])
const props = defineProps({
  list: {
    type: Array,
    default() {
      return []
    }
  },
  nextShowBtn: {
    type: Boolean,
    default: true
  },
  type: Number
})
const currentClass = inject("currentClass", {})
function goRouter(item) {
  if (item.jump_type == 2) {
    if (item.can_click == 2) {
      uni.showToast({
        title: item.alert_msg,
        icon: "none"
      })
      return
    }
    emit("goWebView", item.h5_url)
  } else if (item.jump_type == 1) {
    if (item.can_click == 2) {
      uni.showToast({
        title: item.alert_msg,
        icon: "none"
      })
      return
    }
    let url = "";
    if (!props.nextShowBtn) {
      // 明确传no避免undefine null
      url = `${item.h5_url}?nextShowBtn=no&class_id=${currentClass.value.id}&class_name=${currentClass.value.name}`
    } else {
      url = item.h5_url
    }
    uni.navigateTo({
      url
    })
  }
}
function onClick(index) {
  goRouter(props.list[index])
}
</script>
<style lang="scss" scoped>
.banner {
  width: 690rpx;
  height: 128rpx;
  background: #FF87BC;
  border-radius: 24rpx;
  margin: 40rpx auto;
}
</style>
