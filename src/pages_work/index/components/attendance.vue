<template>
  <view class="content">
    <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/riliback.png">
    </image>
    <view class="top">
      <view class="title">考勤日历</view>
      <view class="time">截至{{ info.today_date }}</view>
      <view class="btn" @click="goRouter">查看详情</view>
    </view>
    <view class="table">
      <image class="back1" src="https://obs.tuoyupt.com/miniprogram/parentindex/rili.png">
      </image>
      <view class="nums">
        <text class="t1">{{ info.shidao_num }}</text>
        <text class="t2 ">（天）出勤</text>
        <text class="t2 t3">缺勤{{ info.queqin_num }}天</text>
        <text class="t2 t4">病假{{ info.bingjia_num }}天</text>
        <text class="t2 t4">事假{{ info.shijia_num }}天</text>
      </view>
      <view class="hr"></view>
      <view class="dots">
        <view class="dot" :class="[getStats(item.state)]" v-for="item of info.list" :key="item.Ymd">
          {{ isToday(item.Ymd) ? '今' : '' }}
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import dayjs from "dayjs";
let now = dayjs();
defineProps({
  info: Object
})
function getStats(state) {
  let str = ""
  switch (state) {
    case 2:
      str = 'dot1'
      break;
    case 0:
    case 3:
    case 4:
      str = 'dot2'
      break;
  }
  return str;
}
function goRouter() {
  uni.navigateTo({
    url: "/pages_work/attendance/index"
  })
}
function isToday(Ymd) {
  return now.isSame(dayjs(Ymd), 'day')
}
</script>
<style lang="scss" scoped>
.content {
  width: 690rpx;
  height: 486rpx;
  background: #E4E9FF;
  border-radius: 24rpx;
  margin: 24rpx auto;
  position: relative;

  .back {
    width: 690rpx;
    height: 286rpx;
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
  }

  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    padding: 40rpx 30rpx 0;
    position: relative;
    z-index: 2;


    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #262937;
    }

    .time {
      font-size: 24rpx;
      font-weight: 400;
      color: #787C8D;
      margin-left: 20rpx;
    }

    .btn {
      width: 112rpx;
      height: 40rpx;
      background: #FFFFFF;
      border-radius: 20rpx;
      font-size: 20rpx;
      font-weight: 400;
      color: #000000;
      line-height: 40rpx;
      text-align: center;
      margin-left: auto;
    }
  }

  .table {
    width: 658rpx;
    height: 338rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    position: relative;
    z-index: 2;
    margin: 40rpx auto 0;
    padding-top: 72rpx;

    .back1 {
      position: absolute;
      width: 658rpx;
      height: 82rpx;
      left: 0rpx;
      top: -30rpx;
    }

    .nums {
      width: 598rpx;
      height: 90rpx;
      background: #F6F6F6;
      border-radius: 16rpx;
      display: flex;
      flex-direction: row;
      align-items: baseline;
      margin: 0 auto 0;
      box-sizing: border-box;
      padding: 16rpx 20rpx;

      .t1 {
        font-size: 48rpx;
        font-family: RousseauDeco;
        color: #262937;
      }

      .t2 {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
      }

      .t3 {
        margin-left: auto;
      }

      .t4 {
        margin-left: 20rpx;
      }
    }

    .hr {
      width: 598rpx;
      margin: 20rpx auto 40rpx;
      border-top: 1rpx dashed #bbb;
    }

    .dots {
      display: flex;
      flex-direction: row;
      width: 610rpx;
      flex-wrap: wrap;
      margin-left: 30rpx;

      .dot {
        width: 28rpx;
        height: 28rpx;
        background: #F6F6F6;
        border-radius: 8rpx;
        margin-right: 13rpx;
        color: #000;
        line-height: 28rpx;
        font-size: 24rpx;
        margin-bottom: 20rpx;
        text-align: center;
      }

      .dot1 {
        background: #40E08F;
        color: #FFF;
      }

      .dot2 {
        color: #FFF;
        background: #FF7373;
      }
    }
  }
}
</style>