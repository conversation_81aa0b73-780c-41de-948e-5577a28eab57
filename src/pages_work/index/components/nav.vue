<template>
  <view class="content" v-if="list.length">
    <view class="nav" v-for="item in list" :key="item.id" @click="goRouter(item)">
      <image class="icon" :class="{ iconB: type == 1 }" :src="item.icon_url">
      </image>
      <text class="name" :class="{ nameB: type == 1 }">
        {{ item.icon_name }}
      </text>
    </view>
  </view>
</template>
<script setup>
import { inject } from 'vue';
const emit = defineEmits(['goWebView'])
const props = defineProps({
  list: {
    type: Array,
    default: []
  },
  type: Number,
  nextShowBtn: {
    type: Boolean,
    default: true
  }
})
const currentClass = inject("currentClass", {})
function goRouter(item) {
  if (item.jump_type == 2) {
    if (item.can_click == 2) {
      uni.showToast({
        title: item.alert_msg,
        icon: "none"
      })
      return
    }
    emit("goWebView", item.h5_url)
  } else if (item.jump_type == 1) {
    if (item.can_click == 2) {
      uni.showToast({
        title: item.alert_msg,
        icon: "none"
      })
      return
    }
    let url = "";
    if (!props.nextShowBtn) {
      // 明确传no避免undefine null
      url = `${item.h5_url}?nextShowBtn=no&class_id=${currentClass.value.id}&class_name=${currentClass.value.name}`
    } else {
      url = item.h5_url
    }
    uni.navigateTo({
      url
    })
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx 10rpx;

  .nav {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24rpx;

    .icon {
      width: 64rpx;
      height: 64rpx;
    }

    .iconB {
      width: 96rpx;
      height: 96rpx;
    }

    .name {
      font-size: 24rpx;
      font-weight: 400;
      color: #999999;
      margin-top: 20rpx;
    }

    .nameB {
      font-size: 26rpx;
    }
  }
}
</style>