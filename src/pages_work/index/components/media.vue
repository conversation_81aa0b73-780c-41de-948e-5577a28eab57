<template>
  <view class="content">
    <view class="top">
      <view class="title">成长资源</view>
    </view>
    <scroll-view :scroll-x="true" class="scroll" :show-scrollbar="false">
      <view class="warp">
        <view class="item" @click="goRouter(1)">
          <image class="ic" src="https://obs.tuoyupt.com/yili/index/gushi.png">
          </image>
        </view>
      </view>
      <view class="warp">
        <view class="item" @click="goRouter(2)">
          <image class="ic" src="https://obs.tuoyupt.com/yili/index/geyao.png">
          </image>
        </view>
      </view>
      <view class="warp">
        <view class="item" @click="goRouter(3)">
          <image class="ic" src="https://obs.tuoyupt.com/yili/index/huiben.png">
          </image>
        </view>
      </view>
      <view class="zhanwei"></view>
    </scroll-view>
  </view>
</template>
<script setup>
function goRouter(id) {
  if (id == 1) {
    uni.navigateTo({
      url: `/pages/pack/media-library/index`
    })
  }
  if (id == 2) {
    uni.navigateTo({
      url: `/pages/pack/media-library/index2`
    })
  }
  if (id == 3) {
    uni.navigateTo({
      url: `/pages/pack/media-library/index3`
    })
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 690rpx;
  height: 320rpx;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 24rpx;
  margin: 0 auto;
  overflow: hidden;
  background: url(https://obs.tuoyupt.com/yili/index/back.png) 100%/100% no-repeat;

  .top {
    height: 100rpx;
    position: relative;

    .lena {
      height: 64rpx;
      width: 370rpx;
      position: absolute;
      right: 0rpx;
      bottom: -1px;
    }

    .dota {
      height: 34rpx;
      width: 96rpx;
      position: absolute;
      left: 210rpx;
      top: 34rpx;
    }

    .title {
      top: 34rpx;
      left: 30rpx;
      position: absolute;
      font-size: 32rpx;
      font-weight: 600;
      color: #000;
    }
  }

  .scroll {
    width: 100%;
    white-space: nowrap;
    height: 220rpx;
    padding-top: 30rpx;

    .zhanwei {
      width: 30rpx;
      display: inline-block;
    }

    .warp {
      display: inline-block;
      height: 160rpx;
    }

    .item {
      flex-shrink: 0;
      margin-left: 30rpx;

      .ic {
        width: 192rpx;
        height: 168rpx;
      }

    }
  }
}
</style>