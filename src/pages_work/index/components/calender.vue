<template>
  <view class="container">
    <view class="tmt-header">
      日程
    </view>
    <view class="tmt-content">
      <view class="tmt-week-wrap">
        <view class="cell week-item" v-for="(item, index) in week" :key="item.label">{{
          item.label }}
        </view>
      </view>
      <view class="tmt-day-wrap" :style="[{ height: '100rpx' }]">
        <view class="day-content">
          <view class="cell"
            :class="{ selectDay: current.year == item.year && current.month == item.month && current.day == item.day }"
            v-for="(item, index) in daysArrC" :key="item.id" @click="changeDay(item)">
            <view class="dayText" :class="{ weekend: item.isWeekend, today: item.today, }">
              {{ item.day }}
            </view>
            <view class="point" v-if="item.have_act"></view>
            <view class="num" v-if="item.have_act == 1">
              <view v-if="item.act_arr.filter(item => item.cat_id == 2).length" style="color: #FFBF00;">生日提醒</view>
              <view v-if="item.act_arr.filter(item => item.cat_id == 1).length" style="color: #1890FF;">
                {{ item.act_arr.filter(item => item.cat_id != 2)[0].list[0].content }}
              </view>
              <view v-if="item.act_data.filter(item => item.cat_name == '机构活动').length" style="color: #B37FEB;">
                机构活动
              </view>
              <view v-if="item.act_data.filter(item => item.cat_name == '重要备忘').length" style="color: #52C41A;">
                重要备忘
              </view>
            </view>
            <view class="num" v-if="item.have_act > 1">{{ item.have_act }}个事项</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="section">
    <view class="donghua" :style="{ height: `${length == 0 ? 48 : isExpand ? contentHeigth : firstHeight}px` }">
      <view class="event" id="event-content">
        <view class="event-item" id="event-item" v-for="(item, i) in act_arr" :key="item.cat_id">
          <view class="top">
            <image class="icon" :src="getIcon(item.cat_id)"></image>
            <view class="name">{{ item.cat_name }}</view>
            <view class="btn" @click="toDetail(item)">{{ getBtnName(item.cat_id) }}</view>
          </view>
          <template v-if="item.cat_id == 2">
            <template v-if="item.list?.student?.length">
              <view class="msg">
                {{ item.list.student.length }}位宝宝生日（{{ item.list.student.map(i => i.student_name).join('、') }}）
              </view>
            </template>
            <template v-if="item.list?.teacher?.length">
              <view class="msg">
                {{ item.list.teacher.length }}位托育人员生日（{{ item.list.teacher.map(i => i.teacher_name).join('、') }}）
              </view>
            </template>
          </template>
          <template v-else>
            <view class="msg" v-for="it in item.list" :key="it.id">
              {{ it.content }}
            </view>
          </template>
        </view>
        <view class="event-item" id="event-item" v-for="(item, i) in act_data.filter(item => item.cat_name == '机构活动')"
          :key="item.cat_id">
          <view class="top">
            <image class="icon" :src="getIcon(3)"></image>
            <view class="name">{{ item.cat_name }}</view>
            <!-- <view class="btn">{{ getBtnName(item.cat_id) }}</view> -->
          </view>
          <view class="msg activeName" v-for="it in item.list" :key="it.id" @click="toDetail(it)">
            {{ it.title }}
          </view>
        </view>
        <view class="event-item" id="event-item" v-for="(item, i) in act_data.filter(item => item.cat_name == '重要备忘')"
          :key="item.cat_id">
          <view class="top">
            <image class="icon" :src="getIcon(4)"></image>
            <view class="name">{{ item.cat_name }}</view>
            <!-- <view class="btn">{{ getBtnName(item.cat_id) }}</view> -->
          </view>
          <template v-for="it in item.list" :key="it.id">
            <view class="msg" @click="toDetail(it)">
              {{ it.title }}
            </view>
            <view class="content" @click="toDetail(it)">
              {{ it.content }}
            </view>
          </template>

        </view>
        <view class="emtpy" v-if="length == 0">
          暂无日程
        </view>
      </view>
    </view>
    <view class="more" v-if="length > 1" @click="isExpand = !isExpand">
      展开 <u-icon class="icon" :class="{ active: isExpand }" name="arrow-down"></u-icon>
    </view>
  </view>
</template>

<script setup>
import dayjs from 'dayjs';
import { getCurrentInstance } from 'vue';
import { nextTick } from 'vue';
import { ref, onMounted } from 'vue';
const props = defineProps({
  daysArr: {
    type: Array,
  }
})
const isExpand = ref(false);
const length = ref(0);
const act_arr = ref([]);
const act_data = ref([])
const week = [{
  label: '一',
  value: 1
},
{
  label: '二',
  value: 2
},
{
  label: '三',
  value: 3
},
{
  label: '四',
  value: 4
},
{
  label: '五',
  value: 5
},
{
  label: '六',
  value: 6
}, {
  label: '日',
  value: 7
}
];
const today = ref({});
const current = ref({});
onMounted(() => {
  let setTime = new Date();
  let year = setTime.getFullYear()
  let month = setTime.getMonth() + 1
  let day = setTime.getDate()
  current.value = {
    year,
    month,
    day,
  }
  today.value = {
    year,
    month,
    day,
  }
})
let now = dayjs();
async function changeDay(item) {
  current.value = item;
  act_arr.value = item.act_arr;
  act_data.value = item.act_data;
  length.value = Object.keys(act_arr.value).length + Object.keys(act_data.value).length;
  if (length.value > 0) {
    await nextTick();
    getHeight();
  }
}
const daysArrC = ref([]);
async function makeCalendar() {
  let arr = []
  for (let i = 0; i < props.daysArr.length; i++) {
    let item = props.daysArr[i];
    let day = dayjs(item.Ymd);
    let currentDay = dayjs(`${current.value.year}-${current.value.month}-${current.value.day}`);
    let isCurrent = currentDay.isSame(day, 'day');
    let today = now.isSame(day, 'day');
    let have_act = 0
    if (!Array.isArray(item.act_data)) {
      item.act_data = Object.keys(item.act_data).map(key => item.act_data[key])
    }
    if (!item.act_arr) {
      item.act_arr = []
    }
    if (!Array.isArray(item.act_arr)) {
      item.act_arr = Object.keys(item.act_arr).map(key => item.act_arr[key])
    }
    have_act = item.act_data.length + item.act_arr.length
    if (isCurrent) {
      act_arr.value = item.act_arr;
      act_data.value = item.act_data
      length.value = have_act;
      if (length.value > 0) {
        await nextTick();
        setTimeout(() => {
          getHeight();
        }, 0)
      }
    }
    arr.push({
      act_arr: item.act_arr,
      act_data: item.act_data,
      year: day.year(),
      month: day.month() + 1,
      day: item.day,
      isWeekend: day.day() == 0 || day.day() == 6,
      id: item.id,
      have_act: have_act,
      today: today
    })
  }
  daysArrC.value = arr;
}
const { proxy } = getCurrentInstance()
const firstHeight = ref(0)
const contentHeigth = ref(0)
// 计算动画的高度
async function getHeight() {
  let firstItem = await getSize('#event-item');
  let content = await getSize('#event-content');
  firstHeight.value = firstItem.height;
  contentHeigth.value = content.height;
}
function getSize(query) {
  return new Promise((res) => {
    uni.createSelectorQuery().in(proxy).select(query).fields({
      size: true,
    }, function (data) {
      res(data)
    }).exec()
  })
}
function getIcon(id) {
  let str = ''
  switch (id) {
    case 1:
      str = 'https://obs.tuoyupt.com/miniprogram/index/ii4.png';
      break;
    case 2:
      str = 'https://obs.tuoyupt.com/miniprogram/index/ii3.png';
      break;
    case 3:
      str = 'https://obs.tuoyupt.com/miniprogram/index/ii2.png';
      break;
    case 4:
      str = 'https://obs.tuoyupt.com/miniprogram/index/ii1.png';
      break;
  }
  return str;
}
function getBtnName(id) {
  let str = ''
  switch (id) {
    case 1:
      str = '海报';
      break;
    case 2:
      str = '祝福';
      break;
    case 3:
      str = '查看';
      break;
    case 4:
      str = '查看';
      break;
  }
  return str;
}
function toDetail(item) {
  console.log(item)
  if (item.cat_id == 1) {
    uni.navigateTo({
      url: '/pages_work/poster/index',
      success: (result) => {
      },
      fail: () => { },
      complete: () => { }
    });
  } else if (item.cat_id == 2) {
    uni.navigateTo({
      url: `/pages_work/notice/push`
    })
  } else if (item.type == 0) { //重要备忘
    uni.navigateTo({
      url: `/pages_work/index/active_notice_detail?id=${item.id}`
    })
  } else if (item.type == 1) {

    uni.navigateTo({
      url: '/pages_work/message/news_page?newsType=activeJG&id=' + item.id,
      success: (result) => {
      },
      fail: () => { },
      complete: () => { }
    });
  }
}
defineExpose({
  makeCalendar
})
</script>

<style lang="scss">
.p20 {
  padding: 20upx;
}

.container {
  width: 690rpx;
  margin: 20rpx auto 0;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  padding: 0 20upx 20rpx;
  background: linear-gradient(180deg, #EAF6FF 0%, #F8FCFF 100%);
}

.tmt-header {
  font-size: 32rpx;
  font-weight: 500;
  color: #787C8D;
  line-height: 32rpx;
  padding-top: 40rpx;
  padding-left: 12rpx;
  padding-bottom: 20rpx;
}

.selectDay {
  border: 1rpx solid #000 !important;
  border-radius: 50%;
  box-sizing: border-box;
  transform: rotateZ(360deg);
}

.cell {
  width: 64rpx;
  height: 64rpx;
  text-align: center;
  line-height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;
  border: 1rpx solid transparent;
  box-sizing: border-box;
  transform: rotateZ(360deg);

  .point {
    width: 6upx;
    height: 6upx;
    position: absolute;
    bottom: 8upx;
    border-radius: 50%;
    left: 50%;
    transform: translateX(-50%);
    background: #000000;
  }

  .num {
    position: absolute;
    bottom: -35rpx;
    font-size: 20rpx;
    width: 130rpx;
    left: 50%;
    font-family: 'Rousseau-Deco';
    transform: translateX(-50%);
    line-height: 30rpx;
    color: #f00;
  }

  .dayText {
    width: 56upx;
    height: 56upx;
    text-align: center;
    border-radius: 50%;
    font-family: 'Rousseau-Deco';
    font-size: 24rpx;
  }



  .today {
    color: #617EFB;
  }

  .weekend {
    color: #999999;
  }
}

.tmt-content {
  padding-bottom: 20upx;

  .tmt-week-wrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .week-item {
      font-size: 20rpx;
      font-weight: 400;
      color: #999999;
    }
  }

  .tmt-day-wrap {
    transition: height .3s;
    // overflow: hidden;

    .day-content {
      transition: transform .3s;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: space-between;
    }
  }
}

.section {
  width: 690rpx;
  border-radius: 0rpx 0rpx 24rpx 24rpx;
  overflow: hidden;
  margin: 0 auto;
}

.more {
  width: 690rpx;
  height: 60rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #787C8D;
  line-height: 60rpx;
  background: #FFFFFF;

  .icon {
    transition: transform ease .5s;
  }

  .active {
    transform: rotate(180deg);
  }
}

.donghua {
  transition: height ease .5s;
  overflow: hidden;
}

.event {
  width: 100%;
  background: #FFFFFF;
  margin: 0 auto;

  .emtpy {
    width: 690rpx;
    height: 96rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    line-height: 96rpx;
    text-align: center;
    font-size: 24rpx;
    font-weight: 500;
    color: #787C8D;
  }



  .event-item {
    width: 624rpx;
    border-bottom: 1rpx solid #EEEEEE;
    margin: 0 auto;
    padding-bottom: 30rpx;

    .top {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 102rpx;
      align-items: center;
      width: 100%;

      .icon {
        width: 48rpx;
        height: 48rpx;
      }

      .name {
        color: #AEB0BC;
        font-weight: 400;
        font-size: 20rpx;
        margin-left: 16rpx;
      }

      .btn {
        width: 80rpx;
        height: 48rpx;
        background: #FFFFFF;
        color: #787C8D;
        border-radius: 24rpx;
        font-size: 20rpx;
        border: 1rpx solid #EEEEEE;
        text-align: center;
        line-height: 48rpx;
        margin-left: auto;
      }
    }

    .msg {
      width: 624rpx;
      box-sizing: border-box;
      max-height: 104rpx;
      overflow: hidden;
      display: -webkit-box;
      /**对象作为伸缩盒子模型展示**/
      -webkit-box-orient: vertical;
      /**设置或检索伸缩盒子对象的子元素的排列方式**/
      -webkit-line-clamp: 3;
      /**显示的行数**/
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #262937;
      line-height: 48rpx;
      padding-left: 64rpx;
    }

    .activeName {
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      /* 超出几行省略 */
      overflow: hidden;
    }

    .content {
      padding-top: 20rpx;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      /* 超出几行省略 */
      overflow: hidden;
      padding-left: 64rpx;
      color: #666;
    }

    &:last-child {
      border: none;
    }
  }
}
</style>
