<template>
  <view class="content">
    <view class="top">
      <u-tabs :list="tabs" :is-scroll="true" :current="current" @change="change"></u-tabs>
    </view>
    <view class="item" v-for="item in list" :key="item.id">
      <view class="left">
        <text class="title u-line-2" @click="goDetail(item)">{{ item.title }}</text>
        <!-- <text class="des u-line-1" v-if="item.content"></text> -->
        <view style="display: flex; align-items: center; justify-content: space-between;">
          <text class="time u-line-1">{{ item.operate_time }}</text>
          <view>
            <u-icon style="margin: 0 8px;" v-show="userInfo.id" name="star" v-if="item.is_collect == 0" size="28"
              @click="collect(item)"></u-icon>
            <u-icon style="margin: 0 8px;" v-show="userInfo.id" name="star-fill" color="#fa3534"
              v-if="item.is_collect == 1" size="28" @click="cancelCollect(item)"></u-icon>
            <u-icon style="margin: 0 8px;" name="download" size="28" @click="download(item)"></u-icon>
          </view>
        </view>
      </view>
      <img :src="item.cover" v-if="item.cover" alt="">
    </view>
    <u-empty v-if="list.length == 0" style="height: 300rpx;"></u-empty>
    <view class="more" @click="goNews" v-if="list.length != 0">
      查看更多 <u-icon name="arrow-right"></u-icon>
    </view>
  </view>
</template>
<script setup>
import { ref, nextTick, computed } from "vue";
import { onPageScroll, onPullDownRefresh, onShow } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
import request from "@/request";

function goDetail(item) {
  uni.navigateTo({
    url: `/pages/index/newsdetail?type=${type.value}&id=${item.id}`
  })
}

function download(item) {
  uni.navigateTo({
    url: `/pages/index/newsdetail?type=${type.value}&id=${item.id}&download=1`
  })
}

let current = ref(0)
let type = ref(1)

function getList() {
  request({
    url: tabs[current.value].id == 0 ? `/api/parent/article/collectList` : "/api/parent/article/know",
    method: 'post',
    data: {
      type: type.value,
      per_page: 3,
      page: 1,
      key: '',
      user_id: userInfo.id,
      school_id: userInfo.school_id
    }
  }).then(res => {
    list.value = res.data;
  })
}

let list = ref([]);
function goNews() {
  uni.navigateTo({
    url: `/pages/index/news?type=${current.value}`
  })
}
function cancelCollect(item) {
  request({
    url: `/api/parent/article/cancelCollect`,
    method: 'post',
    data: {
      policy_id: item.id,
      user_id: userInfo.id
    }
  }).then(res => {
    item.is_collect = item.is_collect === 0 ? 1 : 0;
    uni.showToast({
      title: '取消收藏'
    })
  })
}

function collect(item) {
  request({
    url: `/api/parent/article/collect`,
    method: 'post',
    data: {
      policy_id: item.id,
      user_id: userInfo.id
    }
  }).then(res => {
    item.is_collect = item.is_collect === 0 ? 1 : 0;
    uni.showToast({
      title: '收藏成功'
    })
  })
}

function change(index) {
  current.value = index;
  type.value = tabs[index].id;
  nextTick(() => {
    getList();
  })
}
let tabs = userInfo.id ? [{
  name: '政策文件',
  id: 1,
}, {
  name: '行业新闻',
  id: '7,8',
}, {
  name: '育儿知识',
  id: 9,
}, {
  name: '我的收藏',
  id: 0,
}] : [{
  name: '政策文件',
  id: 1,
}, {
  name: '行业新闻',
  id: 6,
}, {
  name: '专业指导',
  id: 7,
}, {
  name: '卫生保健',
  id: 8,
}, {
  name: '育儿知识',
  id: 9,
}]

getList();
</script>
<style scoped lang="scss">
.content {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx auto;

  .top {
    font-size: 32rpx;
    font-weight: 500;
    color: #787C8D;
    padding: 15rpx 30rpx;

    .icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
  }

  .item {
    width: 690rpx;
    border-bottom: 1rpx solid #F7F7F7;
    font-size: 32rpx;
    color: #262937;
    line-height: 44rpx;
    padding: 30rpx;
    box-sizing: border-box;

    .title {
      font-size: 28rpx;
      display: block;
      margin-bottom: 20rpx;
    }

    .des {
      color: #666;
      font-size: 28rpx;
      display: block;
    }

    .time {
      color: #d8d8d8;
      font-size: 24rpx;
      display: block;
    }
  }

  .more {
    text-align: center;
    font-size: 28rpx;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 120rpx;
    height: 120rpx;
  }
}
</style>