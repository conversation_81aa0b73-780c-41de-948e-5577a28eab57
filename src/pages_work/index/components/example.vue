<template>
  <view class="content" v-if="list.length">
    <image src="https://obs.tuoyupt.com/miniprogram/parentindex/shifant.png" class="title">
    </image>
    <view class="scroll">
      <view class="zhanwei"></view>
      <view class="item" v-for="(item, index) in list" :key="index">
        <image :src="item.entrance_img" class="img">
        </image>
        <view class="mask"></view>
        <view class="text">{{ item.wechatname }}</view>
      </view>
      <view class="zhanwei"></view>
    </view>
  </view>
</template>
<script setup>
defineProps(['list'])
</script>
<style scoped lang="scss">
.content {
  margin: 20rpx auto;
  width: 690rpx;
  height: 374rpx;
  background: linear-gradient(180deg, #FFEEC9 0%, #FFFFFF 100%);
  border-radius: 24rpx;
  border: 2rpx solid #FFFFFF;

  .title {
    width: 316rpx;
    height: 74rpx;
    display: block;
    margin: 30rpx auto;
  }

  .scroll {
    width: 690rpx;
    height: 270rpx;
    display: flex;
    padding: 4rpx 8rpx;
    box-sizing: border-box;
    flex-direction: row;

    .item {
      width: 192rpx;
      height: 192rpx;
      border-radius: 24rpx;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      margin-right: 16rpx;

      .img {
        width: 192rpx;
        height: 192rpx;
      }

      .mask {
        position: absolute;
        z-index: 2;
        width: 192rpx;
        height: 192rpx;
        left: 0;
        top: 0;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
      }

      .text {
        position: absolute;
        z-index: 3;
        left: 16rpx;
        top: 112rpx;
        width: 160rpx;
        height: 68rpx;
        font-size: 24rpx;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 34rpx;
      }
    }

    .zhanwei {
      width: 30rpx;
      flex-shrink: 0;
      height: 100%;
    }
  }
}
</style>