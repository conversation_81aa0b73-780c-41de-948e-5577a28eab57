<template>
  <view class="content">

    <image class="icon" src="https://obs.tuoyupt.com/miniprogram/parentindex/gzh.png">
    </image>
    <view class="body">
      <image show-menu-by-longpress class="code" src="https://obs.tuoyupt.com/aituoyu/logo/gzh.jpg">
      </image>
      <view class="text">
        <view class="t1">
          官方微信公众号
        </view>
        <view class="t2">
          托育，托起明天的希望 ，育出未来的力量
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>

</script>
<style scoped lang="scss">
.content {
  width: 690rpx;
  height: 672rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx auto;
  overflow: hidden;

  .icon {
    width: 690rpx;
    height: 420rpx;
  }

  .body {
    width: 690rpx;
    height: 288rpx;
    background: #FFFFFF;
    position: relative;
    padding: 0 40rpx;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;

    .code {
      width: 210rpx;
      height: 210rpx;
    }

    .text {
      flex: 1;
      padding-top: 40rpx;
      padding-left: 18rpx;

      .t1 {
        font-size: 32rpx;
        font-weight: 500;
        color: #262937;
        line-height: 44rpx;
      }

      .t2 {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
        line-height: 34rpx;
        margin-top: 4rpx;
      }
    }
  }
}
</style>