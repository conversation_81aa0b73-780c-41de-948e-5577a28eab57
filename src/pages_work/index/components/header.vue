<template>
  <view class="header">
    <view class="header-center" :style="{ background: style.background }">
      <view class="info">
        <view class="name">{{ info.student_name }}</view>
        <view class="age">{{ info.age }}</view>
        <view class="body">
          <view class="item">
            <view class="label">
              身高
            </view>
            <view class="value">
              {{ info.height ? info.height : '-' }}
            </view>
          </view>
          <view class="item">
            <view class="label">
              体重
            </view>
            <view class="value">
              {{ info.weight ? info.weight : '-' }}
            </view>
          </view>
          <view class="item">
            <view class="label">
              BMI
            </view>
            <view class="value">
              {{ info.bmi ? info.bmi : '-' }}
            </view>
          </view>
          <view class="arrow" @click="goRouter(5)">
            <image class="icon" src="https://obs.tuoyupt.com/miniprogram/parentindex/arrow.png"></image>
          </view>
        </view>
      </view>

      <image class="backbaby" src="https://obs.tuoyupt.com/miniprogram/parentindex/baby.png">
      </image>
    </view>
    <view class="nav" :style="{ background: `linear-gradient(180deg, ${style.background}, #FFFFFF)` }">
    </view>
    <image mode="heightFix" class="avatar" :src="style.avatar">
    </image>
    <scroll-view :scroll-x="true" :show-scrollbar="false" class="scroll">
      <view class="zhanwei"></view>
      <view class="item" @click="goRouter(1)">
        <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/nav1.png">
        </image>
        <view class="title">生活记录</view>
        <view class="des">{{ live.life_record == 0 ? '暂无记录' : `${live.life_record}条` }}</view>
      </view>
      <view class="item" @click="goRouter(2)">
        <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/nav2.png">
        </image>
        <view class="title">我的相册</view>
        <view class="des">{{ live.album == 0 ? '暂无记录' : `${live.album}张` }}</view>
      </view>
      <view class="item" @click="goRouter(3)">
        <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/nav3.png">
        </image>
        <view class="title">观察记录</view>
        <view class="des">{{ live.evaluation == 0 ? '暂无记录' : `${live.evaluation}条` }}</view>
      </view>
      <view class="item" @click="goRouter(4)">
        <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/nav4.png">
        </image>
        <view class="title">成长报告</view>
        <view class="des">{{ live.report == 0 ? '暂无记录' : `${live.report}条` }}</view>
      </view>
      <view class="item" @click="goRouter(5)">
        <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/nav5.png">
        </image>
        <view class="title">体检报告</view>
        <view class="des">{{ live.examination == 0 ? '暂无记录' : `${live.examination}条` }}</view>
      </view>
      <view class="zhanwei2"></view>
    </scroll-view>
  </view>
</template>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  info: Object,
  styleType: Number,
  live: Object,
})
const theme = {
  boy: {
    background: "#E4E9FF",
    avatar: "https://obs.tuoyupt.com/miniprogram/parentindex/nbaby.png"
  },
  girl: {
    background: "#FFEBE3",
    avatar: "https://obs.tuoyupt.com/miniprogram/parentindex/nvbaby.png"
  }
}
const style = computed(() => {
  return props.styleType == 2 ? theme.girl : theme.boy
})

function goRouter(index) {
  if (index == 5) {
    uni.navigateTo({
      url: "/pages/index/examination"
    })
  } else if (index == 1) {
    uni.navigateTo({
      url: "/pages/pack/liferecord/student_detail"
    })
  } else if (index == 2) {
    uni.navigateTo({
      url: "/pages/index/album"
    })
  } else if (index == 4) {
    uni.navigateTo({
      url: "/pages/pack/lifereport/index"
    })
  } else if (index == 3) {
    uni.navigateTo({
      url: "/pages/pack/observation-records/index"
    })
  }
}
</script>
<style lang="scss" scoped>
.header {
  position: relative;

  .avatar {
    height: 420rpx;
    position: absolute;
    z-index: 2;
    top: 20rpx;
    right: 62rpx;
  }

  .nav {
    position: relative;
    z-index: 1;
    width: 750rpx;
    height: 256rpx;
  }

  .scroll {
    width: 750rpx;
    height: 200rpx;
    position: absolute;
    z-index: 3;
    bottom: 30rpx;
    left: 0;
    white-space: nowrap;

    .zhanwei {
      display: inline-block;
      width: 30rpx;
    }

    .zhanwei2 {
      flex-shrink: 0;
      display: inline-block;
      width: 10rpx;
    }

    .item {
      width: 200rpx;
      height: 200rpx;
      border-radius: 24rpx;
      margin-right: 20rpx;
      flex-shrink: 0;
      position: relative;
      overflow: hidden;
      box-sizing: border-box;
      padding: 24rpx;
      display: inline-block;

      .back {
        position: absolute;
        width: 200rpx;
        height: 200rpx;
        left: 0;
        top: 0;
        z-index: -1;
      }

      .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #262937;
      }

      .des {
        font-size: 20rpx;
        font-weight: 400;
        color: #787C8D;
      }
    }
  }

  &-center {
    width: 100%;
    height: 316rpx;
    position: relative;
    z-index: 1;

    .backbaby {
      position: absolute;
      z-index: 2;
      width: 690rpx;
      height: 232rpx;
      bottom: 40rpx;
      left: 30rpx;
    }


    .info {
      padding-left: 70rpx;
      padding-top: 70rpx;
      position: relative;
      z-index: 3;
      width: 100%;
      box-sizing: border-box;

      .name {
        font-size: 64rpx;
        font-weight: 600;
        color: #262937;
        line-height: 90rpx;
        max-width: 6em;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .age {
        font-size: 24rpx;
        font-weight: 400;
        color: #AEB0BC;
        line-height: 34rpx;
      }

      .body {
        display: flex;
        flex-direction: row;
        margin-top: 32rpx;

        .arrow {
          width: 32rpx;
          height: 48rpx;
          background: rgba(0, 0, 0, 0.25);
          border-radius: 16rpx;
          display: flex;
          flex-direction: row;
          align-items: center;

          .icon {
            width: 32rpx;
            height: 32rpx;
          }
        }

        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 48rpx;

          .label {
            font-size: 20rpx;
            font-weight: 400;
            color: #787C8D;
            line-height: 28rpx;
          }

          .value {
            font-size: 32rpx;
            font-family: RousseauDeco;
            color: #262937;
            line-height: 38rpx;
          }
        }
      }
    }
  }
}
</style>