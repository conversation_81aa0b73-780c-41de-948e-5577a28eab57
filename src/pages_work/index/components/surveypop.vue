<template>
  <u-popup v-model="popShow" :mask-close-able="true" :border-radius="40" mode="center">
    <view class="pop-content">
      <image class="img" src="https://obs.tuoyupt.com/miniprogram/index/wenjuan.png"></image>
      <view class="title">{{ detail.title }}</view>
      <view class="text">
        {{ detail.desc }}
      </view>
      <view class="btns" @click="goList">
        填写问卷
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { ref, watch } from 'vue';
const popShow = ref(false);
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
import request from "@/request";
function goList() {
  popShow.value = false;
  uni.navigateTo({
    url: "/pages/pack/survey/index"
  })
}
const detail = ref({
  desc: "",
  id: 0,
  title: "",
})
function getPop(id) {
  request({
    url: `/api/parent/jyform/pop`,
    data: {
      school_id: id
    }
  }).then(res => {
    if (res.id) {
      detail.value = res;
      popShow.value = true;
    }
  })
}
watch(userInfo, () => {
  if (userInfo.school_id && userInfo.school_id !== 0) {
    getPop(userInfo.school_id)
  }
}, {
  immediate: true
})
</script>
<style lang="scss" scoped>
.pop-content {
  width: 600rpx;
  min-height: 660rpx;

  .img {
    width: 600rpx;
    height: 340rpx;
  }

  .title {
    font-size: 32rpx;
    font-weight: 600;
    padding: 20rpx 30rpx;
  }

  .text {
    font-size: 28rpx;
    color: #444;
    box-sizing: border-box;
    padding: 12rpx 30rpx;
    line-height: 50rpx;
  }

  .btns {
    width: 80%;
    height: 60rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    background: #5F7CFC;
    text-align: center;
    line-height: 60rpx;
    color: #FFF;
    margin: 40rpx auto 20rpx;
  }
}
</style>