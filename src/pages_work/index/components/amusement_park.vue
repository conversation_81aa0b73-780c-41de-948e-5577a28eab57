<template>
  <view class="content" :style="{ background: style.background }">
    <view class="top">
      <view class="title">宝宝乐园</view>
      <image class="dota" src="https://obs.tuoyupt.com/miniprogram/parentindex/doodo.png">
      </image>
      <image class="lena" src="https://obs.tuoyupt.com/miniprogram/parentindex/leyuan.png">
      </image>
    </view>
    <scroll-view :scroll-x="true" class="scroll" :show-scrollbar="false">
      <view class="warp">
        <view class="item" @click="goRouter(15)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic1.png">
          </image>
          <view class="info">
            <view class="t1">音乐小精灵</view>
            <view class="t2">抓住宝宝听觉敏感黄金期</view>
          </view>
        </view>
      </view>
      <view class="warp">
        <view class="item" @click="goRouter(10)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic2.png">
          </image>
          <view class="info">
            <view class="t1">思维大转盘</view>
            <view class="t2">思维拓展 快乐互动</view>
          </view>
        </view>
      </view>
      <view class="warp">
        <view class="item" @click="goRouter(14)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic3.png">
          </image>
          <view class="info">
            <view class="t1">绘画小课堂</view>
            <view class="t2">挥洒色彩的自由空间</view>
          </view>
        </view>
      </view>
      <view class="warp">
        <view class="item" @click="goRouter(16)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic4.png">
          </image>
          <view class="info">
            <view class="t1">逻辑大冒险</view>
            <view class="t2">激发宝宝的好奇心和探索精神</view>
          </view>
        </view>
      </view>
      <view class="warp">
        <view class="item" @click="goRouter(11)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic5.png">
          </image>
          <view class="info">
            <view class="t1">国学天地</view>
            <view class="t2">领略传统文化小百科的魅力</view>
          </view>
        </view>
      </view>
      <view class="zhanwei"></view>
    </scroll-view>
  </view>
</template>
<script setup>
import { computed } from "vue";
const props = defineProps({
  info: Object,
  styleType: Number
})
const theme = {
  boy: {
    background: "#617EFB",
  },
  girl: {
    background: "#FF7373",
  }
}
const style = computed(() => {
  return props.styleType == 2 ? theme.girl : theme.boy
})
function goRouter(id) {
  uni.navigateTo({
    url: `/pages/index/playground?id=${id}`
  })
}
</script>
<style lang="scss" scoped>
.content {
  width: 690rpx;
  height: 320rpx;
  background: #617EFB;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 24rpx;
  margin: 0 auto;
  overflow: hidden;

  .top {
    height: 100rpx;
    position: relative;

    .lena {
      height: 64rpx;
      width: 370rpx;
      position: absolute;
      right: 0rpx;
      bottom: -1px;
    }

    .dota {
      height: 34rpx;
      width: 96rpx;
      position: absolute;
      left: 210rpx;
      top: 34rpx;
    }

    .title {
      top: 34rpx;
      left: 30rpx;
      position: absolute;
      font-size: 32rpx;
      font-weight: 500;
      color: #FFFFFF;
    }
  }

  .scroll {
    width: 100%;
    white-space: nowrap;
    background: #fff;
    height: 220rpx;
    padding-top: 30rpx;

    .zhanwei {
      width: 30rpx;
      display: inline-block;
    }

    .warp {
      display: inline-block;
      width: 508rpx;
      height: 160rpx;
    }

    .item {
      background: #F6F6F6;
      border-radius: 16rpx;
      flex-shrink: 0;
      display: flex;
      flex-direction: row;
      margin-left: 30rpx;

      .ic {
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
      }

      .info {
        padding-left: 28rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .t1 {
          font-size: 32rpx;
          font-weight: 600;
          color: #000000;
        }

        .t2 {
          font-size: 20rpx;
          color: #B3B3B3;
          margin-top: 10rpx;
        }
      }
    }
  }
}
</style>