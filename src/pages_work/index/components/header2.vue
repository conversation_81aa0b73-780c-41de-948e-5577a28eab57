<template>
  <view class="header">
    <u-navbar :is-fixed="true" title="" :border-bottom="false" :is-back="false">
      <view class="slot-wrap">
        <view class="title">
          首页
        </view>
        <!-- <view class="ai">
          <image @click="toWenwen" class="cion" src="https://obs.tuoyupt.com/miniprogram/parentindex/wenwen.png">
          </image>
          <view class="tips" v-if="showTips">
            <text class="text">hi～我是您的AI育儿助手问问。</text>
            <view class="btn" @click="toWenwen">问一下</view>
            <u-icon @click.stop="closeTips" name="close" color="#FFFFFF" size="32"></u-icon>
          </view>
        </view> -->
      </view>
    </u-navbar>
  </view>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
const props = defineProps({
  styleType: Number,
})
const theme = {
  boy: {
    background: "#E4E9FF",
    avatar: "https://obs.tuoyupt.com/miniprogram/parentindex/nbaby.png"
  },
  girl: {
    background: "#FFEBE3",
    avatar: "https://obs.tuoyupt.com/miniprogram/parentindex/nvbaby.png"
  }
}
const style = computed(() => {
  return props.styleType == 2 ? theme.girl : theme.boy
})
onMounted(() => {
  let time = new Date();
  let nowStr = `${time.getFullYear()}${time.getMonth()}${time.getDate()}`;
  let isShow = uni.getStorageSync("index_ai_tips");
  let isLogin = uni.getStorageSync("isLogin");
  if (!isShow && isLogin) {
    showTips.value = true;
    setTimeout(() => {
      showTips.value = false;
    }, 2000)
    uni.setStorageSync("index_ai_tips", `${time.getFullYear()}${time.getMonth()}${time.getDate()}`)
  }
  if (isShow && isLogin) {
    if (isShow !== nowStr) {
      showTips.value = true;
      setTimeout(() => {
        showTips.value = false;
      }, 2000)
      uni.setStorageSync("index_ai_tips", `${time.getFullYear()}${time.getMonth()}${time.getDate()}`)
    }
  }
})
const showTips = ref(false);
function closeTips() {
  showTips.value = false;
}
const toWenwen = () => {
  uni.navigateTo({
    url: "/pages_work/wenwen/index"
  })
}

</script>
<style lang="scss" scoped>
.slot-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  height: 100%;
  align-items: center;
  box-sizing: border-box;
  padding: 0 20rpx 0 30rpx;

  .title {
    font-size: 40rpx;
    font-weight: 600;
    color: #000000;
  }

  .select {
    font-size: 24rpx;
    font-weight: 400;
    color: #787C8D;
    margin-left: 8rpx;
  }

  .ai {
    margin-left: auto;
    position: relative;

    .tips {
      width: 690rpx;
      height: 80rpx;
      background: rgba(38, 41, 55, 0.8);
      border-radius: 40rpx;
      position: absolute;
      display: flex;
      flex-direction: row;
      align-items: center;
      box-sizing: border-box;
      padding: 0 38rpx;
      transform: translateX(-440rpx);
      top: 84rpx;
      z-index: 4;

      .text {
        font-size: 28rpx;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 40rpx;
      }

      .btn {
        width: 112rpx;
        height: 48rpx;
        background: #FFFFFF;
        border-radius: 32rpx;
        text-align: center;
        font-size: 24rpx;
        color: #262937;
        line-height: 48rpx;
        margin-left: auto;
        margin-right: 34rpx;
      }

    }

    .cion {
      width: 64rpx;
      border-radius: 50%;
      height: 64rpx;
    }
  }
}
</style>