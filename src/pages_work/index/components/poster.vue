<template>
  <view class="content">
    <view class="top">
      <view class="left">
        <view class="t1">
          家长海报
        </view>
        <view class="t2">
          宣传有奖～推荐入园即可获得佣金奖励
        </view>
      </view>
      <view class="more" @click="goPoster">
        更多<u-icon name="arrow-right" color="#AEB0BC"></u-icon>
      </view>
    </view>
    <view class="imgs">
      <view class="imgs1" v-for="item in list" :key="item.id">
        <image class="img" mode="widthFix" @click.stop="goDetail(item.id)" :src="item.img_url_thumb"></image>
      </view>
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  list: {
    type: Array
  }
})
function goPoster() {
  uni.navigateTo({
    url: '/pages_work/poster/index'
  });
}
function goDetail(id) {
  uni.navigateTo({
    url: '/pages_work/poster/detail?id=' + id,
  });
}
</script>
<style lang="scss" scoped>
.content {
  width: 690rpx;
  height: 494rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx auto 0;
  box-sizing: border-box;
  padding: 30rpx;

  .imgs {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 40rpx;

    .imgs1 {
      width: 198rpx;
      height: 312rpx;
      border-radius: 12rpx;
      overflow: hidden;

      .img {
        width: 198rpx;
      }
    }
  }

  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;

    .left {
      .t1 {
        font-size: 32rpx;
        font-weight: 600;
        color: #000000;
        letter-spacing: 1px;
      }

      .t2 {
        font-size: 20rpx;
        font-weight: 400;
        color: #AEB0BC;
      }
    }

    .more {
      font-size: 24rpx;
      font-weight: 400;
      color: #AEB0BC;
    }
  }
}
</style>