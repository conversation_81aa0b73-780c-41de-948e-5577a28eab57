<template>
  <view class="content">
    <view class="left" @click="goRouter(1)">
      <view class="title">
        今天做什么
      </view>
      <view class="mask"></view>
      <scroll-view class="scroll" enable-flex enhanced :show-scrollbar="false" :scroll-y="true">
        <template v-if="daily.length">
          <view class="item" :class="{ hasLine: index < daily.length - 1 }" v-for="(item, index)  in daily" :key="index">
            <view class="dot"></view>
            <view class="info">
              <view class="time">{{ item.start_time }}-{{ item.end_time }}</view>
              <view class="text">{{ item.name }}</view>
            </view>
          </view>
        </template>
        <view v-else class="empty">
          暂无日程
        </view>
      </scroll-view>
    </view>
    <view class="right" @click="goRouter(2)">
      <view class="title">
        今天吃什么
      </view>
      <view class="mask"></view>
      <scroll-view class="scroll" enable-flex enhanced :show-scrollbar="false" :scroll-y="true">
        <template v-if="foods.length">
          <view class="item" v-for="(item, index) in foods" :key="index">
            {{ item }}
          </view>
        </template>
        <view v-else class="empty">
          暂无食谱
        </view>
      </scroll-view>
    </view>
  </view>
</template>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  daily: {
    type: Array
  },
  recipes: {
    type: Array
  },
})
const foods = computed(() => {
  let arr = [];
  props.recipes.forEach(element => {
    element.food_name.forEach(item => {
      arr.push(item)
    })
  });
  return arr;
})
function goRouter(index) {
  if (index == 1) {
    uni.navigateTo({
      url:
        "/pages_work/classdaily/daily"
    });
  }
  if (index == 2) {
    uni.navigateTo({
      url:
        "/pages/index/cookbook"
    });
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 24rpx auto;
  width: 690rpx;
  height: 320rpx;
  overflow: hidden;

  .scroll {
    width: 100%;
    height: 210rpx;
    margin-top: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0 24rpx;
    box-sizing: border-box;
    z-index: 1;
    position: relative;
  }

  .right {
    background: #FFEBE3;
    color: #FF7373;

    .mask {
      position: absolute;
      width: 334rpx;
      height: 104rpx;
      background: linear-gradient(180deg, rgba(255, 235, 227, 0) 0%, #FFEBE3 100%);
      border-radius: 0rpx 0rpx 24rpx 24rpx;
      left: 0;
      bottom: 0;
      z-index: 2;
      pointer-events: none;
    }

    .empty {
      font-size: 28rpx;
    }

    .item {
      padding: 0 20rpx;
      max-width: 286rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 48rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      font-size: 24rpx;
      font-weight: 400;
      text-align: center;
      color: #262937;
      line-height: 48rpx;
      box-sizing: border-box;
      flex-shrink: 0;
      margin-bottom: 16rpx;
    }
  }

  .left {
    background: #EBF6FF;
    color: #617EFB;

    .empty {
      font-size: 28rpx;
    }

    .mask {
      position: absolute;
      width: 334rpx;
      height: 104rpx;
      background: linear-gradient(180deg, rgba(235, 246, 255, 0) 0%, #EBF6FF 100%);
      border-radius: 0rpx 0rpx 24rpx 24rpx;
      left: 0;
      bottom: 0;
      z-index: 2;
      pointer-events: none;
    }

    .item.hasLine {
      &::after {
        content: " ";
        position: absolute;
        width: 1rpx;
        height: 106rpx;
        left: 43.5rpx;
        border-left: 1rpx dashed #ccc;
        top: 38rpx;
        z-index: 2;
      }
    }

    .item {
      width: 286rpx;
      height: 104rpx;
      flex-shrink: 0;
      background: #FFFFFF;
      border-radius: 16rpx;
      display: flex;
      flex-direction: row;
      box-sizing: border-box;
      padding-top: 16rpx;
      margin-bottom: 20rpx;
      position: relative;



      .dot {
        width: 16rpx;
        height: 16rpx;
        background: #FFFFFF;
        border: 2rpx solid #617EFB;
        border-radius: 50%;
        margin-left: 36rpx;
        position: relative;
        top: 8rpx;
      }

      .info {
        padding-left: 16rpx;

        .time {
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #AEB0BC;
          max-width: 200rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .text {
          font-size: 28rpx;
          font-weight: 500;
          color: #262937;
          max-width: 200rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .right,
  .left {
    width: 334rpx;
    height: 320rpx;
    border-radius: 24rpx;
    box-sizing: border-box;
    position: relative;

    .title {
      padding: 40rpx 30rpx 0;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}
</style>