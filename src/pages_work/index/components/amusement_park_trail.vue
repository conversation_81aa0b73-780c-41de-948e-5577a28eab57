<template>
  <view class="content">
    <view class="top">
      <view class="t1">宝宝乐园</view>
      <view class="t2">益智启蒙</view>
    </view>
    <scroll-view :scroll-x="true" class="scroll" enhanced :show-scrollbar="false">
      <view class="body">
        <view class="item" @click="goRouter(15)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic1.png">
          </image>
          <view class="info">
            <view class="t1">音乐小精灵</view>
            <view class="t2">抓住宝宝听觉敏感黄金期</view>
          </view>
          <view class="arr">
            <image class="img" src="https://obs.tuoyupt.com/miniprogram/parentindex/arrow_r.png">
            </image>
          </view>
        </view>
        <view class="item" @click="goRouter(10)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic2.png">
          </image>
          <view class="info">
            <view class="t1">思维大转盘</view>
            <view class="t2">思维拓展 快乐互动</view>
          </view>
          <view class="arr">
            <image class="img" src="https://obs.tuoyupt.com/miniprogram/parentindex/arrow_r.png">
            </image>
          </view>
        </view>
        <view class="item" @click="goRouter(14)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic3.png">
          </image>
          <view class="info">
            <view class="t1">绘画小课堂</view>
            <view class="t2">挥洒色彩的自由空间</view>
          </view>
          <view class="arr">
            <image class="img" src="https://obs.tuoyupt.com/miniprogram/parentindex/arrow_r.png">
            </image>
          </view>
        </view>
        <view class="item" @click="goRouter(16)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic4.png">
          </image>
          <view class="info">
            <view class="t1">逻辑大冒险</view>
            <view class="t2">激发宝宝的好奇心和探索精神</view>
          </view>
          <view class="arr">
            <image class="img" src="https://obs.tuoyupt.com/miniprogram/parentindex/arrow_r.png">
            </image>
          </view>
        </view>
        <view class="item" @click="goRouter(11)">
          <image class="ic" src="https://obs.tuoyupt.com/miniprogram/parentindex/ic5.png">
          </image>
          <view class="info">
            <view class="t1">国学天地</view>
            <view class="t2">领略传统文化小百科的魅力</view>
          </view>
          <view class="arr">
            <image class="img" src="https://obs.tuoyupt.com/miniprogram/parentindex/arrow_r.png">
            </image>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script setup>
import { computed } from "vue";
const props = defineProps({
  info: Object,
  styleType: Number
})
const theme = {
  boy: {
    background: "#617EFB",
  },
  girl: {
    background: "#FF7373",
  }
}
const style = computed(() => {
  return props.styleType == 2 ? theme.girl : theme.boy
})
function goRouter(id) {
  uni.navigateTo({
    url: `/pages/index/playground?id=${id}`
  })
}
</script>
<style lang="scss" scoped>
.content {
  width: 690rpx;
  height: 642rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  margin: 20rpx auto;

  .top {
    width: 690rpx;
    height: 144rpx;
    position: relative;
    background-image: url(https://obs.tuoyupt.com/miniprogram/parentindex/lyppp1.png);
    background-size: contain;
    background-repeat: no-repeat;
    padding-top: 40rpx;
    padding-left: 30rpx;

    .t1 {
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
    }

    .t2 {
      font-size: 20rpx;
      font-weight: 500;
      color: #FFFFFF;
      margin-top: 4rpx;
    }
  }

  .scroll {
    width: 100%;
    background: #fff;
    height: 498rpx;

    .body {
      width: 1148rpx;
      height: 498rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 40rpx 30rpx;
      box-sizing: border-box;
    }

    .item {
      width: 512rpx;
      height: 112rpx;
      border-radius: 16rpx;
      flex-shrink: 0;
      display: flex;
      flex-direction: row;
      margin-left: 30rpx;
      flex-shrink: 0;
      align-items: center;
      margin-bottom: 40rpx;

      .ic {
        width: 112rpx;
        height: 112rpx;
        border-radius: 24rpx;
      }

      .info {
        padding-left: 28rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;

        .t1 {
          font-size: 32rpx;
          font-weight: 600;
          color: #000000;
        }

        .t2 {
          font-size: 20rpx;
          color: #B3B3B3;
          margin-top: 10rpx;
        }
      }

      .arr {
        width: 80rpx;
        height: 64rpx;
        background: #F6F6F6;
        border-radius: 32rpx;
        text-align: center;
        line-height: 64rpx;
        position: relative;
        left: -12rpx;

        .img {
          width: 48rpx;
          height: 30rpx;
          position: relative;
          top: 8rpx;
        }
      }
    }
  }
}
</style>