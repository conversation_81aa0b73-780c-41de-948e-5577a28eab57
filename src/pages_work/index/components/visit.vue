<template>
  <view class="content" v-if="list.length">
    <view class="top">
      <view class="left">
        <image class="icon" src="https://obs.tuoyupt.com/miniprogram/parentindex/dingwei.png">
        </image>
        <text>（{{ city.name }}）附近托育机构</text>
      </view>
      <view class="right" @click="goMore">更多 <u-icon name="arrow-right"></u-icon> </view>
    </view>
    <scroll-view class="list" :style="{ height: height + 'rpx' }" scroll-y>
      <view class="item" v-for="(item, index) in list" :key="index" @click="intoDetail(item.id)">
        <view class="left">
          <image class="icon" src="https://obs.tuoyupt.com/miniprogram/parentindex/dingwei.png">
          </image>
        </view>
        <view class="info">
          <view class="t1">
            {{ item.abbreviation }}
          </view>
          <view class="t2">
            {{ item.provicne_name }}{{ item.city_name }}{{ item.area_name ? item.area_name : '' }}
          </view>
        </view>
        <view class="km">
          <text v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2)
            }}km</text>
          <text v-else>{{ item.distance * 1000 }}m</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script setup>
import { computed } from "vue"
const props = defineProps({
  list: Array,
  city: Object
})
function intoDetail(id) {
  uni.navigateTo({
    url: `/pages/pack1/school/detail?id=${id}`,
  });
}
const height = computed(() => {
  if (props.list.length >= 3) {
    return 420
  } else {
    return props.list.length * 140;
  }
})
function goMore() {
  uni.navigateTo({
    url: '/pages/pack1/school/index?type=2'
  })
}
</script>
<style lang="scss" scoped>
.content {
  width: 690rpx;
  background: #D2F8FB;
  border-radius: 24rpx;
  border: 2rpx solid #FFFFFF;
  box-sizing: border-box;
  margin: 30rpx auto;

  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    height: 120rpx;

    .right {
      font-size: 24rpx;
      font-weight: 400;
      color: #AEB0BC;
    }

    .left {
      display: flex;
      flex-direction: row;
      font-size: 32rpx;
      font-weight: 600;
      color: #262937;
      align-items: center;

      .icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }

  .list {
    width: 100%;
    height: 420rpx;
    margin-top: 20rpx;

    .item {
      display: flex;
      flex-direction: row;
      padding: 0 30rpx;
      height: 140rpx;

      .left {
        width: 80rpx;
        height: 80rpx;

        .icon {
          width: 80rpx;
          height: 80rpx;
        }
      }

      .info {
        flex: 1;
        padding-left: 24rpx;

        .t1 {
          font-size: 32rpx;
          font-weight: 500;
          color: #050F3F;
        }

        .t2 {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
        }
      }

      .km {
        font-size: 24rpx;
        font-weight: 400;
        color: #262937;
      }
    }
  }
}
</style>