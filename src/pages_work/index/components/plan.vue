<template>
  <view class="content" @click="goDetail">
    <view class="body">
      <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/book.png">
      </image>
      <template v-if="info.name">
        <view class="title1">
          {{ info.name }} <u-icon name="arrow-right" color="#000000" size="22"></u-icon>
        </view>
        <view class="date">{{ info.start_time }}-{{ info.end_time }}</view>
      </template>
      <template v-else>
        <view class="title1">
          本周暂无计划
        </view>
      </template>
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  info: {
    type: Object,
    defalut: () => {
      return {
        date: '',
        title: ''
      }
    }
  }
})

function goDetail() {
  uni.navigateTo({
    url: `/pages_work/educationplan/educationplan`
  })
}

</script>
<style lang="scss" scoped>
.content {
  width: 750rpx;
  border-radius: 24rpx;
  margin: 20rpx auto 0;
  padding: 0rpx 30rpx;
  box-sizing: border-box;

  .top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #787C8D;
    }

    .arrow {
      font-size: 24rpx;
      font-weight: 400;
      color: #AEB0BC;
    }
  }



  .body {
    width: 690rpx;
    height: 180rpx;
    background: #F6F6F6;
    border-radius: 24rpx;
    position: relative;
    padding-left: 30rpx;
    box-sizing: border-box;
    padding-top: 20rpx;
    margin-top: 24rpx;

    .back {
      width: 190rpx;
      height: 160rpx;
      bottom: 1rpx;
      position: absolute;
      right: 20rpx;
    }

    .title1 {
      font-size: 32rpx;
      font-weight: 500;
      color: #262937;
      line-height: 44rpx;
    }

    .date {
      font-size: 20rpx;
      font-weight: 400;
      color: #AEB0BC;
      line-height: 28rpx;
      margin-top: 20rpx;
    }

    .des {
      margin-top: 32rpx;
      display: flex;
      flex-direction: row;
      font-size: 24rpx;
      font-weight: 500;
      color: #787C8D;
      line-height: 34rpx;

      .detail1 {
        margin-left: 20rpx;
      }
    }

    .add {
      margin-top: 48rpx;
      width: 136rpx;
      height: 40rpx;
      background: #FFFFFF;
      border-radius: 20rpx;
      text-align: center;
      line-height: 40rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #787C8D;
    }
  }

}
</style>