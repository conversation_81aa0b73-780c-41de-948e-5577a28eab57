<template>
  <view class="air" @click="goAir">
    <image class="icon" src="https://obs.tuoyupt.com/miniprogram/air/airi.png"></image>
    <view class="text">
      <view class="t1">空气质量</view>
      <view class="t2">实时查看机构空气质量</view>
    </view>
    <u-icon name="arrow-right" color="#BCBCBC" size="24"></u-icon>
  </view>
</template>
<script setup>
function goAir() {
  uni.navigateTo({
    url: '/pages/index/air'
  })
}
</script>
<style lang="scss" scoped>
.air {
  width: 690rpx;
  height: 144rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  padding: 0 30rpx 0 26rpx;
  margin: 0 auto;

  .icon {
    width: 88rpx;
    height: 88rpx;
  }

  .text {
    flex: 1;
    padding-left: 32rpx;

    .t1 {
      font-size: 32rpx;
      font-weight: 600;
      color: #262937;
    }

    .t2 {
      font-size: 20rpx;
      font-weight: 400;
      color: #AEB0BC;
      margin-top: 20rpx;
    }
  }
}
</style>