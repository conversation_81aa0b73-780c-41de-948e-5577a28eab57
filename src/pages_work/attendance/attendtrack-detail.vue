<template>
  <view class="pages safe-area-inset-bottom">
    <u-navbar title="请假详情" ref="navbar" :background="{ background: '#FFFFFF' }" :border-bottom="false"></u-navbar>
    <view class="scorll-warp">
      <scroll-view :scroll-y="true" class="scorll">
        <view class="top">
          <view class="left">
            <view class="section1">
              <view class="avter">
                <image class="icon" v-if="info.img_url" :src="info.img_url"></image>
                <view v-else class="noicon">
                  {{ info.student_name.charAt(0) }}
                </view>
              </view>
              <view class="info">
                <view class="name">{{ info.student_name }}</view>
                <view class="class">{{ info.class_name }}</view>
                <view class="time">{{ info.created_at }}</view>
              </view>
            </view>
          </view>
          <view class="status">
            <image class="icon" :src="getStatusUrl(info.confirm_status)">
            </image>
          </view>
        </view>
        <view class="detail">
          <view class="row">
            <view class="label">申请人</view>
            <view class="value">{{ info.applay_name }}</view>
          </view>
          <view class="row">
            <view class="label">请假类型</view>
            <view class="value"> {{ info.track_type == 1 ? '病假' : '事假' }}</view>
          </view>
          <view class="row">
            <view class="label">开始时间</view>
            <view class="value">{{ info.start_time }}</view>
          </view>
          <view class="row">
            <view class="label">结束时间</view>
            <view class="value">{{ info.end_time }}</view>
          </view>
          <view class="row" v-if="info.track_tags">
            <view class="label">病症</view>
            <view class="value">{{ info.track_tags }}</view>
          </view>
        </view>
        <view class="content">
          <text class="text">{{ info.reason }}</text>
          <image class="img" mode="widthFix" :key="index" v-for="(item, index) in info.track_imgs" :src="item">
          </image>
        </view>
      </scroll-view>
    </view>
    <view class="footer" v-if="info.confirm_status == 0 || info.confirm_status == 1">
      <view class="btn btn1" @click="check">
        <view class="icon">
          <u-icon name="minus"></u-icon>
        </view>
        <text>撤销</text>
      </view>
    </view>
  </view>
  <u-modal v-model="modelShow2" :show-title="false" @confirm="modelConfirm2" show-cancel-button confirm-text="是"
    cancel-text="否" @cancel="modelCancel2">
    <view class="form">
      <u-form>
        <u-form-item label-position="top" label="驳回原因"><u-input v-model="remark" /></u-form-item>
      </u-form>
    </view>
  </u-modal>
  <u-modal v-model="modelShow" @confirm="modelConfirm" show-cancel-button confirm-text="是" cancel-text="否"
    @cancel="modelCancel" :content="modelContent"></u-modal>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import request from '@/request';
import { ref } from "vue";
let id = ""
onLoad(option => {
  id = option.id;
  getData()
})
function getData() {
  request({
    url: "/api/parent/attendtrack/form",
    method: "get",
    data: {
      track_id: id
    }
  }).then(res => {
    info.value = res;
  })
}
const info = ref({
  student_name: ""
})
const remark = ref("");
const modelShow = ref(false);
const modelShow2 = ref(false);
const modelContent = ref("")
let checkType = 0;
function check() {
  modelContent.value = '确认撤销请假？'
  modelShow.value = true;
}
function modelCancel() {
  modelShow.value = false;
}
function modelConfirm() {
  uni.showLoading()
  request({
    url: "/api/parent/attendtrack/cancel",
    method: "post",
    data: {
      id: id
    }
  }).then(res => {
    uni.showToast({
      title: "操作成功"
    })
    getData();
  })
}
function getStatusUrl(status) {
  let url = ""
  switch (status) {
    case 0:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/daishenhe.png';
      break;
    case 1:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/tongguo.png';
      break;
    case 2:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/weitonguo.png';
      break;
    case 3:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/chexiao.png';
      break;
  }
  return url;
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;


  .scorll-warp {
    flex: 1;
    min-height: 1rpx;
    width: 100%;

    .scorll {
      height: 100%;
      width: 100%;

      .top {
        display: flex;
        flex-direction: row;
        padding: 0rpx 30rpx;
        flex: 1;
        width: 100vw;
        box-sizing: border-box;

        .left {
          flex: 1;
          height: 160rpx;

          .section1 {
            display: flex;
            flex-direction: row;
            align-items: center;

            .avter {
              width: 80rpx;
              height: 80rpx;

              .noicon {
                width: 80rpx;
                height: 80rpx;
                background: #FF7373;
                border-radius: 50%;
                display: flex;
                flex-direction: row;
                color: #FFF;
                align-items: center;
                font-size: 32rpx;
                justify-content: center;
              }

              .icon {
                width: 80rpx;
                height: 80rpx;
              }
            }

            .info {
              display: flex;
              flex-direction: column;
              justify-content: center;
              margin-left: 24rpx;

              .name {
                font-size: 28rpx;
                font-weight: 500;
                color: #262937;
                line-height: 28rpx;
                margin-top: 20rpx;
              }

              .class {
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 24rpx;
                margin-top: 12rpx;
              }

              .time {
                font-size: 28rpx;
                font-weight: 400;
                color: #787C8D;
                margin-top: 24rpx;
              }
            }
          }

          .section2 {
            margin-top: 40rpx;

            .row {
              display: flex;
              flex-direction: row;
              height: 60rpx;

              .label {
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 24rpx;
              }

              .value {
                font-size: 24rpx;
                font-weight: 400;
                color: #787C8D;
                line-height: 24rpx;
                margin-left: 20rpx;
              }
            }
          }
        }

        .status {
          width: 200rpx;
          height: 160rpx;

          .icon {
            width: 200rpx;
            height: 160rpx;
          }
        }
      }

      .detail {
        width: 690rpx;
        background: #F6F6F6;
        border-radius: 24rpx;
        margin: 20rpx auto;
        padding: 10rpx 0;

        .row {
          display: flex;
          flex-direction: row;
          height: 60rpx;
          align-items: center;

          .label {
            width: 166rpx;
            font-size: 28rpx;
            color: #AEB0BC;
            box-sizing: border-box;
            padding-right: 24rpx;
            text-align: right;
          }

          .value {
            font-size: 28rpx;
            font-weight: 400;
            color: #262937;
          }
        }
      }

      .content {
        width: 100%;
        box-sizing: border-box;
        padding: 0 30rpx;

        .text {
          font-size: 28rpx;
          font-weight: 400;
          color: #262937;
          line-height: 44rpx;
        }

        .img {
          width: 100%;
          margin: 20rpx 0;
          border-radius: 24rpx;
        }
      }
    }
  }

  .footer {
    width: 750rpx;
    height: 100rpx;
    background: #FFFFFF;
    display: flex;
    flex-direction: row;

    .btn {
      flex: 1;
      text-align: center;
      height: 100rpx;
      line-height: 100rpx;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .icon {
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        border: 1rpx solid #787C8D;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 19rpx;
      }

      text {
        margin-left: 12rpx;
      }
    }

    .btn1 {
      color: #787C8D;
    }

    .btn2 {
      color: #FF7373;

      .icon {
        border: 1rpx solid #FF7373;
      }
    }

    .btn3 {
      color: #5F7CFC;

      .icon {
        border: 1rpx solid #5F7CFC;
      }
    }
  }
}

.form {
  width: 100%;
  box-sizing: border-box;
  padding: 0 30rpx;
}
</style>