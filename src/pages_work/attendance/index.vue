<template>
  <view class="pages safe-area-inset-bottom">
    <u-navbar title="宝宝考勤" ref="navbar" :background="{ background: '#FFFFFF' }" :border-bottom="false"></u-navbar>
    <view class="top">
      <view class="left">
        <view class="avter">
          <image class="icon" v-if="userInfo.currentStudent.img_url" :src="userInfo.currentStudent.img_url"></image>
          <view v-else class="noicon">
            {{ userInfo.student_name.charAt(0) }}
          </view>
        </view>
        <view class="info">
          <view class="name">{{ userInfo.student_name }}</view>
          <view class="class">{{ userInfo.class_name }}</view>
        </view>
      </view>
      <view class="btn" @click="goAdd">
        请假
      </view>
    </view>
    <Calendar ref="calendarRef" @changMonth="changMonth" @changeDate="changeDate" :pointList="list">
      <template v-slot:header>
        <view class="nums">
          <view class="item">
            <view class="dot dot1"></view>
            <view class="text">出勤</view>
            <view class="num">{{ nums.shidao }}</view>
          </view>
          <view class="item">
            <view class="dot dot2"></view>
            <view class="text">缺勤</view>
            <view class="num">{{ nums.queqin }}</view>
          </view>
          <view class="item">
            <view class="dot dot3"></view>
            <view class="text">病假</view>
            <view class="num">{{ nums.bingjia }}</view>
          </view>
          <view class="item">
            <view class="dot dot4"></view>
            <view class="text">事假</view>
            <view class="num">{{ nums.shijia }}</view>
          </view>
        </view>
      </template>
    </Calendar>
    <view class="detail">
      <view class="record-item">
        <view class="left left1">
          <view class="type type1">
            入
          </view>
        </view>
        <template v-if="state == -1">
          <view class="info empty">
            休息
          </view>
        </template>
        <template v-if="state == 0 || !hasCheck1">
          <view class="info empty">
            未考勤
          </view>
        </template>
        <template v-if="state == 1">
          <view class="info error">
            缺勤
          </view>
        </template>
        <template v-if="state == 2 && hasCheck1">
          <view class="info">
            <view class="time">
              {{ check1.check_at }}
            </view>
            <template v-if="check1.attend_type == 1">
              <view class="des">卡号：{{ check1.number }}</view>
            </template>
            <template v-if="check1.attend_type == 2">
              <view class="des">人脸：<text class="photo" @click="showPhoto(check1)">查看照片</text> </view>
            </template>
            <template v-if="check1.attend_type == 3">
              <view class="des">接送家长：{{ check1.relationship }}</view>
            </template>
            <template v-if="check1.attend_type == 4">
              <view class="des" v-if="check1.relationship">接送家长：{{ check1.relationship }}</view>
              <view class="des">操作人:{{ check1.teacher_name }}</view>
            </template>
          </view>
        </template>
        <template v-if="state == 3">
          <view class="info error error2">
            病假
          </view>
        </template>
        <template v-if="state == 4">
          <view class="info error error3">
            事假
          </view>
        </template>
      </view>
      <view class="record-item">
        <view class="left">
          <view class="type type2">
            出
          </view>
        </view>
        <template v-if="state == -1">
          <view class="info empty">
            休息
          </view>
        </template>
        <template v-if="state == 0 || !hasCheck2">
          <view class="info empty">
            未考勤
          </view>
        </template>
        <template v-if="state == 1">
          <view class="info error">
            缺勤
          </view>
        </template>
        <template v-if="state == 2 && hasCheck2">
          <view class="info">
            <view class="time">
              {{ check2.check_at }}
            </view>
            <template v-if="check2.attend_type == 1">
              <view class="des">卡号：{{ check2.number }}</view>
            </template>
            <template v-if="check2.attend_type == 2">
              <view class="des">人脸：<text class="photo" @click="showPhoto(check2)">查看照片</text> </view>
            </template>
            <template v-if="check2.attend_type == 3">
              <view class="des">接送家长：{{ check2.relationship }}</view>
            </template>
            <template v-if="check2.attend_type == 4">
              <view class="des" v-if="check2.relationship">接送家长：{{ check2.relationship }}</view>
              <view class="des">操作人:{{ check2.teacher_name }}</view>
            </template>
          </view>
        </template>
        <template v-if="state == 3">
          <view class="info error error2">
            病假
          </view>
        </template>
        <template v-if="state == 4">
          <view class="info error error3">
            事假
          </view>
        </template>
      </view>
    </view>
  </view>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref, nextTick } from "vue";
import Calendar from "@/components/calendar.vue";
import request from "@/request";
import dayjs from "dayjs";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
onLoad(() => {
  getData();
  getDayData();
})
const nums = ref({
  bingjia: 0,
  queqin: 0,
  shidao: 0,
  shijia: 0,
})
const list = ref([]);
let now = dayjs();
const currentDay = ref({
  year: now.year(),
  month: now.month() + 1,
  day: now.date()
})
let currentMonth = dayjs();
const calendarRef = ref(null)
function getData() {
  uni.showLoading();
  request({
    url: "/api/parent/attend/list",
    data: {
      Ym: currentMonth.format("YYYY-MM"),
      student_id: userInfo.student_id,
      class_id: userInfo.class_id
    }
  }).then(async res => {
    uni.hideLoading();
    list.value = res.list;
    nums.value = res.other;
    await nextTick();
    calendarRef.value.makeCalendar(currentMonth.year(), currentMonth.month() + 1);
  })
}
function changMonth(num) {
  if (num == 1) {
    currentMonth = currentMonth.add(1, 'month')
  } else {
    currentMonth = currentMonth.subtract(1, 'month')
  }
  getData()
}
const check1 = ref({})
const check2 = ref({})
const hasCheck1 = ref(false);
const hasCheck2 = ref(false);
const state = ref(0)
function getDayData() {
  uni.showLoading();
  request({
    url: "/api/parent/attend/form",
    data: {
      Ymd: dayjs(`${currentDay.value.year}-${currentDay.value.month}-${currentDay.value.day}`).format("YYYY-MM-DD"),
      student_id: userInfo.student_id
    }
  })
    .then(res => {
      console.log(res)
      if (res.list.length) {
        let data = res.list[0];
        state.value = data.state;
        let checkArr = data.check_arr;
        let i = checkArr.findIndex(item => {
          return item.check_type == 1
        })
        let j = checkArr.findIndex(item => {
          return item.check_type == 2
        })
        if (i > -1) {
          hasCheck1.value = true;
          check1.value = checkArr[i];
        } else {
          hasCheck1.value = false;
        }
        if (j > -1) {
          hasCheck2.value = true;
          check2.value = checkArr[j];
        } else {
          hasCheck2.value = false;
        }
      } else {
        state.value = 0;
        check1.value = {
          state: 0
        }
        hasCheck1.value = false;
        hasCheck2.value = false;
        check2.value = {
          state: 0
        }
      }
      uni.hideLoading();
    })
}
function changeDate(day) {
  currentDay.value = day;
  getDayData();
}
function showPhoto(item) {
  uni.previewImage({
    urls: item.img_url
  });
}
function goAdd() {
  uni.navigateTo({
    url: `/pages_work/attendance/add-attend`
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;

  .top {
    display: flex;
    flex-direction: row;
    padding: 0rpx 30rpx;
    width: 100vw;
    box-sizing: border-box;
    height: 172rpx;
    align-items: center;

    .left {
      flex: 1;
      height: 172rpx;

      display: flex;
      flex-direction: row;
      align-items: center;

      .avter {
        width: 112rpx;
        height: 112rpx;

        .noicon {
          width: 112rpx;
          height: 112rpx;
          background: #FF7373;
          border-radius: 50%;
          display: flex;
          flex-direction: row;
          color: #FFF;
          align-items: center;
          font-size: 32rpx;
          justify-content: center;
        }

        .icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
        }
      }

      .info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 24rpx;

        .name {
          font-size: 40rpx;
          font-weight: 500;
          color: #262937;
        }

        .class {
          font-size: 24rpx;
          font-weight: 400;
          color: #AEB0BC;
          margin-top: 12rpx;
        }
      }


      .section2 {
        margin-top: 40rpx;

        .row {
          display: flex;
          flex-direction: row;
          height: 60rpx;

          .label {
            font-size: 24rpx;
            font-weight: 400;
            color: #AEB0BC;
            line-height: 24rpx;
          }

          .value {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
            line-height: 24rpx;
            margin-left: 20rpx;
          }
        }
      }
    }

    .btn {
      width: 128rpx;
      height: 64rpx;
      background: #FF7373;
      border-radius: 16rpx;
      font-size: 28rpx;
      text-align: center;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 64rpx;
    }
  }

  .nums {
    display: flex;
    flex-direction: row;
    padding-left: 30rpx;

    .item {
      display: flex;
      flex-direction: row;
      height: 44rpx;
      align-items: center;
      margin-right: 30rpx;

      .dot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }

      .text {
        font-size: 28rpx;
        font-weight: 400;
        color: #AEB0BC;
        line-height: 44rpx;
      }

      .num {
        font-size: 28rpx;
        font-weight: 500;
        color: #111111;
      }

      .dot1 {
        background: #3BA0FF;
      }

      .dot2 {
        background: #F2637B;
      }

      .dot3 {
        background: #4DCB73;
      }

      .dot4 {
        background: #FAD337;
      }
    }
  }

  .detail {
    width: 630rpx;
    margin: 0 auto;
    border-top: 1rpx solid #EEEEEE;
    padding-top: 48rpx;

    .record-item {
      display: flex;
      flex-direction: row;

      .left1 {
        &::after {
          content: " ";
          position: absolute;
          height: 100%;
          border-left: 1rpx dashed #AEB0BC;
          left: 32rpx;
        }
      }

      .left {
        width: 104rpx;
        position: relative;

        .type {
          width: 64rpx;
          height: 64rpx;
          border-radius: 32rpx;
          text-align: center;
          line-height: 64rpx;
          color: #FFFFFF;
          font-size: 28rpx;
        }

        .type1 {
          background: #3BA0FF;
        }

        .type2 {
          background: #4DCB73;
        }
      }

      .info {
        flex: 1;
        padding-bottom: 48rpx;

        .time {
          font-size: 40rpx;
          font-weight: 500;
          color: #262937;
          line-height: 44rpx;
        }

        .des {
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 4rpx;
        }

        .photo {
          color: #F2637B;
        }
      }

      .empty {
        font-size: 40rpx;
        font-weight: 500;
        color: #AEB0BC;
        height: 120rpx;
        padding-top: 4rpx;
      }

      .error {
        font-size: 40rpx;
        font-weight: 500;
        color: #F2637B;
        height: 120rpx;
        padding-top: 4rpx;
      }

      .error2 {
        color: #4DCB73;
      }

      .error3 {
        color: #FAD337;
      }
    }
  }
}
</style>