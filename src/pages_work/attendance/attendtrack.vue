<template>
  <view class="pages safe-area-inset-bottom">
    <u-navbar title="宝宝请假" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="scorll-warp">
      <scroll-view :scroll-y="true" class="scorll">
        <template v-if="list.length">
          <view class="record-item" v-for="item in list" :key="item.id" @click="goDetail(item.id)">
            <view class="top">
              <view class="left">
                <view class="section1">
                  <view class="avter">
                    <image class="icon" v-if="item.img_url" :src="item.img_url"></image>
                    <view v-else class="noicon">
                      {{ item.student_name.charAt(0) }}
                    </view>
                  </view>
                  <view class="info">
                    <view class="name">{{ item.student_name }}</view>
                    <view class="class">{{ item.created_at }}</view>
                  </view>
                </view>
                <view class="section2">
                  <view class="row">
                    <view class="label">
                      请假类型
                    </view>
                    <view class="value">
                      {{ item.track_type == 1 ? '病假' : '事假' }}
                    </view>
                  </view>
                  <view class="row">
                    <view class="label">
                      开始时间
                    </view>
                    <view class="value">
                      {{ item.start_time }}
                    </view>
                  </view>
                  <view class="row">
                    <view class="label">
                      结束时间
                    </view>
                    <view class="value">
                      {{ item.end_time }}
                    </view>
                  </view>
                </view>
              </view>
              <view class="status">
                <image class="icon" :src="getStatusUrl(item.confirm_status)">
                </image>
              </view>
            </view>
            <view class="bottom">
              <text class="text">查看详情</text>
              <u-icon color="#262937" size="24" name="arrow-right"></u-icon>
            </view>
          </view>
        </template>
        <template v-else><u-empty text="暂无请假" mode="list"></u-empty></template>
      </scroll-view>
    </view>
    <view class="add" @click="goAdd">
      <image class="addi" src="https://obs.tuoyupt.com/nanjing/pstac/enrollment/add.svg"></image>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import request from '@/request';
import { onShow } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const list = ref([]);

function getData() {
  uni.showLoading();
  request({
    url: "/api/parent/attendtrack/list",
    method: "get",
    data: {
      student_id: userInfo.student_id,
      track_type: 0
    }
  })
    .then(res => {
      uni.hideLoading();
      list.value = res.list;
    })
}

onShow(() => {
  getData();
})
function goAdd() {
  uni.navigateTo({
    url: `/pages_work/attendance/add-attend`
  })
}
function goDetail(id) {
  uni.navigateTo({
    url: `/pages_work/attendance/attendtrack-detail?id=${id}`
  })
}
function getStatusUrl(status) {
  let url = ""
  switch (status) {
    case 0:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/daishenhe.png';
      break;
    case 1:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/tongguo.png';
      break;
    case 2:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/weitonguo.png';
      break;
    case 3:
      url = 'https://obs.tuoyupt.com/miniprogram/enrollment_management/chexiao.png';
      break;
  }
  return url;
}
</script>
<style lang="scss" scoped>
.add {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 5;

  .addi {
    width: 112rpx;
    height: 112rpx;
  }
}

.pages {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #F6F6F6;

  .scorll-warp {
    background: #F6F6F6;
    flex: 1;
    min-height: 1rpx;

    .scorll {
      height: 100%;

      .record-item {
        width: 690rpx;
        height: 416rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        margin: 20rpx auto;
        box-sizing: border-box;
        padding: 0 30rpx;
        display: flex;
        flex-direction: column;

        .top {
          display: flex;
          flex-direction: row;
          padding-top: 40rpx;
          flex: 1;

          .left {
            flex: 1;

            .section1 {
              display: flex;
              flex-direction: row;
              align-items: center;

              .avter {
                width: 80rpx;
                height: 80rpx;

                .noicon {
                  width: 80rpx;
                  height: 80rpx;
                  background: #FF7373;
                  border-radius: 50%;
                  display: flex;
                  flex-direction: row;
                  color: #FFF;
                  align-items: center;
                  font-size: 32rpx;
                  justify-content: center;
                }

                .icon {
                  width: 80rpx;
                  height: 80rpx;
                }
              }

              .info {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 24rpx;

                .name {
                  font-size: 28rpx;
                  font-weight: 500;
                  color: #262937;
                  line-height: 28rpx;
                }

                .class {
                  font-size: 24rpx;
                  font-weight: 400;
                  color: #AEB0BC;
                  line-height: 24rpx;
                }
              }
            }

            .section2 {
              margin-top: 40rpx;

              .row {
                display: flex;
                flex-direction: row;
                height: 60rpx;

                .label {
                  font-size: 24rpx;
                  font-weight: 400;
                  color: #AEB0BC;
                  line-height: 24rpx;
                }

                .value {
                  font-size: 24rpx;
                  font-weight: 400;
                  color: #787C8D;
                  line-height: 24rpx;
                  margin-left: 20rpx;
                }
              }
            }
          }

          .status {
            width: 200rpx;
            height: 160rpx;

            .icon {
              width: 200rpx;
              height: 160rpx;
            }
          }
        }

        .bottom {
          width: 100%;
          border-top: 1rpx solid #ccc;
          height: 82rpx;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .text {
            color: #262937;
            font-size: 24rpx;
          }
        }
      }
    }
  }
}
</style>