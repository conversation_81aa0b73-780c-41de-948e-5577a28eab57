<template>
  <view class="pages">
    <u-navbar title="新增请假" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="formse">
      <scroll-view :scroll-y="true" class="form">
        <u-form class="form" :border-bottom="false" :model="formData" ref="formRef" label-position="top">
          <view class="section">
            <u-form-item label="请假类型">
              <u-radio-group v-model="formData.track_type">
                <u-radio :name="1">
                  病假
                </u-radio>
                <u-radio :name="2">
                  事假
                </u-radio>
              </u-radio-group>
            </u-form-item>
            <u-form-item required label="开始时间" prop="start_time" right-icon="arrow-right"><u-input placeholder="请选择"
                disabled @click="selectDate(1)" v-model="formData.start_time" /></u-form-item>
            <u-form-item required label="结束时间" prop="end_time" right-icon="arrow-right"><u-input placeholder="请选择"
                disabled v-model="formData.end_time" @click="selectDate(2)" /></u-form-item>
            <u-form-item label="病症" right-icon="arrow-right" v-if="formData.track_type == 1">
              <u-input placeholder="请选择" @click="selectList(2)" v-model="formData.track_tags" disabled />
            </u-form-item>
            <u-form-item required :border-bottom="false" label="请假原因" prop="reason">
              <u-input placeholder="请输入" v-model="formData.reason" type="textarea" maxlength="100" />
            </u-form-item>
          </view>
          <view class="section">
            <u-form-item :border-bottom="false" label="添加图片">
              <u-upload width="156" max-count="6" height="156" @on-list-change="imgChange" multiple deletable
                :auto-upload="false">
              </u-upload>
            </u-form-item>
          </view>
        </u-form>
      </scroll-view>
    </view>
    <u-picker mode="time" v-model="timeShow" :params="params" @confirm="timeConfirm"></u-picker>
    <u-select @confirm="confirmSelect" v-model="selectShow" :list="selectEumn"></u-select>
    <u-popup v-model="popupShow" mode="bottom" :border-radius="40" safe-area-inset-bottom closeable>
      <view class="checkbox">
        <u-checkbox-group @change="checkChange" wrap>
          <u-checkbox v-model="item.checked" v-for="(item, index) in track_tags" :key="index" :name="item.value">{{
            item.name
          }}</u-checkbox>
        </u-checkbox-group>
        <u-button type="primary" @click="popupShow = false">确定</u-button>
      </view>
    </u-popup>
    <view class="footer">
      <view class="btn" @click="submit">
        确定
      </view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from 'vue';
import request from '@/request';
import { onReady, onLoad } from '@dcloudio/uni-app';
import { OBSupload } from '@/utils/obs/obs-upload.js'
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
// 初始化
onLoad(async (option) => {
  await getPrepare();
})
let formData = reactive({
  track_type: 1,
  start_time: '',
  end_time: '',
  reason: "",
  track_tags: '',
});
// 表单验证
const formRef = ref(null);
const rules = {
  student_name: [
    {
      required: true,
      message: '请选择婴幼儿',
      trigger: 'blur',
    }
  ],
  start_time: [
    {
      required: true,
      message: '请选择开始时间',
      trigger: 'blur',
    }
  ],
  end_time: [
    {
      required: true,
      message: '请选择结束时间',
      trigger: 'blur',
    }
  ],
  reason: [
    {
      required: true,
      message: '请输入请假原因',
      trigger: 'blur',
    }
  ],
}
onReady(() => {
  formRef.value.setRules(rules);
})
// 提交
function submit() {
  console.log(new Date(formData.end_time).getTime(), new Date(formData.start_time).getTime())
  if (new Date(formData.end_time).getTime() <= new Date(formData.start_time).getTime()) {
    uni.showToast({
      title: "请假结束时间不能小于开始时间",
      icon: "none",
    })
    return
  }
  formRef.value.validate(async valid => {
    if (valid) {
      let imgs = [];
      if (imgList.length) {
        let requestArr = []
        imgList.forEach(item => {
          let fileName = item.file.name.substring(item.file.name.lastIndexOf("/") + 1);
          let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
          requestArr.push(OBSupload(item.url, fileExtension, fileName))
        })
        imgs = await Promise.all(requestArr);
      }
      uni.showLoading();
      request({
        url: `/api/parent/attendtrack/add`,
        method: "post",
        data: {
          school_id: userInfo.school_id,
          class_id: userInfo.class_id,
          student_id: userInfo.student_id,
          ...formData,
          track_imgs: imgs
        },
      }).then(() => {
        uni.hideLoading();
        uni.showToast({
          title: "操作成功！",
          icon: "none",
          mask: true,
          duration: 1500
        })
        setTimeout(uni.navigateBack, 500);
      })
    }
  });
}
// 获取枚举值
const track_tags = ref([]);
function getPrepare() {
  return request({
    url: `/api/parent/attendtrack/select`,
    method: "post",
  }).then(res => {
    track_tags.value = res.track_tags.map(item => {
      return {
        name: item,
        checked: false,
        value: item
      }
    });
  })
}
const params = {
  year: true,
  month: true,
  day: true,
  hour: true,
  minute: true,
  second: false
}
// 时间选择
const timeShow = ref(false);
let dateType = 0;
const selectDate = (type) => {
  dateType = type;
  timeShow.value = true;
}
const timeConfirm = (e) => {
  if (dateType == 1) {
    formData.start_time = `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
  }
  if (dateType == 2) {
    formData.end_time = `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
  }
}
// 图片选择
let imgList = [];
function imgChange(e) {
  imgList = e;
}
// 列表选择
let selectType = 0;
const selectShow = ref(false);
const popupShow = ref(false);
const selectEumn = ref([]);
function selectList(type) {
  if (type == 1) {
    selectType = 1;
    selectEumn.value = student_arr.value;
    selectShow.value = true;
  }
  if (type == 2) {
    popupShow.value = true;
  }
}
function checkChange(e) {
  formData.track_tags = e.join(",");
}
function confirmSelect(e) {
  let val = e[0];
  if (selectType == 1) {
    formData.student_id = val.value;
    formData.student_name = val.label;
  }
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f6f6f6;
  position: relative;

  .errormsag {
    font-size: 24rpx;
    line-height: 24rpx;
    color: #fa3534;
    margin-top: 12rpx;
  }

  .formse {
    flex: 1;
    overflow: hidden;
    padding-bottom: 20rpx;
  }

  .form {
    height: 100%;

    .section {
      width: 690rpx;
      background: #FFFFFF;
      margin: 20rpx 30rpx;
      border-radius: 24rpx;
      padding: 0rpx 30rpx;

      ::v-deep.u-form-item {
        line-height: 50rpx !important;
      }
    }
  }

  .footer {
    width: 750rpx;
    height: calc(100rpx + env(safe-area-inset-bottom));
    background: #FFFFFF;

    .btn {
      width: 690rpx;
      height: 80rpx;
      background: #5F7CFC;
      border-radius: 16rpx;
      margin: 10rpx auto;
      font-size: 28rpx;
      text-align: center;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 80rpx;
    }
  }
}

.checkbox {
  width: 100%;
  padding: 30rpx 30rpx 0;
  box-sizing: border-box;
}
</style>