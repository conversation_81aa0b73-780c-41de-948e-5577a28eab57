<template>
	<u-navbar title="告知详情" :background="navBackground" :border-bottom="false"></u-navbar>

	<view class="inform_details">
		<view class="item_top">
			<image style="border-radius: 50%;" :src="detail_info.student.img_url" v-if="detail_info.student.img_url">
			</image>
			<view class="avatar" v-else>{{
				detail_info.student_name.charAt(0) }}</view>
			<view class="item_top_center">
				<view class="student_name">
					{{ detail_info.student_name }}
				</view>
				<view class="class_name">
					{{ userInfo.class_name }}
				</view>
			</view>
			<view class="item_top_right" @click="getInformUpdate(detail_info.user_id, detail_info.id)"
				v-if="detail_info.is_read == 0">
				撤回
			</view>
			<view class="item_top_right item_top_right_1" v-else-if="detail_info.is_read == 2">
				已撤回
			</view>
		</view>
		<view class="created_time">
			{{ detail_info.created_at }}
		</view>
		<view class="item_main">
			<view class="main_row">
				<view class="main_title">
					申请人
				</view>
				<view class="main_content">
					{{ detail_info.account_name }}
				</view>
			</view>
			<view class="main_row">
				<view class="main_title">
					喂药
				</view>
				<view class="main_content">
					{{ detail_info.types == 1 ? "不需要" : "需要" }}
				</view>
			</view>
			<view class="main_row">
				<view class="main_title">
					执行日期
				</view>
				<view class="main_content">
					{{ detail_info.execute_at ? detail_info.execute_at : "-" }}
				</view>
			</view>
		</view>
		<view class="inform_content">
			{{ detail_info.content }}
		</view>
		<template
			v-if="detail_info.big_content[0].small_content[0].mecname && detail_info.big_content[0].small_content[0].mecnum">
			<view class="inform_schedule" v-for="(item, index) in detail_info.big_content" :key="index">
				<view class="schedule_row_top">
					<image src="https://obs.tuoyupt.com/nanjing/pstac/inform/clock.png" mode=""></image>
					<view class="schedule_time">
						{{ item.execute_at }}
					</view>
				</view>
				<view class="schedule_row" v-for="(el, index2) in item.small_content" :key="index2">
					<view class="schedule_content">
						{{ el.mecname ? el.mecname : '-' }}
					</view>
					<view class="schedule_content">
						{{ el.mecnum ? el.mecnum : '-' }}
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script setup>
import {
	ref
} from "vue";
import request from "@/request";
import {
	onLoad
} from "@dcloudio/uni-app";
const navBackground = ref({
	background: '#F6F6F6'
})
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const detail_info = ref('')
const getInformUpdate = (user_id, id) => {
	uni.showModal({
		title: '提示',
		content: '是否确定撤回',
		success: function (res) {
			if (res.confirm) {
				console.log('用户点击确定');
				updateStatus(user_id, id)
				detail_info.value.is_read = 2
			} else if (res.cancel) {
				console.log('用户点击取消');
			}
		}
	});

}
const updateStatus = async (user_id, id) => {
	await request({
		method: "POST",
		url: `/api/parent/inform/update`,
		data: {
			user_id: user_id,
			id: id
		}
	}).then((result) => {
		console.log(result);
	});
}
const getInformDetails = async (id) => {
	await request({
		method: "GET",
		url: `/api/parent/inform/detail/${id}`,
	}).then((result) => {
		detail_info.value = result
		detail_info.value.big_content = JSON.parse(detail_info.value.big_content)
		console.log(detail_info.value.big_content)
	});
}
onLoad((options) => {
	getInformDetails(options.id)
})
</script>

<style>
page {
	background: #f6f6f6;
}
</style>

<style lang="scss" scoped>
.avatar {
	font-size: 40rpx;
	font-weight: bold;
	width: 112rpx;
	height: 112rpx;
	margin-right: 24rpx;
	border-radius: 50%;
	background: #FF7373;
	color: #FFFFFF;
	text-align: center;
	line-height: 112rpx;
}

.inform_details {
	padding: 20rpx 30rpx;

	.item_top {
		display: flex;
		align-items: center;
		padding: 40rpx 0 0;

		image {
			width: 112rpx;
			height: 112rpx;
			margin-right: 24rpx;
		}

		.item_top_center {
			flex: 1;

			.student_name {
				font-size: 40rpx;
				color: #262937;
			}



			.class_name {
				margin-top: 8rpx;
				font-size: 24rpx;
				color: #AEB0BC;
			}
		}

		.item_top_right {
			width: 128rpx;
			height: 64rpx;
			background: #FF7373;
			border-radius: 16rpx;
			font-size: 28rpx;
			color: #fff;
			text-align: center;
			line-height: 64rpx;
		}

		.item_top_right_1 {
			background: #F6F6F6 !important;
			color: #787C8D !important;
		}
	}

	.created_time {
		margin-top: 4rpx;
		margin-left: 136rpx;
		margin: 4rpx 0 40rpx 136rpx;
		font-size: 28rpx;
		color: #787C8D;
	}

	.item_main {
		padding: 24rpx 30rpx;
		background: #EEEEEE;
		border-radius: 24rpx;

		.main_row {
			display: flex;
			align-items: center;
			font-size: 24rpx;

			.main_title {
				width: 112rpx;
				color: #AEB0BC;
				font-size: 28rpx;
				color: #AEB0BC;
				margin-right: 24rpx;
			}

			.main_content {
				flex: 1;
				font-size: 28rpx;
				color: #262937;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}

			&:nth-child(2) {
				margin: 24rpx 0;
			}
		}
	}

	.inform_content {
		margin: 40rpx 0;
		font-size: 32rpx;
		color: #262937;
		line-height: 44rpx;
	}

	.inform_schedule {
		padding: 20rpx 28rpx 24rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 20rpx;

		.schedule_row_top {
			display: flex;
			align-items: center;
			margin-bottom: 28rpx;

			image {
				width: 48rpx;
				height: 48rpx;
			}

			.schedule_time {
				font-size: 28rpx;
				color: #787C8D;
				margin-left: 24rpx;
			}
		}

		.schedule_row {
			display: flex;
			flex-direction: column;
			margin-left: 70rpx;
			padding: 16rpx 0 24rpx 0;
			font-size: 24rpx;
			border-bottom: 1rpx solid #EEEEEE;

			.schedule_content {
				flex: 1;
				font-size: 28rpx;
				line-height: 40rpx;
				color: #262937;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;

				&:first-child {
					margin-bottom: 20rpx;
				}
			}

			&:last-child {
				border-bottom: none;
			}
		}

	}
}
</style>