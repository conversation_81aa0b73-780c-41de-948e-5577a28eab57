<template>
	<view>
		<u-navbar title="历史告知" :background="navBackground" :border-bottom="false"></u-navbar>
		<template v-if="informList.length">
			<view class="informList" :style="showList">
				<view class="inform_item" v-for="(item, index) in informList" :key="item.id"
					:class="{ active: index < informList.length - 1 }">
					<view class="item_top">
						<image style="border-radius: 50%;" v-if="item.student.img_url" :src="item.student.img_url"></image>
						<view class="avatar" v-else>{{
							item.student_name.charAt(0) }}</view>
						<view class="item_top_center">
							<view class="student_name">
								{{ item.student_name }}
							</view>
							<view class="class_name">
								{{ userInfo.class_name }}
							</view>
						</view>
						<view class="item_top_right" v-if="item.is_read !== 0">
							{{ item.is_read == 1 ? "已读" : "已撤回" }}
						</view>
					</view>
					<view class="item_main">
						<view class="main_row">
							<view class="main_title">
								喂药
							</view>
							<view class="main_content">
								{{ item.types == 1 ? '不需要' : '需要' }}
							</view>
						</view>
						<view class="main_row">
							<view class="main_title">
								告知内容
							</view>
							<view class="main_content">
								{{ item.content ? item.content : '-' }}
							</view>
						</view>
					</view>
					<view class="item_bottom" @click="goDetail(item.id)">
						查看详情
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
			<image src="https://obs.tuoyupt.com/nanjing/pstac/inform/add2.png" class="add_btn2" @click="toAdd"></image>
			<u-loadmore :status="loadStatus" v-if="haveMore" />
		</template>
		<u-empty v-else text="无历史告知" mode="list" :style="showList"></u-empty>
	</view>
</template>
<script setup>
import {
	ref
} from "vue";
import request from '@/request';
import {
	onLoad,
	onPullDownRefresh,
	onReachBottom
} from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const loadStatus = ref('loadmore')
const isBlank = ref(true)
const avatar_url = ref('')
const showList = ref({
	opacity: 0
})
const showCom = ref(true)
const haveMore = ref(true)
const navBackground = {
	backgroundColor: "#F6F6F6",
};

const confirm = (e) => {
	let {
		value,
		label
	} = e[0];
	//更新当前选中班级
	curentClassObj.class_id = value;
	curentClassObj.class_name = label;
	getData();
};

const toAdd = () => {
	uni.navigateTo({
		url: '/pages_work/inform/addInform'
	})
}


// 日期



const informList = ref([]);

const page = ref(1)
function getData() {
	let arr = informList.value || []
	return request({
		url: "/api/parent/inform/list",
		method: "post",
		data: {
			student_id: userInfo.student_id,
			types: '',
			per_page: 20,
			page: page.value
		}
	}).then(res => {
		if (res.length == 20) {
			haveMore.value = true;
			loadStatus.value = 'loadmore'
		} else {
			haveMore.value = false;
			loadStatus.value = 'nomore'
		}
		showList.value = {
			opacity: 1,
		}
		informList.value = res
		for (let i = 0; i < res.length; i++) {
			arr.push(res[i])
		}
		informList.value = arr
		showCom.value = true
		page.value = page.value + 1
	})
}


function goDetail(id) {
	uni.navigateTo({
		url: `/pages_work/inform/inform_detail?id=${id}`
	})
}

onPullDownRefresh(() => {
	request({
		url: "/api/parent/inform/list",
		method: "post",
		data: {
			student_id: userInfo.student_id,
			types: '',
			per_page: 20,
			page: 1
		}
	}).then(res => {
		informList.value = res
		uni.stopPullDownRefresh();
	})
})

onLoad(async () => {

	let user_info = uni.getStorageSync("user_info")
	avatar_url.value = user_info.wechat.avatar_url

	getData();
});

onReachBottom(() => {
	console.log(1)
	if (haveMore.value) {
		loadStatus.value = 'loading'
		getData()
	} else {
		loadStatus.value = 'nomore'
	}
	// if(this.page >= 3) return ;
	// this.status = 'loading';
	// this.page = ++ this.page;
	// setTimeout(() => {
	//     this.list += 10;
	//     if(this.page >= 3) this.status = 'nomore';
	//     else this.status = 'loading';
	// }, 2000)
})
</script>
<style>
page {
	background: #F6F6F6
}
</style>
<style scoped lang="scss">
.add_btn2 {
	position: fixed;
	bottom: 140rpx;
	right: 40rpx;
	width: 112rpx;
	height: 112rpx;
}

.select_class {
	padding: 0 30rpx;
	margin-top: 20rpx;
	margin-bottom: 40rpx;

	.change_class_container {
		display: flex;
		flex-direction: row;
		align-items: center;

		.currentClassName {
			font-size: 48rpx;
			font-weight: 500;
			color: #111111;
			line-height: 48rpx;
		}

		.change {
			width: 104rpx;
			height: 48rpx;
			background: #F6F6F6;
			border-radius: 32rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
			color: #262937;
			line-height: 48rpx;
			margin-left: 16rpx;
		}
	}

	.title {
		font-size: 28rpx;
		font-weight: 400;
		color: #787C8D;
		line-height: 28rpx;
		margin-top: 24rpx;
	}
}

.imgs {
	width: 690rpx;
	display: flex;
	flex-direction: row;
	margin: 40rpx auto;
	justify-content: flex-start;

	.img {
		width: 210rpx;
		height: 210rpx;
		margin-right: 30rpx;
		flex-shrink: 0;
	}
}

.informList {
	padding: 20rpx 30rpx;

	.inform_item {
		background: #fff;
		padding: 0 30rpx;
		border-radius: 24rpx;
		margin-bottom: 20rpx;

		.item_top {
			display: flex;
			align-items: center;
			padding: 40rpx 0 30rpx;

			image {
				width: 80rpx;
				height: 80rpx;
				margin-right: 24rpx;
			}

			.avatar {
				font-size: 32rpx;
				font-weight: bold;
				width: 80rpx;
				height: 80rpx;
				margin-right: 24rpx;
				border-radius: 50%;
				background: #FF7373;
				color: #FFFFFF;
				text-align: center;
				line-height: 80rpx;
			}

			.item_top_center {
				flex: 1;

				.student_name {
					font-size: 28rpx;
					color: #262937;
				}

				.class_name {
					margin-top: 8rpx;
					font-size: 24rpx;
					color: #AEB0BC;
				}
			}

			.item_top_right {
				width: 128rpx;
				height: 64rpx;
				background: #F6F6F6;
				border-radius: 16rpx;
				font-size: 28rpx;
				color: #787C8D;
				text-align: center;
				line-height: 64rpx;
			}
		}

		.item_main {
			padding: 30rpx 0;
			border-top: 1rpx solid #f6f6f6;
			border-bottom: 1rpx solid #f6f6f6;

			.main_row {
				display: flex;
				align-items: center;
				font-size: 24rpx;

				.main_title {
					width: 96rpx;
					color: #AEB0BC;
					margin-right: 24rpx;
				}

				.main_content {
					flex: 1;
					color: #787C8D;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
				}

				&:first-child {
					margin-bottom: 24rpx;
				}
			}
		}

		.item_bottom {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 28rpx 0 30rpx;
			font-size: 24rpx;
			color: #262937;
		}
	}
}
</style>