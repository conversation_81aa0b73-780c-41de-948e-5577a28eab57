<template>
	<u-navbar title="新建告知" :background="navBackground" :border-bottom="false"></u-navbar>
	<view class="inform_details">
		<view class="item_top">
			<image style="border-radius: 50%;" :src="childInfo.avatar" v-if="childInfo.avatar" mode=""></image>
			<view class="item_top__avatar flex-mode" v-else>{{
				childInfo.name.charAt(0) }}</view>
			<view class="item_top_center">
				<view class="student_name">
					{{ childInfo.name }}
				</view>
				<view class="class_name">
					{{ childInfo.class }}
				</view>
			</view>
			<view class="item_top_right">
				<view class="top_right_history" @click="toHistory">
					历史告知
				</view>
				<view class="top_right_sumbit" @click="submit">
					提交
				</view>
			</view>
		</view>
		<view class="inform_content">
			<u-input :custom-style="inputStyle" placeholder="请输入告知内容" v-model="formData.content" type="textarea"
				maxlength="200" />
		</view>
		<view class="inform_section">
			<view class="section_row">
				<view class="row_left">
					<image src="https://obs.tuoyupt.com/nanjing/pstac/inform/date.png" mode=""></image>
					<view class="row_title">
						执行日期
					</view>
				</view>
				<view class="row_right">
					<view class="execute_at" @click="dateShow = true">
						{{ currentDate }}
					</view>
					<u-icon name="arrow-right" color="#262937;" size="18"></u-icon>
				</view>
			</view>
			<view class="section_row">
				<view class="row_left">
					<image src="https://obs.tuoyupt.com/nanjing/pstac/inform/medicine.png" mode=""></image>
					<view class="row_title">
						喂药
					</view>
				</view>
				<view class="row_right">
					<u-switch v-model="types" active-color="#FF7373" inactive-color="#FFFFFF"></u-switch>
				</view>
			</view>
		</view>
		<view class="content_list" v-if="types">
			<view class="inform_schedule" v-for="(m, i) in formData.big_content" :key="i">
				<view class="schedule_row_top">
					<view class="top_left" @click="timePopup(i)">
						<image src="https://obs.tuoyupt.com/nanjing/pstac/inform/clock.svg" mode=""></image>
						<view class="schedule_time">
							{{ m.execute_at }}
						</view>
						<image class="row_triangle" src="https://obs.tuoyupt.com/nanjing/pstac/inform/triangle.svg"></image>
						<!-- <u-icon name="arrow-down-fill" color="#000000" size="18"></u-icon> -->
					</view>
					<view class="del_btn" @click="del_big_content(i)">
						删除
					</view>
				</view>
				<view class="schedule_row" v-for="(n, j) in m.small_content" :key="j">
					<u-input class="schedule_content" placeholder="请输入药品" v-model="n.mecname" maxlength="20" />
					<u-input class="schedule_content" placeholder="请输入用量" v-model="n.mecnum" maxlength="20" />
					<image class="del_icon" src="https://obs.tuoyupt.com/nanjing/pstac/inform/close.svg"
						@click="del_mecInfo(i, j)"></image>
				</view>
				<view class="add_btn" @click="add_mecInfo(i)">
					添加喂药
				</view>
			</view>
			<view class="inform_add_schedule" @click="add_schedule">
				<view class="section_row">
					<view class="row_left">
						<image src="https://obs.tuoyupt.com/nanjing/pstac/inform/add.svg" mode=""></image>
						<view class="row_title">
							添加喂药时段
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<u-picker mode="time" v-model="dateShow" :params="params" @confirm="dateConfirm"></u-picker>
	<u-picker mode="time" v-model="timeShow" :params="paramsTime" @confirm="timeConfirm"></u-picker>
</template>

<script setup>
import {
	reactive,
	ref,
} from "vue";
import request from "@/request";
import {
	onLoad
} from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const navBackground = {
	backgroundColor: "#F6F6F6",
};
const inputStyle = {
	fontSize: "32rpx",
	color: "#262937",
}
let now = dayjs();
const nowExecuteIndex = ref('');
const currentDate = ref(now.format("YYYY-MM-DD"));
const currentTime = ref(now.format("HH:mm"));
const params = {
	year: true,
	month: true,
	day: true,
	hour: false,
	minute: false,
	second: false
}
const paramsTime = {
	year: false,
	month: false,
	day: false,
	hour: true,
	minute: true,
	second: false
}
const info = ref({
	img_url: "",
	name: "",
	student_class: {
		name: ""
	}
})
const childInfo = reactive({
	name: '',
	class: '',
	avatar: '',
	status: ''
})
const parent_name = ref('')
const dateShow = ref(false);
const timeShow = ref(false);
const formData = ref({
	content: "",
	execute_at: "",
	big_content: [{
		execute_at: currentTime.value,
		small_content: [{
			mecname: '',
			mecnum: ''
		}]
	}]
});
const types = ref(false)

function dateConfirm(e) {
	currentDate.value = `${e.year}-${e.month}-${e.day}`
	formData.value.execute_at = currentDate.value
}

function timeConfirm(e) {
	currentTime.value = `${e.hour}:${e.minute}`
	formData.value.big_content[nowExecuteIndex.value].execute_at = currentTime.value
}

function timePopup(i) {
	timeShow.value = true
	nowExecuteIndex.value = i
}

function add_mecInfo(i) {
	console.log(formData.value.big_content[i].small_content);
	formData.value.big_content[i].small_content.push({
		mecname: '',
		mecnum: ''
	})
}

function add_schedule() {
	formData.value.big_content.push({
		execute_at: currentTime.value,
		small_content: [{
			mecname: '',
			mecnum: ''
		}]
	})
}

function del_big_content(i) {
	if (formData.value.big_content.length == 1) {
		types.value = false;
		formData.value = {
			content: formData.value.content,
			big_content: [{
				execute_at: currentTime.value,
				small_content: [{
					mecname: '',
					mecnum: ''
				}]
			}]
		}
	} else {
		formData.value.big_content.splice(i, 1)
	}
}

function del_mecInfo(i, j) {
	if (formData.value.big_content[i].small_content.length == 1) {
		types.value = false;
		formData.value = {
			content: "",
			big_content: [{
				execute_at: '',
				small_content: [{
					mecname: '',
					mecnum: ''
				}]
			}]
		}
	} else {
		formData.value.big_content[i].small_content.splice(j, 1)
	}
}
// 提交
function submit() {
	if (childInfo.status !== 1) {
		uni.showToast({
			title: "暂无权限",
			icon: "none",
			mask: true,
			duration: 1500,
		})
	} else {
		uni.showLoading();
		if (formData.value.content == '') {
			uni.showToast({
				title: "请输入告知内容",
				icon: "none",
				mask: true,
				duration: 1500,
			})
		} else {
			request({
				url: `/api/parent/inform/add`,
				method: "post",
				data: {
					school_id: userInfo.school_id,
					class_id: userInfo.class_id,
					student_id: userInfo.student_id,
					student_name: userInfo.student_name,
					account_name: userInfo.nickname,
					types: types.value ? 2 : 1,
					execute_at: formData.value.execute_at,
					content: formData.value.content,
					big_content: JSON.stringify(formData.value.big_content)
				},
			}).then(() => {
				uni.hideLoading();
				uni.showToast({
					title: "提交成功",
					icon: "none",
					mask: true,
					duration: 1500,
				})
				setTimeout(() => toHistory(), 1500)
				// setTimeout(uni.navigateBack, 1500);
			})
		}
	}
}

function toHistory() {
	uni.navigateTo({
		url: `/pages_work/inform/inform_list`
	})
}

onLoad(() => {
	parent_name.value = userInfo.username;
	childInfo.name = userInfo.student_name;
	childInfo.class = userInfo.class_name;
	childInfo.avatar = userInfo.img_url ?? '';
	// status: "0预备生,1在校生, 2离校生,3毕业生"
	childInfo.status = userInfo.status
	formData.value.execute_at = currentDate.value
})
</script>

<style>
page {
	background: #f6f6f6;
}
</style>

<style lang="scss" scoped>
.inform_details {
	padding: 20rpx 30rpx;

	.item_top {
		display: flex;
		align-items: center;

		&__avatar {
			font-size: 40rpx;
			font-weight: bold;
			width: 112rpx;
			height: 112rpx;
			margin-right: 24rpx;
			border-radius: 50%;
			background: #FF7373;
			color: #FFFFFF;
		}

		image {
			width: 112rpx;
			height: 112rpx;
			margin-right: 24rpx;
			border-radius: 50%;
		}

		.item_top_center {
			flex: 1;

			.student_name {
				font-size: 40rpx;
				color: #262937;
			}

			.class_name {
				margin-top: 8rpx;
				font-size: 24rpx;
				color: #AEB0BC;
			}
		}

		.item_top_right {
			display: flex;
			align-items: center;

			.top_right_history {
				margin-right: 24rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #787C8D;
				line-height: 40rpx;
			}

			.top_right_sumbit {
				width: 128rpx;
				height: 64rpx;
				background: #FF7373;
				border-radius: 16rpx;
				font-size: 28rpx;
				color: #fff;
				text-align: center;
				line-height: 64rpx;
			}
		}
	}

	.inform_content {
		margin: 40rpx 0;
		font-size: 32rpx;
		color: #262937;
		line-height: 44rpx;
	}

	.inform_section {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 20rpx;

		.section_row {
			padding: 36rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.row_left {
				display: flex;
				align-items: center;
				position: relative;

				image {
					width: 40rpx;
					height: 40rpx;
				}

				.row_title {
					font-size: 32rpx;
					font-weight: 400;
					color: #787C8D;
					line-height: 44rpx;
					margin-left: 16rpx;
				}
			}

			.row_right {
				display: flex;
				align-items: center;

				.execute_at {
					font-size: 32rpx;
					font-weight: 400;
					color: #262937;
					line-height: 44rpx;
					margin-right: 8rpx;
				}
			}

		}
	}

	.inform_schedule {
		padding: 20rpx 28rpx 24rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 20rpx;

		.schedule_row_top {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 28rpx;

			.top_left {
				display: flex;
				align-items: center;
				position: relative;

				image {
					width: 48rpx;
					height: 48rpx;
				}

				.schedule_time {
					font-size: 28rpx;
					color: #787C8D;
					margin-left: 24rpx;
					margin-right: 18rpx;
				}

				.row_triangle {
					position: absolute;
					right: -26rpx;
					width: 32rpx;
					height: 32rpx;
				}
			}

			.del_btn {
				font-size: 28rpx;
				color: #262937;
			}
		}

		.schedule_row {
			display: flex;
			flex-direction: column;
			margin-left: 70rpx;
			padding: 16rpx 0 24rpx 0;
			font-size: 24rpx;
			border-bottom: 1rpx solid #EEEEEE;
			position: relative;

			.del_icon {
				position: absolute;
				right: 0;
				top: 74rpx;
				z-index: 9999;
				width: 32rpx;
				height: 32rpx;
			}

			.schedule_content {
				flex: 1;
				font-size: 28rpx;
				line-height: 40rpx;
				color: #262937;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;

				&:first-child {
					margin-bottom: 20rpx;
				}
			}

			&:last-child {
				border-bottom: none;
			}
		}

		.add_btn {
			margin-left: 70rpx;
			margin-top: 20rpx;
			width: 152rpx;
			padding: 16rpx 28rpx;
			font-size: 24rpx;
			line-height: 34rpx;
			color: #262937;
			background: #FFFFFF;
			border-radius: 32rpx;
			border: 1rpx solid #EEEEEE;
		}

	}

	.inform_add_schedule {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 20rpx;
		// margin-top: 20rpx;

		.section_row {
			padding: 36rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.row_left {
				display: flex;
				align-items: center;
				position: relative;

				image {
					width: 48rpx;
					height: 48rpx;
					margin-right: 20rpx;
				}

				.row_title {
					font-size: 28rpx;
					font-weight: 500;
					color: #111111;
					line-height: 40rpx;
				}
			}
		}
	}
}
</style>