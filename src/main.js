import { createSSRApp } from "vue";
import uView from "./uni_modules/vk-uview-ui";
import App from "./App.vue";
import * as Pinia from 'pinia';
import common from './common.scss';
import piniaPersist from 'pinia-plugin-persistedstate';
export function createApp() {
  const app = createSSRApp(App);
  app.use(uView);
  app.use(Pinia.createPinia().use(piniaPersist));
  return {
    app,
    Pinia
  };
}
