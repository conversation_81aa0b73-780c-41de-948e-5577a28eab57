import CryptoJS from 'crypto-js';
import { Base64 } from "js-base64";
const accessKeyId = "LTAI5tFhSXrMSttsPDhoursL";
const accessKeySecret = "******************************";
let http = {};
http.request = function (option, callback) {
  var url = option.url;
  var method = option.method;
  var data = option.data;
  var timeout = option.timeout || 0;
  //创建XMLhttpRequest对象
  var xhr = new XMLHttpRequest();
  (timeout > 0) && (xhr.timeout = timeout);
  //使用open方法设置和服务器的交互信息
  xhr.open(method, url, true);
  if (typeof data === 'object') {
    try {
      data = JSON.stringify(data);
    } catch (e) { }
  }
  //发送请求
  xhr.send(data);
  //如果请求完成，并响应完成，获取到响应数据
  xhr.onreadystatechange = function () {
    if (xhr.readyState == 4) {
      var result = xhr.responseText;
      try { result = JSON.parse(xhr.responseText); } catch (e) { }
      callback && callback(null, result);
    }
  }.bind(this);
  //延时处理
  xhr.ontimeout = function () {
    callback && callback('timeout');
    console.log('error', '连接超时');
  };
};
// post请求
http.post = function (option, callback) {
  option.method = 'post';
  option.contentType = 'application/json;charset=UTF-8'
  this.request(option, callback);
};
//请求数据
const callApiRequest = (request_, API_HTTP_METHOD, endpoint, callback) => {
  const url = generateUrl(request_, API_HTTP_METHOD, endpoint, accessKeySecret);
  http.post({ url: url, timeout: 5000 }, function (err, result) {
    console.log(err, result)
    // 获取结果
    callback && callback(result);
  });
}
//随机数字
function signNRandom() {
  const Rand = Math.random()
  const mineId = Math.round(Rand * 100000000000000)
  return mineId;
};
function callApi(endpoint, action, version, params, callback) {
  // API_HTTP_METHOD推荐使用POST
  const API_HTTP_METHOD = "POST";

  const request_ = {};
  //系统参数
  request_["SignatureMethod"] = "HMAC-SHA1",
    request_["SignatureNonce"] = signNRandom(),
    request_["AccessKeyId"] = accessKeyId,
    request_["SignatureVersion"] = "1.0",
    request_["Timestamp"] = getTimestamp(),
    request_["Format"] = "JSON",
    request_["RegionId"] = "cn-shanghai",
    request_["Version"] = version,
    request_["Action"] = action;
  // 业务参数，请参考具体的AI能力的API文档进行修改
  if (params) {
    for (let key in params) {
      request_[key] = params[key];
    }
  }

  callApiRequest(request_, API_HTTP_METHOD, endpoint, callback);
}
//Timestamp
function getTimestamp() {
  let date = new Date();
  let YYYY = pad2(date.getUTCFullYear());
  let MM = pad2(date.getUTCMonth() + 1);
  let DD = pad2(date.getUTCDate());
  let HH = pad2(date.getUTCHours());
  let mm = pad2(date.getUTCMinutes());
  let ss = pad2(date.getUTCSeconds());
  return `${YYYY}-${MM}-${DD}T${HH}:${mm}:${ss}Z`;
}
function pad2(num) {
  if (num < 10) {
    return '0' + num;
  }
  return '' + num;
};
function ksort(params) {
  let keys = Object.keys(params).sort();
  let newParams = {};
  keys.forEach((key) => {
    newParams[key] = params[key];
  });
  return newParams;
};
function createHmac(stringToSign, key) {
  const CrypStringToSign = CryptoJS.HmacSHA1(CryptoJS.enc.Utf8.parse(stringToSign), key);
  const base64 = CryptoJS.enc.Base64.stringify(CrypStringToSign);
  return base64;
};
function encode(str) {
  var result = encodeURIComponent(str);
  return result.replace(/!/g, '%21')
    .replace(/'/g, '%27')
    .replace(/\(/g, '%28')
    .replace(/\)/g, '%29')
    .replace(/\*/g, '%2A');
};
function sha1(stringToSign, key) {
  return createHmac(stringToSign, key);
};
function getSignature(signedParams, method, secret) {
  var stringToSign = `${method}&${encode('/')}&${encode(signedParams)}`;
  const key = secret + "&";
  return sha1(stringToSign, key);
};
//参数拼接
function objToParam(param) {
  if (Object.prototype.toString.call(param) !== '[object Object]') {
    return '';
  }
  let queryParam = '';
  for (let key in param) {
    if (param.hasOwnProperty(key)) {
      let value = param[key];
      queryParam += toQueryPair(key, value);
    }
  }
  return queryParam;
};
function toQueryPair(key, value) {
  if (typeof value == 'undefined') {
    return `&${key}=`;
  }
  return `&${encodeURIComponent(key)}=${encode(value)}`;
};
function generateUrl(request, httpMethod, endpoint) {
  //参数中key排序
  const sortParams = ksort(request);
  //拼成参数
  const sortQueryStringTmp = objToParam(sortParams);
  const sortedQueryString = sortQueryStringTmp.substring(1);// 去除第一个多余的&符号
  //构造待签名的字符串
  const Signature = getSignature(sortedQueryString, httpMethod, accessKeySecret)
  //签名最后也要做特殊URL编码
  request["Signature"] = encodeURIComponent(Signature);

  //最终生成出合法请求的URL
  const finalUrl = "https://" + endpoint + "/?Signature=" + encodeURIComponent(Signature) + sortQueryStringTmp;
  return finalUrl;
}

function computeSignature(accessKeySecret, canonicalString) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.HmacSHA1(canonicalString, accessKeySecret));
}
function getNonce(length) {
  var str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  var result = '';
  for (var i = length; i > 0; --i)
    result += str[Math.floor(Math.random() * str.length)];
  return result;
}
function getFormDataParams(stsAccessKeyId, stsAccessKeySecret, securityToken) {
  const date = new Date();
  date.setHours(date.getHours() + 1);
  const policyText = {
    expiration: date.toISOString(), // 设置policy过期时间。
    conditions: [
      // 限制上传大小。
      ["content-length-range", 0, 1024 * 1024 * 1024],
    ],
  };
  const policy = Base64.encode(JSON.stringify(policyText)) // policy必须为base64的string。
  const signature = computeSignature(stsAccessKeySecret, policy)
  const formData = {
    OSSAccessKeyId: stsAccessKeyId,
    signature,
    policy,
    'x-oss-security-token': securityToken
  }
  return formData
}
function uploadToTempOss(ossStsToken, tempFilePath, fileName, callback) {
  const host = 'https://viapi-customer-temp.oss-cn-shanghai.aliyuncs.com';
  let formDataParams = getFormDataParams(ossStsToken.AccessKeyId, ossStsToken.AccessKeySecret, ossStsToken.SecurityToken);
  const signature = formDataParams.signature;
  const ossAccessKeyId = ossStsToken.AccessKeyId;
  const policy = formDataParams.policy;
  const key = accessKeyId + '/' + getNonce(6) + '/' + fileName;
  const securityToken = formDataParams['x-oss-security-token'];
  uni.uploadFile({
    url: host,
    filePath: tempFilePath,
    name: 'file', // 必须填file。
    formData: {
      key,
      policy,
      OSSAccessKeyId: ossAccessKeyId,
      signature,
      'x-oss-security-token': securityToken // 使用STS签名时必传。
    },
    success: (res) => {
      if (res.statusCode === 204 || res.statusCode === '204') {
        let result = 'https://viapi-customer-temp.oss-cn-shanghai.aliyuncs.com/' + key;
        callback && callback(result);
      } else {
        console.log('upload error', res);
      }
    },
    fail: err => {
      console.log(err);
    }
  });
}
function getOssStsToken(callback) {
  // 这里endpoint为API访问域名，与类目相关，具体类目的API访问域名请参考：https://help.aliyun.com/document_detail/143103.html
  const endpoint = "viapiutils.cn-shanghai.aliyuncs.com";
  // API Action，能力名称，请参考具体算法文档详情页中的Action参数，这里以银行卡识别为例：https://help.aliyun.com/document_detail/151893.html
  const Action = "GetOssStsToken";
  // API_VERSION为API版本，与类目相关，具体类目的API版本请参考：https://help.aliyun.com/document_detail/464194.html
  const API_VERSION = "2020-04-01";

  callApi(endpoint, Action, API_VERSION, null, callback);
}
function checkLive(ossurl, callback) {
  // 这里endpoint为API访问域名，与类目相关，具体类目的API访问域名请参考：https://help.aliyun.com/document_detail/143103.html
  const endpoint = "facebody.cn-shanghai.aliyuncs.com";
  // API Action，能力名称，请参考具体算法文档详情页中的Action参数，这里以银行卡识别为例：https://help.aliyun.com/document_detail/151893.html
  const Action = "DetectLivingFace";
  // API_HTTP_METHOD推荐使用POST
  const API_HTTP_METHOD = "POST";
  // API_VERSION为API版本，与类目相关，具体类目的API版本请参考：https://help.aliyun.com/document_detail/464194.html
  const API_VERSION = "2019-12-30";

  const request_ = {};
  //系统参数
  request_["SignatureMethod"] = "HMAC-SHA1",
    request_["SignatureNonce"] = signNRandom(),
    request_["AccessKeyId"] = accessKeyId,
    request_["SignatureVersion"] = "1.0",
    request_["Timestamp"] = getTimestamp(),
    request_["Format"] = "JSON",
    request_["RegionId"] = "cn-shanghai",
    request_["Version"] = API_VERSION,
    request_["Action"] = Action;
  //业务参数，请参考具体的AI能力的API文档进行修改，需要注意在调用其他能力时，这里的入参要根据API文档的入参名进行修改，如人脸活体检测API文档里面的参数名称是“Tasks.N.ImageURL”形式的，因为是支持同时检测多个Task的情况，所以入参时是从“Tasks.1.ImageURL”开始的。
  //举例 request_["Tasks.1.ImageURL"] = "传入您的url";
  request_["Tasks.1.ImageURL"] = ossurl;
  callApiRequest(request_, API_HTTP_METHOD, endpoint, callback);
}

function checkDetectFace(ossurl, callback) {
  // 这里endpoint为API访问域名，与类目相关，具体类目的API访问域名请参考：https://help.aliyun.com/document_detail/143103.html
  const endpoint = "facebody.cn-shanghai.aliyuncs.com";
  // API Action，能力名称，请参考具体算法文档详情页中的Action参数，这里以银行卡识别为例：https://help.aliyun.com/document_detail/151893.html
  const Action = "DetectFace";
  // API_HTTP_METHOD推荐使用POST
  const API_HTTP_METHOD = "POST";
  // API_VERSION为API版本，与类目相关，具体类目的API版本请参考：https://help.aliyun.com/document_detail/464194.html
  const API_VERSION = "2019-12-30";

  const request_ = {};
  //系统参数
  request_["SignatureMethod"] = "HMAC-SHA1",
    request_["SignatureNonce"] = signNRandom(),
    request_["AccessKeyId"] = accessKeyId,
    request_["SignatureVersion"] = "1.0",
    request_["Timestamp"] = getTimestamp(),
    request_["Format"] = "JSON",
    request_["RegionId"] = "cn-shanghai",
    request_["Version"] = API_VERSION,
    request_["Action"] = Action;
  request_["ImageURL"] = ossurl;
  request_["Quality"] = true;
  callApiRequest(request_, API_HTTP_METHOD, endpoint, callback);
}
export default function checkFace(tempFilePath, fileName, callback) {
  getOssStsToken((res) => {
    uploadToTempOss(res.Data, tempFilePath, fileName, (ossurl) => {
      checkLive(ossurl, (liveRes) => {
        let checkRes = liveRes.Data.Elements[0];
        if (checkRes.FaceNumber == 0) {
          callback(0)
        } else if (checkRes.FaceNumber > 1) {
          callback(0)
        } else {
          if (checkRes.Results[0].Suggestion != 'pass') {
            callback(0)
          } else {
            checkDetectFace(ossurl, (detectRes) => {
              let ScoreList = detectRes.Data.Qualities.ScoreList;
              if (Number(ScoreList[0]) > 95) {
                callback(1)
              } else {
                callback(0)
              }
            })
          }
        }
      })
    })
  })
}