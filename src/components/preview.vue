<template>
  <u-popup class="custom-poup" blur="10" v-model="show" :closeable="true" mode="center" close-icon-color="#fff"
    close-icon-size="40rpx" close-icon-pos="top-left" border-radius="14" :custom-style="customStyle"
    :mask-custom-style="maskStyle" @close="close">
    <view class="image-num" v-if="type == 'image'" :style="{ top: navBarHeight + 'px' }">
      {{ current + 1 }} / {{ image_list.length }}
    </view>
    <view :style="{ height: `calc(100vh - ${navBarHeight}px - 10rpx)` }" v-if="type == 'video'" class="swiper-wrap">
      <video controls="false" :src="video_url" initial-time="0" autoplay="true" loop="true" muted="false"></video>
    </view>
    <swiper :style="{ height: `calc(100vh - ${navBarHeight}px - 10rpx)` }" v-if="type == 'image'" class="swiper-wrap"
      circular :indicator-dots="false" @change="bindchange">
      <swiper-item v-for="(item, index) in image_list" :key="index">
        <view class="swiper-item">
          <image :src="item" mode="aspectFit" />
        </view>
      </swiper-item>
    </swiper>
  </u-popup>
</template>

<script setup>
import { ref, toRefs } from "vue";
import { onReady, onReachBottom } from "@dcloudio/uni-app";
import request from "@/request.js";

const maskStyle = { background: "rgba(0, 0, 0, 0.5)" };
const customStyle = { background: "rgba(0, 0, 0, 0.5)" };
const props = defineProps(["previewData"]);

const { show, video_url, image_list, type } = toRefs(props.previewData);
const emit = defineEmits(["update:previewData"]);
let current = ref(0);
let res = uni.getSystemInfoSync()
let navBarHeight = res.statusBarHeight //顶部状态栏+顶部导航，大部分机型默认44px

function close() {
  current.value = 0;
  console.log('close');
  emit("update:previewData", {
    show: false,
    type: "image",
    video_url: "",
    image_list: [],
  });
}

function bindchange(e) {
  current.value = e.detail.current;
}

onReady(async () => { });
</script>
<style lang="scss">
.custom-poup {
  video {
    width: 750rpx;
    margin-top: 50%;
    transform: translateY(-50%);
  }

  .swiper-wrap {
    width: 750rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-num {
    width: 100%;
    color: #fff;
    text-align: center;
    font-size: 36rpx;
    position: fixed;
    height: 44px;
    line-height: 44px;
  }

  .swiper-item {
    display: block;
    text-align: center;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 750rpx;
      height: 100%;
    }
  }
}
</style>
