<template>
  <sh-address v-if="!loading" @change="lochange" :custAddress="addArr">
    <slot></slot>
  </sh-address>
</template>
<script setup>
import { ref, onBeforeMount } from "vue";
import Address from "./address.json";
const addArr = ref(Address)
import request from "@/request.js";
const loading = ref(true)
onBeforeMount(async () => {
  request({
    url: "/api/auth/areastreet",
    data: {
      type: 3,
      code: 510500
    }
  }).then(res => {
    addArr.value[0].child[0].child = res.map((item) => {
      return {
        "code": item.code,
        "name": item.text,
        "cityCode": "5105",
        "provinceCode": "51"
      }
    })
    loading.value = false;
  })
})
const emit = defineEmits(['change'])
function lochange(e) {
  emit("change", e)
}
</script>