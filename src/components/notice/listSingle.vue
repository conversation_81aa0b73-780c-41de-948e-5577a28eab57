<template>
    <view class="single-box">
        <view class="single-box__title flex-mode">
            <image :src="avatarUrl" class="single-box__title__avatar"></image>
            <view class="single-box__title__text flex-mode">
                <view class="single-box__title__text__1">{{ props.curDetail.account_name }}</view>
                <view class="single-box__title__text__2">{{ props.curDetail.account_name }}</view>
            </view>
            <image src="https://obs.tuoyupt.com/nanjing/pstac/notice/works.png" class="single-box__title__work"></image>
        </view>
        <view class="single-box__content">
            <view class="single-box__content__article">{{ tempContent }}</view>
            <view v-if="txt_content !== null && txt_content.length > limFont">
                <text class="single-box__content__show" v-if="isShowAllContent" @click="toggleDescription">
                    展开
                </text>
                <text class="single-box__content__show" v-else @click="toggleDescription">
                    收起
                </text>
            </view>
            <view class="flex-mode single-box__content__img1" v-if="imgCount === 1">
                <image></image>
            </view>
        </view>
        <view class="single-box__divider"></view>
    </view>
</template>

<script setup>
import { computed, onMounted, ref, toRefs } from 'vue';
import { onReady, onReachBottom, onShow } from '@dcloudio/uni-app';
import request from "@/request.js"
// 数据
const props = defineProps({
    curDetail: {
        type: Object,
        default: false
    },
})
const txt_content = ref('有一天我划着倦舟归来，告诉自己，再也无须假装年轻，等待一段梨花似雪的相逢。忘记许过的诺言，告诉曾经携手作伴的人，相安无事，莫多惊扰。安然于小小的旧宅里…安然于小小的旧宅里…安然于小小的旧宅里…')
const isShowAllContent = ref(false)
const tempContent = ref('')
const limFont = 75

// 计算属性
const avatarUrl = computed(() => {
    return props.curDetail.account_img ? props.curDetail.account_img : 'https://obs.tuoyupt.com/nanjing/pstac/login/login_logo.png'
})
const imgCount = computed(() => {
    return props.curDetail.data_media.medias.length
})

//方法
const toggleDescription = () => {
    if (isShowAllContent.value) {
        isShowAllContent.value = false
        tempContent.value = txt_content.value
    } else {
        isShowAllContent.value = true
        tempContent.value = txt_content.value.substring(0, limFont) + "..."
    }
}

// 生命周期
onReady(async () => {
    let txtCntIndex = txt_content.value.length
    if (txtCntIndex > limFont) {
        isShowAllContent.value = true
        tempContent.value = txt_content.value.substring(0, limFont) + "..."
    } else {
        isShowAllContent.value = false
        tempContent.value = txt_content.value
    }
    console.log(props.curDetail)

})


</script>

<style lang="scss">
.flex-mode {
    display: flex;
    justify-content: center;
    align-items: center;
}

.single-box {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 40rpx 30rpx 0 30rpx;
    box-sizing: border-box;
    background-color: #FFFFFF;


    &__title {
        width: 100%;

        &__avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 24rpx;
        }

        &__work {
            width: 140rpx;
            height: 64rpx;
        }

        &__text {
            flex-direction: column;
            margin-right: auto;

            &__1 {
                font-size: 28rpx;
                font-weight: 500;
                color: #111111;
                line-height: 28rpx;
            }

            &__2 {
                margin-top: 8rpx;
                font-size: 24rpx;
                font-weight: 400;
                color: #AEB0BC;
                line-height: 24rpx;
            }
        }
    }

    &__content {
        padding-left: 68rpx;

        &__article {
            margin-top: 16rpx;
            font-size: 32rpx;
            font-weight: 400;
            color: #262937;
            line-height: 48rpx;
        }

        &__show {
            font-size: 32rpx;
            font-weight: 400;
            color: #617EFB;
            line-height: 48rpx;
        }

        &__img1 {
            width: 556rpx;
            height: 368rpx;
            border-radius: 4rpx;

            image {
                width: 556rpx;
                height: 368rpx;
                border-radius: 4rpx;
                background-color: #00C777;
            }
        }
    }

    &__divider {
        width: 100%;
        height: 2rpx;
        color: #F6F6F6;
        margin: 32rpx 0 0 0;
    }
}
</style>
