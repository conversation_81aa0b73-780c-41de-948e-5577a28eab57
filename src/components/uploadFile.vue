<template>
    <u-button style="margin-bottom: 24rpx" ref="uploadButton" id="uploadButton" type="primary" size="medium"
        @click="uploadClick">选择文件</u-button>
    <view v-if="props.tips">{{ props.tips }}</view>
    <input ref="uploadInput" id="uploadInput" type="file" accept="*" hidden />
    <u-cell-item hover-class="false" :title-style="{ width: '50vw', overflow: 'hidden' }" v-for="(item, index) in list"
        :key="index" icon="file-text-fill" :title="getName(item)" :arrow="false">
        <u-icon slot="right-icon" size="32" name="close" @click="uploadDelete(index)"></u-icon>
    </u-cell-item>
    <u-modal :show-cancel-button="true" title="提示" v-model="uploadDeleteShow" ref="uModal" :async-close="true"
        @confirm="list.splice(uploadDeleteIndex, 1); uploadDeleteShow = false; emit('getNewData', list)">
        <view class="modal-conent">
            <text class="p">是否删除该文件</text>
        </view>
    </u-modal>
</template>


<script setup>
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { reactive, ref, watch } from "vue";
import { OBSupload } from "@/utils/obs/obs-upload.js";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
import dayjs from 'dayjs';
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);
import { useLocalStore } from "@/stores/useLocalStore.js";
import { id2name, str2arr } from "@/utils/dataHandle";

const props = defineProps({
    fileList: {
        type: Array,
        default() {
            return []
        }
    },
    extension: {
        type: Array,
        default() {
            return ['JPG', 'PNG', "DOC", "DOCX", "PDF"]
        }
    },
    limit: {
        type: Number,
        default() {
            return 6
        }
    },
    tips: {
        type: String,
        default() {
            return ''
        }
    }
})
const emit = defineEmits(['getNewData'])

let uploadButton = ref('');
let uploadInput = ref('');
let uploadDeleteShow = ref(false);
let uploadDeleteIndex = ref('');
let list = ref([]);

watch(() => props.fileList, () => {
    list.value = props.fileList;
}, {
    immediate: true,
    deep: true
})

function uploadDelete(index) {
    console.log(index);
    uploadDeleteShow.value = true;
    uploadDeleteIndex.value = index
}

function getName(name) {
    return name.substring(name.lastIndexOf("/") + 1);
}

function uploadClick(key) {
    uni.chooseFile({
        count: props.limit, //默认100
        extension: props.extension,
        success: async function (res) {
            console.log(res);
            for (const element of res.tempFiles) {
                const fileName = element.name;
                const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
                list.value.push(await OBSupload(element.path, fileExtension, fileName));
            }
            emit('getNewData', list.value)
        }
    });
}
</script>