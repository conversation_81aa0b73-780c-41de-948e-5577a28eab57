<template>
    <view class="u-tabbar" @touchmove.stop.prevent="() => { }">
        <view class="u-tabbar__content safe-area-inset-bottom" :style="{
            height: $u.addUnit(height),
            backgroundColor: bgColor
        }" :class="{
            'u-border-top': borderTop
        }">
            <view class="u-tabbar__content__item" v-for="(item, index) in showList" :key="index" :class="{
                'u-tabbar__content__circle': midButton && item.midButton
            }" @tap.stop="clickHandler(item)" :style="{
                backgroundColor: bgColor
            }">
                <template v-if="showTop && currentIndex == index">
                    <image @click="$emit('backTop')" src="https://obs.tuoyupt.com/miniprogram/index/btop.png"
                        class="top">
                    </image>
                </template>
                <template v-else>
                    <view :class="[
                        midButton && item.midButton
                            ? 'u-tabbar__content__circle__button'
                            : 'u-tabbar__content__item__button'
                    ]">
                        <image :src="currentIndex == index ? item.selectedIconPath : item.iconPath" class="icon">
                        </image>
                    </view>
                    <view class="u-tabbar__content__item__text" :style="{
                        color: currentIndex == index ? '#262937' : '#AEB0BC'
                    }">
                        <text class="u-line-1">{{ item.text }}</text>
                    </view>
                </template>

            </view>
            <view v-if="midButton" class="u-tabbar__content__circle__border" :class="{
                'u-border': borderTop
            }" :style="{
                backgroundColor: bgColor,
                left: midButtonLeft
            }"></view>
        </view>
        <!-- 这里加上一个48rpx的高度,是为了增高有凸起按钮时的防塌陷高度(也即按钮凸出来部分的高度) -->
        <view class="u-fixed-placeholder safe-area-inset-bottom" :style="{
            height: `calc(${$u.addUnit(height)} + ${midButton ? 48 : 0}rpx)`
        }"></view>
        <!-- 点击+弹框-->
    </view>
</template>

<script>
import UPopup from "../uni_modules/vk-uview-ui/components/u-popup/u-popup.vue";
import { mapState } from 'pinia'
import { useUserStore } from "@/stores/userInfo.js"
export default {
    components: { UPopup },
    emits: ["backTop", "input", "change"],
    props: {
        // current值
        currentTab: {
            type: Number,
            default: 0
        },
        // 显示与否
        showTop: {
            type: Boolean,
            default: false
        },
        // 整个tabbar的背景颜色
        bgColor: {
            type: String,
            default: "#ffffff"
        },
        // tabbar的高度，默认50px，单位任意，如果为数值，则为rpx单位
        height: {
            type: [String, Number],
            default: 120
        },
        // 非凸起图标的大小，单位任意，数值默认rpx
        iconSize: {
            type: [String, Number],
            default: 40
        },
        // 凸起的图标的大小，单位任意，数值默认rpx
        midButtonSize: {
            type: [String, Number],
            default: 90
        },
        // 激活时的演示，包括字体图标，提示文字等的演示
        activeColor: {
            type: String,
            default: "#303133"
        },
        // 未激活时的颜色
        inactiveColor: {
            type: String,
            default: "#606266"
        },
        // 是否显示中部的凸起按钮
        midButton: {
            type: Boolean,
            default: false
        },
        // 切换前的回调
        beforeSwitch: {
            type: Function,
            default: null
        },
        // 是否显示顶部的横线
        borderTop: {
            type: Boolean,
            default: true
        },
    },
    data() {
        return {
            // 由于安卓太菜了，通过css居中凸起按钮的外层元素有误差，故通过js计算将其居中
            showPop: false,
            isBlank: false,
            midButtonLeft: "50%",
            pageUrl: "", // 当前页面URL
            list: [
                {
                    iconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/i.svg",
                    selectedIconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/ia.svg",
                    text: "首页",
                    pagePath: "/pages_work/index/index",
                    isTab: true
                },
                {
                    iconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/dt.svg",
                    selectedIconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/dta.svg",
                    text: "动态",
                    pagePath: "/pages_work/notice/index",
                    isTab: true
                },
                // {
                //     iconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/y.svg",
                //     selectedIconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/ya.svg",
                //     text: "育儿",
                //     pagePath: "/pages_work/teach/index",
                //     isTab: false
                // },
                {
                    iconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/msg.svg",
                    selectedIconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/msga.svg",
                    text: "消息",
                    pagePath: "/pages_work/message/index",
                    isTab: true
                },
                {
                    iconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/m.svg",
                    selectedIconPath: "https://obs.tuoyupt.com/miniprogram/tabbar/ma.svg",
                    text: "我的",
                    pagePath: "/pages_work/mine/main",
                    isTab: true
                },
            ]
        };
    },
    created() {
        // 获取引入了u-tabbar页面的路由地址，该地址没有路径前面的"/"
        let pages = getCurrentPages();
        // 页面栈中的最后一个即为项为当前页面，route属性为页面路径
        this.pageUrl = pages[pages.length - 1].route;
    },
    computed: {
        ...mapState(useUserStore, ['identity']),
        showList() {
            if (this.identity == 2) {
                return this.list
            } else {
                let list = JSON.parse(JSON.stringify(this.list))
                return [list[0], list[2], list[3]]
            }
        },
        currentIndex() {
            if (this.identity == 2) {
                return this.currentTab
            } else {
                if (this.currentTab == 2 || this.currentTab == 3) {
                    return this.currentTab - 1
                } else {
                    return this.currentTab
                }
            }
        },
        valueCom() {
            // #ifndef VUE3
            return this.value;
            // #endif

            // #ifdef VUE3
            return this.modelValue;
            // #endif
        },
        elIconPath() {
            return index => {
                // 历遍u-tabbar的每一项item时，判断是否传入了pagePath参数，如果传入了
                // 和data中的pageUrl参数对比，如果相等，即可判断当前的item对应当前的tabbar页面，设置高亮图标
                // 采用这个方法，可以无需使用v-model绑定的value值
                let pagePath = this.list[index].pagePath;
                // 如果定义了pagePath属性，意味着使用系统自带tabbar方案，否则使用一个页面用几个组件模拟tabbar页面的方案
                // 这两个方案对处理tabbar item的激活与否方式不一样
                if (pagePath) {
                    if (pagePath == this.pageUrl || pagePath == "/" + this.pageUrl) {
                        return this.list[index].selectedIconPath;
                    } else {
                        return this.list[index].iconPath;
                    }
                } else {
                    // 普通方案中，索引等于v-model值时，即为激活项
                    return index == this.valueCom
                        ? this.list[index].selectedIconPath
                        : this.list[index].iconPath;
                }
            };
        }
    },
    mounted() {
        this.midButton && this.getMidButtonLeft();
    },
    methods: {
        async clickHandler(item) {
            if (item.isTab) {
                uni.redirectTo({
                    url: item.pagePath
                })
            } else {
                uni.redirectTo({
                    url: item.pagePath
                })
            }
        },
        // 切换tab
        switchTab(index) {
            if (index === 2) {
                this.showPop = true
            } else {
                // 发出事件和修改v-model绑定的值
                this.$emit("change", index);
                // 如果有配置pagePath属性，使用uni.switchTab进行跳转
                if (this.list[index].pagePath) {
                    uni.redirectTo({
                        url: this.list[index].pagePath
                    });
                } else {
                    // 如果配置了papgePath属性，将不会双向绑定v-model传入的value值
                    // 因为这个模式下，不再需要v-model绑定的value值了，而是通过getCurrentPages()适配
                    this.$emit("input", index);
                    this.$emit("update:modelValue", index);
                }
            }
        },
        // 计算角标的right值
        getOffsetRight(count, isDot) {
            // 点类型，count大于9(两位数)，分别设置不同的right值，避免位置太挤
            if (isDot) {
                return -20;
            } else if (count > 9) {
                return -40;
            } else {
                return -30;
            }
        },
        // 获取凸起按钮外层元素的left值，让其水平居中
        getMidButtonLeft() {
            let windowWidth = this.$u.sys().windowWidth;
            // 由于安卓中css计算left: 50%的结果不准确，故用js计算
            this.midButtonLeft = windowWidth / 2 + "px";
        },
        popClick(index) {
            console.log(this.current)
            console.log(index)
            if (index === 1) {
                this.showPop = false
                uni.navigateTo({
                    url: "/pages_work/notice/push"
                })
            }
        }
    }
};
</script>

<style scoped lang="scss">
@import "../uni_modules/vk-uview-ui/libs/css/style.components.scss";

.u-fixed-placeholder {
    /* #ifndef APP-NVUE */
    box-sizing: content-box;
    /* #endif */
}

.main {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto
}

.u-tabbar {
    &__content {
        @include vue-flex;
        align-items: center;
        position: relative;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        z-index: 998;
        /* #ifndef APP-NVUE */
        box-sizing: content-box;

        /* #endif */
        .plus-btn {
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56rpx;
            height: 56rpx;
            background: #617EFB;
            border-radius: 16rpx;

            .icon {
                width: 56rpx;
                height: 56rpx;
            }
        }

        &__circle__border {
            border-radius: 100%;
            width: 110rpx;
            height: 110rpx;
            top: -48rpx;
            position: absolute;
            z-index: 4;
            background-color: #ffffff;
            // 由于安卓的无能，导致只有3个tabbar item时，此css计算方式有误差
            // 故使用js计算的形式来定位，此处不注释，是因为js计算有延后，避免出现位置闪动
            left: 50%;
            transform: translateX(-50%);

            &:after {
                border-radius: 100px;
            }
        }

        &__item {
            flex: 1;
            justify-content: center;
            height: 100%;
            padding: 12rpx 0;
            @include vue-flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            .top {
                width: 88rpx;
                height: 88rpx;
            }

            &__button {
                position: absolute;
                top: 24rpx;
                left: 50%;
                transform: translateX(-50%);

                .icon {
                    width: 48rpx;
                    height: 48rpx;
                }
            }

            &__text {
                color: #AEB0BC;
                font-size: 20rpx;
                line-height: 28rpx;
                position: absolute;
                bottom: 14rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 100%;
                text-align: center;
            }
        }

        &__circle {
            position: relative;
            @include vue-flex;
            flex-direction: column;
            justify-content: space-between;
            z-index: 10;
            /* #ifndef APP-NVUE */
            height: calc(100% - 1px);
            /* #endif */

            &__button {
                width: 90rpx;
                height: 90rpx;
                border-radius: 100%;
                @include vue-flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                background-color: #ffffff;
                top: -40rpx;
                left: 50%;
                z-index: 6;
                transform: translateX(-50%);
            }
        }
    }
}

.pop-box {
    position: relative;
    height: 100%;
    width: 100%;

    &__content {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        top: 10%;
        left: 0;
        right: 0;
        margin: 0 auto;

        image {
            width: 512rpx;
            height: 512rpx;
        }

        &__text1 {
            margin-top: 82rpx;
            font-size: 48rpx;
            font-weight: 500;
            color: #262937;
            line-height: 66rpx;
        }

        &__text2 {
            margin-top: 8rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #262937;
            line-height: 40rpx;
        }

        &__btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            margin-top: 240rpx;

            &__single {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                padding: 32rpx 54rpx;

                image {
                    width: 64rpx;
                    height: 64rpx;
                }

                &__text {
                    margin-top: 20rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #787C8D;
                    line-height: 24rpx;
                }
            }
        }
    }

    .pop-back1 {
        position: absolute;
        right: 0;
        top: 0;
        width: 526rpx;
        height: 458rpx;
        background: #FF7373;
        border-radius: 276rpx;
        opacity: 0.08;
        filter: blur(50px);
    }

    .pop-back2 {
        position: absolute;
        left: 0;
        top: 0;
        width: 585rpx;
        height: 942rpx;
        background: #617EFB;
        border-radius: 350rpx;
        opacity: 0.08;
        filter: blur(50px);
    }

    .pop-close {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 86rpx;
        left: calc(50% - 48rpx);
        width: 96rpx;
        height: 64rpx;
        background: #262937;
        border-radius: 32rpx;
    }
}
</style>
