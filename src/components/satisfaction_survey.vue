<template>
  <u-popup blur="10" closeable v-model="show" :mask-close-able="false" border-radius='24' mode="bottom"
    safe-area-inset-bottom>
    <view class="t1">家长满意度</view>
    <view class="t2">请对您当前所在机构选行满意度评价（匿名）</view>
    <view class="list">
      <view @click="change(1)" class="item">
        <image class="i" v-if="score == 1" src="https://obs.tuoyupt.com/miniprogram/pack/m1.webp">
        </image>
        <image class="i" v-else src="https://obs.tuoyupt.com/miniprogram/pack/f1.webp">
        </image>
        <view>非常不满意</view>
      </view>
      <view @click="change(2)" class="item">
        <image class="i" v-if="score == 2" src="https://obs.tuoyupt.com/miniprogram/pack/m2.webp">
        </image>
        <image class="i" v-else src="https://obs.tuoyupt.com/miniprogram/pack/f2.webp">
        </image>
        <view>不满意</view>
      </view>
      <view @click="change(3)" class="item">
        <image class="i" v-if="score == 3" src="https://obs.tuoyupt.com/miniprogram/pack/m3.webp">
        </image>
        <image class="i" v-else src="https://obs.tuoyupt.com/miniprogram/pack/f3.webp">
        </image>
        <view>一般</view>
      </view>
      <view @click="change(4)" class="item">
        <image class="i" v-if="score == 4" src="https://obs.tuoyupt.com/miniprogram/pack/m4.webp">
        </image>
        <image class="i" v-else src="https://obs.tuoyupt.com/miniprogram/pack/f4.webp">
        </image>
        <view>满意</view>
      </view>
      <view @click="change(5)" class="item">
        <image class="i" v-if="score == 5" src="https://obs.tuoyupt.com/miniprogram/pack/m5.webp">
        </image>
        <image class="i" v-else src="https://obs.tuoyupt.com/miniprogram/pack/f5.webp">
        </image>
        <view>非常满意</view>
      </view>
    </view>
    <view class="btn" @click="submit">确定</view>
  </u-popup>
</template>

<script setup>
import { ref } from "vue";
import dayjs from "dayjs";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const show = ref(false);
const score = ref(5);
function change(i) {
  score.value = i;
}
function check() {
  const day = dayjs();
  if (day.date() == 1) {
    request({
      url: "/api/parent/satisfied/info",
      data: {
        school_id: userInfo.school_id
      }
    }).then(res => {
      if (!res.score) {
        showPop()
      }
    })
  }
}
function showPop() {
  show.value = true;
}
function submit() {
  if (score.value == 0) {
    uni.showToast({
      title: "请选择满意度",
      icon: "none"
    })
    return;
  }
  request({
    url: "/api/parent/satisfied/save",
    method: "post",
    data: {
      school_id: userInfo.school_id,
      score: score.value * 20,
    }
  }).then(res => {
    uni.showToast({
      title: "提交成功",
      icon: "none"
    })
    show.value = false;
  })
}
defineExpose({
  showPop,
  check
})
</script>
<style lang="scss" scoped>
.list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 0 30rpx;
  margin-top: 30rpx;


  .item {
    text-align: center;

    .i {
      width: 80rpx;
      height: 80rpx;
    }
  }

}

.t1 {
  text-align: center;
  font-weight: 600;
  font-size: 34rpx;
  color: #000000;
  line-height: 48rpx;
  padding-top: 40rpx;
}

.t2 {
  font-weight: 400;
  font-size: 26rpx;
  color: #797D8D;
  line-height: 36rpx;
  text-align: center;
  margin-top: 12rpx;
}

.btn {
  width: 690rpx;
  height: 80rpx;
  background: #FF7373;
  border-radius: 16rpx;
  text-align: center;
  line-height: 80rpx;
  margin: 30rpx auto;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
}
</style>