<template>
	<view class="container" :style="{ background }">
		<view class="tmt-header">
			<view class="select-wrap">
				<view class="time">
					{{ `${currentYearMonth.year}年${currentYearMonth.month >= 10 ? currentYearMonth.month : '0' +
						currentYearMonth.month}月`
					}}
				</view>
			</view>
			<view class="arrow">
				<view class="p20" @click="changMonth(-1)">
					<view class="arrow-left">
					</view>
				</view>
				<view class="p20" @click="changMonth(1)">
					<view class="arrow-right">
					</view>
				</view>
			</view>
		</view>
		<slot name="header"></slot>
		<view class="tmt-content">
			<view class="tmt-week-wrap">
				<view class="cell week-item" v-for="(item, index) in week" :key="item.label">{{
					item.label }}
				</view>
			</view>
			<view class="tmt-day-wrap" :style="[{ height: unfold ? contentHeight : '88rpx' }, { color: dayColor }]">
				<view class="day-content"
					:style="{ transform: unfold ? 'translateY(0rpx)' : `translateY(-${currentIndex * 88}rpx)` }">
					<view class="cell" v-for="index of blankDay" :key="index"></view>
					<view class="cell" v-for="(item, index) in daysArr" :key="item.id" @click="changeDay(item)">
						<view class="dayText"
							:class="{ weekend: item.isWeekend, today: item.today, selectDay: current.year == item.year && current.month == item.month && current.day == item.day }">
							{{ item.today ? '今' : item.day }}
							<view class="point" :style="{ background: pointColor(item.point) }" v-if="item.point > 0"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="showBtn" class="tmt-footer" @click="unfold = !unfold">
			{{ unfold ? '收起' : '展开' }} <u-icon :class="['arrow-down', { on: unfold }]" name="arrow-down" color="#000000"
				size="28"></u-icon>
		</view>
	</view>
</template>

<script>
import dayjs from 'dayjs';
export default {
	name: "calendar",
	data() {
		return {
			unfold: true,
			week: [{
				label: '日',
				value: 7
			}, {
				label: '一',
				value: 1
			},
			{
				label: '二',
				value: 2
			},
			{
				label: '三',
				value: 3
			},
			{
				label: '四',
				value: 4
			},
			{
				label: '五',
				value: 5
			},
			{
				label: '六',
				value: 6
			},
			],
			blankDay: 0, //空白天数
			today: {}, //当天
			current: {}, //当前的年月
			daysArr: [],
			list: [], //需要下标的日期
			currentYearMonth: {}, //当前年月
		};
	},
	computed: {
		contentHeight() {
			let height = Math.ceil((this.blankDay + this.daysArr.length) / 7) * 88
			return height + 'rpx'
		},
		currentIndex() {
			let index = Math.ceil((this.blankDay + this.current.day) / 7);
			return index - 1;
		}
	},
	props: {
		pointList: {
			type: Array,
			default() {
				return []
			}
		},
		defaultDate: {
			type: String,
			default() {
				return ''
			}
		},
		showBtn: {
			type: Boolean,
			default() {
				return true
			}
		},
		show: {
			type: Boolean,
			default() {
				return false
			}
		},
		background: {
			type: String,
			default() {
				return 'linear-gradient(180deg, #EAF6FF 0%, #FFFFFF 100%)'
			}
		},
		weekColor: {
			type: String,
			default() {
				return '#9C9D9D'
			}
		},
		dayColor: {
			type: String,
			default() {
				return '#000000'
			}
		},
		selectBg: {
			type: String,
			default() {
				return 'linear-gradient(180deg, #FF855E 0%, #ED6337 100%)'
			}
		},
		backColor: {
			type: String,
			default() {
				return '#fff'
			}
		},
		backBg: {
			type: String,
			default() {
				return 'rgba(255, 255, 255, 0.19)'
			}
		},
		actionColor: {
			type: String,
			default() {
				return '#fff'
			}
		},
		unfoldBtnColor: {
			type: String,
			default() {
				return '#000000'
			}
		}
	},
	created() {
		this.unfold = this.show
		this.list = this.pointList
		this.fomatePointTime()
		this.init(this.defaultDate == '')
	},
	methods: {
		pointColor(type) {
			let color = ""
			switch (type) {
				case 1:
					color = "#F2637B"
					break;
				case 2:
					color = "#3BA0FF"
					break;
				case 3:
					color = "#4DCB73"
					break;
				case 4:
					color = "#FAD337"
					break;
			}
			return color
		},
		changMonth(num) {
			this.$emit("changMonth", num)
		},
		// 获取前几个月,未来几个月的年份和月份
		getMonthYear(num) {
			let month = this.currentYearMonth.month
			let year = this.currentYearMonth.year
			let year2 = year
			let month2 = month + num
			if (month + num <= 0) {
				// 之前年份
				year2 = year - 1 - parseInt(Math.abs(month2) / 12)
				month2 = 12 - (Math.abs(month2) % 12)
			} else if ((month2 / 12) > 1) {
				// 之后年份
				year2 = year + (month2 % 12 == 0 ? 0 : parseInt(month2 / 12))
				month2 = parseInt(month2 % 12) == 0 ? 12 : parseInt(month2 % 12)
			}
			return {
				year: year2,
				month: month2
			}
		},
		changeDay(item) {
			this.current = item;
			let { year, month, day } = item
			this.$emit('changeDate', { year, month, day })
		},
		// 获取某年某月的天数
		getDays(year, month) {
			let now = new Date(year, month, 0)
			return now.getDate()
		},
		//获取某一天为星期几
		getWeekByDay(time) {
			let day = new Date(Date.parse(time.replace(/-/g, '/'))); //将日期值格式化
			return day.getDay() == 0 ? 7 : day.getDay();
		},
		init(nowTime) {
			let setTime = nowTime ? new Date() : new Date(this.defaultDate)
			let year = setTime.getFullYear()
			let month = setTime.getMonth() + 1
			let day = setTime.getDate()
			if (!(this.defaultDate != '' && nowTime)) {
				this.current = {
					year,
					month,
					day,
				}
			}
			this.today = {
				year: new Date().getFullYear(),
				month: new Date().getMonth() + 1,
				day: new Date().getDate()
			}
			this.makeCalendar(year, month)
		},
		fomatePointTime() {
			let pointList = this.list
			pointList = pointList.map(item => {
				item = item.replace(/-/g, '/') //期值格式化
				let timeArr = item.split('/')
				let timeStr = ''
				timeArr.map(time => {
					time = parseInt(time)
					timeStr += time
					return time
				})
				return timeStr
			})
			this.list = pointList
		},
		makeCalendar(year, month) {
			let today = this.today
			let days = this.getDays(year, month) //当月天数
			let firstDayWeek = this.getWeekByDay(`${year}-${month}-1`) //获取每个月第一天的星期
			let weekIndex = this.week.findIndex(item => {
				return item.value == firstDayWeek
			})
			let daysArr = []

			for (let i = 0; i < this.pointList.length; i++) {
				let el = dayjs(this.pointList[i].Ymd);
				daysArr.push({
					year: el.year(),
					month: el.month() + 1,
					day: el.date(),
					point: this.pointList[i].state,
					isWeekend: this.pointList[i].week == '六' || this.pointList[i].week == '日',
					id: el.format("YYYYMMDD"),
					today: el.isSame(`${today.year}-${today.month}-${today.day}`, 'day')
				})
			}
			this.currentYearMonth = {
				year,
				month
			}
			this.daysArr = daysArr;
			this.blankDay = weekIndex == -1 ? 0 : weekIndex;
		},
	}
}
</script>

<style lang="scss">
.p20 {
	padding: 20upx;
}

.container {
	width: 690rpx;
	margin: 0 auto;
	border-radius: 24rpx 24rpx 0rpx 0rpx;
	padding: 0 20upx;
}

.tmt-header {
	display: flex;
	justify-content: space-between;
	padding: 20upx 0;

	.select-wrap {
		display: flex;
		align-items: center;

		.time {
			color: #787C8D;
			font-size: 40upx;
			margin: 0 20upx;
		}
	}

	.arrow {
		display: flex;
		justify-content: space-between;
		flex-direction: row;
		width: 20%;

		.arrow-left {
			width: 14upx;
			height: 14upx;
			transform: rotate(45deg);
			border-left-width: 1upx;
			border-bottom-width: 1upx;
			border-left-style: solid;
			border-bottom-style: solid;
		}

		.arrow-right {
			width: 14upx;
			height: 14upx;
			transform: rotate(45deg);
			border-top-width: 1upx;
			border-right-width: 1upx;
			border-top-style: solid;
			border-right-style: solid;
		}
	}
}

.cell {
	width: 14.28%;
	height: 88upx;
	text-align: center;
	line-height: 88upx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;

	.point {
		width: 6upx;
		height: 6upx;
		position: absolute;
		bottom: 3upx;
		border-radius: 50%;
		left: 50%;
		transform: translateX(-50%);
	}

	.dayText {
		width: 56upx;
		height: 56upx;
		text-align: center;
		line-height: 56upx;
		border-radius: 50%;
	}

	.selectDay {
		border: 1rpx solid #000;
	}

	.today {
		color: #FF7373;
	}

	.weekend {
		color: #999999;
	}
}

.tmt-content {
	padding-bottom: 20upx;

	.tmt-week-wrap {
		display: flex;

		.week-item {
			font-size: 20rpx;
			font-weight: 400;
			color: #999999;
		}
	}

	.tmt-day-wrap {
		transition: height .3s;
		overflow: hidden;

		.day-content {
			transition: transform .3s;
			width: 100%;
			display: flex;
			flex-wrap: wrap;
		}
	}
}

.tmt-footer {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80upx;
	margin-top: -36upx;
	font-size: 24rpx;
	font-weight: 500;
	color: #787C8D;

	.arrow-down {
		transform: rotate(0deg);
		transition: all .3s;
		margin-left: 12rpx;

		&.on {
			transform: rotate(180deg);
		}
	}
}
</style>
