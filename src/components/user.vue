<template>
  <view class="user">
    <button v-if="isLogin" @click="gHome">
      <template v-if="identity == 2">
        <u-icon name="home" color="#222222" size="48"></u-icon>
      </template>
      <template v-else>
        <image src="https://obs.tuoyupt.com/miniprogram/client/images/default.png" />
        <text>{{ username }}</text>
      </template>
    </button>
    <button v-else @click="gotoLogin">
      <image src="https://obs.tuoyupt.com/miniprogram/client/images/default.png" />
      <text>微信登录</text>
    </button>
  </view>
</template>

<script setup>
import request from "@/request.js"
import { useUserStore } from "@/stores/userInfo.js"
import { computed, onMounted } from 'vue';
const userInfo = useUserStore();
const isLogin = computed(() => {
  return userInfo.id !== 0;
})
const identity = computed(() => {
  return userInfo.identity;
})
const username = computed(() => {
  return userInfo.phone.substr(6, 5) + '用户';
})
function gotoLogin() {
  uni.redirectTo({
    url: "/pages_work/login/main"
  })
}
function gHome() {
  if (identity.value == 2) {
    uni.reLaunch({
      url: "/pages_work/index/index"
    })
  }
}
onMounted(() => {
  if (uni.getStorageSync("token") && !isLogin.value) {
    userInfo.refrsh(true);
  }
})
const getPhoneNumber = (e) => {
  if (e.detail.code) {
    // 展示加载框
    uni.showLoading({
      title: '加载中',
    });
    uni.login({
      provider: 'weixin',
      success: (res) => {
        let code = res.code;
        if (res.errMsg == 'login:ok') {
          request({
            url: `/api/auth/firstlogin?code=${code}`,
          }).then(async (res2) => {
            let openid = res2.openid;
            let codephone = e.detail.code;
            let login_type = 2;
            let checkcode = 0;
            request({
              url: "/api/auth/secondlogin",
              method: 'post',
              data: {
                openid,
                codephone,
                login_type,
                checkcode,
              }
            }).then(res => {
              uni.hideLoading();
              if (res.token) {
                uni.setStorageSync("token", res.token);
                uni.setStorageSync("user_info", res.user);
                let user = res.user;
                userInfo.$patch(user)
                uni.showToast({
                  title: "登录成功",
                  icon: "success",
                  duration: 1500,
                })
              } else {
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  confirmText: '确认',
                  cancelText: '取消',
                  success: function (resc) {
                    if (resc.confirm) {
                      uni.showLoading();
                      request({
                        url: "/api/auth/loginbindsure",
                        method: 'post',
                        data: {
                          openid: res.data.mini_openid,
                          uid: res.data.uid,
                        }
                      }).then(res3 => {
                        uni.hideLoading();
                        uni.setStorageSync("token", res3.token);
                        uni.setStorageSync("user_info", res3.user);
                        let user = res3.user;
                        userInfo.$patch(user)
                        uni.showToast({
                          title: "登录成功",
                          icon: "success",
                          duration: 1500,
                        })
                      })
                    }
                  },
                })
              }
            })
          });
        }
        uni.hideLoading()
      },
    });
  }
}
</script>

<style lang="scss">
page {
  background-color: #F6F6F6;
}

.user {
  display: flex;
  align-items: center;
  height: 100rpx;

  button {
    border: none;
    padding: 0 30rpx;
    background: none;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 32rpx;
    font-weight: 400;
    color: #787C8D;
  }

  image {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    margin-right: 14rpx;
  }
}
</style>
