<template>
  <view class="container" :style="{ background }">
    <view class="tmt-header">
      <view class="select-wrap">
        <view class="time">
          {{ `${currentYearMonth.year}-${currentYearMonth.month >= 10 ? currentYearMonth.month : '0' +
            currentYearMonth.month}`
          }}
        </view>
      </view>
      <view class="arrow">
        <view class="p20" @click="changDate(-1)">
          <view class="arrow-left">
          </view>
        </view>
        <view class="p">{{ unfold ? '月' : '周' }}</view>
        <view class="p20" @click="changDate(1)">
          <view class="arrow-right">
          </view>
        </view>
      </view>
    </view>
    <slot name="header"></slot>
    <view class="tmt-content">
      <view class="tmt-week-wrap">
        <view class="cell week-item" v-for="(item, index) in week" :key="item.label">{{
          item.label }}
        </view>
      </view>
      <view class="tmt-day-wrap" :style="[{ height: unfold ? contentHeight : '88rpx' }]">
        <view class="day-content"
          :style="{ transform: unfold ? 'translateY(0rpx)' : `translateY(-${currentIndex * 88}rpx)` }">
          <view class="cell" v-for="index of blankDay" :key="index"></view>
          <view class="cell" v-for="(item, index) in daysArr" :key="item.id" :class="{ disable: isDisbale(item) }"
            @click="changeDay(item)">
            <view class="dayText"
              :class="{ today: item.today, selectDay: current.year == item.year && current.month == item.month && current.day == item.day }">
              {{ item.day }}
              <view class="point" v-if="item.point"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="showBtn" class="tmt-footer" @click="unfold = !unfold">
      {{ unfold ? '收起' : '展开' }} <u-icon :class="['arrow-down', { on: unfold }]" name="arrow-down" color="#000000"
        size="28"></u-icon>
    </view>
  </view>
</template>

<script setup>
import dayjs from 'dayjs';
import { watch } from 'vue';
import { ref, computed } from 'vue';
const props = defineProps({
  pointList: {
    type: Array,
    default() {
      return []
    }
  },
  defaultDate: {
    type: String,
    default() {
      return ''
    }
  },
  minDate: {
    type: String,
    default() {
      return ''
    }
  },
  maxDate: {
    type: String,
    default() {
      return ''
    }
  },
  showBtn: {
    type: Boolean,
    default() {
      return true
    }
  },
  background: {
    type: String,
    default() {
      return '#FFFFFF'
    }
  }
})
watch(() => props.pointList, init, {
  deep: true
})
function isDisbale(item) {
  let { year, month, day } = item
  let id = dayjs(`${year}-${month}-${day}`)
  if (props.minDate) {
    let min = dayjs(props.minDate);
    if (id.isBefore(min, 'day')) {
      return true;
    }
  }
  if (props.maxDate) {
    let max = dayjs(props.maxDate);
    if (id.isAfter(max, 'day')) {
      return true;
    }
  }
  return false
}
const emit = defineEmits(['changeDate'])
// 展开配置
const unfold = ref(false);
const contentHeight = computed(() => {
  let height = Math.ceil((blankDay.value + daysArr.value.length) / 7) * 88
  return height + 'rpx'
})
const currentIndex = computed(() => {
  let index = Math.ceil((blankDay.value + current.value.day) / 7);
  return index - 1;
})
const maxIndex = computed(() => {
  let lastDay = dayjs(`${currentYearMonth.value.year}-${currentYearMonth.value.month}-1`).endOf('month');
  let day = lastDay.date();
  let index = Math.ceil((blankDay.value + day) / 7);
  return index - 1;
})
// 基础配置
const week = [{
  label: '一',
  value: 1
},
{
  label: '二',
  value: 2
},
{
  label: '三',
  value: 3
},
{
  label: '四',
  value: 4
},
{
  label: '五',
  value: 5
},
{
  label: '六',
  value: 6
}, {
  label: '日',
  value: 0
}];
// 日期数据
const daysArr = ref([]);
const blankDay = ref(0); //空白天数
const now = dayjs();
const today = ref(now);
const current = ref();
if (props.defaultDate) {
  current.value = {
    year: defaultDate.year(),
    month: defaultDate.month(),
    day: defaultDate.date(),
  }
} else {
  current.value = {
    year: now.year(),
    month: now.month() + 1,
    day: now.date(),
  }
}
const currentYearMonth = ref({
  year: now.year(),
  month: now.month() + 1,
});
function changDate(num) {
  if (unfold.value) {
    // 切换月
    if (num == 1) {
      if (currentYearMonth.value.month < 12) {
        currentYearMonth.value.month++;
      } else {
        currentYearMonth.value.month = 1;
        currentYearMonth.value.year++;
      }
    } else {
      if (currentYearMonth.value.month > 1) {
        currentYearMonth.value.month--;
      } else {
        currentYearMonth.value.month = 12;
        currentYearMonth.value.year--;
      }
    }
    let lastDay = dayjs(`${currentYearMonth.value.year}-${currentYearMonth.value.month}-1`).endOf('month');
    let day = lastDay.date();
    current.value = {
      year: currentYearMonth.value.year,
      month: currentYearMonth.value.month,
      day: day < current.value.day ? day : current.value.day,
    }
    init()
  } else {
    if (num == 1) {
      if (currentIndex.value < maxIndex.value) {
        let lastDay = dayjs(`${currentYearMonth.value.year}-${currentYearMonth.value.month}-1`).endOf('month');
        let last = current.value.day + 7;
        if (last > lastDay.date()) {
          current.value = {
            year: currentYearMonth.value.year,
            month: currentYearMonth.value.month,
            day: lastDay.date(),
          }
        } else {
          current.value = {
            year: currentYearMonth.value.year,
            month: currentYearMonth.value.month,
            day: last,
          }
        }
      } else {
        if (currentYearMonth.value.month < 12) {
          currentYearMonth.value.month++;
        } else {
          currentYearMonth.value.month = 1;
          currentYearMonth.value.year++;
        }
        current.value = {
          year: currentYearMonth.value.year,
          month: currentYearMonth.value.month,
          day: 1,
        }
        init()
      }
    } else {
      if (currentIndex.value > 0) {
        current.value = {
          year: currentYearMonth.value.year,
          month: currentYearMonth.value.month,
          day: current.value.day - 7 > 0 ? current.value.day - 7 : 1,
        }
      } else {
        if (currentYearMonth.value.month > 1) {
          currentYearMonth.value.month--;
        } else {
          currentYearMonth.value.month = 12;
          currentYearMonth.value.year--;
        }
        let lastDay = dayjs(`${currentYearMonth.value.year}-${currentYearMonth.value.month}-1`).endOf('month');
        current.value = {
          year: currentYearMonth.value.year,
          month: currentYearMonth.value.month,
          day: lastDay.date(),
        }
        init()
      }
    }
  }
}
function changeDay(item) {
  current.value = item
};
watch(() => current.value, () => {
  let { year, month, day } = current.value
  emit('changeDate', { year, month, day })
}, {
  immediate: true
})
// 初始化
init();
function init() {
  let firstDayWeek = dayjs(`${currentYearMonth.value.year}-${currentYearMonth.value.month}-1`);
  blankDay.value = firstDayWeek.day() == 0 ? 6 : firstDayWeek.day() - 1;
  let lastDay = firstDayWeek.endOf('month');
  let days = [];
  for (let index = 1; index <= lastDay.date(); index++) {
    let el = dayjs(`${currentYearMonth.value.year}-${currentYearMonth.value.month}-${index}`);
    let findex = props.pointList.findIndex(item => {
      return el.isSame(dayjs(item.Ymd), 'day')
    })
    days.push({
      year: el.year(),
      month: el.month() + 1,
      day: el.date(),
      point: findex > -1,
      id: el.format("YYYYMMDD"),
      today: el.isSame(today.value, 'day')
    })
  }
  daysArr.value = days;
}

</script>

<style lang="scss">
.p20 {
  padding: 20upx;
}

.container {
  width: 750rpx;
  box-sizing: border-box;
  padding: 0 10rpx;
}

.tmt-header {
  display: flex;
  justify-content: space-between;
  padding: 20upx 0;

  .select-wrap {
    display: flex;
    align-items: center;
    padding-left: 30rpx;

    .time {
      color: #262937;
      font-size: 48upx;
      font-family: 'Rousseau-Deco';
    }
  }

  .arrow {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    width: 30%;
    align-items: center;
    margin: 0 30upx;

    .p {
      font-size: 32rpx;
      font-weight: 400;
      color: #787C8D;
    }

    .arrow-left {
      width: 14upx;
      height: 14upx;
      transform: rotate(45deg);
      border-left-width: 1upx;
      border-bottom-width: 1upx;
      border-left-style: solid;
      border-bottom-style: solid;
    }

    .arrow-right {
      width: 14upx;
      height: 14upx;
      transform: rotate(45deg);
      border-top-width: 1upx;
      border-right-width: 1upx;
      border-top-style: solid;
      border-right-style: solid;
    }
  }
}

.cell {
  width: 14.28%;
  height: 88upx;
  text-align: center;
  line-height: 88upx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-family: 'Rousseau-Deco';

  .point {
    width: 6upx;
    height: 6upx;
    position: absolute;
    bottom: 14upx;
    border-radius: 50%;
    left: 50%;
    background: #333333;
    transform: translateX(-50%);
  }

  .dayText {
    width: 80upx;
    height: 80upx;
    text-align: center;
    line-height: 86upx;
    border-radius: 50%;
  }

  .today {
    color: #617EFB;
    background: #F6F6F6;
  }

  .selectDay {
    background: #617EFB;
    color: #FFFFFF;
  }
}

.disable {
  opacity: 0.6;
  pointer-events: none;
}

.tmt-content {
  padding-bottom: 20upx;

  .tmt-week-wrap {
    display: flex;

    .week-item {
      font-size: 20rpx;
      font-weight: 400;
      color: #999999;
    }
  }

  .tmt-day-wrap {
    transition: height .3s;
    overflow: hidden;

    .day-content {
      transition: transform .3s;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }
  }
}

.tmt-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80upx;
  margin-top: -36upx;
  font-size: 24rpx;
  font-weight: 500;
  color: #787C8D;

  .arrow-down {
    transform: rotate(0deg);
    transition: all .3s;
    margin-left: 12rpx;

    &.on {
      transform: rotate(180deg);
    }
  }
}
</style>
