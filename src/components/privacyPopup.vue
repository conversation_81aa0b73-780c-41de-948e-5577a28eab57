<template>
  <u-popup v-model="popShow" :mask-close-able="false" height="500rpx" :border-radius="40" mode="center">
    <view>
      <view class="title">家长使用端隐私保护指引</view>
      <view class="cnent">
        在使用[家长使用端]服务之前，请仔细阅读<text class="xieyi" @click="openP">{{ privacyContractName }}</text>。如您同意{{
          privacyContractName
        }}，请点击“同意”开始使用[家长使用端]。
      </view>
      <view class="btns">
        <navigator target="miniProgram" class="exit" open-type="exit">
          拒绝
        </navigator>
        <button open-type="agreePrivacyAuthorization" class="btn" @agreeprivacyauthorization="handleAgree">同意</button>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { ref } from 'vue';
const popShow = ref(false)
const privacyContractName = ref("");
if (uni.getPrivacySetting) {
  uni.getPrivacySetting({
    success: res => {
      console.log("是否需要授权：", res.needAuthorization, "隐私协议的名称为：", res.privacyContractName)
      privacyContractName.value = res.privacyContractName;
      if (res.needAuthorization) {
        popShow.value = true;
      }
    },
    fail: () => { },
    complete: () => { },
  })
}
function handleAgree() {
  popShow.value = false;
}
function openP() {
  uni.openPrivacyContract({
    success: res => {
      console.log('openPrivacyContract success')
    },
    fail: res => {
      console.error('openPrivacyContract fail', res)
    }
  })
}
</script>
<style lang="scss" scoped>
.title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin: 40rpx 0;
}

.cnent {
  width: 690rpx;
  font-size: 28rpx;
  color: #444;
  box-sizing: border-box;
  padding: 0 40rpx;
  line-height: 50rpx;
}

.xieyi {
  color: #5F7CFC;
}

.btns {
  width: 500rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 40rpx auto;

  .exit {
    width: 160rpx;
    height: 60rpx;
    font-size: 24rpx;
    background: #eee;
    text-align: center;
    line-height: 60rpx;
    color: #000;
    box-sizing: border-box;
    border-radius: 12rpx;
  }

  .btn {
    width: 160rpx;
    height: 60rpx;
    font-size: 24rpx;
    background: #5F7CFC;
    text-align: center;
    line-height: 60rpx;
    color: #FFF;
    margin: 0;
  }
}
</style>