<template>
  <view class="pages safe-area-inset-bottom">
    <template v-if="list.length">
      <view class="item2" v-for="(item, index) in list" :key="item.id">
        <view class="top">
          <view class="row">
            <view class="age">
              {{ item.student.age }}
            </view>
            <view class="bmi">BMI：{{ getBMI(item) }}</view>
          </view>
          <view class="msg">{{ getMsg(index) }}</view>
          <view class="row">
            <view class="text">{{ item.created_at }}</view>
            <view class="text">检查人：{{ item.user.name }}</view>
          </view>
        </view>
        <view class="info">
          <view class="info-i">
            <view class="label">
              身高(cm)
            </view>
            <view class="value">
              {{ Number(item.height).toFixed(1) }}
            </view>
          </view>
          <view class="info-i">
            <view class="label">
              体重(kg)
            </view>
            <view class="value">
              {{ Number(item.weight).toFixed(1) }}
            </view>
          </view>
          <view class="info-i">
            <view class="label">
              视力
            </view>
            <view class="value">
              {{ item.left_vision ? item.left_vision : '-' }} | {{ item.right_vision ? item.right_vision : '-'
              }}
            </view>
          </view>
          <view class="info-i">
            <view class="label">
              头围(cm)
            </view>
            <view class="value">
              {{ Number(item.head_circum).toFixed(1) }}
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-else><u-empty text="暂无体检记录" mode="list"></u-empty></template>
  </view>
</template>
<script setup>
import { ref } from "vue";
import request from '@/request';
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();

onPullDownRefresh(async () => {
  await getData();
  uni.stopPullDownRefresh();
})
const list = ref([])
function getData() {
  return request({
    url: "/api/parent/examination/studentList",
    method: "get",
    data: {
      student_id: userInfo.student_id,
    }
  }).then(res => {
    list.value = res;
  })
}
onLoad(getData)
function getBMI(item) {
  return (Number(item.weight) / Math.pow(Number(item.height) / 100, 2)).toFixed(2)
}
function getMsg(index) {
  if (index == list.value.length - 1)
    return "这是宝宝的第一条体检记录~从这条开始见证宝宝的健康成长";
  let old = list.value[index + 1];
  let cur = list.value[index];
  let height = Number(cur.height) - Number(old.height);
  let weight = Number(cur.weight) - Number(old.weight);
  let head_circum = Number(cur.head_circum) - Number(old.head_circum);
  let msg1 = '';
  if (height > 0) {
    msg1 = '长高了' + height.toFixed(1) + 'cm';
  } else if (height < 0) {
    msg1 = '长矮了' + Math.abs(height).toFixed(1) + 'cm';
  } else {
    msg1 = '身高无变化';
  }
  let msg2 = '';
  if (weight > 0) {
    msg2 = '体重重了' + weight.toFixed(1) + 'kg';
  } else if (weight < 0) {
    msg2 = '体重轻了' + Math.abs(weight).toFixed(1) + 'kg';
  } else {
    msg2 = '体重无变化';
  }
  let msg3 = '';
  if (head_circum > 0) {
    msg3 = '头围增长了' + head_circum.toFixed(1) + 'cm';
  } else if (head_circum < 0) {
    msg3 = '头围减少了' + Math.abs(head_circum).toFixed(1) + 'cm';
  } else {
    msg3 = '头围与上次测量相同';
  }
  return `宝宝${msg1}，${msg2}，${msg3}`
}
</script>
<style scoped lang="scss">
.pages {
  background: #FAFAFA;
  padding-top: 40rpx;
  min-height: 100vh;
}

.item2 {
  width: 690rpx;
  height: 396rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin: 0 auto;
  margin-bottom: 40rpx;

  .top {
    width: 690rpx;
    height: 236rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .bmi {
        font-size: 28rpx;
        font-weight: 500;
        color: #617EFB;
      }

      .age {
        font-size: 32rpx;
        font-weight: 500;
        color: #262937;
      }

      .text {
        font-size: 24rpx;
        font-weight: 400;
        color: #AEB0BC;
      }
    }

    .msg {
      font-size: 28rpx;
      font-weight: 400;
      color: #787C8D;
    }



    .bmi {
      font-size: 28rpx;
      font-weight: 500;
      color: #252525;
    }
  }

  .info {
    background: #E6F7FF;
    display: flex;
    flex-direction: row;
    height: 160rpx;
    width: 100%;
    border-radius: 0 0 24rpx 24rpx;

    .info-i {
      text-align: center;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 20rpx 0;

      .label {
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.46);
      }

      .value {
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}
</style>