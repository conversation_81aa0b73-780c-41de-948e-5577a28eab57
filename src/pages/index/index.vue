<template>
  <view>
    <image class="main_img" src="https://obs.tuoyupt.com/miniprogram/client/images/aindex_bg.png?1" mode="widthFix"
      lazy-load="false" binderror="" bindload="" />
    <image class="into_img" src="https://obs.tuoyupt.com/miniprogram/client/images/into_poster.png" mode="widthFix"
      lazy-load="false" binderror="" bindload="" @click="intoPoster" />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onReady } from '@dcloudio/uni-app';

onReady(() => {
})

function intoPoster() {
  uni.navigateTo({
    url: '/pages_work/poster/index',
    success: (result) => {
    },
    fail: () => { },
    complete: () => { }
  });
}
</script>

<style>
.main_img {
  width: 100%;
}

.into_img {
  position: fixed;
  bottom: 64rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 214rpx;
  cursor: pointer;
}
</style>
