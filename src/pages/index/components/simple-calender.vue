<template>
  <view class="container" :style="{ background }">
    <view class="tmt-header">
      <view class="select-wrap">
        <view class="time">
          {{ `${year}年第${weeknum}周` }}
        </view>
      </view>
      <view class="arrow">
        <view class="p20" @click="changMonth(-1)">
          上一周
        </view>
        <view class="p20" @click="changMonth(1)">
          下一周
        </view>
      </view>
    </view>
    <view class="tmt-content">
      <view class="tmt-week-wrap">
        <view class="cell week-item" v-for="(item, index) in week" :key="item.label">{{
          item.label }}
        </view>
      </view>
      <view class="tmt-day-wrap" :style="[{ height: '88rpx' }, { color: dayColor }]">
        <view class="day-content">
          <view class="cell" v-for="(item, index) in daysArr" :key="item.id" @click="changeDay(item)">
            <view class="dayText"
              :class="{ weekend: item.isWeekend, today: item.today, selectDay: current.year == item.year && current.month == item.month && current.day == item.day }">
              {{ item.today ? '今' : item.day }}
            </view>
            <view class="point" v-if="item.have_food"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="todayn" v-if="!current.today" @click="backToday">回到今天</view>
  </view>
</template>

<script>
import dayjs from "dayjs";
let now = dayjs();
export default {
  name: "calendar",
  data() {
    return {
      week: [{
        label: '一',
        value: 1
      },
      {
        label: '二',
        value: 2
      },
      {
        label: '三',
        value: 3
      },
      {
        label: '四',
        value: 4
      },
      {
        label: '五',
        value: 5
      },
      {
        label: '六',
        value: 6
      }, {
        label: '日',
        value: 7
      }],
      blankDay: 0, //空白天数
      today: {}, //当天
      current: {}, //当前的年月
      list: [], //需要下标的日期
    };
  },
  computed: {
    daysArr() {
      return this.dayList.map(item => {
        let day = dayjs(item.Ymd);
        return {
          ...item,
          year: day.year(),
          month: day.month() + 1,
          day: item.day,
          isWeekend: day.day() == 0 || day.day() == 6,
          id: item.Ymd,
          today: now.isSame(dayjs(item.Ymd), 'day')
        }
      })
    },
    currentYearMonth() {
      if (this.dayList.length) {
        let day = dayjs(this.dayList[0].Ymd);
        return {
          year: day.year(),
          month: day.month() + 1,
        }
      } else {
        return {
          year: now.year(),
          month: now.month() + 1,
        }
      }
    }
  },
  props: {
    pointList: {
      type: Array,
      default() {
        return []
      }
    },
    defaultDate: {
      type: String,
      default() {
        return ''
      }
    },
    show: {
      type: Boolean,
      default() {
        return true
      }
    },
    background: {
      type: String,
      default() {
        return 'linear-gradient(180deg, #EAF6FF 0%, #F5FAFE 100%)'
      }
    },
    weekColor: {
      type: String,
      default() {
        return '#9C9D9D'
      }
    },
    dayList: {
      type: Array,
    },
    dayColor: {
      type: String,
      default() {
        return '#000000'
      }
    },
    selectBg: {
      type: String,
      default() {
        return 'linear-gradient(180deg, #FF855E 0%, #ED6337 100%)'
      }
    },
    backColor: {
      type: String,
      default() {
        return '#fff'
      }
    },
    backBg: {
      type: String,
      default() {
        return 'rgba(255, 255, 255, 0.19)'
      }
    },
    actionColor: {
      type: String,
      default() {
        return '#fff'
      }
    },
    year: String,
    weeknum: Number,
  },
  created() {
    this.list = this.pointList
    this.fomatePointTime()
    this.init()
  },
  methods: {
    changMonth(num) {
      this.$emit("changMonth", num);
    },
    backToday() {
      this.$emit("backToday");
    },
    // 获取前几个月,未来几个月的年份和月份
    getMonthYear(num) {
      let month = this.currentYearMonth.month
      let year = this.currentYearMonth.year
      let year2 = year
      let month2 = month + num
      if (month + num <= 0) {
        // 之前年份
        year2 = year - 1 - parseInt(Math.abs(month2) / 12)
        month2 = 12 - (Math.abs(month2) % 12)
      } else if ((month2 / 12) > 1) {
        // 之后年份
        year2 = year + (month2 % 12 == 0 ? 0 : parseInt(month2 / 12))
        month2 = parseInt(month2 % 12) == 0 ? 12 : parseInt(month2 % 12)
      }
      return {
        year: year2,
        month: month2
      }
    },
    changeDay(item) {
      this.current = item;
      this.$emit('changeDate', item)
    },
    changeDay1(item) {
      let day = dayjs(item.Ymd);
      let tmmp = {
        ...item,
        year: day.year(),
        month: day.month() + 1,
        day: item.day,
        isWeekend: day.day() == 0 || day.day() == 6,
        id: item.Ymd,
        today: now.isSame(dayjs(item.Ymd), 'day')
      }
      this.current = tmmp
    },
    // 获取某年某月的天数
    getDays(year, month) {
      let now = new Date(year, month, 0)
      return now.getDate()
    },
    //获取某一天为星期几
    getWeekByDay(time) {
      let day = new Date(Date.parse(time.replace(/-/g, '/'))); //将日期值格式化
      return day.getDay() == 0 ? 7 : day.getDay();
    },
    init() {
      let setTime = new Date();
      let year = setTime.getFullYear()
      let month = setTime.getMonth() + 1
      let day = setTime.getDate()
      this.current = {
        year,
        month,
        day,
      }
      this.today = {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate()
      }
      // this.makeCalendar(year, month)
    },
    fomatePointTime() {
      let pointList = this.list
      pointList = pointList.map(item => {
        item = item.replace(/-/g, '/') //期值格式化
        let timeArr = item.split('/')
        let timeStr = ''
        timeArr.map(time => {
          time = parseInt(time)
          timeStr += time
          return time
        })
        return timeStr
      })
      this.list = pointList
    },
    makeCalendar(year, month) {
      let today = this.today
      let days = this.getDays(year, month) //当月天数
      let firstDayWeek = this.getWeekByDay(`${year}-${month}-1`) //获取每个月第一天的星期
      let weekIndex = this.week.findIndex(item => {
        return item.value == firstDayWeek
      })
      let daysArr = []
      for (let i = 1; i <= days; i++) {
        let point = this.list.findIndex(item => {
          return item == String(year) + String(month) + String(i)
        }) != -1;
        daysArr.push({
          year,
          month,
          day: i,
          point,
          isWeekend: this.getWeekByDay(`${year}-${month}-${i}`) > 5,
          id: `${year}${month}${i}`,
          today: year == today.year && month == today.month && i == today.day
        })
      }
      this.currentYearMonth = {
        year,
        month
      }
      this.blankDay = weekIndex == -1 ? 0 : weekIndex
    },
  }
}
</script>

<style lang="scss">
.p20 {
  width: 104rpx;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #FF7373;
  line-height: 48rpx;
  text-align: center;
}

.container {
  width: 690rpx;
  margin: 0 auto;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  padding: 0 20upx 10rpx;
  position: relative;
}

.tmt-header {
  display: flex;
  justify-content: space-between;
  padding: 30upx 0 20rpx;

  .select-wrap {
    display: flex;
    align-items: center;

    .time {
      color: #787C8D;
      font-size: 40upx;
      margin: 0 20upx;
    }
  }

  .arrow {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    width: 35%;

  }
}

.cell {
  width: 14.28%;
  height: 88upx;
  text-align: center;
  line-height: 88upx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .point {
    width: 6upx;
    height: 6upx;
    position: absolute;
    bottom: 20upx;
    border-radius: 50%;
    left: 50%;
    transform: translateX(-50%);
    background: #000;
  }

  .dayText {
    width: 64rpx;
    height: 64rpx;
    text-align: center;
    line-height: 56upx;
    border-radius: 50%;
  }

  .selectDay {
    transform: rotateZ(360deg);
    border: 1rpx solid #000;
  }

  .today {
    color: #FF7373;
  }

  .weekend {
    color: #999999;
  }
}

.tmt-content {
  padding-bottom: 20upx;

  .tmt-week-wrap {
    display: flex;

    .week-item {
      font-size: 20rpx;
      font-weight: 400;
      color: #999999;
    }
  }

  .tmt-day-wrap {
    transition: height .3s;
    overflow: hidden;

    .day-content {
      transition: transform .3s;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }
  }
}

.todayn {
  width: 144rpx;
  height: 48rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.06);
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  line-height: 48rpx;
  text-align: center;
  position: absolute;
  left: 50%;
  bottom: -24rpx;
  transform: translateX(-50%);
}
</style>
