<template>
  <view class="cont" @click="palyVideo">
    <view class="back">
      <image height='596rpx' loading-icon="play-circle-fill" width='372rpx' :src="poster" class="backimg"
        mode="aspectFill"></image>
    </view>
    <u-image height='596rpx' loading-icon="play-circle-fill" width='372rpx' :src="poster" class="poster"
      mode="aspectFit"></u-image>
    <view class="duration">{{ durationT }}</view>
    <video @loadedmetadata="getLength" class="video" :src="playerUrl"></video>
    <u-popup v-model="palyShow" mode="center">
      <video class="video1" :src="playerUrl"></video>
    </u-popup>
  </view>
</template>
<script setup>
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import request from "@/request";
dayjs.extend(duration);
import { ref, onMounted } from "vue"
const props = defineProps({
  url: String
})
const playerUrl = ref('');
const poster = ref('');
function getDeatil(asset_id) {
  return request({
    url: "/api/getdetail",
    data: {
      asset_id,
      categories: "base_info,transcode_info&thumbnail_info,review_info"
    },
    method: "post"
  })
}
onMounted(() => {
  const regExp = /[\?&]asset_id=([^&#]+)/;
  const match = regExp.exec(props.url);
  const asset_id = match && decodeURIComponent(match[1]);
  if (asset_id) {
    getDeatil(asset_id)
      .then(res => {
        if (res.transcode_info.transcode_status == 'TRANSCODE_SUCCEED') {
          playerUrl.value = res.transcode_info.output[0].url;
          poster.value = res.base_info.cover_info_array[0].cover_url;
        } else {
          playerUrl.value = res.base_info.video_url;
        }
      })
  } else {
    playerUrl.value = props.url;
    poster.value = props.url + '?vframe/jpeg/offset/1'
  }
})
const durationT = ref('')
function getLength(e) {
  durationT.value = dayjs.duration(e.detail.duration, 's').format('HH:mm:ss')
}
const palyShow = ref(false)
function palyVideo() {
  palyShow.value = true;
}
</script>
<style lang="scss" scoped>
.cont {
  width: 372rpx;
  height: 596rpx;
  position: relative;
  margin-bottom: 12rpx;

  .back {
    position: absolute;
    width: 372rpx;
    height: 596rpx;
    left: 0;
    top: 0;
    z-index: 1;

    .backimg {
      filter: blur(8rpx);
      width: 409rpx;
      height: 655rpx;
      left: -18.5rpx;
      top: -29.5rpx;
      position: absolute;
    }

    overflow: hidden;
  }

  .poster {
    position: absolute;
    width: 372rpx;
    height: 596rpx;
    left: 0;
    top: 0;
    z-index: 2;
  }

  .video {
    display: none;
  }

  .duration {
    position: absolute;
    font-size: 28rpx;
    font-weight: 500;
    color: #FFFFFF;
    left: 20rpx;
    bottom: 20rpx;
    z-index: 3;
  }
}

.video1 {
  width: 750rpx;
  width: 750rpx;
}
</style>