<template>
  <view class="pages safe-area-inset-bottom">
    <u-navbar :title="title" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="search-box"><u-search placeholder="请输入" v-model="keyword" @custom="search"></u-search></view>
    <u-tabs :list="tabs" :is-scroll="true" :current="current" @change="change"></u-tabs>
    <scroll-view @scroll="start" enhanced :show-scrollbar="showBar" :refresher-triggered="triggered"
      :refresher-enabled="true" @refresherrefresh="refresh" class="scroll" :scroll-y="true" @scrolltolower="loadMore">
      <view class="item" v-for="item in list" :key="item.id">
        <view class="left">
          <text class="title u-line-2" @click="goDetail(item)">{{ item.title }}</text>
          <!-- <text class="des u-line-1" v-if="item.content"></text> -->
          <view style="display: flex; align-items: center; justify-content: space-between;">
            <text class="time u-line-1">{{ item.operate_time }}</text>
            <u-icon v-show="userInfo.id" name="star" v-if="item.is_collect == 0" size="28"
              @click="collect(item)"></u-icon>
            <u-icon v-show="userInfo.id" name="star-fill" color="#fa3534" v-if="item.is_collect == 1" size="28"
              @click="cancelCollect(item)"></u-icon>
          </view>
        </view>
        <img :src="item.cover" v-if="item.cover" alt="">
      </view>
      <u-empty v-if="list.length == 0"></u-empty>
    </scroll-view>
  </view>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();

import { ref, nextTick } from "vue"
const showBar = ref(false);
function start() {
  if (showBar.value == false) {
    showBar.value = true;
  }
}
const title = ref('育儿知识和政策信息')
let page = ref(1);
let limit = 10;
let hasMore = true;
onLoad(option => {
  nextTick(() => {
    current.value = option.type;
    getList();
  })
})
function goDetail(item) {
  uni.navigateTo({
    url: `/pages/index/newsdetail?type=${type.value}&id=${item.id}`
  })
}

function search() {
  page.value = 1;
  getList();
}
let keyword = ref('');
function getList() {
  uni.showLoading()
  request({
    url: tabs[current.value].id == 0 ? `/api/parent/article/collectList` : `/api/parent/article/know`,
    method: 'post',
    data: {
      type: tabs[current.value].id,
      per_page: limit,
      page: page.value,
      key: keyword.value,
      user_id: userInfo.id,
      school_id: userInfo.school_id
    }
  }).then(res => {
    uni.hideLoading();

    if (res.data.length < limit) {
      hasMore = false;
    } else {
      hasMore = true;
    }

    if (tabs[current.value].id == 0) {
      for (const element of res.data) {
        element.is_collect = 1;
      }
    }

    list.value = res.data;
  })
}

function cancelCollect(item) {
  request({
    url: `/api/parent/article/cancelCollect`,
    method: 'post',
    data: {
      policy_id: item.id,
      user_id: userInfo.id
    }
  }).then(res => {
    item.is_collect = item.is_collect === 0 ? 1 : 0;
    uni.showToast({
      title: '取消收藏'
    })
  })
}

function collect(item) {
  request({
    url: `/api/parent/article/collect`,
    method: 'post',
    data: {
      policy_id: item.id,
      user_id: userInfo.id
    }
  }).then(res => {
    item.is_collect = item.is_collect === 0 ? 1 : 0;
    uni.showToast({
      title: '收藏成功'
    })
  })
}
function loadMore() {
  if (hasMore) {
    uni.showLoading();
    page.value++;
    request({
      url: `/api/parent/article/know`,
      method: 'post',
      data: {
        type: tabs[current.value].id,
        per_page: limit,
        page: page.value,
        key: keyword.value,
        user_id: userInfo.id,
        school_id: userInfo.school_id
      }
    }).then(res => {
      if (tabs[current.value].id == 0) {
        for (const element of res.data) {
          element.is_collect = 1;
        }
      }
      list.value.push(...res.data);
      if (res.data.length < limit) {
        hasMore = false;
      } else {
        hasMore = true;
      }
    })
    uni.hideLoading();
  }
}
const triggered = ref(false);
const refresh = async () => {
  triggered.value = true;
  page.value = 1
  await getList()
  triggered.value = false;
}


let current = ref(0)
let type = ref(1)


let list = ref([]);
function change(index) {
  current.value = index;
  page.value = 1;
  nextTick(() => {
    getList();
  })
}
let tabs = userInfo.id ? [{
  name: '政策文件',
  id: 1,
}, {
  name: '行业新闻',
  id: '7,8',
}, {
  name: '育儿知识',
  id: 9,
}, {
  name: '我的收藏',
  id: 0,
}] : [{
  name: '政策文件',
  id: 1,
}, {
  name: '行业新闻',
  id: 6,
}, {
  name: '专业指导',
  id: 7,
}, {
  name: '卫生保健',
  id: 8,
}, {
  name: '育儿知识',
  id: 9,
}]
</script>
<style lang="scss" scoped>
.pages {
  background: #F6F6F6;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .scroll {
    height: calc(100vh - 44px - 2.5rem - 0.1875rem - 3rem);
    background: #FFFFFF;
    margin: 0 auto;

    .item {
      border-bottom: 1rpx solid #F7F7F7;
      font-size: 32rpx;
      font-weight: 600;
      color: #262937;
      line-height: 44rpx;
      padding: 30rpx;
      box-sizing: border-box;

      .title {
        font-size: 28rpx;
        display: block;
        margin-bottom: 20rpx;
      }

      .des {
        color: #666;
        font-size: 28rpx;
        display: block;
      }

      .time {
        color: #d8d8d8;
        font-size: 24rpx;
        display: block;
      }
    }
  }

  .search-box {
    background: #fff;
    padding: 16rpx;
  }
}
</style>