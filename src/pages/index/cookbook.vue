<template>
  <view style="padding-top: 40rpx;">
    <calendar ref="calendarRef" @backToday="backToday" :year="year" :weeknum="weeknum" :show="calendarShow"
      :dayList="dayArr" @changMonth="changMonth" @changeDate="changeDate">
    </calendar>
    <scroll-view scroll-x enable-flex enhanced :show-scrollbar="false" class="imgs" v-if="recipes_imgs.length">
      <image mode="aspectFill" v-for="(item, index) in recipes_imgs" @click="showImg(index)" :key="item" class="img"
        :src="item"></image>
    </scroll-view>
    <template v-if="foods.length">
      <view class="foods">
        <view class="food-item" :class="{ active: index < foods.length - 1 }" v-for="(item, index) in foods"
          :key="item.id">
          <view class="left">
            <image class="img" :src="getIcon(item.times_type)"></image>
          </view>
          <view class="content">
            <view class="title">{{ getName(item.times_type) }}</view>
            <view class="names">
              <view class="name" v-for="name in item.food_name" :key="name">
                {{ name }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    <u-empty v-else marginTop="80" text="暂未设置食谱" mode="list"></u-empty>
  </view>
</template>
<script setup>
import { ref } from "vue";
import request from '@/request';
import calendar from "./components/simple-calender.vue"
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
// 日期
const calendarRef = ref(null);
const now = dayjs();
let currentDate = ref({});
const calendarShow = ref(false);
const currentWeek = ref(0);
const dayArr = ref([])
function changMonth(num) {
  currentWeek.value += num;
  getData();
}
const showImg = (index) => {
  uni.previewImage({
    current: index,
    urls: recipes_imgs.value,
    indicator: "number",
    longPressActions: {
      itemList: ['发送给朋友', '保存图片', '收藏'],
    }
  });
}
const year = ref(now.year());
const weeknum = ref(0);
const recipes = ref({})
const foods = ref([]);
const recipes_imgs = ref([]);
onPullDownRefresh(() => {
  request({
    url: "/api/parent/schoolrecipes/form",
    method: "post",
    data: {
      student_id: userInfo.student_id,
      weekdis_num: currentWeek.value
    }
  }).then(res => {
    dayArr.value = res.weekdate;
    weeknum.value = res.weeknum;
    year.value = res.year;
    recipes.value = res.recipes;
    foods.value = recipes.value[currentDate.value.Ymd].foods;
    recipes_imgs.value = recipes.value[currentDate.value.Ymd].recipes_imgs;
    uni.stopPullDownRefresh();
  })
})
function getData() {
  return request({
    url: "/api/parent/schoolrecipes/form",
    method: "post",
    data: {
      student_id: userInfo.student_id,
      weekdis_num: currentWeek.value
    }
  }).then(res => {
    dayArr.value = res.weekdate;
    weeknum.value = res.weekdate[0].weeknum
    if (currentWeek.value == 0) {
      let index = dayArr.value.findIndex(item => {
        return now.isSame(dayjs(item.Ymd), 'day')
      })
      currentDate.value = dayArr.value[index];
      calendarRef.value.changeDay1(currentDate.value)
    } else {
      currentDate.value = dayArr.value[0];
      calendarRef.value.changeDay1(currentDate.value)
    }
    recipes.value = res.recipes;
    foods.value = recipes.value[currentDate.value.Ymd].foods;
    recipes_imgs.value = recipes.value[currentDate.value.Ymd].recipes_imgs;
  })
}
function backToday() {
  currentWeek.value = 0;
  getData();
}
function changeDate(day) {
  currentDate.value = day;
  foods.value = recipes.value[currentDate.value.Ymd].foods;
  recipes_imgs.value = recipes.value[currentDate.value.Ymd].recipes_imgs;
}
onLoad(() => {
  getData();
})
function getName(type) {
  let str = "";
  switch (type) {
    case 1:
      str = "早餐";
      break;
    case 2:
      str = "早加餐";
      break;
    case 3:
      str = "午餐";
      break;
    case 4:
      str = "午加餐";
      break;
    case 5:
      str = "晚餐";
      break;
  }
  return str;
}
function getIcon(type) {
  let src = "";
  switch (type) {
    case 1:
      src = "https://obs.tuoyupt.com/miniprogram/enrollment_management/i.png";
      break;
    case 2:
      src = "https://obs.tuoyupt.com/miniprogram/enrollment_management/i4.png";
      break;
    case 3:
      src = "https://obs.tuoyupt.com/miniprogram/enrollment_management/i2.png";
      break;
    case 4:
      src = "https://obs.tuoyupt.com/miniprogram/enrollment_management/i5.png";
      break;
    case 5:
      src = "https://obs.tuoyupt.com/miniprogram/enrollment_management/i3.png";
      break;
  }
  return src;
}
</script>
<style scoped lang="scss">
.select_class {
  padding: 0 30rpx;
  margin-top: 20rpx;
  margin-bottom: 40rpx;

  .change_class_container {
    display: flex;
    flex-direction: row;
    align-items: center;

    .currentClassName {
      font-size: 48rpx;
      font-weight: 500;
      color: #111111;
      line-height: 48rpx;
    }

    .change {
      width: 104rpx;
      height: 48rpx;
      background: #F6F6F6;
      border-radius: 32rpx;
      text-align: center;
      font-size: 28rpx;
      font-weight: 400;
      color: #262937;
      line-height: 48rpx;
      margin-left: 16rpx;
    }
  }

  .title {
    font-size: 28rpx;
    font-weight: 400;
    color: #787C8D;
    line-height: 28rpx;
    margin-top: 24rpx;
  }
}

.imgs {
  width: 690rpx;
  display: flex;
  flex-direction: row;
  margin: 40rpx auto;
  justify-content: flex-start;
  height: 210rpx;

  .img {
    width: 210rpx;
    height: 210rpx;
    margin-right: 30rpx;
    flex-shrink: 0;
  }
}

.foods {
  width: 690rpx;
  margin: 40rpx auto 0;
  background: #FFFFFF;

  .active {
    &::after {
      position: absolute;
      content: " ";
      z-index: 1;
      width: 1rpx;
      height: 100%;
      top: 0;
      left: 60rpx;
      background: #EEEEEE;
    }
  }

  .food-item {
    display: flex;
    flex-direction: row;
    padding-bottom: 40rpx;
    position: relative;


    .left {
      position: relative;
      width: 120rpx;
      text-align: center;



      .img {
        position: relative;
        z-index: 2;
        width: 48rpx;
        height: 48rpx;
      }
    }

    .content {
      flex: 1;

      .title {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
      }

      .names {
        margin-top: 20rpx;
        display: flex;
        width: 100%;
        flex-wrap: wrap;

        .name {
          font-size: 28rpx;
          font-weight: 600;
          color: #262937;
          margin-right: 12rpx;
        }
      }
    }
  }
}
</style>