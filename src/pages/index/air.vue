<template>
  <u-navbar title="空气监测" :background="{ background: 'rgba(255, 255,255, 0)' }" :immersive="true"
    :border-bottom="false"></u-navbar>
  <view class="image">
    <image class="i" src="https://obs.tuoyupt.com/miniprogram/air/air.webp"></image>
    <view class="btn" @click="openPdf"></view>
  </view>
</template>
<script setup>
function openPdf() {
  uni.showLoading({
    mask: true
  })
  uni.downloadFile({
    url: 'https://obs.tuoyupt.com/miniprogram/air/%E9%A1%BA%E4%B9%89%E6%A3%80%E6%B5%8B%E6%8A%A5%E5%91%8A-%E8%BD%BB%E5%BE%AE.pdf',
    success: function (res) {
      var filePath = res.tempFilePath;
      uni.openDocument({
        filePath: filePath,
        showMenu: true,
        success(e) {
          console.log(e)
        },
        fail(e) {
          console.log(e)
        },
        complete() {
          uni.hideLoading()
        }
      });
    },
    fail(e) {
      uni.hideLoading()
      console.log(e)
    }
  });
}
</script>
<style lang="scss" scoped>
.image {
  width: 750rpx;
  height: 1624rpx;
  position: relative;
  font-size: 0;
  display: block;

  .btn {
    position: absolute;
    width: 180rpx;
    height: 70rpx;
    left: 300rpx;
    top: 1460rpx;
  }

  .i {
    width: 750rpx;
    height: 1624rpx;
    display: block;
  }
}
</style>