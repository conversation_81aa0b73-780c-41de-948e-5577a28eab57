<template>
    <view class="pages safe-area-inset-bottom">
        <u-navbar :title="title" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
        <u-tabs active-color="#FF7373" :list="tabList" :is-scroll="false" v-model="current" @change="change"></u-tabs>
        <view class="section">
            <scroll-view :refresher-triggered="triggered" class="scroll" :scroll-y="true" @scrolltolower="loadMore">
                <view class="item" v-for="item in list" :key="item.id" @click="goDetail(item)">
                    {{ item.title }}
                </view>
            </scroll-view>
        </view>
    </view>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import request from '@/request';
const title = ref('服务指南')
let type = 1;
let page = 1;
let limit = 20;
let hasMore = true;
const list = ref([]);
const current=ref(0);
const tabList = ref([{
    name: '全部',
    id:1,
}, {
    name: '公示公告',
    id:2,
}, {
    name: '重要活动',
    id:3,
}, {
    name: '新闻动态',
    id:4,
}])
function change(t){
    type=t+1;
    getList();
}
onLoad(() => {
    getList()
})
function goDetail(item) {
    uni.navigateTo({
        url: `./newsdetail?type=${2}&id=${item.id}`
    })
}
function getList() {
    uni.showLoading();
    let t=type==1?"2,3,4":type;
    return request({
        url: `/api/getNewsRecur?type=${t}&limit=${limit}&page=${page}`
    }).then(res => {
        uni.hideLoading();
        list.value = res;
        if (res.length < limit) {
            hasMore = false;
        } else {
            hasMore = true;
        }
    })
}
function loadMore() {
    if (hasMore) {
        page++;
        request({
            url: `/api/getNewsRecur?type=${type == 1 ? 'xwdt' : 'zcfg'}&limit=${limit}&page=${page}`
        }).then(res => {
            list.value.push(...res);
            if (res.length < limit) {
                hasMore = false;
            } else {
                hasMore = true;
            }
        })
    }
}
</script>
<style lang="scss" scoped>
.pages {
    background: #FFFFFF;
    height: 100vh;
    display: flex;
    flex-direction: column;

    .section {
        flex: 1;
        min-height: 1rpx;
    }

    .scroll {
        height: 100%;
        width: 100%;
        background: #FFFFFF;
        margin: 0 auto;

        .item {
            width: 690rpx;
            border-bottom: 1rpx solid #F7F7F7;
            font-size: 32rpx;
            font-weight: 600;
            color: #262937;
            line-height: 44rpx;
            padding: 30rpx;
            box-sizing: border-box;
        }
    }
}
</style>