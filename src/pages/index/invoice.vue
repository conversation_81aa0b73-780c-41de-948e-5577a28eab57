<template>
	<view class="pages safe-area-inset-bottom">
		<template v-if="list.length">
			<view class="group">
				<u-checkbox-group wrap>
					<u-checkbox v-model="item.checked" v-for="(item, index) in list" :key="item.id" :name="item.id">
						<view class="item">
							<view class="left">
								<view class="info">
									<text>{{ item.created_at }}</text>
									<text>{{ item.price }}元</text>
								</view>
								<view>
									{{ item.remark }}
								</view>
							</view>
							<u-button class="custom-style" type="error" size="mini" @click="openPdf(item)">查看</u-button>
						</view>
					</u-checkbox>
				</u-checkbox-group>
			</view>
		</template>
		<template v-else><u-empty text="暂无发票" mode="list"></u-empty></template>
		<view class="footer-zhanwei"></view>
		<view class="footer">
			<view class="footer-content">
				<u-checkbox v-model="checkAll" @change="changeAll" name="all">全选</u-checkbox>
				<view>合计：{{ totalPrice }}元</view>
				<u-button class="custom-style" size="medium" type="error" @click="downALL">下载</u-button>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import request from '@/request';
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import { useUserStore } from '@/stores/userInfo.js';
const userInfo = useUserStore();

onPullDownRefresh(async () => {
	page = 1;
	await getData();
	uni.stopPullDownRefresh();
});
const totalPrice = computed(() => {
	return list.value.reduce((a, b) => {
		if (b.checked) {
			a += Number(b.price)
		}
		return a
	}, 0)
});

const checkAll = ref(false);
function changeAll(e) {
	if (e.value) {
		list.value.forEach(item => {
			item.checked = true
		})
	} else {
		list.value.forEach(item => {
			item.checked = false
		})
	}
}
function downALL() {
	let lists = list.value.filter(item => item.checked);
	if (lists.length == 0) {
		uni.showToast({
			title: "请选择发票",
			icon: "none"
		})
		return
	}
	lists.forEach((item) => {
		request({
			url: "/api/parent/invoicemanage/detail",
			data: {
				id: item.id
			}
		}).then(res => {
			window.open(res.img_url)
		})
	})
}
function openPdf(item) {
	uni.showLoading()
	request({
		url: "/api/parent/invoicemanage/detail",
		data: {
			id: item.id
		}
	}).then(res => {
		uni.hideLoading();
		if (res) {
			uni.navigateTo({
				url: `/pages/index/pdf?url=${encodeURIComponent(res.img_url)}`,
			});
		}
	})
}
const list = ref([]);
watch(list, (val) => {
	let checkNum = val.filter(item => item.checked).length;
	if (checkNum == val.length) {
		checkAll.value = true;
	} else {
		checkAll.value = false;
	}
}, {
	deep: true
})
let hasMore = true;
let page = 1;
function getData() {
	return request({
		url: '/api/parent/invoicemanage/list',
		method: 'get',
		data: {
			student_id: userInfo.student_id,
			page: 1,
			per_page: 100,
		}
	}).then((res) => {
		list.value = res;
		if (res.length == 10) {
			hasMore = true;
		} else {
			hasMore = false;
		}
	});
}
// function getMore() {
// 	if (hasMore) {
// 		page++;
// 		request({
// 			url: '/api/parent/invoicemanage/list',
// 			method: 'get',
// 			data: {
// 				student_id: userInfo.student_id
// 			}
// 		}).then((res) => {
// 			list.value.push(...res);
// 			if (res.length == 10) {
// 				hasMore = true;
// 			} else {
// 				hasMore = false;
// 			}
// 		});
// 	}

// }
onLoad(getData);
// onReachBottom(getMore);
</script>
<style scoped lang="scss">
.custom-style {
	background: #FF7373;
	margin: 0;
}

.pages {
	background: #fafafa;
	padding-top: 40rpx;
	min-height: 100vh;
}

.group {
	padding: 0 30rpx;

	.item {
		background: #FFFFFF;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 600rpx;
		margin-left: 30rpx;
		margin-bottom: 30rpx;
		align-items: center;
		padding: 10rpx 30rpx;
		border: 1rpx solid #999;
		border-radius: 12rpx;

		.left {
			display: flex;
			flex-direction: column;
			width: 70%;

			.info {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
			}
		}
	}
}

.footer-zhanwei {
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.dis {
	pointer-events: none;
}

.footer {
	background: #FFFFFF;
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
	display: block;
	position: fixed;
	z-index: 3;
	left: 0;
	bottom: 0;

	.footer-content {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		background: #FFFFFF;
		box-sizing: border-box;
		padding: 0 30rpx;
		justify-content: space-between;
	}
}

.item2 {
	width: 690rpx;
	height: 396rpx;
	border-radius: 24rpx;
	overflow: hidden;
	margin: 0 auto;
	margin-bottom: 40rpx;

	.top {
		width: 690rpx;
		height: 236rpx;
		background: #ffffff;
		box-sizing: border-box;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.row {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;

			.bmi {
				font-size: 28rpx;
				font-weight: 500;
				color: #617efb;
			}

			.age {
				font-size: 32rpx;
				font-weight: 500;
				color: #262937;
			}

			.text {
				font-size: 24rpx;
				font-weight: 400;
				color: #aeb0bc;
			}
		}

		.msg {
			font-size: 28rpx;
			font-weight: 400;
			color: #787c8d;
		}

		.bmi {
			font-size: 28rpx;
			font-weight: 500;
			color: #252525;
		}
	}

	.info {
		background: #e6f7ff;
		display: flex;
		flex-direction: row;
		height: 160rpx;
		width: 100%;
		border-radius: 0 0 24rpx 24rpx;

		.info-i {
			text-align: center;
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			padding: 20rpx 0;

			.label {
				font-size: 28rpx;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.46);
			}

			.value {
				font-size: 32rpx;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.85);
			}
		}
	}
}
</style>
