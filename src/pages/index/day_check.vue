<template>
  <u-navbar title="晨午晚检记录" ref="navbar" :background="{ background: 'raba(255,255,255,0)' }"
    :border-bottom="false"></u-navbar>
  <calendar @changeDate="changeDate" :pointList="pointList" />
  <template v-if="info">
    <view class="item" @click="showDetail(checkInfo.morning)">
      <view class="left">
        <view class="name">晨检信息</view>
        <view class="time" v-if="checkInfo.morning.status == 1">
          <text>正常项：{{ checkInfo.morning.normal }}</text>
          <text class="text">异常项：{{ checkInfo.morning.abnormal }}</text>
        </view>
        <view class="time" v-else>暂无记录</view>
      </view>
      <image class="icon" src="https://obs.tuoyupt.com/miniprogram/client/images/goh.png">
      </image>
    </view>
    <view class="item" @click="showDetail(checkInfo.noon)">
      <view class="left">
        <view class="name">午检信息</view>
        <view class="time" v-if="checkInfo.noon.status == 1">
          <text>正常项：{{ checkInfo.noon.normal }}</text>
          <text class="text">异常项：{{ checkInfo.noon.abnormal }}</text>
        </view>
        <view class="time" v-else>暂无记录</view>
      </view>
      <image class="icon" src="https://obs.tuoyupt.com/miniprogram/client/images/goh.png">
      </image>
    </view>
    <view class="item" @click="showDetail(checkInfo.night)">
      <view class="left">
        <view class="name">晚检信息</view>
        <view class="time" v-if="checkInfo.night.status == 1">
          <text>正常项：{{ checkInfo.night.normal }}</text>
          <text class="text">异常项：{{ checkInfo.night.abnormal }}</text>
        </view>
        <view class="time" v-else>暂无记录</view>
      </view>
      <image class="icon" src="https://obs.tuoyupt.com/miniprogram/client/images/goh.png">
      </image>
    </view>
  </template>
  <template v-else><u-empty text="暂无检查信息" mode="list"></u-empty></template>
  <u-popup v-model="show" border-radius="24" mode="bottom" safe-area-inset-bottom>
    <view class="popcontent">
      <view class="top">
        <view class="section">
          <view class="name">{{ info.name }}</view>
          <view class="tmp">体温：<text :style="{ color: detailInfo.list[0].status == 2 ? 'red' : '' }"> {{
            detailInfo.list[0].system_name }}{{ detailInfo.list[0].status == 2 ? '°C' : '' }} </text></view>
        </view>
        <view class="tags">
          <view class="tag" v-for="(item, index) in detailInfo.list[1].system_name.split(',')" :key="index">
            {{ item }}
          </view>
        </view>
      </view>
      <view class="infos">
        <view class="row" v-for="item in detailInfo.list.slice(2)" :key="item.id">
          <view class="label">
            {{ item.name }}
          </view>
          <view class="value">
            {{ item.system_name }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            备注
          </view>
          <view class="value">
            {{ detailInfo.base?.mark || '无' }}
          </view>
        </view>
        <view class="imgs">
          <image class="img" @click="showImgs(index)" v-for="(item, index) in detailInfo.base.cover" :key="item"
            :src="item">
          </image>
        </view>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import request from '@/request';
import calendar from "@/components/calendar_n.vue";
import dayjs from "dayjs";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const currentTime = ref("")
let Ym = "";
function changeDate(date) {
  let day = dayjs(`${date.year}-${date.month}-${date.day}`)
  currentTime.value = day.format("YYYY-MM-DD");
  Ym = day.format("YYYY-MM");
  getData();
  getMonth();
}
onLoad(() => {
  getData();
  getMonth();
})
const checkInfo = ref({
  "morning": {
    "normal": 8,
    "abnormal": 0
  },
  "noon": {
    "normal": 4,
    "abnormal": 0
  },
  "night": {
    "normal": 5,
    "abnormal": 0
  }
})
const info = ref({
  img_url: "",
  name: "",
  student_class: {
    name: ""
  }
})
const pointList = ref([]);
function getMonth() {
  if (info.value.id) {
    request({
      method: "get",
      url: "/api/checkup/date/children",
      data: {
        student_id: userInfo.student_id,
        class_id: userInfo.class_id,
        school_id: userInfo.school_id,
        Ym
      },
    }).then(res => {
      pointList.value = res.filter(item => item.is_have == 1).map(item => {
        return {
          Ymd: item.time,
        }
      })
    })
  }
}
function getData() {
  request({
    method: "get",
    url: "/api/checkup/student/detail",
    data: {
      student_id: userInfo.student_id,
      class_id: userInfo.class_id,
      school_id: userInfo.school_id,
      time: currentTime.value
    },
  }).then(res => {
    checkInfo.value = res;
  })
}

const show = ref(false);
const detailInfo = ref({
  base: {
    mark: ""
  },
  list: [
    {
      system_name: "正常"
    }, {
      system_name: "正常"
    }
  ]
})
function showDetail(item) {
  if (item.status == 0)
    return
  uni.showLoading();
  request({
    url: "/api/checkup/children/detail",
    data: {
      category_id: item.category_id,
      no_id: item.no_id
    }
  }).then(res => {
    show.value = true;
    detailInfo.value = res;
    uni.hideLoading()
  })
}
function showImgs(index) {
  uni.previewImage({
    current: index,
    urls: detailInfo.value.base.cover
  })
}
</script>
<style scoped lang="scss">
.class {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
  padding: 0 30rpx;
  margin-bottom: 40rpx;

  .t1 {
    font-size: 32rpx;
    font-weight: 500;
    color: #787C8D;
  }

  .t2 {
    padding: 0 20rpx;
    height: 48rpx;
    background: #F6F6F6;
    border-radius: 8rpx;
    font-size: 24rpx;
    font-weight: 400;
    color: #787C8D;
    line-height: 48rpx;
    text-align: center;
  }
}

.item {
  width: 690rpx;
  height: 128rpx;
  background: #FFFFFF;
  border-bottom: 1rpx solid #EEE;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  margin: 0rpx auto;

  .name {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
  }

  .time {
    font-size: 24rpx;
    font-weight: 400;
    color: #AEB0BC;
    margin-top: 8rpx;

    .text {
      margin-left: 20rpx;
    }
  }

  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.popcontent {
  width: 750rpx;
  height: 1000rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  overflow: hidden;

  .top {
    width: 750rpx;
    height: 160rpx;
    border-bottom: 1rpx solid #eeeeee;
    background: linear-gradient(180deg, #F2F4FF 0%, #FFFFFF 100%);
    box-sizing: border-box;
    padding: 32rpx 30rpx 0;

    .section {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 36rpx;
      font-family: RousseauDeco;
      color: #262937;
    }

    .tags {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 12rpx;

      .tag {
        padding: 0 16rpx;
        font-size: 24rpx;
        font-weight: 500;
        color: #5F7CFC;
        line-height: 48rpx;
        height: 48rpx;
        background: #F2F4FF;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }
    }
  }

  .infos {
    width: 750rpx;
    box-sizing: border-box;
    padding: 0 30rpx;

    .imgs {
      margin-top: 40rpx;

      .img {
        display: inline-block;
        width: 128rpx;
        height: 128rpx;
        border-radius: 8rpx;
        margin-right: 24rpx;
      }
    }

    .row {
      display: flex;
      flex-direction: row;
      margin-top: 24rpx;

      .label {
        width: 152rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #787C8D;
      }

      .value {
        font-size: 28rpx;
        font-weight: 400;
        color: #262937;
        line-height: 44rpx;
      }
    }
  }
}
</style>