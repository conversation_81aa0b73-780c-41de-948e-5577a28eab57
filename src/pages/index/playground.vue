<template>
  <view class="pages">
    <image class="back" src="https://obs.tuoyupt.com/miniprogram/parentindex/lbackimg.webp">
    </image>
    <view class="backs" @click="backP">
      <image class="backbtn" src="https://obs.tuoyupt.com/miniprogram/parentindex/lback.webp">
      </image>
    </view>
    <view class="title">{{ game.name }}</view>
    <scroll-view class="scroll" scroll-x>
      <view class="zhanwei"></view>
      <view class="warp" v-for="item in game.info" :key="item.id">
        <view class="item" @click="goGame(item)">
          <image class="cover" mode="aspectFill" :src="item.cover_url"></image>
          <view class="name">{{ item.name }}</view>
          <view class="btn">开始</view>
          <image class="shadw" src="https://obs.tuoyupt.com/miniprogram/parentindex/lbi.webp">
          </image>
        </view>
      </view>
      <view class="zhanwei"></view>
    </scroll-view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from '@dcloudio/uni-app';
let id = '';
let isLand = false;
onLoad(option => {
  id = option.id;
  getList();
  isLand = isLandscape();
  if (!isLand) {
    uni.showModal({
      title: "提示",
      content: "请切换到横屏显示",
      showCancel: false
    })
  }
})
function isLandscape() {
  return window.matchMedia("(orientation: landscape)").matches;
}
const game = ref({
  name: "",
  info: []
})
function goGame(item) {
  uni.navigateTo({
    url: `/pages/webview/lindex?url=${encodeURIComponent(item.game_src)}`
  })
}
function getList() {
  uni.showLoading({
    mask: true
  })
  uni.request({
    url: `https://service.wormhoo.com/api/game/bb/lists?cat_id=${id}`
  }).then(res => {
    return res.data
  }).then(res => {
    uni.hideLoading();
    game.value = res.data;
  })
}
function backP() {
  uni.navigateBack();
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  position: relative;

  .back {
    width: 100%;
    height: 100%;
  }

  .title {
    width: 118rpx;
    height: 30rpx;
    background: #3FCFFF;
    border-radius: 15rpx;
    text-align: center;
    font-size: 15rpx;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
    top: 14rpx;
  }

  .backs {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    position: absolute;
    top: 14rpx;
    left: 22rpx;
    z-index: 2;

    .backbtn {
      width: 37rpx;
      height: 37rpx;
      border-radius: 50%;
    }
  }

  .scroll {
    width: 100vw;
    height: 100vh;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    white-space: nowrap;

    .warp {
      width: 147rpx;
      height: 198rpx;
      display: inline-block;
      position: relative;
      margin-right: 36rpx;
      top: 50%;
      transform: translateY(-50%);

      .item {
        width: 147rpx;
        height: 198rpx;
        background: #FFFFFF;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 24rpx;
        flex-shrink: 0;
        position: relative;

        .shadw {
          width: 79rpx;
          height: 11rpx;
          position: absolute;
          left: 33rpx;
          bottom: -22rpx;
        }

        .cover {
          width: 128rpx;
          height: 97rpx;
          border-radius: 12rpx;
          margin-top: 9rpx;
        }

        .name {
          font-size: 15rpx;
          font-weight: 600;
          color: #262937;
          margin-top: 11rpx;
          max-width: 80%;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .btn {
          width: 84rpx;
          height: 30rpx;
          background: #FFAA33;
          border-radius: 15rpx;
          text-align: center;
          line-height: 30rpx;
          color: #FFFFFF;
          font-size: 15rpx;
          margin-top: 12rpx;
        }
      }
    }

    .zhanwei {
      width: 120rpx;
      display: inline-block;
    }
  }
}
</style>