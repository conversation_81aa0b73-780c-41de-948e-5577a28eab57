<template>
  <u-navbar :title="title" :border-bottom="false"></u-navbar>
  <view class="news_container" id="news" :style="{ opacity: showPage ? 1 : 0 }">
    <view class="news_title">
      {{ newsDetail.title }}
    </view>
    <view class="news_time">
      {{ newsDetail.operate_time }}
    </view>
    <view class="news_summary">
      {{ newsDetail.description }}
    </view>
    <u-parse :tag-style="richStyle" :html="richText" :selectable="true" :show-with-animation="true"></u-parse>
    <image v-if="newsDetail.video_url" class="music-btn" :class="musicClass"
      src="https://obs.tuoyupt.com/aituoyu/img/message/music.svg" @click="musicPlay"></image>
  </view>
</template>

<script setup>
import {
  ref,
  getCurrentInstance,
  onUnmounted
} from "vue";
import request from "@/request";
import { id2name, str2arr } from "@/utils/dataHandle";
import {
  onLoad,
} from "@dcloudio/uni-app";
import html2canvas from 'html2canvas';
const newsDetail = ref({})
const userId = ref([])
const newId = ref([])
const anmiationData = ref({})
const musicClass = ref('music-btn__active')
const richText = ref('')
const playing = ref(true)
const showPage = ref(false)
const richStyle = ref({
  p: 'color: #262937 !important;font-size:34rpx !important;line-height:56rpx !important ;white-space: pre-line !important',
  video: 'width: 100% !important;height: 225px !important'
})
const innerAudioContext = uni.createInnerAudioContext();

const getNewsDetails = async (id) => {
  let params = {
    id: id,
  }
  await request({
    method: "GET",
    url: `/api/newsDetail`,
    data: params
  }).then((result) => {
    newsDetail.value = result
    let str = result.content
    if (str) {
      str = str.replace(/\r\n/g, "</br>")
      richText.value = str
    }
    innerAudioContext.autoplay = true;
    if (result.video_url) {
      innerAudioContext.src = result.video_url
    }
    showPage.value = true
  });
}

const musicPlay = () => {
  if (musicClass.value == 'music-btn') {
    musicClass.value = 'music-btn__active'
    innerAudioContext.play()
  } else {
    musicClass.value = 'music-btn'
    innerAudioContext.pause()
  }
}
let tabs = [{
  name: '政策文件',
  id: 1,
}, {
  name: '行业新闻',
  id: 6,
}, {
  name: '专业指导',
  id: 7,
}, {
  name: '卫生保健',
  id: 8,
}, {
  name: '育儿知识',
  id: 9,
}]
const title = ref('政策信息')
onLoad(async (options) => {
  title.value = id2name(options.type, tabs)
  newId.value = options.id
  await getNewsDetails(options.id)
  if (options.download) {
    setTimeout(() => {
      html2canvas(document.getElementById('news'), {
        dpi: 300,
        width: 800,
        x: -25,
        y: -25,
        height: document.getElementById('news').clientHeight + 100,
        useCORS: true,
        backgroundColor: '#fff',
      }).then(function (canvas) {
        const dataURL = canvas.toDataURL('image/png');
        let newname = `内容.png`;
        const creatDom = document.createElement('a');
        document.body.appendChild(creatDom);
        creatDom.href = dataURL;
        creatDom.download = newname;
        creatDom.click();
        setTimeout(() => {
          window.close();
        }, 0);
      });
    }, 500);
  }
})
onUnmounted(() => {
  innerAudioContext.destroy()
})
</script>

<style>
page {
  background: #F6F6F6
}
</style>

<style lang="scss" scoped>
.news_container {
  opacity: 0;
  padding: 40rpx 30rpx;
  background: #fff;
  margin-bottom: 20rpx;
  transition: all .5s linear;

  .news_title {
    font-size: 44rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #111111;
    line-height: 60rpx;
  }

  .news_time {
    margin-top: 18rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 44rpx;
  }

  .news_summary {
    margin: 40rpx 0;
    font-size: 30rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #787C8D;
    line-height: 44rpx;
  }

  .news_content {
    font-size: 34rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #262937;
    line-height: 56rpx;
  }

  .adv_details {
    margin: 40rpx 0;
    padding: 24rpx 40rpx;
    background: #F6F6F6;
    border-radius: 24rpx;
    display: flex;
    justify-content: space-between;

    .adv_name {
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #262937;
      line-height: 44rpx;
      margin: 16rpx 0;
    }

    .adv_tele {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #787C8D;
    }

    .adv_pic {
      width: 176rpx;
      height: 176rpx;
      flex-shrink: 0;
    }
  }

  .btns {
    display: flex;
    justify-content: center;
  }

  .btn {
    width: 208rpx;
    height: 64rpx;
    background: #F6F6F6;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #262937;
    line-height: 64rpx;
    text-align: center;
  }


}

.next_news {
  padding: 40rpx 30rpx;
  background: #fff;

  .next_tip {
    display: inline-block;
    background: #617EFB;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    color: #FFFFFF;
    line-height: 34rpx;
  }

  .news_title {
    font-size: 44rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #111111;
    line-height: 60rpx;
  }

  .news_time {
    margin-top: 18rpx;
    margin-bottom: 16rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #AEB0BC;
    line-height: 44rpx;
  }

  .bottom-overflow-fade {
    mask-image: linear-gradient(to bottom, #AEB0BC 20%, transparent 100%);
    -webkit-mask-image: linear-gradient(to bottom, #AEB0BC 20%, transparent 100%);
  }

  .next_btns {
    display: flex;
    justify-content: center;

    .next_btn {
      display: flex;
      align-items: center;
      padding: 12rpx 24rpx;
      text-align: center;
      background: rgba(97, 126, 251, 0.12);
      border-radius: 32rpx;
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #617EFB;
      line-height: 40rpx;

      .next {
        margin-left: 16rpx;
        width: 28rpx;
        height: 28rpx;
        background: #617EFB;
        border-radius: 14rpx;
      }
    }


  }
}

.music-btn {
  position: fixed;
  top: 348rpx;
  right: 32rpx;
  width: 112rpx;
  height: 112rpx;
}

.music-btn__active {
  animation: spin 5s linear infinite;
  position: fixed;
  top: 348rpx;
  right: 32rpx;
  width: 112rpx;
  height: 112rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>