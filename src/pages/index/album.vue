<template>
  <view class="pages">
    <u-navbar title="我的相册" ref="navbar" :background="{ background: 'raba(255,255,255,0)' }"
      :border-bottom="false"></u-navbar>
    <view class="top">
      <view class="top-content">
        <text>持续不断记录，意义自然呈现</text>
        <view class="btn" @click="changeArticle" :class="{ active: is_article === 1 }">仅作品</view>
      </view>
    </view>
    <view class="fixed">
      <view class="btn" :class="{ active: type === 1 }" @click="changeType(1)">照片</view>
      <view class="hr"></view>
      <view class="btn" :class="{ active: type === 2 }" @click="changeType(2)">视频</view>
    </view>
    <view class="full">
      <scroll-view class="scroll" :scroll-y="true" @scrolltolower="loadMore">
        <template v-if="mList.length">
          <view class="msection" v-for="item in mList" :key="item.year + item.month">
            <view class="top">
              <view class="month">
                {{ item.month }}
              </view>
              <view class="year">
                <view class="t1">
                  {{ item.year }}年
                </view>
                <view class="t2">
                  {{ textEmnu[item.month] }}
                </view>
              </view>
              <view class="num">
                {{ item.count }}{{ type === 1 ? '张' : '条' }}
              </view>
            </view>
            <template v-if="type == 1">
              <view class="imgs">
                <u-image @click="previewMedia(img)" v-for="img in item.list" :key="img.id" width="250rpx"
                  height="250rpx" :src="img.url"></u-image>
              </view>
            </template>
            <template v-else>
              <view class="videos">
                <mvideo v-for="video in item.list" :key="video.id" :url="video.url">
                </mvideo>
              </view>
            </template>
          </view>
        </template>
        <view style="height: 40vh;" v-else>
          <u-empty text="暂无内容"></u-empty>
        </view>
        <template v-if="showBeforeYear">
          <view class="more" @click="getBefore" v-if="hasMore">
            点击继续回顾{{ beforeYear }}...
          </view>
          <view class="more" v-else>
            没有更多了
          </view>
        </template>
        <view v-else class="more">
          <u-loadmore :status="status" :loadText="loadText" />
        </view>
      </scroll-view>
    </view>
  </view>
</template>
<script setup>
import mvideo from "./components/mvideo.vue"
import dayjs from 'dayjs';
import { ref } from 'vue';
import { useUserStore } from "@/stores/userInfo.js"
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request";
const userInfo = useUserStore();
let currentTime = dayjs();
const beforeYear = ref(currentTime.subtract(1, "year").year());
let mList = ref([])
const type = ref(1);
const is_article = ref(0)
onLoad(async () => {
  await getStuInfo()
  getData()
})
const status = ref('loadmore')
const loadText = {
  loadmore: '轻轻上拉',
  loading: '正在加载',
  nomore: '没有更多了'
}
const showBeforeYear = ref(false);
const hasMore = ref(true)
let page = 1;
function getData() {
  uni.showLoading({
    mask: true
  })
  request({
    url: "/api/parent/studentphoto/list",
    data: {
      student_id: userInfo.student_id,
      month: currentTime.format("YYYY-MM"),
      is_article: is_article.value,
      type: type.value,
      per_page: type.value == 1 ? 20 : 10,
      page
    }
  }).then(res => {
    uni.hideLoading();
    if (res.count > 0) {
      mList.value.push({
        year: currentTime.year(),
        month: currentTime.format('MM'),
        count: res.count,
        list: res.res
      })
      if (res.count < (type.value == 1 ? 20 : 10)) {
        loadMore();
      }
    } else {
      getBeforeMonth();
    }
  })
}
function getBeforeMonth() {
  if (currentTime.month() > 0) {
    if (joinDate) {
      if (joinDate.isBefore(currentTime, 'month')) {
        currentTime = currentTime.subtract(1, 'month');
        status.value = 'loading';
        request({
          url: "/api/parent/studentphoto/list",
          data: {
            student_id: userInfo.student_id,
            month: currentTime.format("YYYY-MM"),
            is_article: is_article.value,
            type: type.value,
            per_page: type.value == 1 ? 20 : 10,
            page
          }
        }).then(res => {
          status.value = 'loadmore';
          if (res.count > 0) {
            mList.value.push({
              year: currentTime.year(),
              month: currentTime.format('MM'),
              count: res.count,
              list: res.res
            })
            let sources = mList.value.reduce((a, b) => {
              return a.concat(b.list.map(it => {
                return it.url
              }))
            }, []);
            if (sources.length < (type.value == 1 ? 20 : 10)) {
              loadMore();
            }
          } else {
            getBeforeMonth();
          }
        })
      } else {
        showBeforeYear.value = true;
        hasMore.value = false;
      }
    } else {
      currentTime = currentTime.subtract(1, 'month');
      status.value = 'loading';
      request({
        url: "/api/parent/studentphoto/list",
        data: {
          student_id: userInfo.student_id,
          month: currentTime.format("YYYY-MM"),
          is_article: is_article.value,
          type: type.value,
          per_page: type.value == 1 ? 20 : 10,
          page
        }
      }).then(res => {
        status.value = 'loadmore';
        if (res.count > 0) {
          mList.value.push({
            year: currentTime.year(),
            month: currentTime.format('MM'),
            count: res.count,
            list: res.res
          })
        } else {
          getBeforeMonth();
        }
      })
    }
  } else {
    showBeforeYear.value = true;
  }
}
function loadMore() {
  let currentIndex = mList.value.length - 1;
  if (mList.value[currentIndex].count > mList.value[currentIndex].list.length) {
    page++;
    status.value = 'loading';
    request({
      url: "/api/parent/studentphoto/list",
      data: {
        student_id: userInfo.student_id,
        month: currentTime.format("YYYY-MM"),
        is_article: is_article.value,
        type: type.value,
        per_page: type.value == 1 ? 20 : 10,
        page
      }
    }).then(res => {
      uni.hideLoading();
      status.value = 'loadmore';
      mList.value[currentIndex].list.push(...res.res)
    })
  } else {
    page = 1;
    getBeforeMonth();
  }
}
let joinDate = null;
function getStuInfo() {
  return request({
    url: `/api/parent/studentphoto/studentdetail/${userInfo.student_id}`
  }).then(res => {
    if (res.join_date) {
      let join_date = dayjs(res.join_date);
      if (join_date.isValid()) {
        joinDate = join_date;
        if (!joinDate.isBefore(currentTime, 'year')) {
          hasMore.value = false;
        }
      }
    }
  })
}
function getBefore() {
  showBeforeYear.value = false;
  currentTime = currentTime.month(11).subtract(1, 'year');
  beforeYear.value = currentTime.subtract(1, "year").year();
  page = 1;
  hasMore.value = true;
  getData();
}
function changeType(t) {
  if (type.value !== t) {
    showBeforeYear.value = false;
    type.value = t;
    mList.value = []
    currentTime = dayjs();
    beforeYear.value = currentTime.subtract(1, "year").year();
    page = 1;
    hasMore.value = true;
    getData();
  }
}
function changeArticle() {
  showBeforeYear.value = false;
  if (is_article.value === 1) {
    is_article.value = 0
  } else {
    is_article.value = 1;
  }
  mList.value = []
  currentTime = dayjs();
  beforeYear.value = currentTime.subtract(1, "year").year();
  page = 1;
  getData();
}
function previewMedia(img) {
  let sources = mList.value.reduce((a, b) => {
    return a.concat(b.list.map(it => {
      return it.url
    }))
  }, []);
  uni.previewImage({
    urls: sources,
    showmenu: true,
    current: img.url
  })
}
const textEmnu = {
  '01': '洗雨吹风一月春，山红漫漫绿纷纷。',
  '02': '新年都未有芳华，二月初惊见草芽。',
  '03': '三月湖天春昼长，东风飘暖草浮光。',
  '04': '人间四月芳菲尽，山寺桃花始盛开。',
  '05': '五月榴花照眼明，枝间时见子初成。',
  '06': '六月荷花香满湖, 红衣绿扇映清波。',
  '07': '七月江边暑已微，虚窗卧看雨霏霏。',
  '08': '八月长江万里晴，千帆一道带风轻。',
  '09': '荒畦九月稻叉牙，蛰萤低飞陇径斜。',
  '10': '庭中栽得红荆树，十月花开不待春。',
  '11': '十一月中长至夜，三千里外远行人。',
  '12': '城南小陌又逢春，只见梅花不见人。',
}
</script>
<style lang="scss" scoped>
.pages {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;

  .full {
    flex: 1;
    min-height: 1rpx;

    .scroll {
      height: 100%;
      box-sizing: border-box;

      .msection {
        width: 750rpx;
        background: #FFFFFF;
        box-sizing: border-box;

        .top {
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 128rpx;
          width: 100%;
          padding: 0 30rpx;
          box-sizing: border-box;
          position: sticky;
          top: 0;
          left: 0;
          background: #FFFFFF;
          z-index: 3;

          .month {
            font-size: 64rpx;
            font-family: Rousseau-Deco;
            color: #262937;
          }

          .year {
            padding-left: 18rpx;
            flex: 1;

            .t1 {
              font-size: 24rpx;
              font-weight: 500;
              color: #787C8D;
            }

            .t2 {
              font-size: 24rpx;
              font-weight: 500;
              color: #262937;

            }
          }

          .num {
            font-size: 24rpx;
            font-weight: 600;
            color: #262937;
          }
        }

        .imgs {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
        }

        .videos {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: space-between;
        }
      }
    }
  }

  .fixed {
    position: fixed;
    width: 280rpx;
    height: 80rpx;
    background: rgba(38, 41, 55, 0.88);
    border-radius: 56rpx;
    backdrop-filter: blur(2px);
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    left: 236rpx;
    bottom: 60rpx;
    z-index: 10;

    .btn {
      font-size: 28rpx;
      font-weight: 500;
      color: #ccc;
      height: 80rpx;
      width: 138rpx;
      text-align: center;
      line-height: 80rpx;
      display: block;
    }

    .active {
      color: #FFFFFF;
    }

    .hr {
      width: 2rpx;
      height: 16rpx;
      background: #787C8D;
      border-radius: 1rpx;
    }
  }
}

.top {
  width: 750rpx;
  height: 100rpx;
  background: #FFFFFF;

  .top-content {
    width: 690rpx;
    height: 100rpx;
    border-bottom: 1rpx solid #EEEEEE;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 40rpx;
    font-weight: 600;
    color: #262937;
    margin: 0 auto;
    justify-content: space-between;

    .btn {
      width: 104rpx;
      height: 48rpx;
      background: #F6F6F6;
      border-radius: 24rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #AEB0BC;
      line-height: 48rpx;
      text-align: center;
    }

    .active {
      color: #FFFFFF;
      background: #262937;
    }
  }
}

.more {
  text-align: center;
  height: 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #262937;
  line-height: 32rpx;
  padding-top: 32rpx;
  padding-bottom: 200rpx;
}
</style>