<template>
  <view class="page-online-chat">
    <u-navbar :title="doctor<PERSON><PERSON> + '医生'" :background="{ background: '#FFFFFF' }" :border-bottom="true">
    </u-navbar>

    <!-- 聊天内容区域 -->
    <scroll-view class="chat-content" scroll-y :scroll-top="scrollTop" scroll-with-animation
      :scroll-into-view="scrollIntoView" @scroll="onScroll">
      <!-- 系统提示 -->
      <view class="system-tip" v-if="consultData.question">
        <view class="tip-title">咨询问题：</view>
        <view class="tip-content">{{ consultData.question }}</view>
      </view>
      <view class="system-tip" v-else>
        请输入您遇到的问题，家庭医生将第一时间为您解答！
      </view>

      <!-- 聊天记录 -->
      <view class="message-list">
        <view v-for="(message, index) in messageList" :key="index" class="message-item" :class="message.type">
          <view class="message-content">
            <view class="avatar">
              <text class="avatar-text">{{ message.type === 'user' ? '我' : '医' }}</text>
            </view>
            <view class="content-wrapper">
              <view class="message-text">{{ message.content }}</view>
              <view class="message-time">{{ message.time }}</view>
            </view>
          </view>
        </view>
      </view>

    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="chat-footer">
      <view class="action-buttons">
        <view class="action-btn warning-btn" @click="showEvaluate">
          {{ consultData.evaluate_star ? '查看评价' : '评价反馈' }}
        </view>
        <view class="action-btn primary-btn" @click="showEndConsultConfirm" v-if="consultData.status !== 3">
          结束咨询
        </view>
      </view>

      <view class="input-area">
        <u-input v-model="inputMessage" placeholder="输入您的问题吧" :border="true" shape="round" />
        <u-button type="primary" shape="circle" @click="sendMessage" :loading="sending" :disabled="sending">
          发送
        </u-button>
      </view>
    </view>

    <!-- 评价反馈弹窗 -->
    <FeedbackModal
      v-model="showFeedback"
      v-model:rating="rating"
      v-model:feedback-text="feedbackText"
      :title="consultData.evaluate_star ? '查看评价' : '评价反馈'"
      description="期待您的建议反馈，我们会去监督改正"
      :loading="submittingFeedback"
      :is-view-mode="!!consultData.evaluate_star"
      @submit="submitFeedback"
      @cancel="closeFeedback"
    />

    <!-- 结束咨询确认弹窗 -->
    <u-modal v-model="showEndConfirm" :show-cancel-button="true" confirm-text="确认结束" cancel-text="取消"
      @confirm="confirmEndConsult" @cancel="cancelEndConsult" title="结束咨询">
      <view class="end-confirm-modal">
        <view class="confirm-content">
          <view class="confirm-text">确定要结束本次咨询吗？</view>
          <view class="confirm-tip">结束后将无法继续与医生对话</view>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref, nextTick, onMounted } from "vue";
import dayjs from "dayjs";
import FeedbackModal from "./components/FeedbackModal.vue";
import request from "@/request";

const scrollTop = ref(0);
const scrollIntoView = ref('');
const inputMessage = ref('');
const sending = ref(false);
const showFeedback = ref(false);
const rating = ref(0);
const feedbackText = ref('');
const submittingFeedback = ref(false);
const showEndConfirm = ref(false);

// 页面数据
const consultId = ref('');
const consultData = ref({});
const doctorName = ref('医生');

// 聊天记录数据
const messageList = ref([]);

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) {
    uni.showToast({
      title: '请输入消息内容',
      icon: 'none'
    });
    return;
  }

  if (!consultId.value) {
    uni.showToast({
      title: '咨询ID不存在',
      icon: 'none'
    });
    return;
  }

  sending.value = true;
  const messageContent = inputMessage.value.trim();

  try {
    // 先添加用户消息到列表（乐观更新）
    const userMessage = {
      type: 'user',
      content: messageContent,
      time: dayjs().format('YYYY-MM-DD HH:mm')
    };

    messageList.value.push(userMessage);
    inputMessage.value = '';

    // 等待DOM更新后滚动到底部
    await nextTick();
    scrollToBottom();

    // 调用API发送消息
    await request({
      url: '/api/parent/consult/send',
      method: 'POST',
      data: {
        consult_id: consultId.value,
        reply: messageContent
      }
    });

    // 延迟刷新聊天记录，获取可能的医生回复
    setTimeout(() => {
      refreshChatMessages();
    }, 1000);

  } catch (error) {
    console.error('发送消息失败:', error);

    // 发送失败，移除刚添加的消息
    messageList.value.pop();

    // 恢复输入框内容
    inputMessage.value = messageContent;

    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none'
    });
  } finally {
    sending.value = false;
  }
};

// 滚动到底部
const scrollToBottom = () => {
  // 使用随机值确保滚动动作生效
  const randomValue = 999999 + Math.random();
  scrollTop.value = randomValue;
};

// 监听滚动事件
const onScroll = () => {
  // 可以在这里处理滚动事件，比如判断是否滚动到顶部加载更多消息
};

// 显示评价
const showEvaluate = () => {
  // 检查是否已评价，如果已评价则回显数据
  if (consultData.value.evaluate_star) {
    rating.value = consultData.value.evaluate_star;
    feedbackText.value = consultData.value.evaluate || '';
  } else {
    rating.value = 0;
    feedbackText.value = '';
  }
  showFeedback.value = true;
};

// 关闭评价弹窗
const closeFeedback = () => {
  showFeedback.value = false;
  rating.value = 0;
  feedbackText.value = '';
};

// 显示结束咨询确认弹窗
const showEndConsultConfirm = () => {
  showEndConfirm.value = true;
};

// 确认结束咨询
const confirmEndConsult = async () => {
  showEndConfirm.value = false;

  try {
    await request({
      url: '/api/parent/consult/edit',
      method: 'POST',
      data: {
        id: consultId.value,
        status: 3 // 已结束
      }
    });

    uni.showToast({
      title: '咨询已结束',
      icon: 'success'
    });

    // 更新本地数据
    consultData.value.status = 3;

    // 延迟跳转回上一页或首页
    setTimeout(() => {
      uni.navigateBack({
        fail: () => {
          // 如果没有上一页，跳转到首页
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }
      });
    }, 1500);
  } catch (error) {
    console.error('结束咨询失败:', error);
    uni.showToast({
      title: '结束咨询失败',
      icon: 'none'
    });
  }
};

// 取消结束咨询
const cancelEndConsult = () => {
  showEndConfirm.value = false;
};

// 提交反馈
const submitFeedback = async (feedbackData) => {
  submittingFeedback.value = true;

  try {
    // 格式化评价时间
    const evaluateDate = dayjs().format('YYYY-MM-DD HH:mm:ss');

    await request({
      url: '/api/parent/consult/edit',
      method: 'POST',
      data: {
        id: consultId.value,
        status: 3, // 已结束
        evaluate_star: feedbackData.rating,
        evaluate: feedbackData.feedbackText,
        evaluate_date: evaluateDate
      }
    });

    uni.showToast({
      title: '反馈提交成功',
      icon: 'success'
    });

    // 重置表单并关闭弹窗
    rating.value = 0;
    feedbackText.value = '';
    showFeedback.value = false;

    // 更新本地数据
    consultData.value.status = 3;
    consultData.value.evaluate_star = feedbackData.rating;
    consultData.value.evaluate = feedbackData.feedbackText;
    consultData.value.evaluate_date = evaluateDate;

  } catch (error) {
    console.error('提交反馈失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    });
  } finally {
    submittingFeedback.value = false;
  }
};

// 格式化时间
function formatTime(dateStr) {
  if (!dateStr) return '';
  const date = dayjs(dateStr);
  if (!date.isValid()) return '';
  return date.format('YYYY-MM-DD HH:mm');
}

// 转换聊天记录格式
function formatChatMessages(replies) {
  if (!replies || !Array.isArray(replies)) return [];

  const messages = [];

  replies.forEach(reply => {
    if (reply.type === 1) {
      // 医生回复
      if (reply.reply) {
        messages.push({
          type: 'doctor',
          content: reply.reply,
          time: formatTime(reply.created_at)
        });
      }
    } else if (reply.type === 2) {
      // 家长回复
      if (reply.reply) {
        messages.push({
          type: 'user',
          content: reply.reply,
          time: formatTime(reply.created_at)
        });
      }
    }
  });

  // 按时间排序
  return messages.sort((a, b) => dayjs(a.time).valueOf() - dayjs(b.time).valueOf());
}

// 获取咨询详情数据
async function getConsultDetail() {
  if (!consultId.value) return;

  uni.showLoading({ title: '加载中...' });

  try {
    const res = await request({
      url: '/api/parent/consult/detail',
      method: 'GET',
      data: {
        id: consultId.value
      }
    });

    if (res) {
      consultData.value = res;

      // 设置医生姓名
      if (res.doctor_name) {
        doctorName.value = res.doctor_name;
      }

      // 转换聊天记录
      if (res.sun && Array.isArray(res.sun)) {
        messageList.value = formatChatMessages(res.sun);
      }

      // 如果有初始问题，添加到聊天记录开头
      if (res.question && messageList.value.length === 0) {
        messageList.value.unshift({
          type: 'user',
          content: res.question,
          time: formatTime(res.created_at)
        });
      }

      // 滚动到底部
      await nextTick();
      scrollToBottom();
    }
  } catch (error) {
    console.error('获取咨询详情失败:', error);
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
}

// 刷新聊天记录（不显示loading）
async function refreshChatMessages() {
  if (!consultId.value) return;

  try {
    const res = await request({
      url: '/api/parent/consult/detail',
      method: 'GET',
      data: {
        id: consultId.value
      }
    });

    if (res && res.sun && Array.isArray(res.sun)) {
      const newMessages = formatChatMessages(res.sun);

      // 如果有初始问题且新消息为空，添加初始问题
      if (res.question && newMessages.length === 0) {
        newMessages.unshift({
          type: 'user',
          content: res.question,
          time: formatTime(res.created_at)
        });
      }

      // 检查是否有新消息
      if (newMessages.length > messageList.value.length) {
        messageList.value = newMessages;

        // 滚动到底部
        await nextTick();
        scrollToBottom();
      }
    }
  } catch (error) {
    console.error('刷新聊天记录失败:', error);
  }
}

onLoad((options) => {
  consultId.value = options.id || '';
  if (consultId.value) {
    getConsultDetail();
  }
});


onMounted(async () => {
  await nextTick()
  scrollToBottom();
})
</script>

<style lang="scss" scoped>
.page-online-chat {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
  /* 防止页面整体滚动 */

  .chat-content {
    flex: 1;
    padding: 20rpx;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .system-tip {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 40rpx;
    padding: 24rpx 32rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;

    .tip-title {
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }

    .tip-content {
      line-height: 1.5;
      color: #666;
    }
  }

  .message-list {
    .message-item {
      margin-bottom: 32rpx;
      &:last-child {
        margin-bottom: 0;
      }
      &.user {
        .message-content {
          flex-direction: row-reverse;

          .avatar {
            background: #007aff;

            .avatar-text {
              color: white;
            }
          }

          .content-wrapper {
            align-items: flex-end;
            margin-right: 24rpx;
            margin-left: 0;

            .message-text {
              background: #007aff;
              color: white;
              border-radius: 24rpx 24rpx 8rpx 24rpx;
            }

            .message-time {
              text-align: right;
              margin-right: 8rpx;
            }
          }
        }
      }

      &.doctor {
        .message-content {
          flex-direction: row;

          .avatar {
            background: #4CAF50;

            .avatar-text {
              color: white;
            }
          }

          .content-wrapper {
            align-items: flex-start;
            margin-left: 24rpx;
            margin-right: 0;

            .message-text {
              background: white;
              color: #333;
              border-radius: 24rpx 24rpx 24rpx 8rpx;
              border: 1rpx solid #f0f0f0;
            }

            .message-time {
              text-align: left;
              margin-left: 8rpx;
            }
          }
        }
      }

      .message-content {
        display: flex;
        align-items: flex-start;

        .avatar {
          width: 88rpx;
          height: 88rpx;
          background: #e0e0e0;
          border-radius: 44rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .avatar-text {
            font-size: 32rpx;
            color: #666;
            font-weight: 500;
          }
        }

        .content-wrapper {
          display: flex;
          flex-direction: column;
          max-width: 480rpx;

          .message-text {
            padding: 24rpx 28rpx;
            font-size: 28rpx;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
          }

          .message-time {
            font-size: 20rpx;
            color: #999;
            margin-top: 8rpx;
          }
        }
      }
    }
  }



  .end-confirm-modal {
    padding: 40rpx 20rpx 20rpx;

    .confirm-content {
      text-align: center;

      .confirm-text {
        font-size: 32rpx;
        color: #333;
        margin-bottom: 16rpx;
        font-weight: 500;
      }

      .confirm-tip {
        font-size: 24rpx;
        color: #999;
        line-height: 1.5;
      }
    }
  }

  .chat-footer {
    background: white;
    padding: 24rpx;
    border-top: 1rpx solid #f0f0f0;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));

    .action-buttons {
      display: flex;
      gap: 24rpx;
      margin-bottom: 24rpx;
      justify-content: flex-start;

      .action-btn {
        border-radius: 44rpx;
        font-size: 24rpx;
        font-weight: 500;
        padding: 16rpx 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #ffffff;

        &.warning-btn {
          background: #ff9900;
        }

        &.primary-btn {
          background: #007aff;
        }
      }
    }

    .input-area {
      display: flex;
      align-items: center;
      gap: 20rpx;
    }
  }
}
</style>
