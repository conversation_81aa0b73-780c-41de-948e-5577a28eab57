<template>
  <u-modal 
    v-model="visible" 
    :show-cancel-button="!isViewMode" 
    :confirm-text="isViewMode ? '关闭' : '提交'" 
    :cancel-text="'取消'"
    @confirm="handleConfirm" 
    @cancel="handleCancel" 
    :async-close="!isViewMode"
    :title="title"
  >
    <view class="feedback-modal">
      <view class="feedback-title">{{ description }}</view>

      <!-- 星级评价 -->
      <view class="star-rating">
        <view 
          class="star-item" 
          v-for="index in 5" 
          :key="index"
          @click="setRating(index)"
        >
          <u-icon 
            :name="index <= currentRating ? 'star-fill' : 'star'" 
            :color="index <= currentRating ? '#FFD700' : '#E5E5E5'" 
            :size="starSize"
          ></u-icon>
        </view>
      </view>

      <!-- 反馈输入框 -->
      <view class="feedback-input">
        <u-input 
          v-model="currentFeedbackText" 
          type="textarea" 
          :placeholder="placeholder"
          :maxlength="maxLength"
          :disabled="isViewMode"
          :height="textareaHeight"
        />
      </view>
    </view>
  </u-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

// Props定义
const props = defineProps({
  // 控制弹窗显示
  modelValue: {
    type: Boolean,
    default: false
  },
  // 弹窗标题
  title: {
    type: String,
    default: '评价反馈'
  },
  // 描述文本
  description: {
    type: String,
    default: '期待您的建议反馈，我们会去监督改正'
  },
  // 输入框占位符
  placeholder: {
    type: String,
    default: '请输入您的反馈意见'
  },
  // 最大输入长度
  maxLength: {
    type: Number,
    default: 500
  },
  // 文本域高度
  textareaHeight: {
    type: String,
    default: '120'
  },
  // 星星大小
  starSize: {
    type: String,
    default: '40'
  },
  // 初始评分
  rating: {
    type: Number,
    default: 0
  },
  // 初始反馈文本
  feedbackText: {
    type: String,
    default: ''
  },
  // 是否为查看模式（只读）
  isViewMode: {
    type: Boolean,
    default: false
  },
  // 提交时的加载状态
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits定义
const emit = defineEmits([
  'update:modelValue',
  'update:rating', 
  'update:feedbackText',
  'submit',
  'cancel'
]);

// 内部状态
const currentRating = ref(props.rating);
const currentFeedbackText = ref(props.feedbackText);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 监听props变化，同步内部状态
watch(() => props.rating, (newVal) => {
  currentRating.value = newVal;
});

watch(() => props.feedbackText, (newVal) => {
  currentFeedbackText.value = newVal;
});

// 监听内部状态变化，向外发射事件
watch(currentRating, (newVal) => {
  emit('update:rating', newVal);
});

watch(currentFeedbackText, (newVal) => {
  emit('update:feedbackText', newVal);
});

// 设置星级评价
const setRating = (value) => {
  if (!props.isViewMode) {
    currentRating.value = value;
  }
};

// 处理确认按钮
const handleConfirm = () => {
  if (props.isViewMode) {
    // 查看模式，直接关闭
    visible.value = false;
    return true;
  }
  
  // 编辑模式，验证并提交
  if (currentRating.value === 0) {
    uni.showToast({
      title: '请选择星级评价',
      icon: 'none'
    });
    return false; // 阻止弹窗关闭
  }

  // 发射提交事件
  emit('submit', {
    rating: currentRating.value,
    feedbackText: currentFeedbackText.value
  });
  
  return false; // 由父组件控制弹窗关闭
};

// 处理取消按钮
const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

// 重置表单
const resetForm = () => {
  currentRating.value = 0;
  currentFeedbackText.value = '';
};

// 暴露方法给父组件
defineExpose({
  resetForm
});
</script>

<style lang="scss" scoped>
.feedback-modal {
  padding: 40rpx 20rpx 20rpx;

  .feedback-title {
    text-align: center;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 32rpx;
    line-height: 1.5;
    font-weight: 500;
  }

  .star-rating {
    display: flex;
    justify-content: center;
    gap: 16rpx;
    margin-bottom: 32rpx;

    .star-item {
      cursor: pointer;
      transition: transform 0.2s ease;
      
      &:active {
        transform: scale(0.9);
      }
    }
  }

  .feedback-input {
    margin-bottom: 20rpx;
    
    :deep(.u-input) {
      border: 2rpx solid #E5E5E5;
      border-radius: 8rpx;
      background: #FAFAFA;
      padding:0 10rpx !important;
      
      &.u-input--disabled {
        background: #F5F5F5;
        color: #999;
      }
    }
  }
}
</style>
