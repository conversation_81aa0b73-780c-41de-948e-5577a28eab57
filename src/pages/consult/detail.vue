<template>
  <view class="page-detail">
    <u-navbar :title="navTitle" :background="{ background: '#FFFFFF' }" :border-bottom="true" />
    
    <!-- 基本信息卡片 -->
    <view class="card-item">
      <u-form label-position="top" label-width="auto">
        <u-form-item label="问题描述">
          <view class="form-value">{{ detailData.question }}</view>
        </u-form-item>

        <u-form-item :label="getTimeLabel()" >
          <view class="form-value">{{ getDisplayTime() }}</view>
        </u-form-item>

        <u-form-item label="联系电话">
          <view class="form-value">{{ detailData.parent_phone }}</view>
        </u-form-item>

        <u-form-item label="家庭医生">
          <view class="form-value">{{ detailData.doctor_name }}</view>
        </u-form-item>

        <u-form-item label="就诊地点" v-if="type === 'offline'">
          <view class="form-value">{{ detailData.reservation_addr }}</view>
        </u-form-item>
      </u-form>
    </view>

    <!-- 医生回复信息 -->
    <view class="card-item" v-if="detailData.sun && detailData.sun.length > 0">
      <view class="header">
        <view class="title">医生回复</view>
      </view>

      <view class="reply-list">
        <view class="reply-item" v-for="(reply, index) in getDoctorReplies()" :key="reply.id">
          <u-form label-position="top" label-width="auto">

            <!-- 初步诊断结果：电话咨询不显示 -->
            <u-form-item label="初步诊断结果" v-if="type !== 'phone' && reply.result">
              <view class="form-value">{{ reply.result }}</view>
            </u-form-item>

            <!-- 医生意见：所有类型都显示 -->
            <u-form-item label="医生意见" v-if="reply.reply">
              <view class="form-value">{{ reply.reply }}</view>
            </u-form-item>

            <!-- 就诊照片：电话咨询不显示 -->
            <u-form-item label="就诊照片" v-if="type !== 'phone' && reply.image">
              <view class="photo-grid">
                <view class="photo-item" v-for="(photo, photoIndex) in getPhotoList(reply.image)" :key="photoIndex">
                  <image class="photo-image" :src="photo" mode="aspectFill" @click="previewImage(photo, getPhotoList(reply.image))"></image>
                </view>
              </view>
            </u-form-item>
          </u-form>

          <view class="reply-divider" v-if="index < getDoctorReplies().length - 1"></view>
        </view>
      </view>
    </view>
    
    <!-- 评价反馈按钮 -->
    <view class="feedback-btn" @click="handleFeedback">
      <u-icon name="star" color="#FF9500" size="32"></u-icon>
      <text class="btn-text">{{ detailData.evaluate_star ? '查看评价' : '评价反馈' }}</text>
    </view>

    <!-- 评价反馈弹窗 -->
    <FeedbackModal
      v-model="showFeedbackModal"
      v-model:rating="rating"
      v-model:feedback-text="feedbackText"
      title="评价反馈"
      description="期待您的建议反馈，我们会去监督改正"
      :is-view-mode="isViewMode"
      :loading="submitting"
      :max-length="200"
      star-size="46"
      textarea-height="120"
      @submit="submitFeedback"
      @cancel="handleCancel"
    />
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import dayjs from "dayjs";
import request from "@/request";
import FeedbackModal from "./components/FeedbackModal.vue";

const type = ref('phone'); // phone, offline
const consultId = ref('');
const navTitle = ref('');
const detailData = ref({});
const showFeedbackModal = ref(false);
const rating = ref(0);
const feedbackText = ref('');
const submitting = ref(false);
const isViewMode = ref(false); // 是否为查看模式（已评价）

// 检查日期是否有效
function isValidDate(dateStr) {
  if (!dateStr || dateStr === '0000-00-00 00:00:00') return false;
  const date = dayjs(dateStr);
  return date.isValid() && date.year() > 1970;
}

// 格式化时间
function formatTime(dateStr) {
  if (!dateStr || dateStr === '0000-00-00 00:00:00') return '';
  const date = dayjs(dateStr);
  if (!date.isValid() || date.year() <= 1970) return '';
  return date.format('YYYY-MM-DD HH:mm');
}

// 根据类型获取时间标签
function getTimeLabel() {
  const labelMap = {
    'phone': '预约时间',
    'offline': '就诊时间'
  };
  return labelMap[type.value] || '预约时间';
}

// 获取显示时间
function getDisplayTime() {
  // 优先显示预约时间
  if (isValidDate(detailData.value.reservation_start_date)) {
    return formatTime(detailData.value.reservation_start_date);
  }
  // 其次显示咨询时间
  if (isValidDate(detailData.value.consult_date)) {
    return formatTime(detailData.value.consult_date);
  }
  // 最后显示创建时间
  if (isValidDate(detailData.value.created_at)) {
    return formatTime(detailData.value.created_at);
  }
  return '';
}

// 处理照片列表
function getPhotoList(imageStr) {
  if (!imageStr) return [];
  try {
    // 如果是JSON字符串，解析它
    if (imageStr.startsWith('[') || imageStr.startsWith('{')) {
      return JSON.parse(imageStr);
    }
    // 如果是逗号分隔的字符串
    return imageStr.split(',').filter(url => url.trim());
  } catch (error) {
    // 如果解析失败，当作单个URL处理
    return [imageStr];
  }
}

// 预览图片
function previewImage(current, urls) {
  uni.previewImage({
    current,
    urls
  });
}

// 获取医生回复列表（只显示医生的回复，type=1）
function getDoctorReplies() {
  if (!detailData.value.sun || !Array.isArray(detailData.value.sun)) {
    return [];
  }

  return detailData.value.sun.filter(reply => reply.type === 1);
}


// 处理评价反馈点击
function handleFeedback() {
  // 检查是否已评价
  if (detailData.value.evaluate_star) {
    // 已评价，显示评价内容（只读模式）
    isViewMode.value = true;
    rating.value = detailData.value.evaluate_star || 5;
    feedbackText.value = detailData.value.evaluate || '';
  } else {
    // 未评价，显示评价表单
    isViewMode.value = false;
    rating.value = 0;
    feedbackText.value = '';
  }
  showFeedbackModal.value = true;
}

// 提交评价
async function submitFeedback(feedbackData) {
  submitting.value = true;
  try {
    // 格式化评价时间
    const evaluateDate = dayjs().format('YYYY-MM-DD HH:mm:ss');

    await request({
      url: '/api/parent/consult/edit',
      method: 'POST',
      data: {
        id: consultId.value,
        status: 3, // 已结束
        evaluate_star: feedbackData.rating,
        evaluate: feedbackData.feedbackText,
        evaluate_date: evaluateDate
      }
    });

    // 更新本地数据
    detailData.value.status = 3;
    detailData.value.evaluate_star = feedbackData.rating;
    detailData.value.evaluate = feedbackData.feedbackText;
    detailData.value.evaluate_date = evaluateDate;

    uni.showToast({
      title: '评价提交成功',
      icon: 'success'
    });

    showFeedbackModal.value = false;
  } catch (error) {
    console.error('提交评价失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
}

// 处理取消按钮
function handleCancel() {
  showFeedbackModal.value = false;
}

// 获取详情数据
async function getDetailData() {
  uni.showLoading();
  try {
    const res = await request({
      url: '/api/parent/consult/detail',
      method: 'GET',
      data: {
        id: consultId.value
      }
    });

    if (res) {
      detailData.value = res;
    }
  } catch (error) {
    console.error('获取详情数据失败:', error);
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
}

onLoad((options) => {
  type.value = options.type || 'phone';
  consultId.value = options.id || '1';
  
  const titleMap = {
    'phone': '预约电话咨询',
    'offline': '预约线下就诊'
  };
  navTitle.value = titleMap[type.value] || '咨询详情';
  
  getDetailData();
});
</script>

<style lang="scss" scoped>
.page-detail {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  background: #f6f6f6;
  padding-bottom: 160rpx;

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;

    .header {
      padding: 24rpx 0;
      border-bottom: 1px solid #eee;

      .title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .form-value {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
      word-break: break-all;

      &.advice-text {
        line-height: 1.6;
      }
    }

    .photo-grid {
      display: flex;
      gap: 20rpx;
      flex-wrap: wrap;
      margin-top: 16rpx;

      .photo-item {
        width: 160rpx;
        height: 160rpx;

        .photo-image {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
          object-fit: cover;
        }

        .photo-placeholder {
          width: 100%;
          height: 100%;
          border: 2rpx solid #E5E5E5;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #FAFAFA;

          .photo-text {
            font-size: 48rpx;
            color: #CCC;
          }
        }
      }
    }

    .reply-list {
      .reply-item {
        margin-bottom: 40rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .reply-divider {
        height: 1px;
        background: #f0f0f0;
        margin: 40rpx 0;
      }
    }

    .star-rating {
      display: flex;
      align-items: center;
      gap: 8rpx;
    }
  }

  .feedback-btn {
    position: fixed;
    bottom: 40rpx;
    right: 40rpx;
    background: #FFF;
    border-radius: 50rpx;
    padding: 20rpx 32rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    z-index: 10;

    .btn-text {
      font-size: 28rpx;
      color: #FF9500;
      font-weight: 500;
    }
  }


}
</style>
