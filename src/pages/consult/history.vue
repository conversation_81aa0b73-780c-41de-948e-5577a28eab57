<template>
  <view class="page-history">
    <u-navbar title="历史记录" :background="{ background: '#FFFFFF' }" :border-bottom="true" />

    <view class="card-item" v-for="(item, index) in list" :key="index" @click="goDetail(item.id)">
      <!-- 红点提示 -->
      <view class="red-dot" v-if="item.is_display_red_point == 1"></view>

      <view class="info-item doctor-info">
        <view class="doctor-section">
          <text class="label">{{ getConsultTypeLabel() }}医生：</text>
          <text class="value">{{ item.doctor?.name || '暂无' }}</text>
        </view>
        <view class="status-section">
          <u-tag type="warning" size="mini" shape="square" text="待处理" v-if="item.status == 1"></u-tag>
          <u-tag type="success" size="mini" shape="square" text="已回复" v-if="item.status == 2"></u-tag>
          <u-tag type="info" size="mini" shape="square" text="已结束" v-if="item.status == 3"></u-tag>
        </view>
      </view>

      <view class="info-item">
        <text class="label">{{ getConsultTypeLabel() }}时间：</text>
        <text class="value">{{ getDisplayTime(item) }}</text>
      </view>

      <view class="info-item" v-if="item.question">
        <text class="label">咨询问题：</text>
        <text class="value question-text">{{ item.question }}</text>
      </view>

      <view class="info-item" v-if="item.reservation_addr && item.reservation_addr.trim() && type == 'offline'">
        <text class="label">预约地址：</text>
        <text class="value">{{ item.reservation_addr }}</text>
      </view>



      <view class="footer">
        <text class="title">查看详情</text>
        <u-icon name="arrow-right"></u-icon>
      </view>
    </view>

    <!-- 空状态 -->
    <u-empty v-if="!loading && list.length == 0" :text="`暂无${navTitle}记录`" mode="data"></u-empty>

    <!-- 加载更多 -->
    <view class="load-more" v-if="list.length > 0">
      <u-loadmore :status="loadStatus" :loading-text="loadingText" :loadmore-text="loadmoreText"
        :nomore-text="nomoreText" />
    </view>

  </view>
</template>

<script setup>
import { onLoad, onShow, onReachBottom } from "@dcloudio/uni-app";
import { ref } from "vue";
import dayjs from "dayjs";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
import request from "@/request";

const type = ref('online'); // online, phone, offline
const navTitle = ref('');
const list = ref([]);
const loading = ref(false);
const page = ref(1);
const perPage = ref(10);
const hasMore = ref(true);
const loadStatus = ref('loadmore'); // loadmore, loading, nomore

// 加载状态文本
const loadingText = ref('正在加载...');
const loadmoreText = ref('点击加载更多');
const nomoreText = ref('没有更多了');

// 根据类型获取标签
function getConsultTypeLabel() {
  const labelMap = {
    'online': '咨询',
    'phone': '预约',
    'offline': '预约'
  };
  return labelMap[type.value] || '咨询';
}

// 获取咨询类型数字
function getConsultType() {
  const typeMap = {
    'online': 1,  // 线上咨询
    'phone': 2,   // 电话预约
    'offline': 3  // 线下预约
  };
  return typeMap[type.value] || 1;
}

// 检查日期是否有效
function isValidDate(dateStr) {
  if (!dateStr || dateStr === '0000-00-00 00:00:00') return false;
  const date = dayjs(dateStr);
  return date.isValid() && date.year() > 1970;
}

// 格式化时间
function formatTime(dateStr) {
  if (!dateStr || dateStr === '0000-00-00 00:00:00') return '暂无';
  const date = dayjs(dateStr);
  if (!date.isValid() || date.year() <= 1970) return '暂无';
  return date.format('YYYY-MM-DD HH:mm');
}

// 获取显示时间（优先显示预约时间，否则显示创建时间）
function getDisplayTime(item) {
  // 如果有有效的预约时间，优先显示预约时间
  if (isValidDate(item.reservation_start_date)) {
    return formatTime(item.reservation_start_date);
  }
  // 否则显示创建时间
  return formatTime(item.created_at);
}

// 跳转详情页
function goDetail(id) {
  if (type.value == 'online') {
    uni.navigateTo({
      url: `/pages/consult/online-chat?id=${id}`
    });
    return;
  }
  uni.navigateTo({
    url: `/pages/consult/detail?id=${id}&type=${type.value}`,
  });
}

// 获取数据
async function getData(isRefresh = false) {
  if (loading.value) return;

  loading.value = true;
  if (isRefresh) {
    page.value = 1;
    hasMore.value = true;
    loadStatus.value = 'loading';
  } else if (page.value === 1) {
    uni.showLoading({ title: '加载中...' });
  } else {
    loadStatus.value = 'loading';
  }

  try {
    const res = await request({
      url: '/api/parent/consult/history',
      method: 'POST',
      data: {
        page: page.value,
        per_page: perPage.value,
        consult_type: getConsultType()
      }
    });

    if (res && res.data) {
      const newData = res.data || [];

      if (isRefresh || page.value === 1) {
        list.value = newData;
      } else {
        list.value = [...list.value, ...newData];
      }

      // 根据分页信息判断是否还有更多数据
      const currentPage = res.current_page || page.value;
      const lastPage = res.last_page || 1;

      if (currentPage >= lastPage || newData.length < perPage.value) {
        hasMore.value = false;
        loadStatus.value = 'nomore';
      } else {
        hasMore.value = true;
        loadStatus.value = 'loadmore';
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
    loadStatus.value = 'loadmore';
  } finally {
    loading.value = false;
    if (page.value === 1) {
      uni.hideLoading();
    }
  }
}

// 加载更多数据
function loadMore() {
  if (!hasMore.value || loading.value) return;
  page.value++;
  getData();
}

onLoad((options) => {
  type.value = options.type || 'online';

  const titleMap = {
    'online': '在线咨询',
    'phone': '电话咨询',
    'offline': '线下就诊'
  };
  navTitle.value = titleMap[type.value] || '在线咨询';
});

onShow(() => {
  getData(true); // 刷新数据
});

// 触底加载更多
onReachBottom(() => {
  loadMore();
});
</script>

<style lang="scss" scoped>
.page-history {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: #f6f6f6;

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    position: relative;

    .red-dot {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 16rpx;
      height: 16rpx;
      background: #ff4757;
      border-radius: 50%;
    }

    .info-item {
      display: flex;
      align-items: flex-start;

      .label {
        width: 160rpx;
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #9a9a9a;

        &.question-text {
          line-height: 1.4;
          max-height: 80rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      .star-rating {
        display: flex;
        align-items: center;
        gap: 8rpx;
      }

      &.doctor-info {
        justify-content: space-between;
        align-items: center;

        .doctor-section {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .status-section {
          display: flex;
          align-items: center;
        }
      }
    }

    .footer {
      padding: 24rpx 0 0;
      border-top: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #2979ff;

      .title {
        font-size: 28rpx;
      }
    }
  }



  .load-more {
    padding: 40rpx 0;
  }
}
</style>
