<template>
  <view class="page-consult-form">
    <u-navbar :title="config.title" :background="{ background: '#FFFFFF' }" :border-bottom="true">
      <template #right>
        <view class="custom-right">
          <u-icon name="order" size="50" @click="handleRightClick"></u-icon>
        </view>
      </template>
    </u-navbar>

    <u-form ref="formRef" :border-bottom="false" :model="formData" label-position="top">
      <view class="card-item" style="padding-left: 48rpx;">
        <u-form-item required label="问题描述" prop="description">
          <u-input v-model="formData.description" type="textarea" height="200rpx" placeholder="请描述您遇到的问题！"
            maxlength="500" :auto-height="true" />
        </u-form-item>

        <!-- 时间选择 -->
        <u-form-item v-if="config.showTime" required :label="config.timeLabel" prop="startTime">
          <u-input type="select" v-model="formData.startTime" :placeholder="config.timePlaceholder"
            @click="show.startTime = true" />
        </u-form-item>

        <!-- 结束时间 -->
        <u-form-item v-if="config.showEndTime" required :label="config.endTimeLabel" prop="endTime">
          <u-input type="select" v-model="formData.endTime" :placeholder="config.endTimePlaceholder"
            @click="show.endTime = true" />
        </u-form-item>

        <!-- 联系电话 -->
        <u-form-item v-if="config.showPhone" required label="联系电话" prop="phone">
          <u-input v-model="formData.phone" placeholder="请输入您的联系电话" maxlength="11" />
        </u-form-item>

        <u-form-item required label="家庭医生" prop="doctor" :border-bottom="config.showLocation">
          <u-input type="select" v-model="text.doctor" placeholder="请选择您需要预约的家庭医生" @click="show.doctor = true">
          </u-input>
        </u-form-item>

        <!-- 线下就诊显示地址 -->
        <u-form-item v-if="config.showLocation" required label="就诊地点" prop="location" :border-bottom="false">
          <view>
            <!-- <view class="location-item">
              <text class="location-label">地区：</text>
              <text class="location-link" @click="handleSelectLocation">查看位置</text>
            </view> -->
            <u-input v-model="formData.location" placeholder="请输入详细位置" />
          </view>
        </u-form-item>
      </view>
    </u-form>

    <!-- 开始时间选择器 -->
    <u-picker mode="time" v-model="show.startTime" @confirm="startTimeConfirm" :params="{
      year: true,
      month: true,
      day: true,
      hour: true,
      minute: true,
      second: false
    }"></u-picker>

    <!-- 结束时间选择器 -->
    <u-picker v-if="config.showEndTime" mode="time" v-model="show.endTime" @confirm="endTimeConfirm" :params="{
      year: true,
      month: true,
      day: true,
      hour: true,
      minute: true,
      second: false
    }"></u-picker>

    <!-- 医生选择器 -->
    <u-picker mode="selector" v-model="show.doctor" @confirm="doctorConfirm" :range="formattedDoctorList"
      range-key="displayName"></u-picker>

    <!-- 提交按钮 -->
    <view class="form-footer">
      <u-button type="primary" @click="submit" :loading="submiting" :disabled="submiting" shape="circle">
        {{ config.submitText }}
      </u-button>
    </view>

    <!-- 成功弹窗 -->
    <u-modal v-model="successShow" :show-cancel-button="false" confirm-text="确定" @confirm="handleSuccessConfirm">
      <view class="modal-content">
        <text class="success-title">{{ successMessage }}</text>
      </view>
    </u-modal>
  </view>
</template>

<script setup>
import { onLoad, onReady } from "@dcloudio/uni-app";
import { reactive, ref, computed } from "vue";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";

const formRef = ref(null);
const submiting = ref(false);
const successShow = ref(false);
const userStore = useUserStore();
const loadingDoctors = ref(false);
const consultType = ref('online'); // 默认在线咨询

// 咨询类型配置
const consultConfigs = {
  online: {
    title: '在线咨询',
    consultType: '1',
    timeLabel: '',
    timePlaceholder: '',
    submitText: '发起咨询',
    successMessageTemplate: '尊敬的家长，您已成功发起与（{doctorName}）的在线咨询，请等待医生回复。',
    showLocation: false,
    showEndTime: false,
    showTime: false,
    showPhone: false
  },
  phone: {
    title: '预约电话咨询',
    consultType: '2',
    timeLabel: '咨询开始时间',
    endTimeLabel: '咨询结束时间',
    timePlaceholder: '请选择电话咨询开始时间',
    endTimePlaceholder: '请选择电话咨询结束时间',
    submitText: '立即预约',
    successMessageTemplate: '尊敬的家长，您已成功预约（{doctorName}）的电话咨询，医生将通过电话在预约的时间内联系，请保持手机畅通。',
    showLocation: false,
    showEndTime: true,
    showTime: true,
    showPhone: true
  },
  offline: {
    title: '预约线下就诊',
    consultType: '3',
    timeLabel: '就诊开始时间',
    endTimeLabel: '就诊结束时间',
    timePlaceholder: '请选择就诊开始时间',
    endTimePlaceholder: '请选择就诊结束时间',
    submitText: '立即预约',
    successMessageTemplate: '尊敬的家长，您已成功预约（{doctorName}）的线下就诊，医生已接收到预约信息，还需等待医生确认预约时间，确认后将进行预约时间段进行上门就诊，请保持手机畅通。',
    showLocation: true,
    showEndTime: true,
    showTime: true,
    showPhone: true
  }
};

// 当前配置
const config = computed(() => consultConfigs[consultType.value] || consultConfigs.online);

// 动态成功消息
const successMessage = computed(() => {
  if (!selectedDoctor.value) {
    return config.value.successMessageTemplate.replace('{doctorName}', 'XX医生');
  }
  return config.value.successMessageTemplate.replace('{doctorName}', selectedDoctor.value.name);
});

const formData = reactive({
  description: '',
  startTime: '',
  endTime: '',
  phone: '',
  doctor: '',
  location: ''
});

const text = reactive({
  doctor: ''
});

const selectedDoctor = ref(null);

const show = reactive({
  startTime: false,
  endTime: false,
  doctor: false
});

const doctorList = ref([]);

// 格式化医生显示列表
const formattedDoctorList = computed(() => {
  return doctorList.value.map(doctor => ({
    ...doctor,
    displayName: `${doctor.name} - ${doctor.hospitalName}`
  }));
});

// 动态验证规则
const rules = computed(() => {
  const baseRules = {
    description: [{ required: true, message: "请输入问题描述" }],
    doctor: [{ required: true, message: "请选择家庭医生" }]
  };

  if (config.value.showTime) {
    baseRules.startTime = [{ required: true, message: `请选择${config.value.timeLabel}` }];
  }

  if (config.value.showPhone) {
    baseRules.phone = [{ required: true, message: "请输入联系电话" }];
  }

  if (config.value.showEndTime) {
    baseRules.endTime = [
      { required: true, message: `请选择${config.value.endTimeLabel}` },
      {
        validator: (_, value, callback) => {
          if (!value || !formData.startTime) {
            callback();
            return;
          }

          const startTime = new Date(formData.startTime);
          const endTime = new Date(value);

          if (endTime <= startTime) {
            callback(new Error('结束时间必须大于开始时间'));
            return;
          }

          callback();
        }
      }
    ];
  }

  if (config.value.showLocation) {
    baseRules.location = [{ required: true, message: "请输入就诊地点" }];
  }

  return baseRules;
});

// 获取医生列表
const getDoctorList = async () => {
  if (!userStore.school_id) {
    uni.showToast({
      title: '获取学校信息失败',
      icon: 'error'
    });
    return;
  }

  loadingDoctors.value = true;
  try {
    const data = await request({
      url: '/api/parent/consult/doctor',
      method: 'POST',
      data: {
        school_id: userStore.school_id
      }
    });

    if (data && Array.isArray(data)) {
      doctorList.value = data;
    } else {
      uni.showToast({
        title: '暂无可选医生',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('获取医生列表失败:', error);
    uni.showToast({
      title: '获取医生列表失败',
      icon: 'error'
    });
  } finally {
    loadingDoctors.value = false;
  }
};

// 格式化时间显示
const formatDateTime = (timeObj) => {
  if (Object.keys(timeObj).length === 0) return '';
  const { year, month, day, hour, minute } = timeObj;
  return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 开始时间选择确认
const startTimeConfirm = (e) => {
  formData.startTime = formatDateTime(e);
};

// 结束时间选择确认
const endTimeConfirm = (e) => {
  const endTimeStr = formatDateTime(e);
  formData.endTime = endTimeStr;

  // 实时验证结束时间是否大于开始时间
  if (formData.startTime && endTimeStr) {
    const startTime = new Date(formData.startTime);
    const endTime = new Date(endTimeStr);

    if (endTime <= startTime) {
      uni.showToast({
        title: '结束时间必须大于开始时间',
        icon: 'none',
        duration: 2000
      });
    }
  }
};

// 医生选择确认
const doctorConfirm = (e) => {
  const selectedDoctorInfo = formattedDoctorList.value[e[0]];
  formData.doctor = selectedDoctorInfo.id;
  text.doctor = selectedDoctorInfo.displayName;
  selectedDoctor.value = selectedDoctorInfo; // 保存选中的医生信息
};



// 查看位置
const handleSelectLocation = () => {
  console.log('查看位置');
};

// 右侧按钮点击 - 跳转历史记录
const handleRightClick = () => {
  uni.navigateTo({ url: `/pages/consult/history?type=${consultType.value}` });
};

// 提交表单
const submit = () => {
  try {
    formRef.value.validate((valid) => {
      if (valid) {
        submitForm();
      }
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 提交表单数据
const submitForm = async () => {
  uni.showLoading({ title: '提交中...' });
  submiting.value = true;

  try {
    const data = {
      consult_type: config.value.consultType,
      question: formData.description,
      doctor_id: formData.doctor,
      parent_phone: formData.phone,
      consult_date: '',
      reservation_start_date: '',
      reservation_end_date: '',
      reservation_addr: ''
    };

    // 根据咨询类型添加不同的参数
    if (consultType.value === 'online') {
      // 在线咨询只需要基本信息
    } else if (consultType.value === 'phone') {
      data.consult_date = formData.startTime;
      // 电话咨询也需要结束时间，可以用于预约时长
      data.reservation_start_date = formData.startTime;
      data.reservation_end_date = formData.endTime;
    } else if (consultType.value === 'offline') {
      data.reservation_start_date = formData.startTime;
      data.reservation_end_date = formData.endTime;
      data.reservation_addr = formData.location;
    }

    const res = await request({
      url: '/api/parent/consult/add',
      method: 'POST',
      data
    });

    if (res) {
      successShow.value = true;
    }
  } catch (error) {
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'error'
    });
  } finally {
    submiting.value = false;
    uni.hideLoading();
  }
};

// 成功弹窗确认
const handleSuccessConfirm = () => {
  successShow.value = false;
  uni.navigateTo({ url: `/pages/consult/history?type=${consultType.value}` });
};

onLoad((options) => {
  // 获取咨询类型参数
  if (options.type) {
    consultType.value = options.type;
  }

  // 页面加载时获取医生列表
  getDoctorList();
});

onReady(() => {
  formRef.value.setRules(rules.value);
});
</script>

<style lang="scss" scoped>
.page-consult-form {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: #f6f6f6;
  padding-bottom: 160rpx;

  .custom-right {
    padding: 15rpx;
    margin-right: 30rpx;
  }

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 0 24rpx 24rpx;
    display: flex;
    flex-direction: column;
  }

  .select-link {
    color: #2979ff;
    font-size: 26rpx;
  }

  .location-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .location-label {
      font-size: 28rpx;
      color: #333;
      margin-right: 20rpx;
    }

    .location-link {
      color: #2979ff;
      font-size: 26rpx;
    }
  }

  .form-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 32rpx;
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    background: #ffffff;
  }

  .modal-content {
    padding: 48rpx;
    line-height: 1.5;

    .success-title {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      display: block;
      text-align: center;
    }
  }
}
</style>
