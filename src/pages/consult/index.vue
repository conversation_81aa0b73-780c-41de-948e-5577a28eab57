<template>
  <view class="page-consult">
    <u-navbar title="在线咨询" :background="{ background: '#FFFFFF' }" :border-bottom="true" />

    <!-- 在线咨询 -->
    <view class="item item1">
      <view class="title">在线咨询</view>
      <view class="p">专业医师在线解答，随时随地获得专业建议</view>
      <view class="btn-group">
        <view class="btn btn-primary launch-btn" @click="startConsult('online')">
          <u-badge v-if="redDotData.online.launch > 0" :count="redDotData.online.launch" is-center
            size="mini"></u-badge>
          发起咨询 <u-icon name="arrow-right" color="#FFFFFF" size="24" is-center></u-icon>
        </view>
        <view class="btn btn-secondary history-btn" @click="viewHistory('online')">
          <u-badge v-if="redDotData.online.history > 0" :count="redDotData.online.history" is-center
            size="mini"></u-badge>
          历史记录
        </view>
      </view>
    </view>

    <!-- 预约电话咨询 -->
    <view class="item item2">
      <view class="title">预约电话咨询</view>
      <view class="p">预约专家电话咨询，一对一深度交流沟通</view>
      <view class="btn-group">
        <view class="btn btn-primary launch-btn" @click="startConsult('phone')">
          <u-badge v-if="redDotData.phone.launch > 0" :count="redDotData.phone.launch" is-center 
            size="mini"></u-badge>
          发起申请 <u-icon name="arrow-right" color="#FFFFFF" size="24"></u-icon>
        </view>
        <view class="btn btn-secondary history-btn" @click="viewHistory('phone')">
          <u-badge v-if="redDotData.phone.history > 0" :count="redDotData.phone.history" is-center 
            size="mini"></u-badge>
          历史记录
        </view>
      </view>
    </view>

    <!-- 预约线下就诊 -->
    <view class="item item3">
      <view class="title">预约线下就诊</view>
      <view class="p">预约线下面诊服务，享受专业全面的诊疗体验</view>
      <view class="btn-group">
        <view class="btn btn-primary launch-btn" @click="startConsult('offline')">
          <u-badge v-if="redDotData.underline.launch > 0" :count="redDotData.underline.launch" is-center 
            size="mini"></u-badge>
          发起申请 <u-icon name="arrow-right" color="#FFFFFF" size="24"></u-icon>
        </view>
        <view class="btn btn-secondary history-btn" @click="viewHistory('offline')">
          <u-badge v-if="redDotData.underline.history > 0" :count="redDotData.underline.history" is-center 
            size="mini"></u-badge>
          历史记录
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow } from "@dcloudio/uni-app";
import request from "@/request.js";

// 红点数据
const redDotData = ref({
  online: { history: 0, launch: 0 },
  phone: { history: 0, launch: 0 },
  underline: { history: 0, launch: 0 }
});

// 获取咨询红点数据
const getConsultRedDot = async () => {
  try {
    const data = await request({
      url: '/api/parent/consult/list',
      method: 'POST'
    });
    redDotData.value = data;
  } catch (error) {
    console.error('获取红点数据失败:', error);
  }
};

// 发起咨询/申请
const startConsult = (type) => {
  switch (type) {
    case 'online':
      // 跳转到统一咨询预约页面
      uni.navigateTo({ url: `/pages/consult/consult-form?type=online` });
      break;
    case 'phone':
      // 跳转到统一咨询预约页面
      uni.navigateTo({ url: `/pages/consult/consult-form?type=phone` });
      break;
    case 'offline':
      // 跳转到统一咨询预约页面
      uni.navigateTo({ url: `/pages/consult/consult-form?type=offline` });
      break;
  }
};

// 查看历史记录
const viewHistory = (type) => {
  uni.navigateTo({ url: `/pages/consult/history?type=${type}` });
};

onShow(() => {
  // 页面显示时获取红点数据
  getConsultRedDot();
});
</script>

<style lang="scss" scoped>
.page-consult {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;

  .item {
    position: relative;
    margin: 30rpx 30rpx 0;
    padding: 32rpx;
    width: 690rpx;
    border-radius: 20rpx;

    .title {
      font-size: 36rpx;
      color: #262937;
      margin-bottom: 10rpx;
      font-weight: bold;
    }

    .p {
      font-size: 26rpx;
      color: #8D7878;
      margin-bottom: 32rpx;
    }

    .btn-group {
      display: flex;
      gap: 20rpx;
    }

    .btn {
      height: 60rpx;
      line-height: 60rpx;
      border-radius: 30rpx;
      font-size: 26rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 24rpx;


    }

    .btn-primary {
      background: #FF8A8A;
    }

    .btn-secondary {
      background: rgba(255, 138, 138, 0.3);
      color: #FF8A8A;
      border: 2rpx solid #FF8A8A;
    }

    .history-btn {
      position: relative;
    }

    .launch-btn {
      position: relative;
    }
  }

  .item1 {
    background: linear-gradient(135deg, #FFE5E5 0%, #FFF0F0 100%);
    border: 2rpx solid #FFE5E5;

    .btn-primary {
      background: #FF8A8A;
    }

    .btn-secondary {
      background: rgba(255, 138, 138, 0.1);
      color: #FF8A8A;
      border: 2rpx solid #FF8A8A;
    }
  }

  .item2 {
    background: linear-gradient(135deg, #E5F7E8 0%, #F0FFF2 100%);
    border: 2rpx solid #E5F7E8;

    .btn-primary {
      background: #76E98B;
    }

    .btn-secondary {
      background: rgba(118, 233, 139, 0.1);
      color: #76E98B;
      border: 2rpx solid #76E98B;
    }
  }

  .item3 {
    background: linear-gradient(135deg, #E5F3FF 0%, #F0F8FF 100%);
    border: 2rpx solid #E5F3FF;

    .btn-primary {
      background: #70C1F8;
    }

    .btn-secondary {
      background: rgba(112, 193, 248, 0.1);
      color: #70C1F8;
      border: 2rpx solid #70C1F8;
    }
  }
}
</style>