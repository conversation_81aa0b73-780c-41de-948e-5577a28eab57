<template>
  <view class="pages">
    <u-navbar title="成长报告" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="body">
      <scroll-view :scroll-top="0" :refresher-triggered="triggered" :scroll-y="true" class="scroll"
        @scrolltolower="scrolltolower" :refresher-enabled="true" @refresherrefresh="refresh">
        <template v-if="list.length">
          <view class="contain">
            <view class="item" v-for="item in list" :key="item.id" @click="goDetail(item)">
              <view class="left">
                <view class="name">{{ item.name }}</view>
                <view class="time">数据范围：{{ item.begin_time }}-{{ item.end_time }}</view>
              </view>
            </view>
          </view>
        </template>
        <u-empty v-else mode="data" text=" ">
          <template v-slot:bottom>
            <view class="empty">还未创建成长报告</view>
          </template>
        </u-empty>
      </scroll-view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import request from '@/request';
import { onShow } from '@dcloudio/uni-app';
const triggered = ref(false);
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
onShow(() => {
  getData();
});
let per_page = 20;
let page = 1;
const list = ref([])
let hasMore = true;
function getData() {
  uni.showLoading()
  return request({
    url: '/api/parent/growth/getproject',
    data: {
      per_page,
      page,
      class_id: userInfo.class_id,
      student_id: userInfo.student_id
    }
  }).then(res => {
    uni.hideLoading();
    list.value = res;
  })
}
async function refresh() {
  hasMore = true;
  triggered.value = true;
  page = 1;
  await getData();
  triggered.value = false;
}
function scrolltolower() {
  if (hasMore) {
    page++;
    request({
      url: '/api/teach/growth/getproject',
      data: {
        per_page,
        page,
        class_id: curentClassObj.class_id
      }
    }).then(res => {
      if (res.length > 0) {
        list.value.push(...res);
      } else {
        hasMore = false;
      }
    })
  }
}
function goDetail(item) {
  uni.navigateTo({
    url: `/pages/pack/lifereport/report?project_id=${item.id}&student_id=${userInfo.student_id}`
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #F6F6F6;

  .title {
    font-size: 48rpx;
    font-weight: 500;
    color: #111111;
    line-height: 48rpx;
    padding: 40rpx 30rpx;

    .icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .body {
    flex: 1;
    min-height: 1rpx;


    .scroll {
      height: 100%;
      padding: 0 30rpx;
      width: 100vw;
      box-sizing: border-box;

      .empty {
        text-align: center;
        color: #AEB0BC;
      }

      .contain {
        background: #FFFFFF;
        border-radius: 24rpx;
      }

      .item {
        width: 100%;
        padding: 30rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        box-sizing: border-box;
        align-items: center;

        .name {
          font-size: 28rpx;
          font-weight: 500;
          color: #333333;
        }

        .time {
          font-size: 24rpx;
          font-weight: 400;
          color: #AEB0BC;
          margin-top: 8rpx;
        }

        .btn {
          width: 64rpx;
          height: 48rpx;
          background: #F6F6F6;
          border-radius: 32rpx;
          color: #000000;
          text-align: center;
          line-height: 46rpx;
        }
      }
    }
  }
}

.add {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 5;

  .addi {
    width: 112rpx;
    height: 112rpx;
  }
}

.loading {
  width: 100vw;
  height: calc(100vh - 120rpx);

  .des {
    width: 690rpx;
    height: 100rpx;
    left: 30rpx;
    margin-bottom: 40rpx;
    margin-left: 30rpx;
  }
}
</style>