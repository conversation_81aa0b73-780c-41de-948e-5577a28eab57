<template>
  <view class="pages safe-area-inset-bottom" :class="{ pc: isPc }">
    <view class="content">
      <swiper class="scroll-view" :autoplay="false" @change="changeC">
        <swiper-item class="scroll-item">
          <Index :join_date="info.join_date" :name="info.name"></Index>
        </swiper-item>
        <swiper-item class="scroll-item">
          <Base :data="info">
          </Base>
        </swiper-item>
        <swiper-item class="scroll-item">
          <Examination :data="info" ref="exaRef"></Examination>
        </swiper-item>
        <swiper-item class="scroll-item">
          <Attendance :data="info"></Attendance>
        </swiper-item>
        <swiper-item class="scroll-item">
          <Record :data="info"></Record>
        </swiper-item>
        <swiper-item class="scroll-item" v-for="(item, index) in imgList" :key="index">
          <Photos :templateType="item.type" :list="item.list"></Photos>
        </swiper-item>
        <swiper-item class="scroll-item">
          <Creation :list="info.zuopin_list"></Creation>
        </swiper-item>
        <swiper-item class="scroll-item" v-for="item in abilityList" :key="item.type">
          <Ability :list="item.list" :type="item.type"></Ability>
        </swiper-item>
        <swiper-item class="scroll-item">
          <Summarize :data="info"></Summarize>
        </swiper-item>
      </swiper>
    </view>
    <view class="footer">
      <view class="left">
        <image class="icon" :class="{ play: isPlay }" @click="playMusic"
          src="http://obs.tuoyupt.com/miniprogram/report/music.png"></image>
        <text class="text">{{ info.music_name }}</text>
      </view>
      <view class="right">
        <!-- <view v-if="pageIndex == total - 1" class="share">
          分享
        </view> -->
        <view class="dot">
          {{ pageIndex + 1 }}/{{ total }}
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import request from "@/request";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { ref, onMounted, nextTick } from "vue";
import Index from "./components/index.vue";
import Base from "./components/base.vue";
import Examination from "./components/examination.vue";
import Attendance from "./components/attendance.vue";
import Record from "./components/record.vue";
import Photos from "./components/photos.vue";
import Creation from "./components/creation.vue";
import Ability from "./components/ability.vue";
import Summarize from "./components/summarize.vue"
const pageIndex = ref(0);
const total = ref(7);
function changeC(e) {
  pageIndex.value = e.detail.current;
}
let student_id = '';
let project_id = '';
let isTest = false;
const isPc = ref(true)
uni.getSystemInfo({
  success: function (res) {
    console.log(res.model)
    if (res.model === 'PC') {
      isPc.value = true;
    } else {
      isPc.value = false;
    }
  }
});
onLoad((option) => {
  document.title = '成长报告';
  student_id = option.student_id;
  project_id = option.project_id;
  isTest = option.istest === 1 || option.istest === '1';
  getData();
})
function getData() {
  uni.showLoading({
    mask: true
  })
  request({
    url: "/api/parent/growth/getstudentdetail",
    data: {
      student_id: student_id || 33,
      project_id: project_id || 4
    }
  }, isTest).then(async data => {
    data.zuopin_list = data.zuopin_list ? data.zuopin_list.split(',') : [];
    info.value = data;
    document.title = info.value.name + '成长报告';
    if (data.image.length) {
      imgList.value = splitImag(data.image);
    }
    if (info.value.ability_assessment) {
      abilityList.value = getAblit();
    }
    total.value = total.value + imgList.value.length + abilityList.value.length;
    playInit();
    await nextTick()
    exaRef.value.init();
    uni.hideLoading();
  })
}
onMounted(() => {
  // exaRef.value.init();
})
const exaRef = ref(null);
//数据
const imgList = ref([]);
const abilityList = ref([]);
const info = ref({
  join_date: "2020-01-01",
  name: "",
  age: '',
  height: 1,
  weight: 2,
  head_circum: 2,
  right_vision: 1,
  left_vision: 3,
  music_url: "",
  music_name: "",
  attend_total: "",
  exceed_rate: "",
  zuopin_list: []
})
//播放背景音乐
const innerAudioContext = uni.createInnerAudioContext();
function playInit() {
  innerAudioContext.autoplay = true;
  innerAudioContext.loop = true;
  innerAudioContext.src = info.value.music_url;
}
const isPlay = ref(true);
function playMusic() {
  if (isPlay.value) {
    isPlay.value = false;
    innerAudioContext.pause()
  } else {
    isPlay.value = true;
    innerAudioContext.play()
  }
}
onUnload(() => {
  innerAudioContext.destroy();
})
function splitImag(imgs) {
  let arr = [];
  let length = imgs.length;
  let template = photoTemp[length];
  let currentIndex = 0;
  for (let index = 0; index < template.length; index++) {
    let type = template[index];
    let subarr;
    switch (type) {
      case 1:
        subarr = imgs.slice(currentIndex, currentIndex + 1);
        arr.push({
          list: subarr,
          type: 1
        });
        currentIndex += 1;
        break;
      case 2:
        subarr = imgs.slice(currentIndex, currentIndex + 2);
        arr.push({
          list: subarr,
          type: 2
        });
        currentIndex += 2;
        break;
      case 3:
        subarr = imgs.slice(currentIndex, currentIndex + 2);
        arr.push({
          list: subarr,
          type: 3
        });
        currentIndex += 2;
        break;
      case 4:
        subarr = imgs.slice(currentIndex, currentIndex + 3);
        arr.push({
          list: subarr,
          type: 4
        });
        currentIndex += 3;
        break;
      case 5:
        subarr = imgs.slice(currentIndex, currentIndex + 3);
        arr.push({
          list: subarr,
          type: 5
        });
        currentIndex += 3;
        break;
    }
  }
  return arr;
}
const photoTemp = {
  1: [1],
  2: [2],
  3: [4],
  4: [2, 3],
  5: [2, 4],
  6: [4, 5],
  7: [2, 3, 4],
  8: [2, 4, 5],
  9: [4, 5, 5],
  10: [2, 3, 4, 5],
  11: [2, 4, 5, 4],
  12: [2, 3, 2, 4, 5],
  13: [2, 3, 4, 5, 4],
  14: [2, 4, 5, 4, 5],
  15: [4, 5, 4, 5, 4],
}
function getAblit() {
  let ability_assessment = JSON.parse(info.value.ability_assessment);
  let arr = []
  if (info.value.service_id == 1) {
    arr.push({
      type: 1,
      list: [ability_assessment.动作能力, ability_assessment.情感与社会能力]
    })
    arr.push({
      type: 2,
      list: [ability_assessment.认知能力, ability_assessment.生活能力]
    })
    arr.push({
      type: 3,
      list: [ability_assessment.语言能力]
    })
  } else {
    arr.push({
      type: 4,
      list: [ability_assessment.生活自理, ability_assessment.健康体能]
    })
    arr.push({
      type: 5,
      list: [ability_assessment.语言表达, ability_assessment.社会交往]
    })
    arr.push({
      type: 6,
      list: [ability_assessment.美术创造, ability_assessment.音乐综合]
    })
    arr.push({
      type: 7,
      list: [ability_assessment.数学思维, ability_assessment.科学探索]
    })
  }
  return arr;
}
</script>
<style lang="scss" scoped>
.pages {
  font-family: 'MengQu';
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;

  .back {
    position: absolute;
    width: 80rpx;
    line-height: 80rpx;
    height: 80rpx;
    left: 0;
    text-align: center;
    z-index: 9;
  }

  .content {
    flex: 1;
    min-height: 1rpx;
    width: 100%;

    .scroll-view {
      height: 100%;
      width: 100%;

      .scroll-item {
        display: inline-block;
        flex-shrink: 0;
        width: 750rpx;
        height: 100%;
        background-image: url(http://obs.tuoyupt.com/miniprogram/report/gwback.png);
        background-repeat: repeat-y;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }

  .footer {
    width: 750rpx;
    height: 120rpx;
    background: #FFFFFF;
    display: flex;
    flex-direction: row;
    padding: 0 30rpx;
    align-items: center;

    .left {
      display: flex;
      flex-direction: row;
      align-items: center;

      .icon {
        width: 68rpx;
        height: 68rpx;
        filter: grayscale(100%);
      }

      .icon.play {
        filter: grayscale(0%);
        animation: rotation 5s linear infinite;
      }

      @keyframes rotation {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(-360deg);
        }
      }

      .text {
        margin-left: 26rpx;
        color: #2F1B16;
        font-size: 38rpx;
      }
    }

    .right {
      margin-left: auto;

      .share {
        width: 118rpx;
        height: 64rpx;
        background: #351F1E;
        border-radius: 32rpx;
        border: 2rpx solid #2F1B16;
        font-size: 38rpx;
        font-weight: normal;
        color: #FFFFFF;
        line-height: 64rpx;
        text-align: center;
      }

      .dot {
        font-size: 38rpx;
        color: #2F1B16;
      }
    }
  }
}

.pc {
  max-width: 375px;
  margin: 0 auto;
}
</style>