<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/zuopin.png"></image>
    <template v-if="list.length == 1">
      <image mode="aspectFill" class="photo1" :src="list[0]">
      </image>
    </template>
    <template v-if="list.length == 2">
      <image mode="aspectFill" v-for="(item, index) in list" :key="index" :class="['photo2' + (index + 1)]" :src="item">
      </image>
    </template>
    <template v-if="list.length == 3">
      <image mode="aspectFill" v-for="(item, index) in list" :key="index" :class="['photo3' + (index + 1)]" :src="item">
      </image>
    </template>
    <template v-if="list.length == 4">
      <image mode="aspectFill" v-for="(item, index) in list" :key="index" :class="['photo4' + (index + 1)]" :src="item">
      </image>
    </template>
    <template v-if="list.length == 5">
      <image mode="aspectFill" v-for="(item, index) in list" :key="index" :class="['photo5' + (index + 1)]" :src="item">
      </image>
    </template>
  </view>
</template>
<script setup>
const props = defineProps({
  list: {
    type: Array,
  }
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .photo1 {
    position: absolute;
    width: 540rpx;
    height: 840rpx;
    left: 108rpx;
    top: 442rpx;
  }

  .photo21 {
    position: absolute;
    width: 540rpx;
    height: 410rpx;
    left: 108rpx;
    top: 442rpx;
  }

  .photo22 {
    position: absolute;
    width: 540rpx;
    height: 410rpx;
    left: 108rpx;
    top: 868rpx;
  }

  .photo31 {
    position: absolute;
    width: 540rpx;
    height: 410rpx;
    left: 108rpx;
    top: 442rpx;
  }

  .photo32 {
    position: absolute;
    width: 260rpx;
    height: 410rpx;
    left: 108rpx;
    top: 868rpx;
  }

  .photo33 {
    position: absolute;
    width: 260rpx;
    height: 410rpx;
    left: 388rpx;
    top: 868rpx;
  }

  .photo41 {
    position: absolute;
    width: 260rpx;
    height: 410rpx;
    left: 108rpx;
    top: 442rpx;
  }

  .photo42 {
    position: absolute;
    width: 260rpx;
    height: 410rpx;
    left: 388rpx;
    top: 442rpx;
  }

  .photo43 {
    position: absolute;
    width: 260rpx;
    height: 410rpx;
    left: 108rpx;
    top: 868rpx;
  }

  .photo44 {
    position: absolute;
    width: 260rpx;
    height: 410rpx;
    left: 388rpx;
    top: 868rpx;
  }

  .photo51 {
    position: absolute;
    width: 540rpx;
    height: 286rpx;
    left: 108rpx;
    top: 442rpx;
  }

  .photo52 {
    position: absolute;
    width: 260rpx;
    height: 260rpx;
    left: 108rpx;
    top: 748rpx;
  }

  .photo53 {
    position: absolute;
    width: 260rpx;
    height: 180rpx;
    left: 388rpx;
    top: 748rpx;
  }

  .photo54 {
    position: absolute;
    width: 260rpx;
    height: 260rpx;
    left: 108rpx;
    top: 1028rpx;
  }

  .photo55 {
    position: absolute;
    width: 260rpx;
    height: 340rpx;
    left: 388rpx;
    top: 948rpx;
  }
}
</style>