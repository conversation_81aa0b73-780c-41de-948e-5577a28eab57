<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/index.png"></image>
    <view class="t">
      <view>{{ date }}，很幸运遇见了你~</view>
      <view>我们已携手渡过了{{ total }}天的美好时光~</view>
      <view>今后也要给予你</view>
      <view>足够的爱与精心的照护…</view>
    </view>
    <view class="name">{{ name }}</view>
    <view class="fanye" v-if="showTips">
      <image class="icon" src="http://obs.tuoyupt.com/miniprogram/report/zyhd.png"></image>左右滑动翻页
    </view>
  </view>
</template>
<script setup>
import dayjs from 'dayjs';
import { computed } from 'vue';
const props = defineProps({
  join_date: String,
  name: String,
  showTips: Boolean
})
const date = computed(() => {
  return dayjs(props.join_date).format("YYYY年MM月DD日")
})
const total = computed(() => {
  return dayjs().diff(dayjs(props.join_date), 'days');
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .fanye {
    width: 242rpx;
    height: 80rpx;
    background: rgba(0, 0, 0, 0.65);
    border-radius: 40rpx;
    font-size: 22rpx;
    font-weight: bold;
    color: #FFFFFF;
    line-height: 80rpx;
    text-align: center;
    position: absolute;
    left: 254rpx;
    top: 1240rpx;
    font-family: SourceHanSansCN;

    .icon {
      width: 52rpx;
      height: 36rpx;
      vertical-align: text-bottom;
      margin-right: 4rpx;
    }
  }

  .t {
    position: absolute;
    color: #2F1B16;
    font-size: 32rpx;
    transform: rotate(15deg);
    width: 700rpx;
    line-height: 58rpx;
    left: 50rpx;
    top: 880rpx;
  }

  .name {
    position: absolute;
    color: #009FA9;
    font-size: 44rpx;
    transform: rotate(15deg);
    width: 558rpx;
    line-height: 58rpx;
    left: 70rpx;
    top: 1126rpx;
    letter-spacing: 8rpx;
  }
}
</style>