<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/baseback.png"></image>
    <view class="t t1"><text class="label">姓名</text>{{ data.name }}</view>
    <view class="t t2"><text class="label">年龄</text>{{ data.age }}</view>
    <view class="t t3"><text class="label">星座</text>{{ data.star }}</view>
    <view class="t t4"><text class="label">入园时间</text>{{ data.join_date }}</view>
    <view class="text">{{ data.summary }}</view>
  </view>
</template>
<script setup>
defineProps({
  data: Object
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .t {
    position: absolute;
    color: #351F1E;
    width: 558rpx;
    height: 44rpx;
    font-size: 36rpx;

    .label {
      color: #989898;
      margin-right: 18rpx;
    }
  }

  .t1 {
    left: 350rpx;
    top: 460rpx;
  }

  .t2 {
    left: 350rpx;
    top: 520rpx;
  }

  .t3 {
    left: 350rpx;
    top: 580rpx;
  }

  .t4 {
    left: 350rpx;
    top: 640rpx;
  }

  .text {
    width: 518rpx;
    height: 428rpx;
    font-size: 32rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 38rpx;
    left: 132rpx;
    position: absolute;
    top: 1080rpx;
    word-break: break-all;
  }
}
</style>