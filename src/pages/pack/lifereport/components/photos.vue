<template>
  <view class="items">
    <template v-if="templateType == 1">
      <image class="zhus zhus1" src="http://obs.tuoyupt.com/miniprogram/report/zhus.png"></image>
      <image class="ptit ptit1" src="http://obs.tuoyupt.com/miniprogram/report/ptit.png"></image>
      <image class="zpiank zpiank1" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo1" :src="list[0].url">
      </image>
      <image class="tuding tuding1" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk1" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk1">
        <view class="t">
          {{ list[0].description }}
        </view>
        <view class="time">
          {{ dayjs(list[0].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
    </template>
    <template v-if="templateType == 2">
      <image class="zhus zhus1" src="http://obs.tuoyupt.com/miniprogram/report/zhus.png"></image>
      <image class="ptit ptit2" src="http://obs.tuoyupt.com/miniprogram/report/ptit.png"></image>
      <image class="zpiank zpiank21" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo21" :src="list[0].url">
      </image>
      <image class="tuding tuding21" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk21" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk21">
        <view class="t">
          {{ list[0].description }}
        </view>
        <view class="time">
          {{ dayjs(list[0].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
      <image class="zpiank zpiank22" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo22" :src="list[1].url">
      </image>
      <image class="tuding tuding22" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk22" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk22">
        <view class="t">
          {{ list[1].description }}
        </view>
        <view class="time">
          {{ dayjs(list[1].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
    </template>
    <template v-if="templateType == 3">
      <image class="zhus zhus3" src="http://obs.tuoyupt.com/miniprogram/report/zhus.png"></image>
      <image class="ptit ptit3" src="http://obs.tuoyupt.com/miniprogram/report/ptit.png"></image>
      <image class="zpiank zpiank31" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo31" :src="list[0].url">
      </image>
      <image class="tuding tuding31" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk31" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk31">
        <view class="t">
          {{ list[0].description }}
        </view>
        <view class="time">
          {{ dayjs(list[0].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
      <image class="zpiank zpiank32" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo32" :src="list[1].url">
      </image>
      <image class="tuding tuding32" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk32" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk32">
        <view class="t">
          {{ list[1].description }}
        </view>
        <view class="time">
          {{ dayjs(list[1].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
    </template>
    <template v-if="templateType == 4">
      <image class="ptit ptit4" src="http://obs.tuoyupt.com/miniprogram/report/ptit.png"></image>
      <image class="zpiank zpiank41" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo41" :src="list[0].url">
      </image>
      <image class="tuding tuding41" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk41" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk41">
        <view class="t">
          {{ list[0].description }}
        </view>
        <view class="time">
          {{ dayjs(list[0].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
      <image class="zpiank zpiank42" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo42" :src="list[1].url">
      </image>
      <image class="tuding tuding42" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk42" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk42">
        <view class="t">
          {{ list[1].description }}
        </view>
        <view class="time">
          {{ dayjs(list[1].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
      <image class="zpiank zpiank43" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo43" :src="list[2].url">
      </image>
      <image class="tuding tuding43" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk43" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk43">
        <view class="t">
          {{ list[2].description }}
        </view>
        <view class="time">
          {{ dayjs(list[2].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
    </template>
    <template v-if="templateType == 5">
      <image class="ptit ptit5" src="http://obs.tuoyupt.com/miniprogram/report/ptit.png"></image>
      <image class="zpiank zpiank51" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo51" :src="list[0].url">
      </image>
      <image class="tuding tuding51" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk51" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk51">
        <view class="t">
          {{ list[0].description }}
        </view>
        <view class="time">
          {{ dayjs(list[0].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
      <image class="zpiank zpiank52" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo52" :src="list[1].url">
      </image>
      <image class="tuding tuding52" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk52" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk52">
        <view class="t">
          {{ list[1].description }}
        </view>
        <view class="time">
          {{ dayjs(list[1].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
      <image class="zpiank zpiank53" src="http://obs.tuoyupt.com/miniprogram/report/zpiank.png"></image>
      <image mode="aspectFill" class="photo photo53" :src="list[2].url">
      </image>
      <image class="tuding tuding53" src="http://obs.tuoyupt.com/miniprogram/report/tud.png">
      </image>
      <image class="shuimk shuimk53" src="http://obs.tuoyupt.com/miniprogram/report/shuimk.png">
      </image>
      <view class="text shuimk53">
        <view class="t">
          {{ list[2].description }}
        </view>
        <view class="time">
          {{ dayjs(list[2].created_at).format("YYYY-MM-DD") }}
        </view>
      </view>
    </template>
  </view>
</template>
<script setup>
import dayjs from 'dayjs';
defineProps({
  templateType: {
    type: Number,
    default: 5
  },
  list: Array
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .zhus {
    position: absolute;
    width: 220rpx;
    height: 220rpx;
  }

  .zhus1 {
    left: 404rpx;
    top: 102rpx;
  }

  .zhus3 {
    left: 104rpx;
    top: 102rpx;
  }



  .ptit {
    width: 566rpx;
    height: 202rpx;
    position: absolute;
  }

  .ptit1 {
    left: 84rpx;
    top: 286rpx;
    transform: rotate(15deg);
  }

  .ptit2 {
    left: 30rpx;
    top: 190rpx;
    transform: rotate(15deg);
  }

  .ptit3 {
    left: 170rpx;
    top: 212rpx;
    transform: rotate(-15deg);
  }

  .ptit4 {
    left: 100rpx;
    top: 60rpx;
    transform: rotate(15deg);
  }

  .ptit5 {
    left: 40rpx;
    top: 60rpx;
    transform: rotate(-15deg);
  }

  .zpiank {
    position: absolute;

  }

  .photo {
    position: absolute;
  }

  .zpiank1 {
    left: 120rpx;
    top: 550rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(-15deg);
  }

  .zpiank21 {
    left: 223rpx;
    top: 381rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(-15deg);
  }

  .zpiank31 {
    left: 30rpx;
    top: 381rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(4deg);
  }

  .zpiank41 {
    left: 21rpx;
    top: 200rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(15deg);
  }

  .zpiank51 {
    left: 222rpx;
    top: 213rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(-15deg);
  }

  .zpiank53 {
    left: 100rpx;
    top: 900rpx;
    width: 504rpx;
    height: 512rpx;
  }

  .photo41 {
    left: 65rpx;
    top: 220rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(15deg);
  }

  .photo51 {
    left: 259rpx;
    top: 250rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(-16deg);
  }

  .photo53 {
    left: 144rpx;
    top: 930rpx;
    width: 420rpx;
    height: 420rpx;
  }

  .zpiank22 {
    left: 21rpx;
    top: 879rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(15deg);
  }

  .zpiank32 {
    left: 200rpx;
    top: 879rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(-15deg);
  }

  .zpiank42 {
    left: 215rpx;
    top: 520rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(15deg);
  }

  .zpiank43 {
    left: 100rpx;
    top: 879rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(-15deg);
  }

  .zpiank52 {
    left: 15rpx;
    top: 560rpx;
    width: 504rpx;
    height: 512rpx;
    transform: rotate(15deg);
  }

  .photo1 {
    left: 156rpx;
    top: 570rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(-16deg);
  }

  .photo21 {
    left: 260rpx;
    top: 410rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(-16deg);
  }

  .photo31 {
    left: 73rpx;
    top: 418rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(4deg);
  }

  .photo22 {
    left: 65rpx;
    top: 900rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(15deg);
  }

  .photo42 {
    left: 260rpx;
    top: 550rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(15deg);
  }

  .photo52 {
    left: 60rpx;
    top: 590rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(15deg);
  }

  .photo43 {
    left: 149rpx;
    top: 904rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(-16deg);
  }

  .photo32 {
    left: 241rpx;
    top: 906rpx;
    width: 420rpx;
    height: 420rpx;
    transform: rotate(-16deg);
  }

  .tuding {
    position: absolute;
    width: 66rpx;
    height: 106rpx;
  }

  .tuding1 {
    left: 455rpx;
    top: 495rpx;
  }

  .tuding21 {
    left: 571rpx;
    top: 314rpx;
  }

  .tuding31 {
    left: 23rpx;
    top: 313rpx;
  }

  .tuding22 {
    left: 100rpx;
    top: 790rpx;
    transform: scaleX(-1);
  }

  .tuding32 {
    left: 588rpx;
    top: 773rpx;
    transform: scaleX(-1);
  }

  .tuding41 {
    left: 90rpx;
    top: 112rpx;
  }

  .tuding42 {
    left: 320rpx;
    top: 420rpx;
    transform: scaleX(-1);
  }

  .tuding52 {
    left: 124rpx;
    top: 460rpx;
    transform: scaleX(-1);
  }

  .tuding43 {
    left: 490rpx;
    top: 780rpx;
    transform: scaleX(-1);
  }

  .tuding51 {
    left: 560rpx;
    top: 120rpx;
  }

  .tuding53 {
    left: 344rpx;
    top: 849rpx;
    transform: scaleX(-1);
  }

  .shuimk {
    width: 254rpx;
    height: 259rpx;
    position: absolute;
    z-index: 2;
  }

  .shuimk1 {
    left: 276rpx;
    top: 1050rpx;
  }

  .shuimk21 {
    left: 30rpx;
    top: 450rpx;
  }

  .shuimk31 {
    left: 460rpx;
    top: 450rpx;
  }

  .shuimk22 {
    left: 466rpx;
    top: 1114rpx;
  }

  .shuimk32 {
    left: 30rpx;
    top: 1080rpx;
  }

  .shuimk41 {
    left: 466rpx;
    top: 250rpx;
  }

  .shuimk42 {
    left: 30rpx;
    top: 680rpx;

  }

  .shuimk51 {
    left: 30rpx;
    top: 250rpx;

  }

  .shuimk52 {
    left: 466rpx;
    top: 680rpx;
  }

  .shuimk53 {
    left: 466rpx;
    top: 1100rpx;
  }

  .shuimk43 {
    left: 466rpx;
    top: 1100rpx;
  }

  .text {
    position: absolute;
    display: flex;
    padding: 30rpx;
    box-sizing: border-box;
    flex-direction: column;
    width: 254rpx;
    height: 259rpx;
    z-index: 2;

    .t {
      flex: 1;
    }
  }

  .text1 {
    width: 254rpx;
    height: 259rpx;
    left: 276rpx;
    top: 1050rpx;
  }

}
</style>