<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/shenghuojil.png"></image>
    <view class="text t1">
      平均每天喝水{{ data.avg_water }}ml
    </view>
    <view class="text t2">
      平均每天喝奶{{ data.avg_milk }}ml
    </view>
    <view class="text t3">
      <view>平均每日大便{{ data.avg_bigtoilet }}次</view>
      <view>小便{{ data.avg_smalltoilet }}次</view>
    </view>
    <view class="text t4">
      平均每天睡眠{{ data.avg_sleep }}分钟
    </view>
  </view>
</template>
<script setup>
defineProps({
  data: Object
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .text {
    width: 386rpx;
    font-size: 36rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 66rpx;
    position: absolute;
    left: 230rpx;

  }

  .t1 {
    top: 500rpx;
  }

  .t2 {
    top: 665rpx;
  }

  .t3 {
    top: 846rpx;
  }

  .t4 {
    top: 1070rpx;
  }
}
</style>