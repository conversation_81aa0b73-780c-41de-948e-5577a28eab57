<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/kaoqingback.png"></image>
    <view class="zongjie">
      <view>宝宝累计出勤 <text class="b">{{ data.attend_total }}天</text></view>
      <view>超过了全班{{ data.exceed_rate }}%的小朋友</view>
    </view>
  </view>
</template>
<script setup>
defineProps({
  data: Object
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .zongjie {
    width: 500rpx;
    font-size: 36rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 66rpx;
    position: absolute;
    left: 150rpx;
    top: 680rpx;

    .b {
      font-size: 48rpx;
    }
  }

}
</style>