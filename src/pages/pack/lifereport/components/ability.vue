<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/nengli.png"></image>
    <view class="sec s1">
      <image class="timg" :src="`http://obs.tuoyupt.com/miniprogram/report/${imgT[0]}.png`"></image>
      <view class="text">{{ list[0] }}</view>
    </view>
    <view class="sec s2" v-if="type !== 3">
      <image class="timg" :src="`http://obs.tuoyupt.com/miniprogram/report/${imgT[1]}.png`"></image>
      <view class="text">{{ list[1] }}</view>
    </view>
  </view>
</template>
<script setup>
import { computed } from 'vue';
const props = defineProps({
  list: {
    type: Array,
  },
  type: {
    type: Number,
    default: 1
  },
})
let tempEm = {
  1: ['nt1', 'nt2'],
  2: ['nt3', 'nt4'],
  3: ['nt5', ''],
  4: ['ny1', 'ny2'],
  5: ['ny3', 'ny4'],
  6: ['ny5', 'ny6'],
  7: ['ny7', 'ny8'],
}
const imgT = computed(() => {
  return tempEm[props.type]
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .s1 {
    left: 86rpx;
    top: 368rpx;
  }

  .s2 {
    left: 86rpx;
    top: 764rpx;
  }

  .sec {
    position: absolute;

    .timg {
      width: 490rpx;
      height: 165rpx;
    }

    .text {
      width: 576rpx;
      height: 176rpx;
      font-size: 36rpx;
      font-weight: normal;
      color: #351F1E;
      line-height: 44rpx;
      word-break: break-all;
      overflow: auto;
    }
  }

}
</style>