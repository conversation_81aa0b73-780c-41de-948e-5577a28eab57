<template>
  <view class="items">
    <image class="back" src="http://obs.tuoyupt.com/miniprogram/report/tjda.png"></image>
    <view class="zongjie">
      <template v-if="isFirst">
        <view>{{ msg1 }}</view>
        <view>{{ msg2 }}</view>
      </template>
      <template v-else>
        <view>入园后，</view>
        <view>宝宝身高{{ msg1 }}</view>
        <view>{{ msg2 }}</view>
        <view>{{ msg3 }}</view>
      </template>
    </view>
    <view class="miaoshu">
      <view v-for="(item, index) in list" class="item" :key="index">
        <image class="img" :src="item.img"></image>
        <view class="text">{{ item.text }}</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
const props = defineProps({
  data: {
    type: Object,
  }
})

defineExpose({
  init
})
const list = ref([])
const isFirst = ref(true);
const msg1 = ref('')
const msg2 = ref('')
const msg3 = ref('')

function init() {
  let examination = props.data.examination;
  let height = props.data.height;
  let weight = props.data.weight;
  let head_circum = props.data.head_circum;
  let first_weight = props.data.first_weight;
  let first_heihgt = props.data.first_heihgt;
  let first_head_circum = props.data.first_head_circum;

  if (Number(examination) > 1) {
    isFirst.value = false;
    let diff_height = Number(height) - Number(first_heihgt);
    if (diff_height > 0) {
      msg1.value = '长高了' + diff_height.toFixed(1) + 'cm，';
    } else if (diff_height < 0) {
      msg1.value = '长矮了' + Math.abs(diff_height).toFixed(1) + 'cm，';
    } else {
      msg1.value = '身高无变化，';
    }
    let diff_weight = Number(weight) - Number(first_weight);
    if (diff_weight > 0) {
      msg2.value = '体重重了' + diff_weight.toFixed(1) + 'kg，';
    } else if (diff_weight < 0) {
      msg2.value = '体重轻了' + Math.abs(diff_weight).toFixed(1) + 'kg，';
    } else {
      msg2.value = '体重无变化，';
    }
    let diff_head_circum = Number(head_circum) - Number(first_head_circum);
    if (diff_head_circum > 0) {
      msg3.value = '头围增长了' + diff_head_circum.toFixed(1) + 'cm。';
    } else if (diff_head_circum < 0) {
      msg3.value = '头围减少了' + Math.abs(diff_head_circum).toFixed(1) + 'cm。';
    } else {
      msg3.value = '头围与上次测量相同。';
    }
  } else {
    msg1.value = '这是宝宝的第一条体检记录~';
    msg2.value = '从这条开始见证宝宝的健康成长！';
  }
  if (props.data.height) {
    list.value.push({
      img: "http://obs.tuoyupt.com/miniprogram/report/sg.png",
      text: `${props.data.height}cm`
    })
  }
  if (props.data.weight) {
    list.value.push({
      img: "http://obs.tuoyupt.com/miniprogram/report/tz.png",
      text: `${props.data.weight}kg`
    })
  }
  if (props.data.left_vision &&
    props.data.left_vision != '-' &&
    props.data.right_vision &&
    props.data.left_vision != '-') {
    list.value.push({
      img: "http://obs.tuoyupt.com/miniprogram/report/sl.png",
      text: `左眼${props.data.left_vision}，右眼${props.data.right_vision}`
    })
  }
  if (props.data.head_circum) {
    list.value.push({
      img: "http://obs.tuoyupt.com/miniprogram/report/tw.png",
      text: `${props.data.head_circum}cm`
    })
  }
  if (props.data.weight && props.data.height) {
    list.value.push({
      img: "http://obs.tuoyupt.com/miniprogram/report/bmi.png",
      text: getBMI()
    })
  }
}
function getBMI() {
  return (Number(props.data.weight) / Math.pow(Number(props.data.height) / 100, 2)).toFixed(2)
}
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;

  .back {
    position: absolute;
    left: 0;
    top: 0;
    width: 750rpx;
    height: 1624.18rpx;
  }

  .zongjie {
    width: 458rpx;
    height: 176rpx;
    font-size: 36rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 44rpx;
    position: absolute;
    left: 100rpx;
    top: 440rpx;
  }

  .miaoshu {
    position: absolute;
    left: 100rpx;
    top: 700rpx;
    display: flex;
    flex-direction: column;

    .item {
      position: relative;
      margin-bottom: 20rpx;

      .text {
        position: absolute;
        width: 360rpx;
        height: 48rpx;
        left: 220rpx;
        top: -10rpx;
        font-size: 40rpx;
      }

      .img {
        width: 530rpx;
        height: 48rpx;
      }
    }

  }
}
</style>