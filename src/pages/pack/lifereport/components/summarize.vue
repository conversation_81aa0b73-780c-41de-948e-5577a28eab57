<template>
  <view class="items">
    <view class="title">在园总结</view>
    <view class="t t1">
      宝宝在托<text class="num">{{ data.join_day }}</text>天，考勤率高达<text class="num">{{ data.attend_rate }}%</text>.
    </view>
    <view class="t t2">
      <view>在这里，宝宝认识了<text class="num">{{ data.friend }}</text>位小朋友，</view>
      <view>
        你们一起在{{ data.class_name ? data.class_name.replace('班', '') : '' }}班学习和生活...</view>
      <view>
        一起入园，一起游戏...
      </view>
      <view>
        一起吃饭、一起唱歌跳舞...
      </view>
    </view>
    <view class="t t3">
      <view>
        宝宝在托这段时间
      </view>
      <view>
        保健员为宝宝进行了<text class="num">{{ data.examination }}</text>次全面的体检，身体非常健康...
      </view>
      <view class="mt">
        保育员精心记录了<text class="num">{{ data.life_record }}</text>条宝宝在托生活记录，详尽记录了在托的日常生活。
      </view>
      <view class="mt">
        爸爸妈妈共收到了<text class="num">{{ data.class_circle }}</text>条班级动态，其中包含了宝宝的<text class="num">{{
        data.class_circle_zuopin }}</text>幅作品，展示了他们的创造力和想象力...
      </view>
      <view>
        宝宝的成长时光被点赞了<text class="num">{{ data.be_zan }}</text>次，产生了<text class="num">{{ data.be_comment
          }}</text>条互动留言，大家对宝宝都非常喜爱。
      </view>
    </view>
    <view class="text">
      未完待续。。。
    </view>
    <image class="huahua" src="http://obs.tuoyupt.com/miniprogram/report/huahau.png"></image>
  </view>
</template>
<script setup>
defineProps({
  data: Object
})
</script>
<style scoped lang="scss">
.items {
  position: relative;
  width: 750rpx;
  height: 100%;
  background: #FFF9EA;

  .title {
    position: absolute;
    width: 100%;
    height: 76rpx;
    text-align: center;
    font-size: 64rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 76rpx;
    top: 176rpx;
  }

  .t {
    position: absolute;
    width: 690rpx;
    font-size: 36rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 44rpx;
    left: 30rpx;

    .num {
      font-size: 44rpx;
      position: relative;
      display: inline-block;
      z-index: 2;
      padding: 0 4rpx;

      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 100%;
        height: 20rpx;
        background: #FFB9B9;
        border-radius: 16rpx;
        z-index: -1;
        left: 0;
        bottom: 0;
      }
    }

    .mt {
      margin-top: 34rpx;
    }
  }

  .t1 {
    top: 280rpx;
  }

  .t2 {
    top: 372rpx;
  }

  .t3 {
    top: 608rpx;
  }

  .huahua {
    width: 206rpx;
    height: 206rpx;
    position: absolute;
    left: 494rpx;
    top: 1204rpx;
  }

  .text {
    width: 350rpx;
    height: 60rpx;
    font-size: 50rpx;
    font-weight: normal;
    color: #351F1E;
    line-height: 60rpx;
    left: 30rpx;
    position: absolute;
    top: 1252rpx;
  }
}
</style>