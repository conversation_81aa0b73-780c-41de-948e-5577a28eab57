<template>
  <view class="pages">
    <u-navbar title="问卷调查" ref="navbar" :background="{ background: '#F6F6F6' }" :border-bottom="false"></u-navbar>
    <view class="body">
      <scroll-view :scroll-top="0" :refresher-triggered="triggered" :scroll-y="true" class="scroll"
        @scrolltolower="scrolltolower" :refresher-enabled="true" @refresherrefresh="refresh">
        <template v-if="list.length">
          <view class="item" v-for="item in list" :key="item.id">
            <view class="r1">
              <view class="left">
                <view class="title">{{ item.title }}</view>
                <view class="text">{{ item.desc }}</view>
              </view>
              <view class="right">
                {{ item.school_id ? "机构发布" : "政府发布" }}
              </view>
            </view>
            <view class="r2">
              <view class="time">截止日期 {{ item.end_time ? item.end_time : "不限" }}</view>
              <view class="btn" :class="{ dis: item.is_read || isOver(item) }" @click.stop="showAction(item)">
                {{ item.is_read ? '已提交' : isOver(item) ? '已截止' : '去答卷' }}
              </view>
            </view>
          </view>
        </template>
        <u-empty v-else mode="data" text="暂无问卷">
        </u-empty>
      </scroll-view>
    </view>
  </view>
  <u-popup v-model="popShow" width="690rpx" height="1000rpx" :mask-close-able="true" :border-radius="40" mode="center">
    <view class="pop-content">
      <view class="title">问卷调查隐私协议</view>
      <scroll-view scroll-y style="height: 700rpx;">
        <text class="text">
          尊敬的用户

          感谢您参与我们的问卷调查。我们非常重视您的隐私，并致力于保护您的个人信息安全。本隐私协议政策旨在向您说明我们在问卷调查过程中如何收集、使用、存储和保护您的个人信息。\n

          **一、信息收集**\n
          1. 我们仅收集与问卷调查目的相关的必要信息，例如您的基本人口统计信息、观点和意见等。\n
          2. 收集信息的方式将通过问卷中的明确提问进行。\n

          **二、信息使用**\n
          1. 您提供的信息将仅用于进行数据分析和研究，以帮助我们改进产品、服务或了解市场趋势。\n
          2. 我们不会将您的个人信息用于任何商业营销目的。\n

          **三、信息存储**\n
          1. 您的信息将被安全地存储在我们的系统中，采取合理的技术和组织措施确保其安全性。\n
          2. 我们将按照适用的法律法规规定的期限保存您的信息。\n

          **四、信息共享**\n
          1. 我们不会与任何第三方共享您的个人信息，除非在法律要求或为了完成问卷调查目的必须的情况下。\n
          2. 在任何情况下，我们都会确保信息共享遵循严格的安全和保密标准。\n

          **五、您的权利**\n
          1. 您有权随时查看、更正或删除您提供的个人信息。\n
          2. 如果您对我们的隐私政策或信息处理方式有任何疑问或担忧，您可以随时与我们联系。\n

          **六、隐私政策更新**\n
          我们可能会不时更新本隐私协议政策，以适应法律、技术或业务的变化。更新后的政策将在我们的网站上公布。\n

          通过参与问卷调查，您表示同意本隐私协议政策。如果您不同意本政策，请不要参与问卷调查。\n

          再次感谢您的支持与合作。\n

          [家长使用端]\n
          [2024.05.30]
        </text>
      </scroll-view>
      <u-checkbox v-model="checkValue" shape="circle" icon-size="25">
        <view class="main-check flex-mode">
          已阅读并同意问卷调查隐私协议
        </view>
      </u-checkbox>
      <view class="btns" @click="goDetail">
        填写问卷
      </view>
    </view>
  </u-popup>

</template>
<script setup>
import { ref } from 'vue';
import request from '@/request';
import { onShow } from '@dcloudio/uni-app';
import { useUserStore } from "@/stores/userInfo.js";
import dayjs from 'dayjs';
const userInfo = useUserStore();
const triggered = ref(false);

onShow(() => {
  getData();
})
const popShow = ref(false);
const checkValue = ref(false);
let per_page = 20;
let page = 1;
const list = ref([])
let hasMore = true;
function getData() {
  uni.showLoading()
  return request({
    url: '/api/parent/jyform/list',
    data: {
      per_page,
      page,
      school_id: userInfo.school_id
    }
  }).then(res => {
    uni.hideLoading();
    list.value = res;
    hasMore = !list.length < per_page;
  })
}
function isOver(item) {
  return item.end_time ? dayjs().isAfter(dayjs(item.end_time)) : false;
}
let info = {};
function showAction(item) {
  info = JSON.parse(JSON.stringify(item));
  popShow.value = true;
  checkValue.value = false;
}
async function refresh() {
  hasMore = true;
  triggered.value = true;
  page = 1;
  await getData();
  triggered.value = false;
}
function scrolltolower() {
  if (hasMore) {
    page++;
    request({
      url: '/api/teach/jyform/list',
      data: {
        per_page,
        page,
        school_id: userInfo.school_id
      }
    }).then(res => {
      list.value.push(...res);
      hasMore = !list.length < per_page;
    })
  }
}
function goDetail() {
  if (checkValue.value) {
    request({
      url: `/api/parent/jyform/detail`,
      data: {
        id: info.id
      }
    }).then(res => {
      request({
        url: `/api/parent/studentphoto/studentdetail/${userInfo.student_id}`
      }).then(res => {
        let school_name = res.school_name ? res.school_name : '';
        let surl = `/h5/wenjuan/index.html?id=${info.id}&page=form&n=${userInfo.nickname}&p=${userInfo.phone}&s=${school_name}`
        popShow.value = false;
        window.location.assign(surl)
      })
    })
  } else {
    uni.showToast({
      title: "请先阅读并勾选协议",
      icon: "none"
    })
  }
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #F6F6F6;

  .body {
    flex: 1;
    min-height: 1rpx;


    .scroll {
      height: 100%;
      padding: 0 30rpx;
      width: 100vw;
      box-sizing: border-box;

      .empty {
        text-align: center;
        color: #AEB0BC;
      }


      .item {
        width: 100%;
        box-sizing: border-box;
        align-items: center;
        background: #FFFFFF;
        border-radius: 24rpx;
        padding: 24rpx;
        margin-bottom: 20rpx;

        .r2 {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          margin-top: 24rpx;

          .time {
            color: #617EFB;
            font-size: 24rpx;
          }

          .btn {
            width: 120rpx;
            height: 40rpx;
            text-align: center;
            line-height: 40rpx;
            color: #FFFFFF;
            background: #617EFB;
            border-radius: 20rpx;
            font-size: 24rpx;
          }

          .dis {
            background: #AEB0BC;
            pointer-events: none;
          }
        }

        .r1 {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;
          border-bottom: 1rpx solid #EEEEEE;
          padding-bottom: 30rpx;

          .left {
            flex: 1;
            min-width: 1rpx;

            .title {
              width: 100%;
              word-break: break-all;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              font-size: 32rpx;
              line-height: 36rpx;
              font-weight: 600;
              overflow: hidden;
            }

            .text {
              width: 100%;
              word-break: break-all;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              font-size: 26rpx;
              line-height: 32rpx;
              margin-top: 20rpx;
              overflow: hidden;
            }
          }

          .right {
            width: 120rpx;
            text-align: right;
            font-size: 28rpx;
            color: #617EFB;
          }
        }
      }
    }
  }
}

.add {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 5;

  .addi {
    width: 112rpx;
    height: 112rpx;
  }
}

.loading {
  width: 100vw;
  height: calc(100vh - 120rpx);

  .des {
    width: 690rpx;
    height: 100rpx;
    left: 30rpx;
    margin-bottom: 40rpx;
    margin-left: 30rpx;
  }
}

.pop-content {
  padding: 40rpx;

  .title {
    font-size: 40rpx;
    text-align: center;
    line-height: 80rpx;
    font-weight: 600;
  }

  .text {
    font-size: 28rpx;
    line-height: 40rpx;
  }

  .btns {
    width: 80%;
    height: 60rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    background: #5F7CFC;
    text-align: center;
    line-height: 60rpx;
    color: #FFF;
    margin: 40rpx auto 20rpx;
  }
}
</style>