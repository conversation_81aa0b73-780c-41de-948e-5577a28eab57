<template>
  <view class="pages">
    <u-navbar :immersive="true" title-color="#FFFFFF" back-icon-color="#FFFFFF"
      :background="{ background: 'rgba(255, 255, 255, 0)' }" ref="navbar" :border-bottom="false" title="录入人脸">
    </u-navbar>
    <view class="title">请单人正脸出镜，头部保持在取景框内</view>
    <view class="camera">
      <video ref="video" style="object-fit: fill;width: 600rpx; height: 600rpx;" :show-center-play-btn="false" muted
        preload :controls="false"></video>
    </view>
    <view class="tishi">
      <view class="yy">
        <view>
          <u-icon name="checkmark-circle-fill" size="42" color="#1ED074"></u-icon>
        </view>
        <view class="t1">请您面对屏幕</view>
      </view>
      <view class="yy">
        <view>
          <u-icon name="checkmark-circle-fill" size="42" color="#1ED074"></u-icon>
        </view>
        <view class="t1">保证脸部无遮挡</view>
      </view>
      <view class="yy">
        <view>
          <u-icon name="checkmark-circle-fill" size="42" color="#1ED074"></u-icon>
        </view>
        <view class="t1">保持光线充足</view>
      </view>
    </view>
    <view class="footer">
      <view class="btn" @click="takePhoto">
        <view class="b"></view>
      </view>
      <view class="btn2" @click="changePosition">
        <image class="img" src="https://obs.tuoyupt.com/miniprogram/client/images/qiehuan.png"></image>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
const constraints = {
  audio: false,
  video: {
    width: 600,
    height: 600,
    facingMode: "user"
  }
}
let video = ref(null)

/**
* 使用摄像头
*/
function useCamera() {
  if (window.stream) {
    window.stream.getTracks().forEach(track => {
      track.stop();
    });
  }

  // 如果不是通过loacalhost或者通过https访问会将报错捕获并提示
  try {
    if (navigator.mediaDevices === undefined) {
      navigator.mediaDevices = {};
    }
    if (navigator.mediaDevices.getUserMedia === undefined) {
      navigator.mediaDevices.getUserMedia = function (constraints) {
        var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia || navigator.oGetUserMedia;
        if (!getUserMedia) {
          return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
        }
        return new Promise(function (resolve, reject) {
          getUserMedia.call(navigator, constraints, resolve, reject);
        });
      }
    }

    let promise = navigator.mediaDevices.getUserMedia(constraints);
    promise.then((stream) => {
      window.stream = stream;
      // 返回参数
      const videos = document.querySelector("video");
      if ("srcObject" in videos) {
        videos.srcObject = stream;
      } else {
        // 防止在新的浏览器里使用它，应为它已经不再支持了
        videos.src = window.URL.createObjectURL(stream);
      }
      videos.onloadedmetadata = function (e) {
        videos.play();
      };
    }).catch((error) => {
      console.log(error);
    });

  } catch (err) {
  }
}
useCamera()
function changePosition() {
  deviceQH()
}
function deviceQH() {
  if (window.stream) {
    window.stream.getTracks().forEach((track) => {
      track.stop();
    });
  }
  constraints.video.facingMode = position.value == 'front' ? 'user' : { exact: 'environment' };

  let promise = navigator.mediaDevices.getUserMedia(constraints); // 调用将询问用户是否允许访问摄像机。如果用户拒绝，它将引发异常并且不返回流。因此，必须在 try/catch 块内处理这种情况,它返回一个Promise，必须使用 async/await 或 then 块
  promise.then((stream) => {
    window.stream = stream;
    // 返回参数
    const videos = document.querySelector("video");
    if ("srcObject" in videos) {
      videos.srcObject = stream;
    } else {
      // 防止在新的浏览器里使用它，应为它已经不再支持了
      videos.src = window.URL.createObjectURL(stream);
    }
    videos.onloadedmetadata = function (e) {
      videos.play();
    };
  }).catch((error) => {
    console.log(error);
  });
}
/**
 * 拍照上传
 */
function photoShoot() {
  uni.showLoading({
    title: '加载中',
  });
  // 拿到图片的base64
  let myCanvas = document.createElement("canvas");
  let context = myCanvas.getContext("2d");
  const video = document.querySelector("video");
  myCanvas.width = Math.min(video.videoWidth, video.videoHeight);
  myCanvas.height = Math.max(video.videoWidth, video.videoHeight);
  context.drawImage(video, 0, 0, myCanvas.width, myCanvas.height);
  let canvas = myCanvas.toDataURL("image/png");
  // 拍照以后将video隐藏
  // 停止摄像头成像
  video.srcObject.getTracks()[0].stop()
  video.pause()
  uni.hideLoading();
  if (canvas) {
    // 拍照将base64转为file流文件
    let blob = dataURLtoBlob(canvas);
    let file = blobToFile(blob, "imgName");
    let image = window.URL.createObjectURL(file)
    console.log('image:', image)
    uni.setStorageSync("faceimg", image)
    uni.navigateTo({
      url: "/pages/pack/face/result?isP=1"
    })
  } else {
    console.log('canvas生成失败')
  }
}
/**
 * 将图片转为blob格式
 * dataurl 拿到的base64的数据
 */
function dataURLtoBlob(dataurl) {
  let arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime
  });
}
/**
 * 生成文件信息
 * theBlob 文件
 * fileName 文件名字
 */
function blobToFile(theBlob, fileName) {
  theBlob.lastModifiedDate = new Date().toLocaleDateString();
  theBlob.name = fileName;
  return theBlob;
}
function takePhoto() {
  // #ifdef H5
  photoShoot()
  // #endif

  // #ifdef MP
  const ctx = uni.createCameraContext();
  ctx.takePhoto({
    quality: 'high',
    success: (res) => {
      let img = res.tempImagePath;
      uni.compressImage({
        src: img,
        quality: 60,
        compressedWidth: 500,
        compressHeight: 500,
        success(res) {
          uni.setStorageSync("faceimg", res.tempFilePath)
          uni.redirectTo({
            url: "/pages/oa/face/result?isP=1"
          })
        }
      })
    },
    fail(err) {
      console.log(err)
    }
  });
  // #endif
}
</script>
<style lang="scss">
.camera video {
  object-fit: fill !important;
}
</style>
<style scoped lang="scss">
.pages {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  padding-top: 200rpx;

  .title {
    text-align: center;
    color: #FFFFFF;
    margin-bottom: 50rpx;
  }

  .camera {
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background: #FFF;
    border: 1rpx dashed #eee;
    overflow: hidden;
    margin: 0 auto;

    ::v-deep .uni-video-video {
      object-fit: fill !important;
    }
  }

  .tishi {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-top: 80rpx;

    .yy {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;

      .t1 {
        font-size: 24rpx;
        font-weight: 400;
        color: #FFFFFF;
        margin-top: 24rpx;
      }
    }
  }


  .footer {
    width: 750rpx;
    height: 360rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    border-top: 1rpx solid #FFF;

    .btn2 {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.18);
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      left: 590rpx;
      top: 132rpx;
      position: absolute;

      .img {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .btn {
      width: 144rpx;
      height: 144rpx;
      border: 2rpx solid #FFFFFF;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      position: absolute;
      left: 304rpx;
      top: 80rpx;

      .b {
        width: 128rpx;
        height: 128rpx;
        background: #FFFFFF;
        border-radius: 50%;
      }
    }
  }
}
</style>