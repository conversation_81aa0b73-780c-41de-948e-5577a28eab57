<template>
  <view class="pages">
    <u-navbar title="人脸管理" ref="navbar" :background="{ background: '#FFFFFF' }" :border-bottom="false"></u-navbar>
    <image class="img" :src="faceImg"></image>
    <template v-if="isP">
      <template v-if="hasError">
        <view class="t1 tr">未通过</view>
        <view class="t2">照片不符合要求请重新录入</view>
      </template>
      <template v-else>
        <view class="t1">通过验证</view>
        <view class="t2">请提交注册</view>
      </template>
    </template>
    <template v-if="type == 1">
      <view class="t1">{{ relationship.relationship_name }}</view>
      <view class="t2">注册成功～</view>
    </template>
    <template v-if="type == 2">
      <view class="t1">{{ relationship.relationship_name }}</view>
      <view class="t2">审核中</view>
    </template>
    <template v-if="type == 3">
      <view class="t1">{{ relationship.relationship_name }}</view>
      <view class="t2">注册失败</view>
    </template>
    <template v-if="type == 1">
      <view class="zhanwei"></view>
      <view class="footer">
        <view class="footer-content">
          <view class="btn btn2 btnf" @click="delFace">删除</view>
        </view>
      </view>
    </template>
    <template v-if="isP && hasError">
      <view class="zhanwei"></view>
      <view class="footer">
        <view class="footer-content">
          <view class="btn btn2 btnf" @click="reTake">重拍</view>
        </view>
      </view>
    </template>
    <template v-if="isP && !hasError">
      <view class="zhanwei"></view>
      <view class="footer">
        <view class="footer-content">
          <view class="btn" @click="reTake">重拍</view>
          <view class="btn btn2" @click="addFace">提交注册</view>
        </view>
      </view>
    </template>
  </view>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import checkFace from "@/lib/face/index.js"
import request from '@/request';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
const faceImg = ref('');
const type = ref(0);
const isP = ref(false)
const relationship = ref({
  relationship_name: "",
  id: ''
})
onLoad(option => {
  if (option.isP == 1) {
    isP.value = true;
    faceImg.value = uni.getStorageSync("faceimg");
    checkFaceImg();
  } else {
    let face = uni.getStorageSync("face_item");
    faceImg.value = face.face;
    relationship.value = face;
    if (face.status == 1) {
      type.value = 1;
    } else if (face.status == 0) {
      type.value = 2;
    } else if (face.status == 4) {
      type.value = 3;
    }

  }
})
const hasError = ref(false);
function checkFaceImg() {
  uni.showLoading({
    mask: true,
    title: "加载中"
  })
  let filePath = faceImg.value;
  let fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
  try {
    checkFace(filePath, fileName, (res) => {
      uni.hideLoading();
      if (res == 0) {
        hasError.value = true;
      } else if (res == 1) {
        hasError.value = false;
      }
    })
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "网络连接失败，请重试",
      icon: "none",
      duration: 2000
    })
  }
}
function reTake() {
  uni.redirectTo({
    url: "/pages/pack/face/face"
  })
}
function delFace() {
  uni.showModal({
    title: "确定删除人脸？",
    success: function (res) {
      if (res.confirm) {
        request({
          url: '/api/device/changeFaceStatus',
          method: 'post',
          data: {
            face_id: relationship.value.id,
            status: 3
          }
        }).then(res => {
          uni.showToast({
            title: "删除成功",
            icon: "none"
          })
          setTimeout(uni.navigateBack, 1500)
        })
      }
    }
  })
}
function addFace() {
  uni.showLoading({
    mask: true,
    title: "加载中"
  })
  let relationship = uni.getStorageSync("face_relationship")
  let filePath = faceImg.value;
  let fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
  let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
  OBSupload(filePath, fileExtension, fileName)
    .then(faceurl => {
      request({
        url: "/api/device/face/add",
        method: "post",
        data: {
          identity: relationship.id == 0 ? 2 : 1,
          student_id: userInfo.student_id,
          face: faceurl,
          relationship: relationship.id
        }
      }).then(res => {
        isP.value = false;
        type.value = 1;
        uni.hideLoading();
        uni.showToast({
          title: "注册成功",
          icon: "success"
        })
        setTimeout(uni.navigateBack, 1500)
      })
    })
}
</script>
<style scoped lang="scss">
.pages {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  align-items: center;

  .img {
    width: 599rpx;
    height: 600rpx;
    margin-top: 100rpx;
    border-radius: 50%;
  }

  .t1 {
    height: 48rpx;
    font-size: 48rpx;
    font-weight: 500;
    color: #262937;
    line-height: 48rpx;
    margin-top: 80rpx;
  }

  .tr {
    color: #FF7373;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 500;
    color: #AEB0BC;
    margin-top: 24rpx;
  }

  .zhanwei {
    width: 100%;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
  }

  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
    position: fixed;
    bottom: 0;
    left: 0;
    display: block;
    z-index: 4;

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 330rpx;
        height: 80rpx;
        border-radius: 16rpx;
        text-align: center;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FF7373;
        line-height: 80rpx;
      }

      .btnf {
        width: 100%;
      }

      .btn2 {
        color: #FFFFFF;
        background: #FF7373;
      }
    }
  }
}
</style>