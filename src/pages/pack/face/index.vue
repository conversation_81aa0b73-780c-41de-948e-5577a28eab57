<template>
  <view class="pages">
    <u-navbar :immersive="true" :background="{ background: 'rgba(255,255,255,0)' }" ref="navbar" :border-bottom="false"
      title="人脸管理">
    </u-navbar>
    <image class="header" src="https://obs.tuoyupt.com/miniprogram/face/head.png"></image>
    <view class="content">
      <view class="top">
        <view class="index">1</view>
        <view class="t1">录入宝宝&家人人脸</view>
        <view class="index index1">2</view>
        <view class="t1">入离机构进行人脸识别</view>
      </view>
      <view class="body">
        <image class="shili" src="https://obs.tuoyupt.com/miniprogram/face/shii.png"></image>
        <view class="t1">请完成人脸录入</view>
        <view class="t2">请确认机构开通了人脸考勤功能，一个孩子最多
          录入5位家长
        </view>
        <view class="t3" v-if="registered.length == 0" @click="showP">开始录入</view>
        <view class="row" v-else>
          <view class="icons" v-for="item in registered" :key="item.id">
            <image class="icon" @click="goDetail(item)" :src="item.face"></image>
            <view class="status" v-if="item.status == 0">
              审核中
            </view>
            <view class="status" v-if="item.status == 4">
              注册失败
            </view>
          </view>
          <image v-if="registered.length < 5" class="icons" @click="showP" src="https://obs.tuoyupt.com/nanjing/pstac/notice/add.svg"></image>
        </view>
        <view class="t4">
          <u-checkbox active-color="#FF7373" v-model="checkValue" shape="circle" icon-size="32" corlor="#FF7373">
            <view class="check">
              已阅读并同意<text class="link" style="margin-left: 10rpx;">《隐私政策》</text>
            </view>
          </u-checkbox>
        </view>
      </view>
      <view class="f1">
       <!-- 京学集团技术支持 -->
      </view>
      <view class="f2">
        托起明天的希望，育出未来的力量
      </view>
    </view>
  </view>
  <u-popup v-model="showPop" closeable border-radius="24" mode="bottom" safe-area-inset-bottom>
    <view class="pop">
      <view class="title">
        请选择身份
      </view>
      <view class="rows">
        <view class="item" @click="goFace(item)" v-for="item in unregistered" :key="item.id">
          {{ item.name }}
        </view>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { ref } from "vue";
const checkValue = ref(false);
import request from '@/request';
import { onShow } from "@dcloudio/uni-app";
const registered = ref([])
const unregistered = ref([])
onShow(() => {
  getData();
})
const showPop = ref(false)
function showP() {
  if (checkValue.value) {
    showPop.value = true;
  } else {
    uni.showToast({
      title: "请阅读并同意隐私政策",
      icon: "none"
    })
  }

}
function getData() {
  if (unregistered.value.length == 0) {
    request({
      url: "/api/device/relationship"
    }).then(res => {
      unregistered.value = res;
    })
  }
  uni.showLoading()
  request({
    url: "/api/device/faceRelationship"
  }).then(res => {
    registered.value = res;
    uni.hideLoading();
  })
}
function goFace(relationship) {
  showPop.value = false;
  uni.setStorageSync("face_relationship", relationship)
  uni.navigateTo({
    url: "/pages/pack/face/face"
  })
}
function goDetail(item) {
  uni.setStorageSync("face_item", item)
  uni.navigateTo({
    url: "/pages/pack/face/result"
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  background: #F6f6f6;
  display: flex;
  flex-direction: column;
  align-items: center;

  .header {
    width: 750rpx;
    height: 750rpx;
  }

  .content {
    width: 690rpx;
    height: 814rpx;
    background: rgba(255, 255, 255, 0.34);
    border-radius: 40rpx;
    margin-top: -300rpx;
    border-radius: 40rpx;

    .body {
      border-radius: 40rpx;
      background: #FFFFFF;
      height: 734rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 88rpx;

      .shili {
        width: 228rpx;
        height: 228rpx;
      }

      .t1 {
        font-size: 34rpx;
        font-weight: 600;
        color: #262937;
        line-height: 52rpx;
        margin-top: 30rpx;
      }

      .t2 {
        font-size: 24rpx;
        font-weight: normal;
        color: #919191;
        margin-top: 24rpx;
        max-width: 584rpx;
        text-align: center;
      }

      .t3 {
        width: 650rpx;
        height: 88rpx;
        background: #FF7373;
        border-radius: 24rpx;
        text-align: center;
        font-size: 34rpx;
        font-weight: normal;
        color: #FFFFFF;
        line-height: 88rpx;
        margin-top: 64rpx;
      }

      .row {
        display: flex;
        flex-direction: row;
        margin-top: 20rpx;



        .icons {
          width: 88rpx;
          height: 88rpx;
          border-radius: 44rpx;
          margin-right: 20rpx;
          position: relative;

          .icon {
            width: 88rpx;
            height: 88rpx;
            border-radius: 44rpx;
          }

          .status {
            position: absolute;
            left: 8rpx;
            bottom: 0;
            width: 72rpx;
            height: 32rpx;
            background: #FF7373;
            border-radius: 16rpx;
            text-align: center;
            font-size: 16rpx;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 32rpx;
          }
        }
      }

      .t4 {
        margin-top: 36rpx;
      }

      .check {
        color: #AEB0BC;
        font-size: 24rpx;

        .link {
          color: #FF7373;
        }
      }
    }

    .top {
      width: 100%;
      height: 80rpx;
      background: #DFF2FC;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 30rpx;
      border-radius: 40rpx 40rpx 0 0;

      .index {
        width: 32rpx;
        height: 32rpx;
        background: #90CEFF;
        border-radius: 12rpx;
        text-align: center;
        line-height: 32rpx;
        font-size: 24rpx;
        color: #FFFFFF;
      }

      .index1 {
        margin-left: 12rpx;
      }

      .t1 {
        font-size: 26rpx;
        font-weight: normal;
        color: #262937;
        margin-left: 2rpx;
      }
    }
  }

  .f1 {
    font-size: 32rpx;
    font-weight: normal;
    color: #CFD1DA;
    line-height: 32rpx;
    margin-top: 80rpx;
    text-align: center;
  }

  .f2 {
    font-size: 16rpx;
    font-weight: normal;
    color: #CFD1DA;
    line-height: 28rpx;
    text-align: center;
    margin-top: 12rpx;
  }
}

.pop {

  width: 750rpx;
  background: #FFFFFF;

  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #262937;
    line-height: 44rpx;
    text-align: center;
    line-height: 100rpx;
    height: 100rpx;
  }

  .rows {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 10rpx 30rpx;

    .item {
      width: 212rpx;
      height: 60rpx;
      background: #F6F6F6;
      border-radius: 8rpx;
      font-size: 24rpx;
      text-align: center;
      font-weight: 400;
      color: #262937;
      line-height: 60rpx;
      margin-bottom: 28rpx;
    }
  }
}
</style>