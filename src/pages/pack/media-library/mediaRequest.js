import { peixunUrl } from "@/config/index";
const request = (option) => {
    return new Promise((resolve, reject) => {
        uni
            .request({
                method: option.method || "GET",
                url: peixunUrl + option.url,
                data: option.data,
                dataType: "json",
            })
            .then((response) => {
                let { code, data } = response.data;
                if (code == 0) {
                    resolve(data);
                } else {
                    reject(response);
                }
            })
            .catch((error) => {
                uni.hideLoading()
                reject(error);
            });
    });
};


export default request;
