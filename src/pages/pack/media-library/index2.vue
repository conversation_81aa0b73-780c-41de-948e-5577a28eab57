<template>
  <view class="pages">
    <u-navbar title="儿童歌谣" title-color="#FFFFFF" back-icon-color="#FFFFFF" ref="navbar"
      :background="{ background: '#FF7373' }" :border-bottom="false"></u-navbar>
    <view class="section">
      <view class="item" @click="goDetail(47)">
        <image class="cover" src="https://obs.tuoyupt.com/yili/index/ergeew.png"></image>
        <view class="title">
          宝宝听儿歌
        </view>
      </view>
      <view class="item" @click="goDetail(48)">
        <image class="cover" src="https://obs.tuoyupt.com/yili/index/weqwsd.png"></image>
        <view class="title">
          五大能力培养儿歌
        </view>
      </view>
      <view class="item" @click="goDetail(54)">
        <image class="cover" src="https://obs.tuoyupt.com/yili/index/ocasd.png"></image>
        <view class="title">
          手指谣
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/player/tja.png"></image>
          <text>推荐</text>
        </view>
        <view class="btn" @click="goSCp">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/player/sc.png"></image>
          <text>收藏</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
function goDetail(id) {
  uni.navigateTo({
    url: `./scene?id=${id}`
  })
}
function goSCp() {
  uni.navigateTo({
    url: `./collect`
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .section {
    flex: 1;
    min-height: 1rpx;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;


    .item {
      border-radius: 24rpx;
      overflow: hidden;
      margin: 30rpx 0;
      position: relative;
      z-index: 2;
      width: 323rpx * 2;
      height: 112rpx * 2;
      background: #DDECFF;
      border-radius: 11rpx;
      padding-left: 30rpx;
      .cover {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
        left: 0;
        top: 0;
      }

      .title {
        font-size: 40rpx;
        color: #000000;
        line-height: 19rpx;
        text-align: left;
        font-style: normal;
        position: relative;
        z-index: 3;
        line-height: 112rpx * 2;
      }
    }
  }


  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 50%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;

        .img {
          width: 60rpx;
          height: 60rpx;
          margin-right: 12rpx;
        }
      }
    }
  }
}
</style>