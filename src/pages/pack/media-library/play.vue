<template>
  <view class="pages">
    <u-navbar title="播放器" title-color="#FFFFFF" back-icon-color="#FFFFFF" ref="navbar"
      :background="{ background: 'rgb(249, 74, 86)' }" :border-bottom="false"></u-navbar>
    <view class="play">
      <image class="tz" :class="{ tza: isPlay }" src="https://obs.tuoyupt.com/miniprogram/player/tz1.png">
      </image>
      <image class="cp" :class="{ cpp: !isPlay }" src="https://obs.tuoyupt.com/miniprogram/player/cp.png">
      </image>
    </view>
    <view class="t1">
      专辑名称：{{ category_name }}
    </view>
    <view class="t1">
      正在播放：{{ currentInfo.name }}
    </view>
    <view class="handarea">
      <view class="row1">
        <view @click="handFavorite" class="fart">
          <image class="shouc" v-if="!currentInfo.is_favorite" src="https://obs.tuoyupt.com/miniprogram/player/scf.png">
          </image>
          <template v-else>
            <image class="shouc" src="https://obs.tuoyupt.com/miniprogram/player/scfab.png">
            </image>
            <text>已收藏</text>
          </template>
        </view>
        <view @click="showPop">
          <u-icon size="40" name="more-dot-fill"></u-icon>
        </view>
      </view>
      <view class="row">
        <u-slider @start="sliderStart" @end="sliderEnd" @moving="sliderMove" active-color="#FFFFFF"
          inactive-color="#FFFFFF" v-model="current">
        </u-slider>
        <view class="currentTimeF">
          {{ currentTimeF }}
        </view>
        <view class="all">
          {{ maxTime }}
        </view>
      </view>
      <view class="row2">
        <view></view>
        <!-- <view @click="changeLoop">
          <image class="type" v-if="isLoop" src="https://obs.tuoyupt.com/miniprogram/player/loop-1.svg">
          </image>
          <image class="type" v-else src="https://obs.tuoyupt.com/miniprogram/player/loop.svg">
          </image>
        </view> -->
        <view @click="onPre">
          <u-icon name="skip-back-left" size="60"></u-icon>
        </view>
        <view @click="onHandlePlay">
          <u-icon name="play-circle" v-if="!isPlay" size="140"></u-icon>
          <u-icon name="pause-circle" v-else size="140"></u-icon>
        </view>
        <view @click="onNext">
          <u-icon name="skip-forward-right" size="60"></u-icon>
        </view>
        <view>
        </view>
      </view>
    </view>
    <u-popup v-model="popShow" height="750rpx" closeable safe-area-inset-bottom :border-radius="40" mode="bottom">
      <view class="body">
        <view class="title">播放列表</view>
        <view v-for="(item, index) in list" :class="{ active: item.id == currentInfo.id }" class="item" :key="item.id"
          @click="playChange(item)">
          <view class="index">
            {{ index + 1 }}
          </view>
          <view class="name">
            {{ item.name }}
          </view>
          <view v-if="item.id == currentInfo.id">
            正在播放
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script setup>
import { onLoad,onUnload } from '@dcloudio/uni-app';
import { computed, ref } from 'vue';
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
import request from "./mediaRequest";
let currentInfo = ref({
  audio_zh_resource: [],
  audio_en_resource: [],
  name: '',
  id: 0,
  is_favorite: 0,
});
function handFavorite() {
  uni.$u.throttle(() => {
    request({
      url: `/api/padsource/favorite`,
      method: "post",
      data: {
        resource_id: currentInfo.value.id,
        uid: userInfo.id
      }
    }).then(res => {
        currentInfo.value.is_favorite = res.is_favorite;
        if (currentInfo.value.is_favorite == 1) {
          uni.showToast({
            title: "收藏成功，可在收藏列表收听",
            duration: 1000,
            mask: true,
            icon: "none",
          });
        } else {
          uni.showToast({
            title: "取消收藏",
            duration: 1000,
            mask: true,
            icon: "none",
          });
        }
    })
  }, 500)
}
const list = ref([])
const popShow = ref(false);
function showPop() {
  popShow.value = true;
}
const currentIndex = ref(0)
onLoad(() => {
  currentInfo.value = uni.getStorageSync("musicpalyitem")
  list.value = uni.getStorageSync("musicpalyList");
  category_name.value = uni.getStorageSync("musicpalycate");
  currentIndex.value = list.value.findIndex((item) => {
    return item.id == currentInfo.value.id;
  })
  playInit();
})
onUnload(()=>{
  audioManager.stop();
})
const isLoop = ref(false);
function changeLoop() {
  uni.$u.throttle(() => {
    isLoop.value = !isLoop.value;
  }, 500)
}
const currentTime = ref(0)
function updateTime() {
  if (max.value != Math.floor(audioManager.duration)) {
    max.value = Math.floor(audioManager.duration);
  }
  if (!isMove) {
    currentTime.value = Math.floor(audioManager.currentTime);
    current.value = Math.floor(audioManager.currentTime / max.value * 100);
  }
}
function onNext() {
  if (list.value.length == 1) {
    uni.showToast({
      title: "当前列表无新歌曲",
      icon: "none"
    })
    return
  }
  if (currentIndex.value < list.value.length - 2) {
    currentIndex.value++;
    currentInfo.value = list.value[currentIndex.value];
    playInit();
  } else {
    currentIndex.value = 0;
    currentInfo.value = list.value[currentIndex.value];
    playInit();
  }
}
function onPre() {
  if (list.value.length == 1) {
    uni.showToast({
      title: "当前列表无新歌曲",
      icon: "none"
    })
    return
  }
  if (currentIndex.value > 1) {
    currentIndex.value--;
    currentInfo.value = list.value[currentIndex.value];
    playInit();
  } else {
    currentIndex.value = list.value.length - 1;
    currentInfo.value = list.value[currentIndex.value];
    playInit();
  }
}
function onEnded() {
  if (isLoop.value) {
    audioManager.seek(0);
  } else {
    onNext();
  }
}
const audioManager = uni.createInnerAudioContext();
audioManager.onTimeUpdate(updateTime);
audioManager.autoplay=true;
audioManager.onEnded(onEnded);
audioManager.onPause(() => {
  isPlay.value = false;
});
audioManager.onStop(() => {
  isPlay.value = false;
});
audioManager.onPlay(() => {
  isPlay.value = true;
});
function onHandlePlay() {
  uni.$u.throttle(() => {
    if (isPlay.value) {
      audioManager.pause();
      isPlay.value = false;
    } else {
      audioManager.play();
      isPlay.value = true;
    }
  }, 500)
}
function playChange(item) {
  currentInfo.value = item;
  currentIndex.value = list.value.findIndex((item) => {
    return item.id == currentInfo.value.id;
  })
  playInit()
}
const category_name = ref("")
function playInit() {
  current.value = 0;
  currentTime.value = 0;
  audioManager.title = currentInfo.value.name;
  max.value = currentInfo.value.duration;
  audioManager.src = currentInfo.value.src;
  isPlay.value = false;
}
const current = ref(0);
let isMove = false;
function sliderEnd() {
  let time = Math.floor(current.value / 100 * max.value);
  audioManager.seek(time)
  isMove = false;
}
function sliderStart() {
  isMove = true;
}
function sliderMove() {
  let time = Math.floor(current.value / 100 * max.value);
  currentTime.value = Math.floor(time);
}
const max = ref(100);
const maxTime = computed(() => {
  return  formatSeconds(max.value)
})
function formatSeconds(seconds) {
  let minutes = Math.floor(seconds / 60);
  let remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds}`;
}
const currentTimeF = computed(() => {
  return formatSeconds(currentTime.value)
})
const isPlay = ref(false);
</script>
<style lang="scss" scoped>
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

::v-deep.u-drawer-content {
  padding-top: 30rpx;
}

.pages {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgb(249, 74, 86);
  color: #FFFFFF;
  align-items: center;

  .play {
    width: 600rpx;
    height: 700rpx;
    position: relative;
    text-align: center;

    .cp {
      width: 480rpx;
      height: 480rpx;
      margin-top: 120rpx;
      animation: rotate 5s linear infinite forwards;
    }

    .cpp {
      animation-play-state: paused;
    }

    .tz {
      width: 120rpx;
      height: 420rpx;
      position: absolute;
      right: 20rpx;
      transform: rotate(-15deg);
      transform-origin: 60rpx 90rpx;
      transition: transform 0.3s ease-in-out;
      z-index: 2;
    }

    .tza {
      transform: rotate(10deg);
    }
  }

  .t1 {
    font-size: 32rpx;
    line-height: 60rpx;
    height: 60rpx;
    flex-shrink: 0;
    max-width: 80%;
    text-align: center;
  }

  .handarea {
    margin-top: 60rpx;
  }

  .row {
    width: 690rpx;
    margin-top: 60rpx;
    padding: 0 30rpx;
    position: relative;

    .currentTimeF {
      position: absolute;
      left: 0;
      top: 20rpx;
    }

    .all {
      position: absolute;
      right: 0;
      top: 20rpx;
    }

    .inset {
      position: relative;

      .dot {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: #FFFFFF;
      }

      .badge-button {
        position: absolute;
        left: -50%;
        top: 20rpx;
      }
    }

  }

  .row1 {
    display: flex;
    flex-direction: row;
    width: 690rpx;
    justify-content: space-between;
    align-items: center;
    height: 60rpx;
    flex-shrink: 0;
    margin-top: 20rpx;
    padding: 0 30rpx;

    .fart {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
    }

    .shouc {
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
    }
  }

  .row2 {
    display: flex;
    flex-direction: row;
    width: 690rpx;
    justify-content: space-between;
    align-items: center;
    height: 200rpx;
    flex-shrink: 0;
    padding: 0 30rpx;
    margin-top: 60rpx;

    .type {
      width: 60rpx;
      height: 60rpx;
    }
  }
}

.body {
  padding: 0rpx 30rpx;

  .zhanwei {
    height: 30rpx;
  }

  .title {
    color: #000000;
  }

  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 90rpx;
    font-size: 28rpx;
    color: #000000;

    .index {
      width: 60rpx;
      text-align: center;
    }

    .name {
      flex: 1;
      min-width: 1rpx;
      max-width: 80%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  .item.active {
    color: rgb(249, 74, 86);
  }

}
</style>