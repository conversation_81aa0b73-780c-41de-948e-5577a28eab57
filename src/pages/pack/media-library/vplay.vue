<template>
    <view class="pages">
        <u-navbar title="视频播放" title-color="#FFFFFF" back-icon-color="#FFFFFF" ref="navbar"
            :background="{ background: '#FF7373' }" :border-bottom="false"></u-navbar>
        <view class="top">
            <view class="ti"><u-icon name="play-right-fill" color="red"></u-icon> 正在播放：{{ currentInfo.name }}</view>
            <video class="video" autoplay controls :src="currentInfo.src">

            </video>
        </view>
        <view class="tt1">
            专辑列表
        </view>
        <view class="list">
            <view v-for="(item, index) in list" @click="play(item)" class="item" :key="item.id">
                <view class="index">
                    {{ index + 1 }}
                </view>
                <view class="name">
                    {{ item.name }}
                </view>
                <image class="img" src="https://obs.tuoyupt.com/yili/index/play.svg"></image>
            </view>
        </view>
    </view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
const currentInfo=ref({});
const list=ref([]);
const category_name =ref("");
const currentIndex =ref(0);
onLoad(() => {
    currentInfo.value = uni.getStorageSync("videopalyitem")
    list.value = uni.getStorageSync("videopalyList");
    category_name.value = uni.getStorageSync("videopalycate");
    currentIndex.value = list.value.findIndex((item) => {
        return item.id == currentInfo.value.id;
    })
})
function play(item) {
    currentInfo.value=item;
}
</script>
<style lang="scss" scoped>
.pages {

    .top {
        width: 100vw;
        position: relative;
        .ti{
            padding: 20rpx 30rpx;
        }
        .video{
            width: 690rpx;
            height: 400rpx;
            margin-left: 30rpx;
        }
    }

    .tt1 {
        font-size: 32rpx;
        font-weight: 600rpx;
        padding: 20rpx 30rpx 0;
    }

    .list {
        padding: 0rpx 30rpx;

        .item {
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 100rpx;
            font-size: 28rpx;

            .index {
                width: 60rpx;
                text-align: center;
            }

            .name {
                flex: 1;
                min-width: 1rpx;
                max-width: 80%;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .img {
                width: 48rpx;
                height: 48rpx;
            }
        }
    }

}
</style>