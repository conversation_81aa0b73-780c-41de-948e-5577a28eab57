<template>
  <view class="pages">
    <u-navbar title="我的收藏" title-color="#FFFFFF" back-icon-color="#FFFFFF" ref="navbar"
      :background="{ background: '#FF7373' }" :border-bottom="false"></u-navbar>
    <scroll-view :scroll-top="0" :refresher-triggered="triggered" :scroll-y="true" class="scroll"
      :refresher-enabled="true" @refresherrefresh="refresh">
      <view v-if="list.length" class="list">
        <view class="title">收藏列表</view>
        <view v-for="(item, index) in list" class="item" :key="item.id">
          <view class="index" @click="goPlay(item)">
            {{ index + 1 }}
          </view>
          <view class="name" @click="goPlay(item)">
            {{ item.resource.name }}
          </view>
          <image class="img" @click="onCalne(item)" src="https://obs.tuoyupt.com/miniprogram/player/scfa.png"></image>
        </view>
      </view>
      <u-empty v-else mode="data" text=" ">
        <template v-slot:bottom>
          <view class="empty">还未收藏歌曲</view>
        </template>
      </u-empty>
    </scroll-view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn" @click="goTJp">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/player/tj.png"></image>
          <text>推荐</text>
        </view>
        <view class="btn">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/player/sca.png"></image>
          <text>收藏</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import request from "./mediaRequest";
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const triggered = ref(false);
const refresh = async () => {
  triggered.value = true;
  await getData()
  triggered.value = false;
}
const list = ref([])
function getData() {
  uni.showLoading();
  return request({
    url: `/api/padsource/favorites`,
    data: {
      uid: userInfo.id || 2
    }
  }).then(res => {
    uni.hideLoading();
    list.value = res.list;
  })
}
function onCalne(item) {
  uni.showModal({
    title: '提示？',
    content: "确定要取消收藏？",
    success(res) {
      if (res.confirm) {
        request({
          url: "/api/padsource/favorite",
          method: "post",
          data: {
            resource_id: item.resource_id,
            uid: userInfo.id || 2
          }
        }).then(res => {
          uni.$emit("media-refresh")
          uni.showToast({
            title: "取消成功",
            duration: 1000,
            mask: true,
            icon: "none",
          });
          getData();
        })
      }
    }
  })
}
onLoad(async () => {
  if (userInfo.id == 0) {
    uni.showToast({
      title: "请登录"
    })
    setTimeout(() => {
      uni.redirectTo({
        url: "/pages_work/login/main"
      })
    }, 1500)
    return
  }
  getData()
})
function goPlay(item) {
  let slist = list.value.map(it => {
    return {
      ...it.resource
    }
  });
  uni.setStorageSync("musicpalyList", slist)
  uni.setStorageSync("musicpalyitem", {
    ...item.resource
  })
  uni.setStorageSync("musicpalycate", "我的收藏")
  uni.navigateTo({
    url: `./play`
  })
}
function goTJp() {
  uni.navigateBack();
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .scroll {
    width: 100%;
    flex: 1;
    min-height: 1rpx;

    .list {
      padding: 30rpx;

      .title {
        line-height: 100rpx;
        font-weight: 600;
        font-size: 32rpx;
      }
    }

    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 100rpx;
      font-size: 28rpx;

      .index {
        width: 60rpx;
        text-align: center;
      }

      .name {
        flex: 1;
        min-width: 1rpx;
        max-width: 80%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .img {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }



  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 50%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;

        .img {
          width: 60rpx;
          height: 60rpx;
          margin-right: 12rpx;
        }
      }
    }
  }
}
</style>