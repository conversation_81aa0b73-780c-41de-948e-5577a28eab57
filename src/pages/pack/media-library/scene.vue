<template>
  <view class="pages">
    <u-navbar title="儿歌童谣" title-color="#FFFFFF" back-icon-color="#FFFFFF" ref="navbar"
      :background="{ background: '#FF7373' }" :border-bottom="false"></u-navbar>
    <view class="top">
      <image :src="info.cover" class="back"></image>
      <view class="content">
        <view class="text">
          <view class="t1">
            {{ info.name }}
          </view>
          <view class="t2">
            专辑： {{ info.cateName }}
          </view>
          <view class="t2">
            共{{ total }}首
          </view>
        </view>
      </view>
    </view>
    <view class="tt1">
      播放列表
    </view>
    <view class="list">
      <view v-for="(item, index) in list" class="item" :key="item.id">
        <view class="index" @click="goPlay(item)">
          {{ index + 1 }}
        </view>
        <view class="name" @click="goPlay(item)">
          {{ item.name }}
        </view>
        <image v-if="item.is_favorite" @click="handFavorite(item)" class="img"
          src="https://obs.tuoyupt.com/miniprogram/player/scfa.png"></image>
        <image v-else class="img" @click="handFavorite(item)" src="https://obs.tuoyupt.com/miniprogram/player/sc.png">
        </image>
      </view>
    </view>
    <view class="footer-zhanwei"></view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/player/tja.png"></image>
          <text>推荐</text>
        </view>
        <view class="btn" @click="goSCp">
          <image class="img" src="https://obs.tuoyupt.com/miniprogram/player/sc.png"></image>
          <text>收藏</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad, onReachBottom } from '@dcloudio/uni-app';
import { useUserStore } from "@/stores/userInfo.js";
import request from "./mediaRequest";
const userInfo = useUserStore();
const info = ref({
  id: 3,
  name: "宝宝听儿歌",
  cateName: "儿歌童谣",
  cover: "https://obs.tuoyupt.com/yili/index/ergeew.png",
  src: ""
})
let page = 1;
let total = ref(0);
const list = ref([]);
function getData() {
  uni.showLoading();
  request({
    url: `/api/padsource/resource/ids/page`,
    data: {
      uid: userInfo.id,
      ids: id,
      page: page,
      per_page: 20
    }
  }).then(res => {
    list.value = res.data;
    total.value = res.total;
    uni.hideLoading()
  })
}
onReachBottom(() => {
  if (list.value.length < total.value) {
    page++;
    request({
      url: `/api/padsource/resource/ids/page`,
      data: {
        uid: userInfo.id,
        ids: id,
        page: page,
        per_page: 20
      }
    }).then(res => {
      list.value.push(...res.data);
      total.value = res.total;
      uni.hideLoading()
    })
  }
})
let id = "";
onLoad((option) => {
  uni.$on("media-refresh",()=>{
    page=1;
    getData();
  })
  id = option.id;
  if (id == 47) {
    info.value.name = "宝宝听儿歌";
    info.value.cover = "https://obs.tuoyupt.com/yili/index/ergeew.png";
  }
  if (id == 48) {
    info.value.name = "五大能力培养儿歌";
    info.value.cover = "https://obs.tuoyupt.com/yili/index/weqwsd.png";
  }
  if (id == 54) {
    info.value.name = "手指谣";
    info.value.cover = "https://obs.tuoyupt.com/yili/index/ocasd.png";
  }
  getData();
})
function goSCp() {
  uni.navigateTo({
    url: `./collect`
  })
}
function handFavorite(item) {
  if (!uni.getStorageSync("token")) {
    uni.showToast({
      title: "请登录"
    })
    setTimeout(() => {
      uni.redirectTo({
        url: "/pages_work/login/main"
      })
    }, 1500)
    return
  }
  uni.$u.throttle(() => {
    request({
      url: `/api/padsource/favorite`,
      method: "post",
      data: {
        resource_id: item.id,
        uid: userInfo.id
      }
    }).then(res => {
      if (res.is_favorite == 1) {
        uni.showToast({
          title: "收藏成功，可在收藏列表收听",
          duration: 1000,
          mask: true,
          icon: "none",
        });
      } else {
        uni.showToast({
          title: "取消收藏",
          duration: 1000,
          mask: true,
          icon: "none",
        });
      }
      item.is_favorite = res.is_favorite
    })
  }, 500)
}
function goPlay(item) {
  let slist = list.value;
  uni.setStorageSync("musicpalyList", slist)
  uni.setStorageSync("musicpalyitem", item)
  if (id == 47) {
    uni.setStorageSync("musicpalycate", "宝宝听儿歌")
  }
  if (id == 48) {
    uni.setStorageSync("musicpalycate", "五大能力培养儿歌")
  }
  if (id == 54) {
    uni.setStorageSync("musicpalycate", "手指谣")
  }
  uni.navigateTo({
    url: `./play`
  })
}
</script>
<style lang="scss" scoped>
.pages {

  .top {
    width: 100vw;
    height: 260rpx;
    position: relative;

    .back {
      width: 100vw;
      height: 260rpx;
      left: 0;
      top: 0;
      z-index: 1;
      position: absolute;
    }


    .content {
      width: 100vw;
      height: 260rpx;
      left: 0;
      top: 0;
      z-index: 3;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 30rpx;
      position: relative;

      .text {
        padding: 20rpx 30rpx;

        .t1 {
          font-size: 38rpx;
          font-weight: 600rpx;
        }

        .t2 {
          font-size: 32rpx;
          color: #0000007A;
          font-weight: 500rpx;
        }
      }
    }
  }

  .tt1 {
    font-size: 32rpx;
    font-weight: 600rpx;
    padding: 20rpx 30rpx 0;
  }

  .list {
    padding: 0rpx 30rpx;

    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 100rpx;
      font-size: 28rpx;

      .index {
        width: 60rpx;
        text-align: center;
      }

      .name {
        flex: 1;
        min-width: 1rpx;
        max-width: 80%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .img {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .footer-zhanwei {
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
  }

  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
    position: fixed;
    left: 0;
    bottom: 0;

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 50%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;

        .img {
          width: 60rpx;
          height: 60rpx;
          margin-right: 12rpx;
        }
      }
    }
  }
}
</style>