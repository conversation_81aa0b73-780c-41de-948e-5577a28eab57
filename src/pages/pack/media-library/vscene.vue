<template>
  <view class="pages">
    <u-navbar title="经典绘本" title-color="#FFFFFF" back-icon-color="#FFFFFF" ref="navbar"
      :background="{ background: '#FF7373' }" :border-bottom="false"></u-navbar>
    <view class="top">
      <image :src="info.cover" class="back"></image>
      <view class="content">
        <view class="text">
          <view class="t1">
            {{ info.name }}
          </view>
          <view class="t2">
            专辑： {{ info.cateName }}
          </view>
          <view class="t2">
            共{{ total }}本
          </view>
        </view>
      </view>
    </view>
    <view class="tt1">
      播放列表
    </view>
    <view class="list">
      <view v-for="(item, index) in list" @click="goPlay(item)" class="item" :key="item.id">
        <view class="index">
          {{ index + 1 }}
        </view>
        <view class="name">
          {{ item.name }}
        </view>
        <image class="img" src="https://obs.tuoyupt.com/yili/index/play.svg"></image>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad, onReachBottom } from '@dcloudio/uni-app';
import { useUserStore } from "@/stores/userInfo.js";
import request from "./mediaRequest";
const userInfo = useUserStore();
const info = ref({
  id: 3,
  name: "宝宝生活能力绘本",
  cateName: "经典绘本",
  cover: "https://obs.tuoyupt.com/yili/index/ergeew.png",
  src: ""
})
let page = 1;
let total = ref(0);
const list = ref([]);
function getData() {
  uni.showLoading();
  request({
    url: `/api/padsource/resource/ids/page?ids=${id}&page=${page}&per_page=20`
  }).then(res => {
    list.value = res.data;
    total.value = res.total;
    uni.hideLoading()
  })
}
onReachBottom(() => {
  if (list.value.length < total.value) {
    page++;
    request({
      url: `/api/padsource/resource/ids/page?ids=${id}&page=${page}&per_page=20`
    }).then(res => {
      list.value.push(...res.data);
      total.value = res.total;
      uni.hideLoading()
    })
  }
})
let id = "";
onLoad((option) => {
  id = option.id;
  if (id == 68) {
    info.value.name = "宝宝生活能力绘本";
    info.value.cover = "https://obs.tuoyupt.com/yili/index/shengghuoasd.png";
  }
  if (id == 69) {
    info.value.name = "24节气故事本";
    info.value.cover = "https://obs.tuoyupt.com/yili/index/ershisijieqi2.png";
  }
  getData();
})
function goPlay(item) {
  let slist = list.value;
  uni.setStorageSync("videopalyList", slist)
  uni.setStorageSync("videopalyitem", item)
  if (id == 68) {
    uni.setStorageSync("videopalycate", "宝宝生活能力绘本")
  }
  if (id == 69) {
    uni.setStorageSync("videopalycate", "24节气故事本")
  }
  uni.navigateTo({
    url: `./vplay`
  })
}
</script>
<style lang="scss" scoped>
.pages {

  .top {
    width: 100vw;
    height: 260rpx;
    position: relative;

    .back {
      width: 100vw;
      height: 260rpx;
      left: 0;
      top: 0;
      z-index: 1;
      position: absolute;
    }


    .content {
      width: 100vw;
      height: 260rpx;
      left: 0;
      top: 0;
      z-index: 3;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 30rpx;
      position: relative;

      .text {
        padding: 20rpx 30rpx;

        .t1 {
          font-size: 38rpx;
          font-weight: 600rpx;
        }

        .t2 {
          font-size: 32rpx;
          color: #0000007A;
          font-weight: 500rpx;
        }
      }
    }
  }

  .tt1 {
    font-size: 32rpx;
    font-weight: 600rpx;
    padding: 20rpx 30rpx 0;
  }

  .list {
    padding: 0rpx 30rpx;

    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 100rpx;
      font-size: 28rpx;

      .index {
        width: 60rpx;
        text-align: center;
      }

      .name {
        flex: 1;
        min-width: 1rpx;
        max-width: 80%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .img {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

}
</style>