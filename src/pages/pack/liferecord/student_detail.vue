<template>
  <u-navbar title="生活记录" ref="navbar" :background="{ background: '#FFFFFF' }" :border-bottom="false"></u-navbar>
  <calendar :maxDate="maxDate" @changeDate="changeDate" :pointList="pointList" />
  <view class="stu-info">
    <view class="avter">
      <image class="img" :src="info.img_url" v-if="info.img_url"></image>
      <view v-else class="ic">
        {{ info.name.charAt(0) }}
      </view>
    </view>
    <view class="info">
      <view class="name">{{ info.name }}</view>
      <view class="age">{{ info.age }}</view>
    </view>
    <view class="right">
      <view class="emo">
        <image class="icon" v-if="mood == 1" src="https://obs.tuoyupt.com/miniprogram/grouch/st4.png">
        </image>
        <image class="icon" v-else src="https://obs.tuoyupt.com/miniprogram/grouch/st3.png">
        </image>
        <view class="text">高兴</view>
      </view>
      <view class="emo">
        <image class="icon" v-if="mood == 2" src="https://obs.tuoyupt.com/miniprogram/grouch/st6.png">
        </image>
        <image class="icon" v-else src="https://obs.tuoyupt.com/miniprogram/grouch/st5.png">
        </image>
        <view class="text">一般</view>
      </view>
      <view class="emo">
        <image class="icon" v-if="mood == 3" src="https://obs.tuoyupt.com/miniprogram/grouch/st2.png">
        </image>
        <image class="icon" v-else src="https://obs.tuoyupt.com/miniprogram/grouch/st1.png">
        </image>
        <view class="text">低落</view>
      </view>
    </view>
  </view>
  <template v-if="!isMax">
    <view class="section section1" @click="goDrinkChart">
      <view class="top">
        <image class="icon" src="https://obs.tuoyupt.com/miniprogram/grouch/icon4.png"></image>
        <view class="name">喂水{{ serviceType == 1 ? '喂奶' : '' }}</view>
        <view class="nums" v-if="nums.milk_count > 0 || nums.water_count > 0">
          <text class="num" v-if="serviceType == 1">喂奶{{ nums.milk_count }}次</text>
          <text class="num n2">喂水{{ nums.water_count }}次</text>
        </view>
      </view>
      <view class="content" v-if="nums.milk_count > 0 || nums.water_count > 0">
        <view class="row">
          <view class="title">{{ drink_record.type == 0 ? '喝水' : '喝奶' }}{{ drink_record.drink_volume }}ml</view>
          <view class="time1">{{ drink_record.operate_time }}</view>
          <view class="time2">{{ drink_record.time }}</view>
        </view>
        <view class="remake">备注：{{ drink_record.remark ?? '无' }}</view>
      </view>
    </view>
    <view class="section section2" @click="goEatChart">
      <view class="top">
        <image class="icon" src="https://obs.tuoyupt.com/miniprogram/grouch/icon2.png"></image>
        <view class="name">吃饭{{ serviceType == 1 ? '辅食' : '' }}</view>
        <view class="nums" v-if="nums.food > 0 || nums.support_food > 0">
          <text class="num">进食{{ nums.food }}次</text>
          <text class="num n2" v-if="serviceType == 1">辅食{{ nums.support_food }}次</text>
        </view>
      </view>
      <view class="content" v-if="nums.food > 0 || nums.support_food > 0">
        <view class="row">
          <view class="title">{{ food_record.type == 0 ? '吃饭' : '辅食' }}{{
            food_record.eat_volume == 0 ? '量少' : food_record.eat_volume == 1 ? '适中' : '量多' }}</view>
          <view class="time1">{{ food_record.operate_time }}</view>
          <view class="time2">{{ food_record.time }}</view>
        </view>
        <view class="remake">备注：{{ food_record.remark ?? '无' }}</view>
      </view>
    </view>
    <view class="section section3" @click="goToiletChart">
      <view class="top">
        <image class="icon" src="https://obs.tuoyupt.com/miniprogram/grouch/icon1.png"></image>
        <view class="name">大便小便</view>
        <view class="nums" v-if="nums.big_toilet > 0 || nums.small_toilet > 0 || nums.change_diaper > 0">
          <text class="num">大便{{ nums.big_toilet }}次</text>
          <text class="num n2">小便{{ nums.small_toilet }}次</text>
          <text class="num n2">换尿布{{ nums.change_diaper }}次</text>
        </view>
      </view>
      <view class="content" v-if="nums.big_toilet > 0 || nums.small_toilet > 0">
        <view class="row">
          <view class="title">{{ toilet_record.type == 1 ? '大便' : '小便' }}{{ gettoiletMsg }}<view class="color"
              :style="{ background: gettoiletColor }"></view>
          </view>
          <view class="time1">{{ toilet_record.operate_time }}</view>
          <view class="time2">{{ toilet_record.time }}</view>
        </view>
        <view class="remake">备注：{{ toilet_record.remark ?? '无' }}</view>
      </view>
    </view>
    <view class="section section4" @click="goSleepChart">
      <view class="top">
        <image class="icon" src="https://obs.tuoyupt.com/miniprogram/grouch/icon5.png"></image>
        <view class="name">睡眠</view>
        <view class="nums" v-if="nums.sleep > 0">
          <text class="num">睡眠{{ nums.sleep }}次</text>
        </view>
      </view>
      <view class="content" v-if="nums.sleep > 0">
        <view class="row">
          <view class="title">睡眠{{ sleep_record.sleep_long }}分钟</view>
          <view class="time1">{{ sleep_record.operate_time }}</view>
          <view class="time2">{{ sleep_record.time }}</view>
        </view>
        <view class="remake">备注：{{ sleep_record.remark ?? '无' }}</view>
      </view>
    </view>
    <view class="section section5" @click="goClothChart">
      <view class="top">
        <image class="icon" src="https://obs.tuoyupt.com/miniprogram/grouch/icon3.png"></image>
        <view class="name">换衣</view>
        <view class="nums" v-if="nums.cloth > 0">
          <text class="num">换衣{{ nums.cloth }}次</text>
        </view>
      </view>
      <view class="content" v-if="nums.cloth > 0">
        <view class="row">
          <view class="title">换衣({{ cloth[cloth_record.reason].displayName }})</view>
          <view class="time1">{{ cloth_record.operate_time }}</view>
          <view class="time2">{{ cloth_record.time }}</view>
        </view>
        <view class="remake">备注：{{ cloth_record.remark ?? '无' }}</view>
      </view>
    </view>
  </template>
  <template v-else>
    <u-empty text="超出今日" mode="history"></u-empty>
  </template>
  <view class="zhanwei"></view>
</template>
<script setup>
import { bigColors, smColors, shape, cloth } from './data';
import { ref, computed } from "vue";
import { onShow } from "@dcloudio/uni-app";
import request from '@/request';
import calendar from "@/components/calendar_n.vue";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
import dayjs from "dayjs";
const currentTime = ref("")

const now = dayjs();
const maxDate = now.format("YYYY-MM-DD");
const isMax = ref(false);
function changeDate(date) {
  let day = dayjs(`${date.year}-${date.month}-${date.day}`)
  if (now.isBefore(day)) {
    isMax.value = true;
    mood.value = 0;
  } else {
    isMax.value = false;
    currentTime.value = day.format("YYYY-MM-DD");
    getData();
  }
}
const mood = ref(0);
const info = ref({
  name: "",
  age: '',
  img_url: '',
})
onShow(getData)
const nums = ref({
  water_count: 0,//喝水次数
  milk_count: 0,//喝奶次数
  food: 0,//主食次数
  support_food: 0,//辅食次数
  small_toilet: 0,//小便次数
  big_toilet: 0,//大便次数
  sleep: 0,//睡觉次数
  cloth: 0,//换衣次数
  change_diaper: 0,// 换尿布次数
})
const drink_record = ref({})
const food_record = ref({})
const toilet_record = ref({})
const sleep_record = ref({})
const cloth_record = ref({})
const pointList = ref([])
const serviceType = ref(2);
function getData() {
  if (!currentTime.value) return;
  request({
    method: "get",
    url: "/api/parent/liferecord/studentdetail",
    data: {
      student_id: userInfo.student_id,
      date: currentTime.value
    },
  }).then(data => {
    let { res, month } = data;
    pointList.value = month.map(item => {
      return {
        Ymd: item.day,
      }
    })
    info.value = res.student;
    serviceType.value = res.student.service_id;
    mood.value = res.mood;
    nums.value = res;
    drink_record.value = res.drink_record;
    food_record.value = res.food_record;
    toilet_record.value = res.toilet_record;
    sleep_record.value = res.sleep_record;
    cloth_record.value = res.cloth_record;
  })
}

const gettoiletColor = computed(() => {
  if (nums.value.big_toilet > 0 || nums.value.small_toilet > 0) {
    let color = '';
    if (toilet_record.value.type == 1) {
      color = bigColors[toilet_record.value.big_color].color;
    } else {
      color = smColors[toilet_record.value.small_color].color;
    }
    return color;
  } else {
    return ''
  }
})
const gettoiletMsg = computed(() => {
  if (nums.value.big_toilet > 0 || nums.value.small_toilet > 0) {
    let str = '';
    if (toilet_record.value.type == 1) {
      str += bigColors[toilet_record.value.big_color].name;
      str += shape[toilet_record.value.big_shape].displayName;
    } else {
      str += smColors[toilet_record.value.small_color].name;
    }
    return str;
  } else {
    return ''
  }
})

function goEatChart() {
  if (nums.value.food > 0 || nums.value.support_food > 0) {
    uni.navigateTo({
      url: `/pages/pack/liferecord/eat_chart?id=${userInfo.student_id}&date=${currentTime.value}&serviceType=${serviceType.value}&type=${food_record.value.type}`
    })
  } else {
    uni.navigateTo({
      url: `/pages/pack/liferecord/eat_chart?id=${userInfo.student_id}&date=${currentTime.value}&serviceType=${serviceType.value}`
    })
  }
}
function goDrinkChart() {
  if (nums.value.milk_count > 0 || nums.value.water_count > 0) {
    uni.navigateTo({
      url: `/pages/pack/liferecord/drink_chart?id=${userInfo.student_id}&date=${currentTime.value}&serviceType=${serviceType.value}&type=${drink_record.value.type}`
    })
  } else {
    uni.navigateTo({
      url: `/pages/pack/liferecord/drink_chart?id=${userInfo.student_id}&date=${currentTime.value}&serviceType=${serviceType.value}`
    })
  }
}
function goSleepChart() {
  uni.navigateTo({
    url: `/pages/pack/liferecord/sleep_chart?id=${userInfo.student_id}&date=${currentTime.value}`
  })
}
function goToiletChart() {
  if (nums.value.small_toilet > 0 || nums.value.big_toilet > 0) {
    uni.navigateTo({
      url: `/pages/pack/liferecord/toilet_chart?id=${userInfo.student_id}&date=${currentTime.value}&type=${toilet_record.value.type}`
    })
  } else {
    uni.navigateTo({
      url: `/pages/pack/liferecord/toilet_chart?id=${userInfo.student_id}&date=${currentTime.value}`
    })
  }
}
function goClothChart() {
  uni.navigateTo({
    url: `/pages/pack/liferecord/cloth_chart?id=${userInfo.student_id}&date=${currentTime.value}`
  })
}
</script>
<style scoped lang="scss">
.stu-info {
  width: 750rpx;
  height: 128rpx;
  background: #FFFFFF;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  align-items: center;
  padding: 0 30rpx;

  .avter {
    width: 112rpx;
    height: 112rpx;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    .img {
      width: 112rpx;
      height: 112rpx;
    }

    .ic {
      width: 112rpx;
      height: 112rpx;
      background: #6595FF;
      border-radius: 50%;
      text-align: center;
      line-height: 112rpx;
      color: #FFFFFF;
      font-size: 40rpx;
    }
  }

  .info {
    flex: 1;
    padding-left: 16rpx;

    .name {
      font-size: 40rpx;
      font-weight: 500;
      color: #262937;
      max-width: 5em;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .age {
      font-size: 24rpx;
      font-weight: 400;
      color: #AEB0BC;
    }
  }

  .right {
    display: flex;
    flex-direction: row;

    .emo {
      display: flex;
      flex-direction: column;
      margin-left: 50rpx;
      align-items: center;

      .icon {
        width: 64rpx;
        height: 64rpx;
      }

      .text {
        font-size: 24rpx;
        font-weight: 500;
        color: #262937;
        margin-top: 8rpx;
      }
    }
  }
}

.section {
  width: 690rpx;
  box-shadow: 0rpx 0rpx 24rpx 0rpx rgba(0, 0, 0, 0);
  border-radius: 24rpx;
  box-sizing: border-box;
  padding: 40rpx 30rpx 30rpx;
  margin: 24rpx auto;

  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;

    .icon {
      width: 48rpx;
      height: 48rpx;
      border-radius: 24rpx;
    }

    .name {
      font-size: 24rpx;
      font-weight: 500;
      color: #262937;
      margin-left: 12rpx;
      margin-right: auto;
    }

    .nums {
      .num {
        font-size: 24rpx;
        font-weight: 400;
        color: #262937;
      }

      .n2 {
        margin-left: 24rpx;
      }
    }

    .btn {
      width: 80rpx;
      height: 48rpx;
      border-radius: 24rpx;
      text-align: center;
      line-height: 48rpx;
      margin-left: 40rpx;

      .icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  .content {
    width: 100%;
    min-height: 160rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    box-sizing: border-box;
    padding: 0 30rpx;
    margin-top: 20rpx;

    .row {
      display: flex;
      flex-direction: row;
      height: 96rpx;
      align-items: center;

      .title {
        font-size: 36rpx;
        font-weight: 600;
        color: #262937;

        .color {
          width: 32rpx;
          height: 32rpx;
          background: #F09290;
          display: inline-block;
          border-radius: 50%;
          position: relative;
          left: 8rpx;
          top: 4rpx;
        }
      }

      .time1 {
        font-size: 20rpx;
        font-weight: 400;
        color: #787C8D;
        margin-left: auto;
      }

      .time2 {
        font-size: 28rpx;
        font-weight: 500;
        color: #262937;
        margin-left: 8rpx;
      }
    }

    .remake {
      font-size: 24rpx;
      font-weight: 500;
      color: #AEB0BC;
      line-height: 34rpx;
    }
  }
}

.section1 {
  background: #FFF2F2;

  .top {
    .btn {
      background: #FF7373;
    }
  }
}

.section2 {
  background: #FFF3DF;

  .top {
    .btn {
      background: #FFBB49;
    }
  }
}

.section3 {
  background: #E6FDFF;

  .top {
    .btn {
      background: #28D0DF;
    }
  }
}

.section4 {
  background: #EFF4FF;

  .top {
    .btn {
      background: #6595FF;
    }
  }
}

.section5 {
  background: #E9FFE9;

  .top {
    .btn {
      background: #6CC66C;
    }
  }
}

.zhanwei {
  height: 100rpx;
  width: 100%;
}
</style>