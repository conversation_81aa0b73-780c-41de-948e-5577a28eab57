<template>
  <view class="pages">
    <u-navbar :background="{ background: '#F6F6F6' }" :border-bottom="false" title="换衣统计">
    </u-navbar>
    <view class="section">
      <view class="top">
        <view class="ti">次数</view>
        <view class="arrow">
          <view @click="jWeek">
            <u-icon name="arrow-left"></u-icon>
          </view>
          <view @click="xWeek">
            <u-icon name="arrow-right"></u-icon>
          </view>
        </view>
      </view>
      <view class="chart">
        <Chart :data="chartData" ref="chartRef" @click="onChange"></Chart>
      </view>
    </view>
    <view class="section">
      <view class="info-section">
        <view class="left">
          <view class="time">
            {{ dayjs(currentDate).format("YYYY年MM月DD日") }}
          </view>
          <view class="text">
            换衣{{ current.cloth }}次
          </view>
        </view>
      </view>
      <template v-if="list.length">
        <view class="record-item" :class="{ active: index !== list.length - 1 }" v-for="(item, index) in list"
          :key="item.id">
          <view class="left">
            <image class="type" src="https://obs.tuoyupt.com/miniprogram/grouch/icon3.png">
            </image>
          </view>
          <view class="info">
            <view class="time">
              {{ item.time }}
            </view>
            <view class="title">
              换衣
            </view>
            <view class="des">
              原因：{{ cloth[item.reason].displayName }}
            </view>
            <view class="des">
              备注：{{ item.remark ? item.remark : '无' }}
            </view>
            <view class="imgs" v-if="item.images">
              <image @click="showImgs(item, index)" v-for="(img, index) in item.images.split(',').slice(0, 4)"
                :key="img" :src="img" class="img"></image>
              <template v-if="item.images.split(',').length > 4">
                <view class="more">
                  <image @click="showImgs(item, 4)" :src="item.images.split(',')[4]" class="img"></image>
                  <view @click="showImgs(item, 4)" v-if="item.images.split(',').length > 5" class="mask">+{{
                    item.images.split(',').length - 5 }}
                  </view>
                </view>
              </template>
            </view>
          </view>
        </view>
      </template>
      <view v-else style="height: 400rpx;"><u-empty text="暂无记录"></u-empty></view>
    </view>
  </view>
</template>
<script setup>
import { onLoad, onReady } from '@dcloudio/uni-app';
import request from '@/request';
import Chart from "./components/chart"
import { ref, computed } from "vue";
import { cloth } from './data';
import dayjs from 'dayjs';
let student_id = '';
let odate = '';
const chartRef = ref(null)
onLoad((option) => {
  student_id = option.id;
  odate = option.date;
})
onReady(() => {
  const now = dayjs();
  const date = dayjs(odate);
  let duration = Number(now.diff(date, 'day'));
  dayIndex.value = 6 - Math.floor(duration % 7);
  chartRef.value.changeIndex(dayIndex.value);
  week.value = 0 - Math.floor(duration / 7)
  getChart();
})
const chartData = computed(() => {
  let x = chartList.value.map(item => dayjs(item.day).format('M.DD'));
  let y = chartList.value.map(item => item.cloth);
  if (week.value == 0 && x.length) {
    x[6] = '今'
  }
  return {
    xData: x,
    yData: y,
    name: "次数"
  }
})
const week = ref(0)
const list = ref([])
function onChange(e) {
  dayIndex.value = e;
  currentDate.value = chartList.value[dayIndex.value].day;
  current.value = chartList.value[dayIndex.value];
  getDetail();
}
const chartList = ref([]);
const dayIndex = ref(6);
const current = ref({})
function getChart() {
  uni.showLoading({
    mask: true
  })
  request({
    url: '/api/parent/liferecord/chart',
    data: {
      student_id,
      week: week.value
    }
  }).then(res => {
    chartList.value = res;
    currentDate.value = res[dayIndex.value].day;
    current.value = res[dayIndex.value];
    getDetail();
    uni.hideLoading();
  })
}
const currentDate = ref(new Date());
function getDetail() {
  request({
    url: '/api/parent/liferecord/chartdetail',
    data: {
      student_id,
      date: currentDate.value,
      life_type: 8
    }
  }).then(res => {
    list.value = res.res;
  })
}
function jWeek() {
  week.value--;
  getChart()
}
function xWeek() {
  if (week.value == 0)
    return;
  week.value++;
  getChart()
}
function showImgs(item, index) {
  uni.previewImage({
    current: index,
    urls: item.images.split(',')
  });
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F6F6F6;
  min-height: 100vh;

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 24rpx auto;
    padding-bottom: 12rpx;

    .top {
      width: 690rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 94rpx;
      box-sizing: border-box;
      padding: 0 32rpx;
      align-items: center;

      .ti {
        width: 104rpx;
        height: 44rpx;
        background: #F6F6F6;
        border-radius: 28rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
        line-height: 44rpx;
        text-align: center;
      }

      .arrow {
        display: flex;
        flex: row;
        width: 120rpx;
        justify-content: space-between;
        align-items: center;
      }
    }

    .chart {
      width: 690rpx;
      height: 456rpx;
    }

    .info-section {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 160rpx;
      box-sizing: border-box;
      padding: 0 32rpx;
      align-items: center;

      .time {
        font-size: 40rpx;
        font-weight: 600;
        color: #262937;
        line-height: 56rpx;
      }

      .text {
        font-size: 28rpx;
        font-weight: 500;
        color: #AEB0BC;
        line-height: 40rpx;
      }

      .btn {
        width: 128rpx;
        height: 64rpx;
        background: #617EFB;
        border-radius: 32rpx;
        border: 2rpx solid #617EFB;
        text-align: center;
        line-height: 64rpx;
        font-size: 28rpx;
        color: #FFFFFF;
      }
    }

    .record-item {
      display: flex;
      flex-direction: row;
      padding-bottom: 80rpx;
      position: relative;
      width: 640rpx;
      margin: 0 auto;

      .left {
        position: relative;
        width: 48rpx;

        .type {
          width: 48rpx;
          height: 48rpx;
        }
      }

      .info {
        flex: 1;
        padding-left: 24rpx;

        .time {
          font-size: 28rpx;
          font-weight: 400;
          color: #AEB0BC;
        }

        .title {
          font-size: 28rpx;
          color: #262937;
          font-weight: 600;
          margin-top: 16rpx;
        }

        .des {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
          line-height: 36rpx;
          margin-top: 8rpx;
        }

        .imgs {
          display: flex;
          flex-direction: row;
          margin-top: 8rpx;

          .img {
            width: 104rpx;
            height: 104rpx;
            border-radius: 8rpx;
            margin-right: 4rpx;
          }

          .more {
            position: relative;
            width: 104rpx;
            height: 104rpx;

            .mask {
              position: absolute;
              width: 104rpx;
              height: 104rpx;
              left: 0;
              top: 0;
              background: rgba(0, 0, 0, 0.3);
              text-align: center;
              color: #FFFFFF;
              line-height: 104rpx;
              font-size: 32rpx;
            }
          }
        }
      }

      .right {
        position: absolute;
        right: 0;
        top: 0;
        width: 64rpx;
        height: 48rpx;
        background: #F6F6F6;
        border-radius: 24rpx;
        border: 1rpx solid #EEEEEE;
        color: #000000;
        font-size: 24rpx;
        text-align: center;
        line-height: 50rpx;
      }
    }

    .active {
      &::after {
        position: absolute;
        content: " ";
        z-index: 1;
        width: 1rpx;
        height: 100%;
        top: 0;
        left: 24rpx;
        background: #EEEEEE;
      }
    }
  }
}

.btns {
  width: 216rpx;
  height: 64rpx;
  background: #EEEEEE;
  border-radius: 32rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-left: 180rpx;

  .btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 104rpx;
    height: 56rpx;
    border-radius: 28rpx;
    color: #AEB0BC;
    font-size: 28rpx;
  }

  .active {
    background: #FFFFFF;
    color: #262937;
    font-weight: 600;
  }
}
</style>