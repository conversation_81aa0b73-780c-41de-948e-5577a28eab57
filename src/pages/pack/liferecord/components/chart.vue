<template>
  <l-echart ref="chartRef" @finished="init"></l-echart>
</template>
<script setup>
import LEchart from './lime-echart/l-echart.vue';
import { ref } from "vue";
import * as echarts from 'echarts/core';
import { GridComponent, MarkAreaComponent } from 'echarts/components';
import { BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { watch } from 'vue';
echarts.use([<PERSON><PERSON><PERSON>omponent, <PERSON><PERSON><PERSON>, <PERSON>vas<PERSON><PERSON><PERSON>, MarkAreaComponent]);
const props = defineProps({
  data: {
    type: Object,
    default: function () {
      return {
        xData: [],
        yData: [],
        name: ""
      }
    }
  }
})
defineExpose({
  changeIndex
})
const emit = defineEmits(['click'])
const chartRef = ref(null);
let index = 6;
function changeIndex(i) {
  index = i;
}
function init() {
  chartRef.value.init(echarts, chart => {
    chart.on('click', 'series.bar', function (e) {
      index = e.dataIndex;
      emit('click', e.dataIndex)
      let optionb = uni.$u.deepClone(option);
      optionb.series[0].data = props.data.yData;
      optionb.xAxis.data = props.data.xData;
      optionb.yAxis.name = props.data.name;
      optionb.series[0].markArea.data[0][0].xAxis = props.data.xData[index]
      optionb.series[0].markArea.data[0][1].xAxis = props.data.xData[index]
      chartRef.value.setOption(optionb);
    });
    watch(() => props.data, (val) => {
      try {
        let optionb = uni.$u.deepClone(option);
        optionb.series[0].data = val.yData;
        optionb.xAxis.data = val.xData;
        optionb.yAxis.name = val.name;
        optionb.series[0].markArea.data[0][0].xAxis = val.xData[index]
        optionb.series[0].markArea.data[0][1].xAxis = val.xData[index]
        chartRef.value.setOption(optionb);
        chart.dispatchAction({
          type: "select",
          //seriesIndex：number | array   系列 index，可以是一个数组指定多个系列
          seriesIndex: 0,
          //dataIndex：number 数据的 index
          dataIndex: index
        })
      } catch (error) {
        console.error(error)
      }
    }, {
      deep: true,
      immediate: true
    })
  });
}
let option = {
  grid: {
    left: 0,
    top: 30,
    right: 40,
    bottom: 20,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    position: 'right',
    splitLine: {
      lineStyle: {
        type: "dashed"
      }
    },
    name: "",
    nameTextStyle: {
      color: "#AEB0BC",
      align: "left",
    },
  },
  series: [
    {
      animation: false,
      data: [],
      type: 'bar',
      barWidth: 12,
      selectedMode: true,
      select: {
        itemStyle: {
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'top'
        }
      },
      markArea: {
        silent: true,
        data: [
          [
            {
              itemStyle: {
                color: '#F6F6F6'
              },
              xAxis: ''
            },
            {
              itemStyle: {
                color: '#F6F6F6'
              },
              xAxis: ''
            }
          ]
        ]
      },
      emphasis: {
        disabled: true
      },
      itemStyle: {
        borderRadius: 6,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#FFEBE3' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#CED6F7' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      }
    }
  ]
};
</script>