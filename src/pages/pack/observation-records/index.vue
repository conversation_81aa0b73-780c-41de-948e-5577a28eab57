<template>
  <view class="full-loading" v-if="loading"><u-loading size="48"></u-loading></view>
  <view class="full-loading" v-else-if="isEmpty">
    <u-navbar :background="{ background: '#F6F6F6' }" :border-bottom="false" title="观察记录">
    </u-navbar>
    <u-empty text="暂无观察记录"></u-empty>
  </view>
  <view class="pages safe-area-inset-bottom" v-else>
    <u-navbar :background="{ background: '#F6F6F6' }" :border-bottom="false">
      <view class="slot" @click="actionShow = true">
        {{ group.version_year }}版观察记录 <u-icon name="arrow-down"></u-icon>
      </view>
    </u-navbar>
    <template v-if="ability_age.length">
      <view class="list">
        <view class="zhanwei"></view>
        <view class="item" @click="changeAge(item.id)" :class="{
          active: item.id == age_id,
        }" v-for="item in ability_age" :key="item.id">{{ item.age_name }}</view>
        <view class="zhanwei"></view>
      </view>
      <template v-if="Statistics.final_score">
        <view class="content">
          <view class="s1">
            <view class="left">
              <view class="info">
                <image class="avter" :src="Statistics.img_url"></image>
                <view class="ls">
                  <view class="name">{{ Statistics.student_name }}</view>
                  <view class="age">{{ Statistics.age }}</view>
                </view>
              </view>
              <view class="p">
                <text>{{ Statistics.count_per }}</text>
                <text class="unit">%</text>
              </view>
            </view>
            <view class="right">
              <image class="fenshub" src="https://obs.tuoyupt.com/miniprogram/report/obxz.png"></image>
              <view class="fen">{{ Statistics.final_score }}</view>
            </view>
          </view>
          <view class="des">
            <view class="t1">
              测评总进度
            </view>
            <template v-if="use_type == 3">
              <view class="item">
                <view class="dot dot1">

                </view>
                <view class="text">
                  能
                </view>
              </view>
              <view class="item">
                <view class="dot dot2">

                </view>
                <view class="text">
                  有时
                </view>
              </view>
              <view class="item">
                <view class="dot dot3">

                </view>
                <view class="text">
                  不能
                </view>
              </view>
            </template>
            <template v-else>
              <view class="item">
                <view class="dot dot1">

                </view>
                <view class="text">
                  通过
                </view>
              </view>
              <view class="item">
                <view class="dot dot2">

                </view>
                <view class="text">
                  不通过
                </view>
              </view>
            </template>
          </view>
          <view class="cate-item" v-for="item in Statistics.ability_per_arr" :key="item.ability_id">
            <view class="t">
              <view class="t1">{{ item.ab_title }}</view>
            </view>
            <view class="line">
              <template v-if="use_type == 3">
                <view class="l1" :style="{ width: item.value_count[2] / item.question_num * 100 + '%' }"></view>
                <view class="l2" :style="{ width: item.value_count[1] / item.question_num * 100 + '%' }"></view>
                <view class="l3" :style="{ width: item.value_count[0] / item.question_num * 100 + '%' }"></view>
              </template>
              <template v-else>
                <view class="l1" :style="{ width: item.value_count[1] / item.question_num * 100 + '%' }"></view>
                <view class="l3" :style="{ width: item.value_count[0] / item.question_num * 100 + '%' }"></view>
              </template>
            </View>
          </view>
          <view class="footer">
            <view class="btn" @click="goReport">
              查看完整报告
            </view>
          </view>
        </view>
        <view class="month-data">
          <picker class="picker" @change="bindDateChange" :end="endTime" mode="date" fields="month">
            <view class="top">
              <text class="t1">
                {{ month }}
              </text>
              <text class="t2">
                月
              </text>
              <text class="t2">
                {{ year }}
              </text>
              <u-icon name="arrow-down-fill" size="24" color="#2B2F3C"></u-icon>
            </view>
          </picker>
          <template v-if="monthList.length">
            <view class="cate-li" v-for="item in monthList" :key="item.ability.btree_id"
              @click="goBDetail(item.ability.btree_id, item.ability.ability_id)">
              <view class="name">
                {{ item.ability.ab_title }}
              </view>
              <view class="lists">
                <view class="qu-item" v-for="it in item.ability.type" :key="it.type_id">{{ it.type_name }}</view>
              </view>
              <view class="foo">
                {{ item.ability.updated_at }} {{ item.ability.teacher_name }}
              </view>
            </view>
          </template>
          <u-empty text="暂无记录" mode="list" v-else></u-empty>
        </view>
      </template>
      <u-empty v-else text="暂无观察记录"></u-empty>
    </template>
    <u-empty v-else text="暂无观察记录"></u-empty>
  </view>
  <u-action-sheet safe-area-inset-bottom :cancel-btn="true" @click="confirm" :list="actionList"
    v-model="actionShow"></u-action-sheet>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '@/request';
import dayjs from 'dayjs';
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const groupList = ref([]);
const group = ref({
  "id": "",
  "group_title": "",
  "group_desc": "",
  "service_id": "",
  "version_class": "",
  "age_type": "",
  "send_channel": "",
  "use_case": "",
  "use_type": "",
  "version_year": "",
  "status": "",
  "lest_used": ""
})
let age_id = ref(0);
const now = dayjs();
const endTime = ref(now.format("YYYY-MM-DD"))
const year = ref(now.format("YYYY"));
const month = ref(now.format("MM"))
const loading = ref(true)
const isEmpty = ref(false)
onLoad(() => {
  getGrouplist();
})
const Statistics = ref({
  statistics_id: "",
  student_name: "",
  img_url: "",
  final_score: "",
  age: "",
  ability_per_arr: []
})
const actionShow = ref(false)
const actionList = ref([])
function confirm(index) {
  group.value = groupList.value[index];
  getAge()
}
function getGrouplist() {
  return request({
    url: `/api/parent/ability/grouplist`,
    method: "post",
    data: {
      school_id: userInfo.school_id,
      student_id: userInfo.student_id,
    }
  }).then(res => {
    groupList.value = res;
    if (res.length) {
      actionList.value = res.map((item) => {
        return {
          id: item.id,
          text: item.group_title,
          subText: item.lest_used == 1 ? '上次使用' : '',
        }
      })
      let last = res.find((item) => item.lest_used);
      if (last) {
        group.value = last;
        getAge();
      } else {
        group.value = res[0];
      }
      loading.value = false;
    } else {
      loading.value = false;
      isEmpty.value = true;
    }
  })
}
const ability_age = ref([])
function getAge() {
  return request({
    url: `/api/parent/ability/groupinfo`,
    method: "post",
    data: {
      group_id: group.value.id,
      student_id: userInfo.student_id,
      school_id: userInfo.school_id || 2,
    }
  }).then(res => {
    ability_age.value = res.ability_age;
    if (res.ability_age.length) {
      let last = res.ability_age.find((item) => item.lest_used);
      age_id.value = last.id;
      getData();
      getMonth();
    }
  })
}
const use_type = ref(3);
function getData() {
  return request({
    url: `/api/parent/ability/studentabilityinfo`,
    method: "post",
    data: {
      school_id: userInfo.school_id || 2,
      group_id: group.value.id,
      age_id: age_id.value,
      student_id: userInfo.student_id
    }
  }).then(res => {
    Statistics.value = res.Statistics;
    ability_age.value = res.ability_age;
    use_type.value = res.use_type;
  })
}
function changeAge(id) {
  if (age_id.value != id) {
    age_id.value = id;
    getData();
    getMonth();
  }
}
const monthList = ref([]);
function getMonth() {
  return request({
    url: `/api/parent/ability/monthabilitylist`,
    method: "post",
    data: {
      school_id: userInfo.school_id || 2,
      group_id: group.value.id,
      age_id: age_id.value,
      student_id: userInfo.student_id,
      Ym: `${year.value}-${month.value}`,
    }
  }).then(res => {
    monthList.value = res;
  })
}
async function bindDateChange(e) {
  let date = e.detail.value;
  let t = dayjs(date);
  year.value = t.format("YYYY")
  month.value = t.format("MM")
  getMonth();
}
function goReport() {
  uni.navigateTo({
    url: `/pages/pack/observation-records/report?student_id=${userInfo.student_id}&age_id=${age_id.value}&group_id=${group.value.id}&statistics_id=${Statistics.value.statistics_id}`
  })
}
function goBDetail(btree_id, ability_id) {
  uni.navigateTo({
    url: `/pages/pack/observation-records/single-report?student_id=${userInfo.student_id}&age_id=${age_id.value}&btree_id=${btree_id}&group_id=${group.value.id}&ability_id=${ability_id}`
  })
}
</script>
<style lang="scss" scoped>
.slot {
  display: flex;
  flex-direction: row;
  color: #2B2F3C;
  margin-left: 180rpx;
  font-weight: 600;
  font-weight: 32rpx;
}

.full-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
}

.pages {
  min-height: 100vh;
  background: #F6F6F6;

  .list {
    width: 750rpx;
    height: 100rpx;
    display: flex;
    flex-direction: row;
    align-items: center;

    .zhanwei {
      width: 30rpx;
      flex-shrink: 0;
    }

    .item {
      padding: 0 20rpx;
      height: 48rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      flex-shrink: 0;
      margin-right: 16rpx;
      text-align: center;
      font-size: 24rpx;
      line-height: 48rpx;
    }

    .active {
      background: #2B2F3C;
      color: #FFFFFF;
    }

    .disable {
      background: #EEEEEE;
      color: #7B7F8F;
    }
  }

  .month-data {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;

    .top {
      height: 134rpx;
      padding: 0 30rpx;
      line-height: 134rpx;
      position: relative;

      .t1 {
        font-size: 64rpx;
        color: #282B39;
        font-weight: 600;
        font-family: 'Rousseau-Deco';
      }

      .t2 {
        font-size: 24rpx;
        font-weight: 600;
        color: #2B2F3C;
      }
    }

    .cate-li {
      width: 630rpx;
      border-radius: 1rpx solid #eee;
      padding: 30rpx 0;
      margin: 0 auto;

      .lists {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        .qu-item {
          padding: 0 20rpx;
          height: 48rpx;
          background: #797D8F;
          border-radius: 24rpx;
          line-height: 48rpx;
          margin: 16rpx 16rpx 16rpx 0;
          font-size: 24rpx;
          font-weight: 400;
          color: #FFFFFF;
        }
      }

      .name {
        font-size: 28rpx;
        font-weight: 500;
        color: #292C39;
        line-height: 32rpx;
      }

      .foo {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
      }
    }
  }

  .content {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 30rpx;
    margin: 0rpx auto;

    .s1 {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      height: 200rpx;

      .left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .info {
          display: flex;
          flex-direction: row;

          .avter {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
          }

          .ls {
            padding-left: 16rpx;

            .name {
              font-size: 28rpx;
              font-weight: 500;
              color: #262937;
            }

            .age {
              font-size: 24rpx;
              font-weight: 400;
              color: #AEB0BC;
            }
          }
        }

        .p {
          font-size: 48rpx;

          .unit {
            font-size: 24rpx;
          }
        }
      }

      .right {
        position: relative;
        width: 160rpx;
        height: 164rpx;

        .fenshub {
          width: 160rpx;
          height: 164rpx;
        }

        .fen {
          position: absolute;
          font-size: 48rpx;
          font-weight: 600;
          color: #9E6D12;
          width: 160rpx;
          left: 0;
          top: 46rpx;
          text-align: center;
        }
      }
    }

    .des {
      display: flex;
      flex-direction: row;
      align-items: baseline;

      .t1 {
        flex: 1;
        font-size: 48rpx;
        font-weight: 600;
        color: #292C39;
      }

      .item {
        display: flex;
        flex-direction: row;
        align-items: center;

        .dot {
          width: 16rpx;
          height: 16rpx;
          margin-right: 16rpx;
        }

        .text {
          font-size: 24rpx;
          font-weight: 500;
          color: #ADAFBC;
          margin-right: 16rpx;
        }

        .dot1 {
          border: 2rpx solid #17CF11;
        }

        .dot2 {
          border: 2rpx solid #617EFB;
        }

        .dot3 {
          border: 2rpx solid #FF7373;
        }
      }
    }

    .cate-item {
      margin-top: 40rpx;

      .line {
        display: flex;
        flex-direction: row;
        width: 630rpx;
        height: 4rpx;
        background: #EEEEEE;
        border-radius: 2rpx;
        margin-top: 16rpx;

        .l1 {
          height: 4rpx;
          width: 20%;
          background: #17CF11;
        }

        .l2 {
          height: 4rpx;
          width: 20%;
          background: #617EFB;
        }

        .l3 {
          height: 4rpx;
          width: 20%;
          background: #FF7373;
        }
      }

      .t {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .t1 {
          font-size: 28rpx;
          font-weight: 600;
          color: #292C39;
        }

        .t2 {
          font-size: 24rpx;
          color: #797D8E;
        }
      }
    }

    .footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      margin-top: 40rpx;

      .btn {
        font-size: 24rpx;
        font-weight: 600;
        color: #2A2D3B;
      }
    }
  }
}
</style>