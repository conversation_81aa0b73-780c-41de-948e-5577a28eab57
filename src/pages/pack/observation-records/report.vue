<template>
  <view class="full-loading" v-if="loading"><u-loading size="48"></u-loading></view>
  <view class="pages" v-else>
    <u-navbar :background="{ background: '#D3F8FF' }" :border-bottom="false" title="完整报告">
    </u-navbar>
    <view class="all">
      <image class="zhangshi" src="https://obs.tuoyupt.com/miniprogram/report/obzh2.png"></image>
      <view class="content">
        <image class="biaoti" src="https://obs.tuoyupt.com/miniprogram/report/obbt.png"></image>
        <view class="fenshu">
          <image class="fenshub" src="https://obs.tuoyupt.com/miniprogram/report/obxz.png"></image>
          <view class="fen">{{ Statistics.final_score }}</view>
        </view>
        <view class="info">
          <image class="avter" :src="Statistics.img_url"></image>
          <text class="name">{{ Statistics.student_name }}</text>
          <text class="age">{{ Statistics.age }}</text>
        </view>
        <view class="rtitle">{{ Statistics.age_group }}月龄观察记录</view>
        <image class="obhr" src="https://obs.tuoyupt.com/miniprogram/report/obhr.png"></image>
        <view class="rate">
          <view class="rate-i" v-for="item in Statistics.ability_per_arr" :key="item.ability_id">
            <image class="icon" :src="item.ab_url"></image>
            <view class="text">{{ item.ab_title }}</view>
            <Rate :current="item.star_persent / 20"></Rate>
          </view>
        </view>
      </view>
      <image class="zhangshi" src="https://obs.tuoyupt.com/miniprogram/report/obzh1.png"></image>
    </view>
    <view class="cate" v-for="item in list" :key="item.ability_id">
      <image class="zhangshi" src="https://obs.tuoyupt.com/miniprogram/report/obzh2.png"></image>
      <view class="content">
        <view class="top">
          <view class="left">
            <view class="t1">{{ item.ab_title }}</view>
          </view>
        </view>
        <view class="hr">
          <image class="obhr" src="https://obs.tuoyupt.com/miniprogram/report/obhr.png"></image>
        </view>
        <view class="icate" v-for="it in item.type" :key="it.type_id">
          <view class="title">{{ it.type_name }}</view>
          <view class="item" v-for="(question, index) in it.question" :key="index">
            <view class="dot">
            </view>
            <view class="text">
              {{ question.que_title }}
            </view>
            <view class="check" :class="['check' + question.value]">
              {{ question.value == 2 ? '能' : question.value == 1 ? '有时' : '不能' }}
            </view>
          </view>
        </view>
      </view>
      <image class="zhangshi" src="https://obs.tuoyupt.com/miniprogram/report/obzh1.png"></image>
    </view>
    <view style="height: 24rpx;"></view>
  </view>
</template>
<script setup>
import Rate from './components/rate.vue';
import { ref } from 'vue';
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
const loading = ref(true);
let statistics_id = '';
let age_id = '';
let student_id = '';
let group_id = '';
onLoad((option) => {
  student_id = option.student_id;
  group_id = option.group_id;
  statistics_id = option.statistics_id;
  age_id = option.age_id;
  getData();
})
const Statistics = ref({
  final_score: '',
  ability_per_arr: [],
  img_url: "",
  student_name: "",
  age: "",
  age_group: ""
})
const list = ref([])
function getData() {
  uni.showLoading()
  request({
    url: "/api/parent/ability/groupreport",
    method: 'post',
    data: {
      school_id: userInfo.school_id || 2,
      statistics_id,
      age_id,
      student_id,
      group_id
    }
  }).then(res => {
    uni.hideLoading();
    loading.value = false;
    Statistics.value = res.Statistics;
    list.value = res.list;
  })
}
</script>
<style lang="scss" scoped>
.pages {
  min-height: 100vh;
  background: #D3F8FF;

  .cate {
    width: 690rpx;
    margin: 0 auto;
    margin-top: 20rpx;

    .zhangshi {
      height: 30rpx;
      width: 690rpx;
      display: block;
    }

    .content {
      width: 690rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      margin: 0 auto;
      padding: 40rpx 30rpx;

      .top {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .left {
          .t1 {
            font-size: 48rpx;
            font-weight: 500;
            color: #292C39;
            line-height: 48rpx;
          }

          .t2 {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
            line-height: 34rpx;
            margin-top: 16rpx;
          }
        }
      }

      .hr {
        width: 100%;
        height: 24rpx;

        .obhr {
          width: 100%;
          height: 24rpx;
        }
      }

      .icate {
        margin-top: 40rpx;

        .title {
          font-size: 28rpx;
          font-weight: 600;
          color: #000000;
        }

        .item {
          display: flex;
          flex-direction: row;
          margin-top: 24rpx;

          .dot {
            width: 16rpx;
            height: 16rpx;
            background: #FFFFFF;
            border: 2rpx solid #2B2F3C;
            border-radius: 50%;
            position: relative;
            top: 16rpx;
          }

          .text {
            flex: 1;
            padding-left: 16rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #787C8D;
            line-height: 40rpx;
          }

          .check {
            width: 100rpx;
            text-align: right;
            font-size: 28rpx;
          }

          .check2 {
            color: #17CF11;
          }

          .check0 {
            color: #FF7373;
          }

          .check1 {
            color: #617EFB;
          }
        }
      }
    }
  }

  .all {
    width: 690rpx;
    margin: 0 auto;

    .zhangshi {
      height: 30rpx;
      width: 690rpx;
      display: block;
    }

    .content {
      background: #FFFFFF;
      width: 690rpx;
      display: flex;
      flex-direction: column;
      align-items: center;

      .biaoti {
        width: 530rpx;
        height: 88rpx;
      }

      .fenshu {
        width: 282rpx;
        height: 282rpx;
        position: relative;
        margin-top: 72rpx;

        .fenshub {
          width: 282rpx;
          height: 282rpx;
        }

        .fen {
          position: absolute;
          font-size: 96rpx;
          font-weight: 600;
          color: #9E6D12;
          width: 282rpx;
          left: 0;
          top: 76rpx;
          text-align: center;
        }
      }

      .info {
        margin-top: 78rpx;
        display: flex;
        flex-direction: row;
        align-items: center;

        .avter {
          width: 48rpx;
          border-radius: 50%;
          height: 48rpx;
        }

        .name {
          font-size: 28rpx;
          font-weight: 500;
          color: #262937;
          margin: 0 12rpx;
        }

        .age {
          font-size: 24rpx;
          font-weight: 400;
          color: #AEB0BC;
        }
      }

      .rtitle {
        font-size: 24rpx;
        font-weight: 500;
        color: #BFA157;
        margin-top: 8rpx;
      }

      .obhr {
        height: 24rpx;
        width: 630rpx;
        margin-top: 40rpx;
      }

      .rate {
        margin-top: 40rpx;

        .rate-i {
          display: flex;
          flex-direction: row;
          align-items: center;
          width: 630rpx;
          margin-bottom: 16rpx;

          .icon {
            width: 48rpx;
            height: 48rpx;
          }

          .text {
            flex: 1;
            font-size: 24rpx;
            font-weight: 500;
            color: #797D8E;
            padding-left: 16rpx;
          }
        }
      }
    }
  }
}
</style>