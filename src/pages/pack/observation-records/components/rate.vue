<template>
  <view class="list">
    <image class="i" v-for="item in active" :key="item" src="https://obs.tuoyupt.com/miniprogram/report/star1.png">
    </image>
    <image class="i" v-for="item in half" :key="item" src="https://obs.tuoyupt.com/miniprogram/report/star5.png">
    </image>
    <image class="i" v-for="item in inactive" :key="item" src="https://obs.tuoyupt.com/miniprogram/report/star0.png">
    </image>
  </view>
</template>
<script setup>
import { computed } from "vue";
const props = defineProps({
  current: {
    type: Number,
    default: 3
  }
})
const active = computed(() => {
  return Math.floor(props.current);
})
const half = computed(() => {
  return Number.parseInt(props.current) === props.current ? 0 : 1;
})
const inactive = computed(() => {
  return Math.floor(5 - props.current);
})
</script>
<style lang="scss" scoped>
.list {
  display: flex;
  flex-direction: row;

  .i {
    width: 24rpx;
    height: 24rpx;
    margin: 0 4rpx;
  }
}
</style>