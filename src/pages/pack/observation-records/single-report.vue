<template>
  <view class="full-loading" v-if="loading"><u-loading size="48"></u-loading></view>
  <view class="pages" v-else>
    <u-navbar :background="{ background: '#F6F6F6' }" :border-bottom="false" title="记录详情">
    </u-navbar>
    <view class="content">
      <view class="top">
        <view class="left">
          <view class="t1">{{ ability.ab_title }}</view>
          <view class="t2">{{ ability.updated_at }} {{ ability.teacher_name }}</view>
        </view>
        <view class="right">
          <view class="t3">
            {{ test_age }}
          </view>
          <view class="t4">
            测评年龄
          </view>
        </view>
      </view>
      <view class="hr">
        <image class="obhr" src="https://obs.tuoyupt.com/miniprogram/report/obhr.png"></image>
      </view>
      <view class="cate" v-for="item in list" :key="item.type.type_id">
        <view class="title">{{ item.type.type_name }}</view>
        <view class="item" v-for="it in item.type.question" :key="it.question_id">
          <view class="dot">
          </view>
          <view class="text">
            {{ it.que_title }}
          </view>
          <template v-if="use_type == 3">
            <view class="check" :class="['color' + it.value]">
              {{ it.value == 2 ? '能' : it.value == 1 ? '有时' : '不能' }}
            </view>
          </template>
          <template v-else>
            <view class="check" :class="['colors' + it.value]">
              {{ it.value == 1 ? '通过' : '不通过' }}
            </view>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
const loading = ref(true);
let btree_id = '';
let ability_id = '';
let age_id = '';
let student_id = '';
let group_id = '';
onLoad((option) => {
  student_id = option.student_id;
  group_id = option.group_id;
  ability_id = option.ability_id;
  btree_id = option.btree_id;
  age_id = option.age_id;
  getData();
})
const ability = ref({
  "ab_title": "",
  "ability_id": "",
  "updated_at": "",
  "teacher_id": 0,
  "age_id": "",
  "group_id": "",
  "btree_id": "",
  "teacher_name": ""
})
const list = ref([])
const test_age = ref("");
const use_type = ref(3);
function getData() {
  request({
    url: `/api/parent/ability/abilitydetail`,
    method: "post",
    data: {
      school_id: userInfo.school_id || 2,
      btree_id,
      group_id,
      student_id,
      age_id,
      ability_id,
    }
  }).then(res => {
    list.value = res.list;
    test_age.value = res.test_age;
    ability.value = res.ability;
    use_type.value = res.use_type;
    loading.value = false;
  })
}
</script>
<style lang="scss" scoped>
.pages {
  min-height: 100vh;
  background: #F6F6F6;

  .content {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 0 auto;
    padding: 40rpx 30rpx;

    .top {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .left {
        .t1 {
          font-size: 48rpx;
          font-weight: 500;
          color: #292C39;
          line-height: 48rpx;
        }

        .t2 {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
          line-height: 34rpx;
          margin-top: 16rpx;
        }
      }

      .right {
        text-align: right;

        .t3 {
          font-size: 28rpx;
          font-weight: 500;
          color: #787C8E;
        }

        .t4 {
          font-size: 20rpx;
          font-weight: 500;
          color: #AEB0BC;
          margin-top: 16rpx;
        }
      }
    }

    .hr {
      width: 100%;
      height: 24rpx;

      .obhr {
        width: 100%;
        height: 24rpx;
      }
    }

    .cate {
      margin-top: 40rpx;

      .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
      }

      .item {
        display: flex;
        flex-direction: row;
        margin-top: 24rpx;

        .dot {
          width: 16rpx;
          height: 16rpx;
          background: #FFFFFF;
          border: 2rpx solid #2B2F3C;
          border-radius: 50%;
          position: relative;
          top: 16rpx;
        }

        .text {
          flex: 1;
          padding-left: 16rpx;
          font-size: 28rpx;
          font-weight: 400;
          color: #787C8D;
          line-height: 40rpx;
        }

        .check {
          width: 100rpx;
          text-align: right;
          font-size: 28rpx;
        }

        .color2 {
          color: #17CF11;
        }

        .color0 {
          color: #FF7373;
        }

        .color1 {
          color: #617EFB;
        }

        .colors0 {
          color: #FF7373;
        }

        .colors1 {
          color: #17CF11;
        }
      }
    }
  }
}
</style>