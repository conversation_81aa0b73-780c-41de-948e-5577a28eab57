<template>
  <view class="page">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="完善个人信息">
    </u-navbar>
    <view class="scorll">
      <scroll-view class="scorllv" scroll-y>
        <u-form :model="form" ref="formRef" :label-style="labelStyle" label-width="180">
          <view class="section">
            <u-form-item label="姓名" prop="name">
              <u-input v-model="form.name" placeholder="请输入" />
            </u-form-item>
            <u-form-item label="证件类型">
              身份证
            </u-form-item>
            <u-form-item label="证件号码" prop="id_card">
              <u-input v-model="form.id_card" placeholder="请输入" />
            </u-form-item>
          </view>
        </u-form>
        <view style="height: 40rpx;"></view>
      </scroll-view>
    </view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn" @click="submit">确认</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import request from '@/request.js';
import { onReady } from "@dcloudio/uni-app"
import { ref } from "vue";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const labelStyle = {
  color: "#787D8E"
}
// 表单验证
const formRef = ref(null);
const rules = {
  name: [
    {
      required: true,
      message: '请输入姓名',
      trigger: 'blur',
    }
  ],
  id_card: [
    {
      required: true,
      message: '请输入身份证号',
      trigger: 'blur',
    }, {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    }
  ]
}
onReady(() => {
  formRef.value.setRules(rules);
})
function submit() {
  formRef.value.validate(async valid => {
    if (valid) {
      uni.showLoading({
        title: '提交中'
      })
      request({
        method: 'POST',
        url: `/api/healthservices/user/addParent`,
        data: {
          ...form.value,
          phone: userInfo.phone
        }
      }).then(async () => {
        await userInfo.refrsh(true);
        uni.hideLoading();
        uni.showToast({
          title: "操作成功！",
          icon: "none",
          mask: true,
          duration: 1500
        })
        uni.redirectTo({
          url: "./add"
        })
      })
    }
  })
}
const form = ref({
  name: "",
  id_card: "",
  phone: ""
})
</script>
<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background: #F1F5F8;

  .scorll {
    flex: 1;
    min-height: 1rpx;

    .scorllv {
      width: 100vw;
      height: 100%;

      .section {
        width: 690rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        padding: 0rpx 30rpx;
        margin: 20rpx auto;

        .title {
          font-size: 36rpx;
          font-weight: 600;
          color: #272A38;
        }

        .sex {
          display: inline-block;
          padding: 0 16rpx;
          height: 48rpx;
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 1rpx solid #D9D9D9;
          font-size: 28rpx;
          font-weight: 400;
          color: #63676F;
          line-height: 48rpx;
          margin-right: 32rpx;
        }

        .active {
          color: #FFF;
          background: #617EFB;
        }

        .codebtn {
          width: 156rpx;
          height: 48rpx;
          background: rgba(216, 216, 216, 0);
          border-radius: 8rpx;
          border: 1rpx solid #D9D9D9;
          font-size: 28rpx;
          font-weight: 400;
          color: #63676F;
          line-height: 48rpx;
          text-align: center;
        }
      }
    }
  }

  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
    display: block;

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 690rpx;
        height: 80rpx;
        border-radius: 16rpx;
        text-align: center;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        background: #617EFB;
        line-height: 80rpx;
      }
    }
  }
}
</style>