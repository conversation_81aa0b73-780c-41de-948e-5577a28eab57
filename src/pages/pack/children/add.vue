<template>
  <view class="page">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="添加婴幼儿信息">
    </u-navbar>
    <view class="scorll">
      <scroll-view class="scorllv" scroll-y>
        <u-form :model="form" ref="formRef" :label-style="labelStyle" label-width="180">
          <view class="section">
            <view class="title">基本信息</view>
            <u-form-item label="儿童姓名" prop="name">
              <u-input v-model="form.name" :disabled="is_exit" placeholder="请输入" />
            </u-form-item>
            <u-form-item label="性别" prop="sex">
              <view class="sex" @click="changeSex(1)" :class="{ active: form.sex == 1 }">
                <u-icon size="24" name="man"></u-icon>
                男宝宝
              </view>
              <view class="sex" @click="changeSex(2)" :class="{ active: form.sex == 2 }">
                <u-icon size="24" name="woman"></u-icon>
                女宝宝
              </view>
              <u-input v-model="form.sex" style="display: none;" placeholder="请输入" />
            </u-form-item>
            <u-form-item label="证件类型">
              身份证
            </u-form-item>
            <u-form-item label="证件号码" prop="id_card">
              <u-input v-model="form.id_card" @blur="getBirth" placeholder="请输入" />
            </u-form-item>
            <template v-if="is_exit">
              <u-form-item label="出生日期">
                <u-input v-model="form.birth" disabled placeholder="请输入证件号码" />
              </u-form-item>
              <u-form-item label="所在地区" prop="areaText">
                <u-input v-model="form.areaText" placeholder="请选择" disabled />
              </u-form-item>
              <u-form-item label="详细地址" prop="address">
                <u-input v-model="form.address" disabled placeholder="请输入" />
              </u-form-item>
            </template>
            <template v-else>
              <u-form-item label="出生日期">
                <u-input v-model="form.birth" disabled placeholder="请输入证件号码" @click="dateShow = true" />
              </u-form-item>
              <u-form-item label="所在地区" prop="areaText">
                <u-input v-model="form.areaText" placeholder="请选择" disabled @click="areaShow = true" />
              </u-form-item>
              <u-form-item label="详细地址" prop="address">
                <u-input v-model="form.address" placeholder="请输入" />
              </u-form-item>
            </template>
          </view>
          <view class="section">
            <view class="title">监护人信息</view>
            <u-form-item label="监护人关系" prop="relation">
              <view class="sex" @click="form.relation = '1'" :class="{ active: form.relation == '1' }">
                父亲
              </view>
              <view class="sex" @click="form.relation = '2'" :class="{ active: form.relation == '2' }">
                母亲
              </view>
            </u-form-item>
          </view>
        </u-form>
        <view style="height: 40rpx;"></view>
      </scroll-view>
    </view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn" @click="submit">确认绑定</view>
      </view>
    </view>
  </view>
  <u-calendar v-model="dateShow" mode="date" @change="change"></u-calendar>
  <u-select v-model="areaShow" label-name="name" value-name="code" mode="mutil-column-auto" :list="list"
    @confirm="confirm"></u-select>
</template>
<script setup>
import request from '@/request';
import Xian from "@/lib/area.json";
import { onReady } from "@dcloudio/uni-app"
import { ref } from "vue";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
const labelStyle = {
  color: "#787D8E"
}
const list = ref(Xian)
function confirm(e) {
  form.value.areaText = e[0].label + e[1].label + e[2].label;
  form.value.area = e[1].value;
  form.value.street = e[2].value;
}
function getBirth() {
  let pattern1 = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (pattern1.test(form.value.id_card)) {
    form.value.birth = `${form.value.id_card.substring(6, 10)}-${form.value.id_card.substring(10, 12)}-${form.value.id_card.substring(12, 14)}`;
    checkisExit();
  }
}
function change(e) {
  form.value.birth = e.result;
}
let students_id = "";
function checkisExit() {
  request({
    method: 'POST',
    url: `/api/healthservices/user/iscard`,
    data: {
      id_card: form.value.id_card,
    }
  }).then((res) => {
    if (res && res.id) {
      is_exit.value = true;
      uni.showToast({
        title: "已存在此孩子",
        icon: "none",
        mask: true,
        duration: 1500
      })
      students_id = res.id;
      form.value.name = res.name;
      form.value.sex = res.sex;
      form.value.province = res.province;
      form.value.city = res.city;
      form.value.area = res.area;
      form.value.street = res.street;
      form.value.areaText = res.city_name + res.area_name + res.street_name;
      form.value.address = res.address;
    } else {
      is_exit.value = false;
    }
  })
}
function changeSex(sex) {
  if (is_exit.value) return;
  form.value.sex = sex;
}
const dateShow = ref(false);
const areaShow = ref(false)
// 表单验证
const formRef = ref(null);
const rules = {
  name: [
    {
      required: true,
      message: '请输入姓名',
      trigger: 'blur',
    }
  ],
  sex: [
    {
      required: true,
      type: "number",
      message: '请选择性别',
      trigger: 'blur',
    }
  ],
  id_card: [
    {
      required: true,
      message: '请输入身份证号',
      trigger: 'blur',
    }, {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    }
  ],
  address: [
    {
      required: true,
      message: '请输入详细地址',
      trigger: 'blur',
    }
  ],
  areaText: [
    {
      required: true,
      message: '请选择地区',
      trigger: 'blur',
    }
  ],
  relation: [
    {
      required: true,
      message: '请选择监护人关系',
      trigger: 'blur',
    }
  ]
}
onReady(() => {
  formRef.value.setRules(rules);
})
function submit() {
  formRef.value.validate(async valid => {
    if (valid) {
      uni.showLoading({
        title: '提交中'
      })
      request({
        method: 'POST',
        url: `/api/healthservices/user/add`,
        data: {
          ...form.value,
          students_id: students_id,
          is_exit: is_exit.value ? 1 : 0
        }
      }).then(() => {
        uni.hideLoading();
        userInfo.refrsh();
        uni.showToast({
          title: "添加成功！",
          icon: "none",
          mask: true,
          duration: 1500
        })
        uni.$emit("add_child_yxyx")
        setTimeout(uni.navigateBack, 1500);
      })
    }
  })
}
const is_exit = ref(false);
const form = ref({
  name: "",
  sex: '',
  birth: "",
  id_card: "",
  province: "610000",
  city: "610100",
  area: "",
  street: '',
  areaText: "",
  address: "",
  relation: "",
})
</script>
<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background: #F1F5F8;

  .scorll {
    flex: 1;
    min-height: 1rpx;

    .scorllv {
      width: 100vw;
      height: 100%;

      .section {
        width: 690rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        padding: 40rpx 30rpx 0;
        margin: 20rpx auto;

        .title {
          font-size: 36rpx;
          font-weight: 600;
          color: #272A38;
        }

        .sex {
          display: inline-block;
          padding: 0 16rpx;
          height: 48rpx;
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 1rpx solid #D9D9D9;
          font-size: 28rpx;
          font-weight: 400;
          color: #63676F;
          line-height: 48rpx;
          margin-right: 32rpx;
        }

        .active {
          color: #FFF;
          background: #617EFB;
        }

        .codebtn {
          width: 156rpx;
          height: 48rpx;
          background: rgba(216, 216, 216, 0);
          border-radius: 8rpx;
          border: 1rpx solid #D9D9D9;
          font-size: 28rpx;
          font-weight: 400;
          color: #63676F;
          line-height: 48rpx;
          text-align: center;
        }
      }
    }
  }

  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
    display: block;

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 690rpx;
        height: 80rpx;
        border-radius: 16rpx;
        text-align: center;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        background: #617EFB;
        line-height: 80rpx;
      }
    }
  }
}
</style>