<template>
  <view class="page">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="婴幼儿管理">
    </u-navbar>
    <view class="scorll">
      <scroll-view class="scorllv" scroll-y>
        <template v-if="list.length">
          <view class="item" v-for="item in list" :key="item.id">
            <view class="top">
              <image class="avter" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/avter.png">
              </image>
              <view class="info">
                <view class="t1">
                  <view class="name">{{ item.name }}</view>
                  <view class="sex">{{ item.sex == 1 ? '男' : "女" }}</view>
                </view>
                <view class="arr">
                  账号信息
                </view>
              </view>
              <view class="right" @click="jieB(item)">
                <image class="icon" src="https://obs.tuoyupt.com/yjy_mini/front_img/children/jiebang.svg">
                </image>
                解绑
              </view>
            </view>
            <view class="infos">
              <view class="row">
                <view class="label">出生日期：</view>
                <view class="value">{{ item.birth }}</view>
              </view>
              <view class="row">
                <view class="label">居住地址：</view>
                <view class="value">{{ item.city_name }}{{ item.area_name }}{{ item.street_name }}{{ item.address }}
                </view>
              </view>
              <view class="row">
                <view class="label">身份证号：</view>
                <view class="value">{{ item.id_card }}</view>
              </view>
            </view>
          </view>
          <view class="zhsnewi"></view>
        </template>
        <u-empty v-else mode="data" text=" ">
          <template v-slot:bottom>
            <view class="empty">暂未绑定婴幼儿</view>
          </template>
        </u-empty>
      </scroll-view>
    </view>
    <view class="footer" v-if="list.length < 5">
      <view class="footer-content">
        <view class="btn" @click="goAdd">添加绑定</view>
      </view>
    </view>
  </view>
  <u-popup v-model="confirmShow" mode="center" border-radius="24">
    <view class="pop1">
      <view class="t1">确认解绑</view>
      <view class="pbody">
        <image class="avter" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/avter.png">
        </image>
        <view class="info">
          <view class="tt1">
            <view class="name">{{ info.name }}</view>
            <view class="sex">{{ info.sex == 1 ? '男' : "女" }}</view>
          </view>
          <view class="arr">
            {{ info.age }}
          </view>
        </view>
      </view>
      <view class="pfooter">
        <view class="bt1" @click="confirmShow = false">取消</view>
        <view class="hr"></view>
        <view class="bt2" @click="confirmP">确认</view>
      </view>
    </view>
  </u-popup>
  <u-popup v-model="codeShow" mode="bottom" safe-area-inset-bottom border-radius="24">
    <view class="pop2">
      <view class="title">解绑验证</view>
      <view class="input-s">
        {{ userInfo.phone }}
      </view>
      <view class="hr"></view>
      <view class="input-s">
        <input placeholder="请输入验证码" v-model="code" class="input" />
        <view class="btn" @tap="getCode">
          {{ tips }}
        </view>
      </view>
      <view class="btng" @click="unBind">
        确定
      </view>
    </view>
  </u-popup>
  <u-verification-code change-text="X秒后重试" :seconds="seconds" ref="uCode1" @change="codeChange"></u-verification-code>
</template>
<script setup>
import { ref } from "vue";
import request from '@/request';
import { onShow } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
onShow(() => {
  if (userInfo.id) {
    getList()
  }
})
const info = ref({});
function jieB(item) {
  info.value = item;
  confirmShow.value = true;
}
function confirmP() {
  confirmShow.value = false;
  codeShow.value = true;
}
const list = ref([])
function getList() {
  request({
    url: "/api/healthservices/user/list",
  }).then(res => {
    list.value = res;
  })
}
const code = ref('')
function unBind() {
  if (code.value == '') {
    uni.showToast({
      title: "请输入验证码",
      icon: "none",
    })
    return
  }
  uni.showLoading({
    title: '加载中'
  })
  request({
    method: 'POST',
    url: `/api/healthservices/user/unbind`,
    data: {
      students_id: info.value.id,
      code: code.value,
      parent_phone: userInfo.phone
    }
  }).then(res => {
    uni.hideLoading();
    uni.showToast({
      title: "操作成功！",
      icon: "none",
      mask: true,
      duration: 1500
    })
    getList();
    codeShow.value = false;
    uni.hideLoading();
  })
}
function goAdd() {
  if (!Boolean(userInfo.id)) {
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
    return
  }
  if (Boolean(userInfo.master_hospital_id)) {
    uni.navigateTo({
      url: "./add"
    })
  } else {
    uni.showToast({
      title: "请先完善个人信息",
      mask: true,
      duration: 1500,
      icon: "none"
    })
    setTimeout(() => {
      uni.navigateTo({
        url: "./profile"
      })
    }, 1500)
  }
}
const confirmShow = ref(false);
const codeShow = ref(false);
const seconds = ref(60);
const tips = ref('')
const uCode1 = ref(null);
function codeChange(text) {
  tips.value = text;
}
function getCode() {
  if (uCode1.value.canGetCode) {
    // 模拟向后端请求验证码
    uni.showLoading({
      title: '正在获取验证码'
    })
    request({
      method: 'POST',
      url: `/api/auth/getphonecode`,
      data: {
        phone: info.value.parent_phone
      }
    }).then(res => {
      uni.hideLoading();
      uCode1.value.start();
    })
  }
}
</script>
<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background: #F1F5F8;

  .scorll {
    flex: 1;
    min-height: 1rpx;

    .scorllv {
      width: 100vw;
      height: 100%;

      .item {
        width: 690rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        margin: 20rpx auto;
        padding: 40rpx 30rpx 30rpx;

        .top {
          width: 100%;
          height: 128rpx;
          display: flex;
          flex-direction: row;
          margin: 0 auto;
          border-bottom: 1rpx solid #eee;

          .avter {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
          }

          .info {
            padding-left: 24rpx;
            flex: 1;

            .t1 {
              display: flex;
              flex-direction: row;
              align-items: center;

              .name {
                font-size: 32rpx;
                font-weight: 600;
                color: #282C39;
                line-height: 56rpx;
              }

              .sex {
                width: 32rpx;
                height: 32rpx;
                background: #D6EDFE;
                border-radius: 6rpx;
                text-align: center;
                line-height: 32rpx;
                font-size: 20rpx;
                font-weight: 400;
                color: #0275FF;
                margin-left: 10rpx;
              }
            }


            .arr {
              font-size: 26rpx;
              font-weight: 400;
              color: #797D8D;
              margin-top: 8rpx;
            }
          }

          .right {
            font-size: 26rpx;
            font-weight: 500;
            color: #1975FF;

            .icon {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }

        .infos {
          .row {
            display: flex;
            flex-direction: row;
            margin-top: 24rpx;

            .label {
              width: 130rpx;
              height: 36rpx;
              font-size: 26rpx;
              font-weight: 400;
              color: #797D8E;
              line-height: 36rpx;
            }

            .value {
              flex: 1;
              font-size: 26rpx;
              text-align: right;
              font-weight: 400;
              color: #3D414E;
              line-height: 36rpx;
            }
          }
        }
      }
    }
  }

  .zhsnewi {
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 130rpx);
    height: calc(env(safe-area-inset-bottom) + 130rpx);
  }

  .footer {
    background: #FFFFFF;
    width: 750rpx;
    height: calc(constant(safe-area-inset-bottom) + 120rpx);
    height: calc(env(safe-area-inset-bottom) + 120rpx);
    position: fixed;
    bottom: 0;
    left: 0;
    display: block;
    z-index: 4;

    .footer-content {
      width: 750rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 0 30rpx;

      .btn {
        width: 690rpx;
        height: 80rpx;
        border-radius: 16rpx;
        text-align: center;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        background: #617EFB;
        line-height: 80rpx;
      }
    }
  }
}

.pop2 {
  width: 750rpx;
  background: #FFFFFF;
  padding-top: 40rpx;
  height: 450rpx;

  .title {
    text-align: center;
    font-size: 32rpx;
    font-weight: 600;
    color: #282B39;
  }

  .input-s {
    width: 690rpx;
    height: 112rpx;
    background: #FFFFFF;
    margin: 0 auto;
    font-size: 34rpx;
    font-weight: 500;
    color: #272A38;
    display: flex;
    flex-direction: row;
    align-items: center;

    .input {
      width: 468rpx;
      height: 48rpx;
      font-size: 34rpx;
      color: #787D8E;
    }

    .btn {
      font-size: 28rpx;
      font-weight: 400;
      color: #63676F;
      line-height: 48rpx;
      padding: 0 12rpx;
      height: 48rpx;
      background: rgba(216, 216, 216, 0);
      border-radius: 8rpx;
      border: 1rpx solid #D9D9D9;
      margin-left: auto;
    }
  }

  .hr {
    margin: 0 auto;
    width: 690rpx;
    height: 1rpx;
    background: #eee;
  }

  .btng {
    width: 690rpx;
    height: 80rpx;
    background: #1975FF;
    border-radius: 16rpx;
    font-size: 28rpx;
    text-align: center;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 80rpx;
    margin: 40rpx auto 0;
  }
}

.pop1 {
  width: 600rpx;
  height: 368rpx;
  background: linear-gradient(180deg, #D5EDFF 0%, #FFFFFF 100%);
  border-radius: 24rpx;

  .t1 {
    font-size: 32rpx;
    font-weight: 600;
    color: #282B39;
    line-height: 32rpx;
    text-align: center;
    padding: 40rpx;
  }

  .pbody {
    width: 540rpx;
    height: 128rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 30rpx;

    .avter {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .info {
      padding-left: 24rpx;
      flex: 1;

      .tt1 {
        display: flex;
        flex-direction: row;
        align-items: center;

        .name {
          font-size: 32rpx;
          font-weight: 600;
          color: #282C39;
          line-height: 56rpx;
        }

        .sex {
          width: 32rpx;
          height: 32rpx;
          background: #D6EDFE;
          border-radius: 6rpx;
          text-align: center;
          line-height: 32rpx;
          font-size: 20rpx;
          font-weight: 400;
          color: #0275FF;
          margin-left: 10rpx;
        }
      }

      .arr {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }
    }
  }

  .pfooter {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 16rpx;

    .bt1 {
      flex: 1;
      height: 112rpx;
      text-align: center;
      line-height: 112rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #7A7E8F;
    }

    .bt2 {
      flex: 1;
      height: 112rpx;
      text-align: center;
      line-height: 112rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #1975FF;
    }

    .hr {
      width: 2rpx;
      height: 28rpx;
      background: #D8D8D8;
      border-radius: 1rpx;
    }
  }
}
</style>