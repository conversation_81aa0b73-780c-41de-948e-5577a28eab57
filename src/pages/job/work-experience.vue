<template>
    <view class="work-experience-page">
        <u-navbar :is-back="true" :title="pageTitle" title-color="#262937" title-size="36" :border-bottom="false"
            @back="goBack">
        </u-navbar>

        <view class="scroll-view-content">
            <view class="content">
                <u-form :model="workData" ref="formRef" label-position="left" label-width="200" :errorType="['toast']">
                    <u-form-item label="工作单位" prop="workplace_name">
                        <u-input v-model="workData.workplace_name" type="text" :border="false" input-align="right"
                            placeholder="请输入工作单位" />
                    </u-form-item>
                    <u-form-item label="工作地点" prop="location" right-icon="arrow-right">
                        <AreaPicker v-model="locationCodes" placeholder="请选择工作地点" @change="onLocationChange" />
                    </u-form-item>
                    <u-form-item label="职位名称" prop="job_title">
                        <u-input v-model="workData.job_title" :border="false" input-align="right" placeholder="请输入职位名称"
                            maxlength="50" />
                    </u-form-item>
                    <u-form-item label="工作时间段" prop="duration" right-icon="arrow-right">
                        <DatePicker v-model="durationData" mode="duration" placeholder="请选择工作时间段"
                            duration-title="选择工作时间段" @change="onDurationChange" />
                    </u-form-item>
                    <u-form-item label="工作内容" prop="work_content" :border-bottom="false">
                        <u-input v-model="workData.work_content" type="textarea" height="120rpx" :auto-height="true"
                            :border="false" input-align="right" placeholder="请输入工作内容" maxlength="500" />
                    </u-form-item>
                </u-form>
            </view>
        </view>



        <!-- Footer Buttons -->
        <BottomButton :buttons="getFooterButtons()" @click="onBottomButtonClick" />
    </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
import { useResumeStore } from './stores/resumeStore';
import DatePicker from './components/DatePicker.vue';
import AreaPicker from './components/AreaPicker.vue';
import BottomButton from './components/BottomButton.vue';
const resumeStore = useResumeStore();

// Page mode and data
const pageMode = ref('add'); // 'add' or 'edit'
const workExperienceId = ref(null);

// Computed page title
const pageTitle = computed(() => {
    return pageMode.value === 'edit' ? '编辑工作经验' : '添加工作经验';
});

const goBack = () => {
    uni.navigateBack();
};

const formRef = ref(null);

// Form data
const workData = reactive({
    id: null,
    cv_id: '',
    workplace_name: '',
    job_title: '',
    work_date_start: '',
    work_date_end: '',
    work_content: '',
    // 地区相关字段
    work_location_province_code: '',
    work_location_city_code: '',
    work_location_area_code: '',
    location: '' // 格式化后的地址文本
});

// Validation rules
const rules = {
    workplace_name: [{ required: true, message: '请输入工作单位', trigger: ['blur', 'change'] }],
    location: [{ required: true, message: '请选择工作地点', trigger: ['blur', 'change'] }],
    job_title: [{ required: true, message: '请输入职位名称', trigger: ['blur', 'change'] }],
    work_date_start: [{ required: true, message: '请选择开始时间', trigger: ['blur', 'change'] }],
    work_date_end: [{ required: true, message: '请选择结束时间', trigger: ['blur', 'change'] }],
    work_content: [{ required: true, message: '请输入工作内容', trigger: ['blur', 'change'] }]
};

// 组件数据状态
const locationCodes = ref([]);
const durationData = ref({});

// 地区选择器方法
const onLocationChange = (codes) => {
    // 更新工作数据 - 直接使用接口字段名称
    workData.work_location_province_code = codes[0] || '';
    workData.work_location_city_code = codes[1] || '';
    workData.work_location_area_code = codes[2] || '';

    // 更新location字段用于表单验证（简单设置为非空值）
    workData.location = codes.length > 0 ? codes.join(',') : '';
};

const onDurationChange = (duration) => {
    if (duration && duration.startDate && duration.endDate) {
        workData.work_date_start = duration.startDate;
        workData.work_date_end = duration.endDate;
    }
};

const getFooterButtons = () => {
    if (pageMode.value === 'add') {
        return [
            { type: 'cancel', text: '取消' },
            { type: 'save', text: '保存' }
        ];
    } else {
        return [
            { type: 'delete', text: '删除' },
            { type: 'save', text: '保存' }
        ];
    }
};

const onBottomButtonClick = (type) => {
    switch (type) {
        case 'cancel':
            cancel();
            break;
        case 'delete':
            deleteWorkExperience();
            break;
        case 'save':
            save();
            break;
    }
};

// Load work experience data for editing
const loadWorkExperienceData = async (id) => {
    // 使用store方法获取原始工作经历数据
    const rawData = resumeStore.getWorkExperienceRawById(id);

    if (rawData) {
        Object.keys(workData).forEach(key => {
            workData[key] = rawData[key] || ''
        })

        // 设置日期数据
        durationData.value = {
            startDate: rawData.work_date_start || '',
            endDate: rawData.work_date_end || ''
        };

        // 设置地区数据
        locationCodes.value = [
            workData.work_location_province_code,
            workData.work_location_city_code,
            workData.work_location_area_code
        ].filter(code => code); // 过滤掉空值

        // 设置location字段用于表单验证
        workData.location = locationCodes.value.length > 0 ? locationCodes.value.join(',') : '';
    }
};

// Actions
const cancel = () => {
    uni.navigateBack();
};

const save = async () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            if (!workData.work_date_start && !workData.work_date_end) {
                uni.showToast({
                    title: '请选择工作时间段',
                    icon: 'none'
                })
                return
            }
            try {
                // 显示加载提示
                uni.showLoading({
                    title: '保存中...'
                });
                // 准备保存数据 - 直接使用接口字段名称
                const saveData = { ...workData };
                delete saveData.location
                if (pageMode.value === 'add') {
                    delete saveData.id
                    saveData.cv_id = resumeStore.personalInfo.id
                }
                // 调用API保存工作经验
                await resumeStore.saveWorkExperience(saveData);

                // 保存成功后重新加载工作经验数据，确保数据一致性
                await resumeStore.fetchWorkHistory();

                uni.hideLoading();
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);

            } catch (error) {
                uni.hideLoading();
                console.error('保存工作经验失败:', error);
            }
        } else {
            console.log('验证失败');
        }
    });
};

// 删除工作经验
const deleteWorkExperience = () => {
    uni.showModal({
        title: '删除确认',
        content: '确定要删除这条工作经验吗？',
        success: async (res) => {
            if (res.confirm) {
                try {
                    // 显示加载提示
                    uni.showLoading({
                        title: '删除中...'
                    });

                    // 调用API删除工作经验
                    await resumeStore.deleteWorkExperience(workExperienceId.value);

                    // 删除成功后重新加载工作经验数据，确保数据一致性
                    await resumeStore.fetchWorkHistory();

                    uni.hideLoading();
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });

                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);

                } catch (error) {
                    uni.hideLoading();
                    console.error('删除工作经验失败:', error);
                    uni.showToast({
                        title: '删除失败',
                        icon: 'error'
                    });
                }
            }
        }
    });
};

// Lifecycle
onLoad(async (options) => {
    // Set page mode and work experience ID from URL parameters
    if (options.mode) {
        pageMode.value = options.mode;
    }
    if (options.workExperienceId) {
        workExperienceId.value = options.workExperienceId;
        await loadWorkExperienceData(options.workExperienceId);
    }
});



onReady(() => {
    formRef.value.setRules(rules);
});

</script>

<style lang="scss" scoped>
.work-experience-page {
    background: #F6F6F6;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.scroll-view-content {
    flex: 1;
    overflow-y: scroll;
}

.content {
    margin: 20rpx;
    padding: 0 32rpx;
    background: #fff;
    border-radius: 16rpx;
}

.picker-value {
    text-align: right;
    width: 100%;
    color: #262937;
    font-size: 28rpx;
}

:deep(uni-picker) {
    width: 100%;
}

.picker-value.placeholder {
    color: #c0c4cc;
}
</style>
