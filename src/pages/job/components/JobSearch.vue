<template>
  <view class="job-search">
    <!-- 地点选择和搜索框 -->
    <view class="search-container">
      <!-- 地点选择 - 使用 AreaPicker 组件 -->
      <view class="location-selector">
        <u-icon name="map-fill" color="#FF7373" size="32"></u-icon>
        <AreaPicker v-model="selectedAreaCodes" :columns="2" :texts-range="textRange" placeholder="选择城市"
          :show-text-fn="customShowText" @change="handleLocationChange" />
        <u-icon name="arrow-down" color="#787C8D" size="16"></u-icon>
      </view>

      <!-- 搜索框 -->
      <view class="search-input-container">
        <u-icon name="search" color="#AEB0BC" size="28"></u-icon>
        <input class="search-input" type="text" :value="searchKeyword" placeholder="大家都在搜：托育师"
          placeholder-style="color: #AEB0BC" @input="handleInput" @confirm="handleSearch" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, defineEmits, onMounted, watch } from 'vue'
import AreaPicker from './AreaPicker.vue'
import request from '@/request.js'

const textRange = ref([])

onMounted(() => {
  uni.getLocation({
    type: 'gcj02',
    success: async (res) => {
      console.log('res', res)
      const { latitude, longitude } = res
      const { result: { addressComponent } } = await request({
        url: '/api/city/name',
        method: 'GET',
        data: {
          lat: latitude,
          lng: longitude
        }
      })
      const { province, city } = addressComponent
      textRange.value = [province, city]
    }
  })
})
const emit = defineEmits(['search', 'locationChange'])

// 搜索相关
const searchKeyword = ref('')

// 地区选择相关
const selectedAreaCodes = ref([])

// 处理输入
const handleInput = (e) => {
  searchKeyword.value = e.detail.value
}

// 处理搜索
const handleSearch = () => {
  emit('search', searchKeyword.value)
}

// 自定义显示文本函数 - 只显示城市名称
const customShowText = (codes, maps) => {
  if (!codes || codes.length === 0) return '选择城市'

  // 只显示城市名称（第二个编码对应的名称）
  if (codes[1]) {
    const cityName = maps.cityCodeMap.get(codes[1])
    return cityName || '选择城市'
  }

  // 如果只有省份，显示省份名称
  if (codes[0]) {
    const provinceName = maps.provinceCodeMap.get(codes[0])
    return provinceName || '选择城市'
  }

  return '选择城市'
}

watch(selectedAreaCodes, (newVal) => {
  emit('locationChange', newVal)
})

// 处理地区变化
const handleLocationChange = (codes) => {
  selectedAreaCodes.value = codes

  // 获取城市名称并触发事件
  if (codes.length >= 2) {
    // 这里可以根据需要获取城市名称
    emit('locationChange', codes)
  }
}
</script>

<style lang="scss" scoped>
.job-search {
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.location-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 20rpx;
  background: #F6F6F6;
  border-radius: 24rpx;
  min-width: 120rpx;
  flex-shrink: 0;
  min-height: 76rpx;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background: #F6F6F6;
  border-radius: 24rpx;
  min-height: 76rpx;

  .search-input {
    flex: 1;
    font-size: 28rpx;
    color: #262937;

    &::placeholder {
      color: #AEB0BC;
    }
  }
}
</style>