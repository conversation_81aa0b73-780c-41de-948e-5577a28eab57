<template>
    <view class="area-picker">
        <view class="picker-input" @click="showPicker = true">
            <text class="picker-text" :class="{ 'placeholder': !displayText }">
                {{ displayText || placeholder }}
            </text>
        </view>

        <u-popup v-model="showPicker" mode="bottom" border-radius="14">
            <view class="picker-popup">
                <view class="picker-header">
                    <text class="picker-cancel" @click="cancelPicker">取消</text>
                    <text class="picker-title">选择地区</text>
                    <text class="picker-confirm" @click="confirmPicker">确定</text>
                </view>

                <view class="picker-content">
                    <picker-view class="picker-view" :value="pickerValue" @change="onPickerChange">
                        <!-- 省份 -->
                        <picker-view-column>
                            <view v-for="item in provinces" :key="item.code" class="picker-item">
                                {{ item.name }}
                            </view>
                        </picker-view-column>

                        <!-- 城市 -->
                        <picker-view-column>
                            <view v-for="item in cities" :key="item.code" class="picker-item">
                                {{ item.name }}
                            </view>
                        </picker-view-column>

                        <!-- 区县 - 只有当 columns 为 3 时才显示 -->
                        <picker-view-column v-if="columns === 3">
                            <view v-for="item in districts" :key="item.code" class="picker-item">
                                {{ item.name }}
                            </view>
                        </picker-view-column>
                    </picker-view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import provincesData from '../utils/lib/address/provinces.json'
import citysData from '../utils/lib/address/citys.json'
import areasData from '../utils/lib/address/areas.json'

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    placeholder: {
        type: String,
        default: '请选择地区'
    },
    // 控制显示的列数：2-省市，3-省市区
    columns: {
        type: Number,
        default: 3,
        validator: (value) => [2, 3].includes(value)
    },
    // 自定义显示文本的函数
    showTextFn: {
        type: Function,
        default: null
    },
    // 通过文本数组匹配选中项，格式：['省份名', '城市名', '区县名']
    textsRange: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const showPicker = ref(false)
// 根据 columns 属性初始化 pickerValue
const pickerValue = ref(props.columns === 2 ? [0, 0] : [0, 0, 0])

// 地区数据 - 省份数据是静态的，不需要响应式
const provinces = provincesData
const cities = ref([])
const districts = ref([])

// 当前选中的地区编码
const selectedCodes = ref([])

// 创建编码到名称的映射，提高查找性能
const provinceCodeMap = new Map(provinces.map(p => [p.code, p.name]))
const cityCodeMap = ref(new Map())
const districtCodeMap = ref(new Map())

// 计算显示文本
const displayText = computed(() => {
    if (!props.modelValue?.length) return ''

    // 如果提供了自定义显示函数，使用自定义函数
    if (props.showTextFn && typeof props.showTextFn === 'function') {
        try {
            return props.showTextFn(props.modelValue, {
                provinceCodeMap,
                cityCodeMap: cityCodeMap.value,
                districtCodeMap: districtCodeMap.value
            })
        } catch (error) {
            console.error('自定义显示函数执行失败:', error)
            // 如果自定义函数出错，回退到默认显示逻辑
        }
    }

    // 默认显示逻辑
    const labels = []

    // 使用 Map 查找，性能更好
    if (props.modelValue[0]) {
        const provinceName = provinceCodeMap.get(props.modelValue[0])
        if (provinceName) labels.push(provinceName)
    }

    if (props.modelValue[1]) {
        const cityName = cityCodeMap.value.get(props.modelValue[1])
        if (cityName) labels.push(cityName)
    }

    // 只有当 columns 为 3 时才显示区县
    if (props.columns === 3 && props.modelValue[2]) {
        const districtName = districtCodeMap.value.get(props.modelValue[2])
        if (districtName) labels.push(districtName)
    }

    return labels.join(' ')
})

// 更新城市数据和映射
const updateCitiesData = (provinceIndex) => {
    const citiesData = citysData[provinceIndex] || []
    cities.value = citiesData
    cityCodeMap.value = new Map(citiesData.map(c => [c.code, c.name]))
    return citiesData
}

// 更新区县数据和映射
const updateDistrictsData = (provinceIndex, cityIndex) => {
    const districtsData = areasData[provinceIndex]?.[cityIndex] || []
    districts.value = districtsData
    districtCodeMap.value = new Map(districtsData.map(d => [d.code, d.name]))
    return districtsData
}

// 更新选中编码
const updateSelectedCodes = (provinceIndex, cityIndex, districtIndex) => {
    const codes = []
    
    if (provinces[provinceIndex]) {
        codes[0] = provinces[provinceIndex].code
    }
    if (cities.value[cityIndex]) {
        codes[1] = cities.value[cityIndex].code
    }
    if (districts.value[districtIndex]) {
        codes[2] = districts.value[districtIndex].code
    }
    
    selectedCodes.value = codes
    return codes
}

/**
 * 根据文本数组匹配地区编码
 * @param {Array} texts - 文本数组 ['省份名', '城市名', '区县名']
 * @returns {Array} - 编码数组
 */
const convertTextsToCodes = (texts) => {
    if (!texts || !Array.isArray(texts) || texts.length === 0) {
        return []
    }

    const codes = []
    let provinceIndex = -1
    let cityIndex = -1
    let districtIndex = -1

    // 查找省份
    if (texts[0]) {
        provinceIndex = provinces.findIndex(p => p.name === texts[0])
        if (provinceIndex !== -1) {
            codes[0] = provinces[provinceIndex].code
        }
    }

    // 查找城市
    if (texts[1] && provinceIndex !== -1) {
        const citiesData = citysData[provinceIndex] || []
        cityIndex = citiesData.findIndex(c => c.name === texts[1])
        if (cityIndex !== -1) {
            codes[1] = citiesData[cityIndex].code
        }
    }

    // 查找区县（仅在3列模式下）
    if (texts[2] && props.columns === 3 && provinceIndex !== -1 && cityIndex !== -1) {
        const districtsData = areasData[provinceIndex]?.[cityIndex] || []
        districtIndex = districtsData.findIndex(d => d.name === texts[2])
        if (districtIndex !== -1) {
            codes[2] = districtsData[districtIndex].code
        }
    }

    return codes.filter(code => code) // 过滤掉空值
}

// picker-view 改变事件
const onPickerChange = (e) => {
    const values = e.detail.value
    const [provinceIndex, cityIndex, districtIndex = 0] = values
    const [prevProvinceIndex, prevCityIndex] = pickerValue.value

    // 省份变化时，重新加载城市和区县
    if (provinceIndex !== prevProvinceIndex) {
        updateCitiesData(provinceIndex)
        
        if (props.columns === 3) {
            updateDistrictsData(provinceIndex, 0)
            pickerValue.value = [provinceIndex, 0, 0]
            updateSelectedCodes(provinceIndex, 0, 0)
        } else {
            pickerValue.value = [provinceIndex, 0]
            updateSelectedCodes(provinceIndex, 0, 0)
        }
    }
    // 城市变化时，重新加载区县（仅在3列模式下）
    else if (cityIndex !== prevCityIndex) {
        if (props.columns === 3) {
            updateDistrictsData(provinceIndex, cityIndex)
            pickerValue.value = [provinceIndex, cityIndex, 0]
            updateSelectedCodes(provinceIndex, cityIndex, 0)
        } else {
            pickerValue.value = [provinceIndex, cityIndex]
            updateSelectedCodes(provinceIndex, cityIndex, 0)
        }
    }
    // 区县变化时（仅在3列模式下）
    else if (props.columns === 3) {
        pickerValue.value = e.detail.value
        updateSelectedCodes(provinceIndex, cityIndex, districtIndex)
    }
}

// 取消选择
const cancelPicker = () => {
    showPicker.value = false
    // 重置为当前值
    initPickerValue()
}

// 确认选择
const confirmPicker = () => {
    showPicker.value = false

    // 确保当前选择的数据和映射都是最新的
    const values = pickerValue.value
    const [provinceIndex, cityIndex, districtIndex = 0] = values
    
    // 确保城市数据已加载
    if (cities.value.length === 0) {
        updateCitiesData(provinceIndex)
    }
    
    // 仅在3列模式下确保区县数据已加载
    if (props.columns === 3 && districts.value.length === 0) {
        updateDistrictsData(provinceIndex, cityIndex)
    }
    
    updateSelectedCodes(provinceIndex, cityIndex, props.columns === 3 ? districtIndex : 0)

    // 过滤掉空值，并根据列数限制返回的数据
    let filteredCodes = selectedCodes.value.filter(code => code)
    if (props.columns === 2) {
        filteredCodes = filteredCodes.slice(0, 2)
    }

    emit('update:modelValue', filteredCodes)
    emit('change', filteredCodes)
}

// 初始化picker值
const initPickerValue = () => {
    // 如果没有选中值，重置为默认状态
    if (!props.modelValue?.length) {
        pickerValue.value = props.columns === 2 ? [0, 0] : [0, 0, 0]
        selectedCodes.value = []
        cities.value = []
        districts.value = []
        cityCodeMap.value.clear()
        districtCodeMap.value.clear()
        return
    }

    // 查找省份索引
    const provinceIndex = Math.max(0, provinces.findIndex(p => p.code === props.modelValue[0]))
    
    // 更新城市数据
    const citiesData = updateCitiesData(provinceIndex)
    
    // 查找城市索引
    const cityIndex = props.modelValue[1] ? 
        Math.max(0, citiesData.findIndex(c => c.code === props.modelValue[1])) : 0
    
    let districtIndex = 0
    
    // 仅在3列模式下处理区县
    if (props.columns === 3) {
        // 更新区县数据
        const districtsData = updateDistrictsData(provinceIndex, cityIndex)
        
        // 查找区县索引
        districtIndex = props.modelValue[2] ? 
            Math.max(0, districtsData.findIndex(d => d.code === props.modelValue[2])) : 0
            
        pickerValue.value = [provinceIndex, cityIndex, districtIndex]
    } else {
        pickerValue.value = [provinceIndex, cityIndex]
    }
    
    selectedCodes.value = [...props.modelValue]
}

// 当选择器打开时，确保数据完整性
const ensurePickerData = () => {
    // 如果没有选中值，加载第一个省的城市数据以供用户选择
    if (!props.modelValue?.length && provinces.length > 0) {
        updateCitiesData(0)
        
        // 仅在3列模式下加载区县数据
        if (props.columns === 3) {
            updateDistrictsData(0, 0)
        }
    }
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue, oldValue) => {
    // 只有在值真正发生变化时才重新初始化
    if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
        initPickerValue()
    }
}, { deep: true, immediate: false })

// 监听选择器显示状态
watch(showPicker, (newValue) => {
    if (newValue) {
        // 选择器打开时，确保有完整的数据供用户选择
        ensurePickerData()
    }
})

// 监听 textsRange 变化，自动转换为编码并更新 v-model
watch(() => props.textsRange, (newTexts, oldTexts) => {
    // 只有在文本数组真正发生变化时才处理
    if (JSON.stringify(newTexts) !== JSON.stringify(oldTexts)) {
        const codes = convertTextsToCodes(newTexts)
        if (codes.length > 0) {
            emit('update:modelValue', codes)
        }
    }
}, { deep: true, immediate: true })

// 组件挂载时初始化
onMounted(() => {
    // 如果有初始值，则初始化picker值
    if (props.modelValue && props.modelValue.length > 0) {
        initPickerValue()
    }
})
</script>

<style lang="scss" scoped>
.area-picker {
    width: 100%;
}

.picker-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60rpx;
    width: 100%;
    cursor: pointer;
}

.picker-text {
    flex: 1;
    font-size: 28rpx;
    color: #262937;
    text-align: right;

    &.placeholder {
        color: #c0c4cc;
    }
}

.picker-popup {
    background: #fff;
    border-radius: 14rpx 14rpx 0 0;
}

.picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel {
    font-size: 28rpx;
    color: #999;
}

.picker-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #262937;
}

.picker-confirm {
    font-size: 28rpx;
    color: #FF7373;
}

.picker-content {
    position: relative;
}

.picker-view {
    width: 100%;
    height: 500rpx;
    background: #fff;
}

.picker-item {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #262937;
}
</style>