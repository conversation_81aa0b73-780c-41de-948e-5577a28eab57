<template>
    <view class="job-status-section">
        <view class="section-card">
            <view class="section-item">
                <view class="section-header">
                    <view class="section-header-left">
                        <view class="section-title-bar"></view>
                        <text class="section-title">求职状态</text>
                    </view>
                </view>
                <view class="section-content-with-arrow">
                    <SelectPicker
                        :model-value="jobStatus"
                        :options="jobStatusOptions"
                        placeholder="请选择求职状态"
                        text-align="left"
                        :right-arrow="true"
                        @update:model-value="onJobStatusChange"
                    />
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import SelectPicker from './SelectPicker.vue'

const props = defineProps({
    jobStatus: {
        type: [String, Number],
        default: ''
    },
    jobStatusOptions: {
        type: Array,
        default: () => []
    },
})

const emit = defineEmits(['update:jobStatus', 'change'])

const onJobStatusChange = (value) => {
    emit('update:jobStatus', value)
    emit('change', value)
}
</script>

<style lang="scss" scoped>

.section-card {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.section-item {
    padding: 32rpx;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
}

.section-header-left {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.section-title-bar {
    width: 6rpx;
    height: 28rpx;
    background: #FF7373;
    border-radius: 3rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #262937;
}

.section-content-with-arrow {
    min-height: 60rpx;
    display: flex;
    align-items: center;
}
</style>