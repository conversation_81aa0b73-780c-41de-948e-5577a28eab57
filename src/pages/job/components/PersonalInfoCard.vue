<template>
    <view class="personal-info-card">
        <view class="user-info-card">
            <view class="user-info-left">
                <image class="avatar" :src="personalInfo.avatar" />
            </view>
            <view class="user-info-center">
                <view class="name">{{ personalInfo.name || '未设置' }}</view>
                <view class="details">
                    {{ personalInfo.experience || '未设置' }} | {{ personalInfo.gender || '未设置' }} | {{ personalInfo.age ||
                    '未设置' }} | {{ personalInfo.degree || '未设置' }}
                </view>
                <view class="phone">
                    <u-icon name="phone-fill" color="#858997" size="28"></u-icon>
                    <text>{{ personalInfo.phone || '未设置' }}</text>
                </view>
            </view>
            <view v-if="showEditButton" class="user-info-right" @click="onEdit">
                <u-icon name="edit-pen" color="#FF7373" size="36"></u-icon>
            </view>
        </view>
    </view>
</template>

<script setup>
const props = defineProps({
    personalInfo: {
        type: Object,
        required: true
    },
    showEditButton: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['edit'])

const onEdit = () => {
    emit('edit')
}
</script>

<style lang="scss" scoped>
.user-info-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;
}

.user-info-left {
    .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
    }
}

.user-info-center {
    flex: 1;

    .name {
        font-size: 36rpx;
        font-weight: 600;
        color: #262937;
        margin-bottom: 12rpx;
    }

    .details {
        font-size: 24rpx;
        color: #858997;
        margin-bottom: 16rpx;
        line-height: 1.4;
    }

    .phone {
        display: flex;
        align-items: center;
        gap: 8rpx;
        font-size: 28rpx;
        color: #858997;
    }
}

.user-info-right {
    padding: 16rpx;
    cursor: pointer;

    &:active {
        opacity: 0.6;
    }
}
</style>