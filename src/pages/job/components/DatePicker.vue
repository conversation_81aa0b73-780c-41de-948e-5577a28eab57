<template>
    <view class="date-picker-wrapper">
        <view class="picker-value" :class="{ 'placeholder': !displayValue }" :style="{ textAlign: textAlign }"
            @click="openPicker">
            {{ displayValue || placeholder }}
        </view>

        <!-- Duration Mode Modal -->
        <u-modal v-if="mode === 'duration'" :modelValue="showDurationModal" :title="durationTitle"
            :show-cancel-button="true" @confirm="confirmDuration" @cancel="cancelDuration">
            <view class="duration-picker-content">
                <view class="date-row">
                    <text class="date-label">开始时间：</text>
                    <view class="date-picker" @click="showStartDatePicker = true">
                        <text>{{ tempStartDate || '请选择' }}</text>
                        <u-icon name="calendar" color="#FF7373" size="36"></u-icon>
                    </view>
                </view>
                <view class="date-row">
                    <text class="date-label">结束时间：</text>
                    <view class="date-picker" @click="showEndDatePicker = true">
                        <text>{{ tempEndDate || '请选择' }}</text>
                        <u-icon name="calendar" color="#FF7373" size="36"></u-icon>
                    </view>
                </view>
            </view>
        </u-modal>

        <!-- Date Pickers -->
        <u-picker v-if="mode === 'single'" v-model="showDatePicker" mode="time" :default-time="singleDefaultTime"
            @confirm="confirmSingleDate"></u-picker>
        <u-picker v-if="mode === 'duration'" v-model="showStartDatePicker" mode="time" :default-time="startDefaultTime"
            @confirm="confirmStartDate"></u-picker>

        <u-picker v-if="mode === 'duration'" v-model="showEndDatePicker" mode="time" :default-time="endDefaultTime"
            @confirm="confirmEndDate"></u-picker>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    mode: {
        type: String,
        default: 'single',
        validator: (value) => ['single', 'duration'].includes(value)
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    durationTitle: {
        type: String,
        default: '选择时间段'
    },
    format: {
        type: String,
        default: 'YYYY.MM'
    },
    separator: {
        type: String,
        default: '—'
    },
    textAlign: {
        type: String,
        default: 'right',
        validator: (value) => ['left', 'center', 'right'].includes(value)
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const showDatePicker = ref(false)
const showDurationModal = ref(false)
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)

const tempStartDate = ref('')
const tempEndDate = ref('')

// 计算单个日期选择器的默认时间
const singleDefaultTime = computed(() => {
    if (props.mode === 'single' && props.modelValue) {
        return props.modelValue
    }
    return ''
})

// 计算开始日期选择器的默认时间
const startDefaultTime = computed(() => {
    if (props.mode === 'duration' && props.modelValue?.startDate) {
        return props.modelValue.startDate
    }
    return ''
})

// 计算结束日期选择器的默认时间
const endDefaultTime = computed(() => {
    if (props.mode === 'duration' && props.modelValue?.endDate) {
        return props.modelValue.endDate
    }
    return ''
})

const displayValue = computed(() => {
    if (props.mode === 'single') {
        return props.modelValue
    } else {
        if (props.modelValue && props.modelValue.startDate && props.modelValue.endDate) {
            return `${props.modelValue.startDate}${props.separator}${props.modelValue.endDate}`
        }
        return ''
    }
})

const openPicker = () => {
    if (props.mode === 'single') {
        showDatePicker.value = true
    } else {
        tempStartDate.value = props.modelValue?.startDate || ''
        tempEndDate.value = props.modelValue?.endDate || ''
        showDurationModal.value = true
    }
}

const confirmSingleDate = (e) => {
    const dateStr = `${e.year}-${String(e.month).padStart(2, '0')}-${String(e.day).padStart(2, '0')}`
    emit('update:modelValue', dateStr)
    emit('change', dateStr)
}

const confirmStartDate = (e) => {
    tempStartDate.value = `${e.year}-${String(e.month).padStart(2, '0')}-${String(e.day).padStart(2, '0')}`
}

const confirmEndDate = (e) => {
    tempEndDate.value = `${e.year}-${String(e.month).padStart(2, '0')}-${String(e.day).padStart(2, '0')}`
}

const confirmDuration = () => {
    if (tempStartDate.value && tempEndDate.value) {
        // 验证结束时间不能小于开始时间
        const startTime = new Date(tempStartDate.value)
        const endTime = new Date(tempEndDate.value)

        if (endTime < startTime) {
            uni.showToast({
                title: '结束时间不能早于开始时间',
                icon: 'none',
                duration: 2000
            })
            return
        } else {
            const durationValue = {
                startDate: tempStartDate.value,
                endDate: tempEndDate.value
            }
            emit('update:modelValue', durationValue)
            emit('change', durationValue)
            showDurationModal.value = false

        }
    }
}

const cancelDuration = () => {
    tempStartDate.value = props.modelValue?.startDate || ''
    tempEndDate.value = props.modelValue?.endDate || ''
    showDurationModal.value = false
}
</script>

<style lang="scss" scoped>
.date-picker-wrapper {
    width: 100%;
}

.picker-value {
    width: 100%;
    color: #262937;
    font-size: 28rpx;
    line-height: normal;
    cursor: pointer;
}

.picker-value.placeholder {
    color: #c0c4cc;
}

.duration-picker-content {
    padding: 40rpx 32rpx;
}

.date-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
}

.date-label {
    font-size: 28rpx;
    color: #262937;
}

.date-picker {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding: 0 20rpx;
    background: #F6F6F6;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #262937;
    flex-grow: 1;
    justify-content: space-between;
}
</style>