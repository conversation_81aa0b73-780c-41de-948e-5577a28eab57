<template>
    <view class="select-picker-wrapper" @click="openPicker">
        <view class="picker-value" :class="{ 'placeholder': !displayValue }" :style="{ textAlign: textAlign }">
            {{ displayValue || placeholder }}
        </view>

        <u-icon v-if="rightArrow" name="arrow-right" color="#BCC0CC" size="28" class="right-arrow" />

        <u-select v-model="showPicker" :list="options" :default-value="defaultValue" @confirm="confirmSelection" />
    </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    },
    options: {
        type: Array,
        default: () => []
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    valueKey: {
        type: String,
        default: 'value'
    },
    labelKey: {
        type: String,
        default: 'label'
    },
    textAlign: {
        type: String,
        default: 'right',
        validator: (value) => ['left', 'center', 'right'].includes(value)
    },
    rightArrow: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const showPicker = ref(false)

const displayValue = computed(() => {
    if (!props.modelValue) return ''

    const selectedOption = props.options.find(option =>
        option[props.valueKey] === props.modelValue
    )

    return selectedOption ? selectedOption[props.labelKey] : ''
})

const defaultValue = computed(() => {
    if (!props.modelValue) return []

    const index = props.options.findIndex(option =>
        option[props.valueKey] === props.modelValue
    )

    return index >= 0 ? [index] : []
})

const openPicker = () => {
    showPicker.value = true
}

const confirmSelection = (selectedItems) => {
    if (selectedItems && selectedItems.length > 0) {
        const selectedValue = selectedItems[0][props.valueKey]
        emit('update:modelValue', selectedValue)
        emit('change', selectedValue, selectedItems[0])
    }
}

// 监听options变化，确保defaultValue正确更新
watch(() => props.options, () => {
    // 当options更新时，触发computed重新计算
}, { deep: true })
</script>

<style lang="scss" scoped>
.select-picker-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.picker-value {
    flex: 1;
    color: #262937;
    font-size: 28rpx;
    line-height: normal;
    cursor: pointer;
}

.picker-value.placeholder {
    color: #c0c4cc;
}

.right-arrow {
    flex-shrink: 0;
}
</style>