<template>
  <view class="function-cards">
    <view class="cards-container">
      <!-- 在线简历 -->
      <view class="function-card" @click="handleCardClick('resume')">
        <view class="card-content-row">
          <view class="card-content-text">
            <view class="card-title">在线简历</view>
            <view class="card-subtitle">求职信息</view>
          </view>
          <view class="card-icon">
            <u-icon name="file-text" color="#FF7373" size="72"></u-icon>
          </view>
        </view>
      </view>

      <!-- 我的求职 -->
      <view class="function-card" @click="handleCardClick('my-jobs')">
        <view class="card-content-row">
          <view class="card-content-text">
            <view class="card-title">我的求职</view>
            <view class="card-subtitle">求职管理</view>
          </view>
          <view class="card-icon">
            <u-icon name="account" color="#4DCB73" size="72"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineEmits } from 'vue'

const emit = defineEmits(['cardClick'])

// 处理卡片点击
const handleCardClick = (type) => {
  emit('cardClick', type)
}
</script>

<style lang="scss" scoped>
.function-cards {
  background: #FFFFFF;
  margin-bottom: 20rpx;
  padding: 40rpx 30rpx;
}

.cards-container {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.function-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  background: #FFF8F8;
  transition: all 0.3s ease;
  min-width: 260rpx;
  max-width: 320rpx;
  box-sizing: border-box;

  &:active {
    transform: scale(0.95);
    background: #FFF4F4;
  }
}

.card-content-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.card-content-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
}

.card-icon {
  width: 96rpx;
  height: 96rpx;
  margin-left: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262937;
  margin-bottom: 8rpx;
}

.card-subtitle {
  font-size: 24rpx;
  color: #787C8D;
}

// 第一个卡片（在线简历）
.function-card:nth-child(1) {
  background: linear-gradient(135deg, #FFE8E8 0%, #FFF2F2 100%);

  &:active {
    background: linear-gradient(135deg, #FFE0E0 0%, #FFEBEB 100%);
  }
}

// 第二个卡片（我的求职）
.function-card:nth-child(2) {
  background: linear-gradient(135deg, #F0FFF4 0%, #F8FFF9 100%);
  
  &:active {
    background: linear-gradient(135deg, #E8FFED 0%, #F1FFF3 100%);
  }
}
</style> 