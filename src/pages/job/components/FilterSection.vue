<template>
  <view class="filter-section">
    <view class="section-title">
      <u-icon :name="icon" color="#FF7373" size="32"></u-icon>
      <text class="title-text">{{ title }}</text>
      <text v-if="multiple" class="title-hint">（可多选）</text>
    </view>
    <view class="section-options">
      <view
        v-for="option in options"
        :key="option.value"
        class="custom-tag"
        :class="{ active: isActive(option) }"
        @click="toggleOption(option)"
      >
        {{ option.label }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
  title: String,
  icon: String,
  options: Array,
  modelValue: [String, Array],
  multiple: Boolean
})

const emit = defineEmits(['update:modelValue'])

const isActive = (option) => {
  const optionValueStr = String(option.value)
  if (props.multiple) {
    // 确保 modelValue 是一个数组，并将所有元素转为字符串进行比较
    return Array.isArray(props.modelValue) && props.modelValue.map(String).includes(optionValueStr)
  }
  // 将 modelValue 转为字符串进行比较
  return String(props.modelValue) === optionValueStr
}

const toggleOption = (option) => {
  const optionValueStr = String(option.value)
  if (props.multiple) {
    // 确保 modelValue 是一个数组
    const currentValues = Array.isArray(props.modelValue) ? [...props.modelValue] : []
    const modelValueStrArray = currentValues.map(String)
    const index = modelValueStrArray.indexOf(optionValueStr)
    
    if (index > -1) {
      // 如果已存在，则移除
      currentValues.splice(index, 1)
    } else {
      // 如果不存在，则添加（以字符串形式）
      currentValues.push(optionValueStr)
    }
    emit('update:modelValue', currentValues)
  } else {
    // 单选时，直接发出字符串形式的值
    emit('update:modelValue', optionValueStr)
  }
}
</script>

<style lang="scss" scoped>
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 12rpx;

  .title-text {
    font-size: 30rpx;
    font-weight: 600;
    color: #262937;
  }

  .title-hint {
    font-size: 24rpx;
    color: #AEB0BC;
  }
}

.section-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  
  .custom-tag {
    font-size: 26rpx;
    padding: 8rpx 20rpx;
    border-radius: 24rpx;
    background: #F6F6F6;
    color: #333;
    position: relative;
    cursor: pointer;
    margin-bottom: 8rpx;
    transition: all 0.2s;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      transform: scale(0.5);
      transform-origin: 0 0;
      pointer-events: none;
      box-sizing: border-box;
      border-radius: 48rpx;
      border: 1px solid transparent;
      z-index: 1;
    }
    
    &.active {
      background: #FFF0F0;
      color: #FF7373;

      &::after {
        border-color: #FF7373;
      }
    }
  }
}

</style>
