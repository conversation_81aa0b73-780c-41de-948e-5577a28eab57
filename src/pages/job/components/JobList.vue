<template>
  <view class="job-list">
    <!-- 自定义筛选标题栏 -->
    <view class="list-header">
      <view class="header-title">职位列表</view>
      <view class="header-filter" @click="onFilterClick">
        <u-icon name="list-dot" color="#787C8D" size="18"></u-icon>
        <text class="filter-text">筛选</text>
      </view>
    </view>
    <view class="header-underline"></view>

    <!-- 空态 -->
    <u-empty v-if="jobList.length === 0" text="暂无招聘信息" mode="data" />
    <!-- 列表内容和加载更多 -->
    <template v-else>
      <JobCard
        v-for="job in jobList"
        :key="job.id"
        :jobData="job"
        @click="onJobClick"
      />
      <!-- 加载更多 -->
      <u-loadmore
        :status="loadStatus"
        loadmoreText="点击加载更多"
        loadingText="加载中..."
        nomoreText="没有更多了"
        @clickLoadmore="onLoadMore"
      />
    </template>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'
import JobCard from './JobCard.vue'

const props = defineProps({
  jobList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click', 'filter', 'loadMore'])

const onFilterClick = () => {
  emit('filter')
}

const onJobClick = (job) => {
  emit('click', job)
}

const onLoadMore = () => {
  if (!props.loading && props.hasMore) {
    emit('loadMore')
  }
}

const loadStatus = computed(() => {
  if (props.loading) return 'loading'
  if (!props.hasMore) return 'nomore'
  return 'loadmore'
})


</script>

<style lang="scss" scoped>
.job-list {
  padding: 0 30rpx;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0 0 0;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262937;
}

.header-filter {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #787C8D;
  font-size: 28rpx;
  .filter-text {
    margin-left: 4rpx;
  }
}

.header-underline {
  height: 6rpx;
  background: #FF7373;
  border-radius: 3rpx;
  margin-bottom: 24rpx;
  margin-top: 8rpx;
}


</style> 