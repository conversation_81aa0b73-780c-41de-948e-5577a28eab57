<template>
  <view class="job-card" @click="handleCardClick">
    <!-- 职位基本信息 -->
    <view class="job-header">
      <view class="job-title-row">
        <view class="job-title">{{ jobData.title }}</view>
        <view class="job-type" :class="jobData.type === '全职' ? 'full-time' : 'part-time'">
          {{ jobData.type }}
        </view>
      </view>
      <view class="job-salary">{{ jobData.salaryRange }}</view>
    </view>
    <!-- 职位要求 -->
    <view class="job-requirements">
      <view class="requirement-item">{{ jobData.area }}</view>
      <view class="requirement-item">{{ jobData.experience }}</view>
      <view class="requirement-item">{{ jobData.education }}</view>
    </view>

    <!-- 福利标签 -->
    <view class="job-benefits">
      <view 
        v-for="(benefit, index) in jobData.benefits" 
        :key="index"
        class="benefit-tag"
      >
        {{ benefit }}
      </view>
    </view>

    <!-- 公司信息 -->
    <view class="company-info">
      <view class="company-left">
        <view class="company-avatar">
          <image class="avatar-image" :src="jobData.logo" mode="aspectFill" lazy-load></image>
        </view>
        <view class="company-details">
          <view class="company-name">{{ jobData.company }}</view>
          <view class="company-address">{{ jobData.address }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
  jobData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click'])


// 处理卡片点击
const handleCardClick = () => {
  emit('click', props.jobData)
}
</script>

<style lang="scss" scoped>
.job-card {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  
  &:active {
    background: #FAFAFA;
  }
}

.job-header {
  margin-bottom: 24rpx;
  
  .job-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    
    .job-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #262937;
      flex: 1;
    }
    
    .job-type {
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 22rpx;
      font-weight: 500;
      margin-left: 16rpx;
      
      &.full-time {
        background: #FFE8E8;
        color: #FF7373;
      }
      
      &.part-time {
        background: #FFF2E8;
        color: #FF8C42;
      }
    }
  }
  
  .job-salary {
    font-size: 30rpx;
    font-weight: 600;
    color: #FF7373;
  }
}

.job-requirements {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
  
  .requirement-item {
    font-size: 26rpx;
    color: #787C8D;
    position: relative;
    
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: -12rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 2rpx;
      height: 20rpx;
      background: #E5E5E5;
    }
  }
}

.job-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
  
  .benefit-tag {
    padding: 8rpx 16rpx;
    background: #FFF0F0;
    border-radius: 16rpx;
    font-size: 24rpx;
    color: #FF7373;
    border: 1rpx solid #FFE8E8;
  }
}

.company-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .company-left {
    display: flex;
    align-items: center;
    flex: 1;
    
    .company-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 12rpx;
      overflow: hidden;
      margin-right: 20rpx;
      background: #F6F6F6;
      
      .avatar-image {
        width: 100%;
        height: 100%;
      }
    }
    
    .company-details {
      flex: 1;
      
      .company-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #262937;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .company-address {
        font-size: 24rpx;
        color: #AEB0BC;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.4;
      }
    }
  }
}
</style> 