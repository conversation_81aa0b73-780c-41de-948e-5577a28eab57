<template>
    <view class="bottom-button-wrapper">
        <view class="footer-buttons">
            <button 
                v-for="button in buttons" 
                :key="button.type"
                :class="getButtonClass(button)"
                @click="handleClick(button)"
            >
                {{ button.text }}
            </button>
        </view>
    </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    buttons: {
        type: Array,
        default: () => [
            { type: 'primary', text: '保存' }
        ]
    },
    theme: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'danger'].includes(value)
    }
})

const emit = defineEmits(['click'])

const getButtonClass = (button) => {
    const baseClasses = ['btn']
    
    switch (button.type) {
        case 'primary':
            baseClasses.push('primary-btn')
            break
        case 'cancel':
            baseClasses.push('cancel-btn')
            break
        case 'delete':
            baseClasses.push('delete-btn')
            break
        case 'save':
            baseClasses.push('save-btn')
            break
        default:
            baseClasses.push('default-btn')
    }
    
    return baseClasses.join(' ')
}

const handleClick = (button) => {
    emit('click', button.type, button)
}
</script>

<style lang="scss" scoped>
.bottom-button-wrapper {
    position: sticky;
    bottom: 0;
    background: #fff;
    z-index: 100;
}

.footer-buttons {
    display: flex;
    gap: 20rpx;
    padding: 20rpx;
    background: #fff;
    border-top: 1rpx solid #F0F0F0;
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

    .btn {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        margin: 0;
        border: none;
        outline: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
        }
    }

    .primary-btn, .save-btn {
        background: #FF7373;
        color: #fff;
        
        &:hover {
            background: #ff5c5c;
        }
    }

    .cancel-btn {
        background: #fff;
        color: #FF7373;
        border: 2rpx solid #FF7373;
        
        &:hover {
            background: #fff8f8;
        }
    }

    .delete-btn {
        background: #fff;
        color: #E53E3E;
        border: 2rpx solid #E53E3E;
        
        &:hover {
            background: #fef7f7;
        }
    }

    .default-btn {
        background: #f5f5f5;
        color: #666;
        
        &:hover {
            background: #e8e8e8;
        }
    }
}

.footer-buttons .btn:only-child {
    width: 100%;
}
</style>