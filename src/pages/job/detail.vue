<template>
  <view class="job-detail-page">
    <u-navbar :is-back="true" title="招聘职位详情" title-color="#262937" title-size="36" :border-bottom="false"
      @back="goBack">
    </u-navbar>

    <scroll-view scroll-y class="scroll-view-content">
      <!-- 实际内容 -->
      <view class="content">
        <!-- 职位基础信息 -->
        <view class="card">
          <view class="job-header">
            <view class="title-container">
              <text class="title">{{ jobDetails.title }}</text>
              <text class="type">{{ jobDetails.type }}</text>
            </view>
            <text class="salary">{{ jobDetails.salary }}</text>
          </view>
          <view class="company">{{ jobDetails.company }}</view>
          <view class="tags-container">
            <text class="tag-grey">{{ jobDetails.location }}</text>
            <text class="tag-grey">{{ jobDetails.experience }}</text>
            <text class="tag-grey">{{ jobDetails.education }}</text>
          </view>
          <view class="tags-container">
            <text v-for="(benefit, index) in jobDetails.benefits" :key="index" class="tag-blue">{{ benefit }}</text>
          </view>
        </view>

        <!-- 机构地址 -->
        <view class="card">
          <view class="section-title">机构地址</view>
          <view class="address-container">
            <u-icon name="map-fill" color="#FF7373" size="32"></u-icon>
            <text class="address-text">{{ jobDetails.address }}</text>
          </view>
        </view>

        <!-- 职位要求 -->
        <view class="card">
          <view class="section-title">职位要求</view>
          <view class="requirements-grid">
            <text>所属岗位：{{ jobDetails.requirements.position }}</text>
            <text>性别要求：{{ jobDetails.requirements.gender }}</text>
            <text>年龄要求：{{ jobDetails.requirements.age }}</text>
            <text>学历要求：{{ jobDetails.requirements.education }}</text>
            <text class="requirement-full-width">专业要求：{{ jobDetails.requirements.major }}</text>
          </view>
          <view class="section-subtitle">持证要求：</view>
          <view class="tags-container">
            <text v-for="(cert, index) in jobDetails.certifications" :key="index" class="tag-grey">{{ cert }}</text>
          </view>
        </view>

        <!-- 职位描述 -->
        <view class="card">
          <view class="section-title">职位描述</view>
          <view class="section-subtitle">岗位职责</view>
          <view class="description-content">
            <view v-for="(item, index) in jobDetails.responsibilities" :key="index">{{ index + 1 }}、{{ item }}</view>
          </view>
        </view>

        <!-- 工作环境 -->
        <view class="card">
          <view class="section-title">工作环境</view>
          <view class="tags-container">
            <text v-for="(tag, index) in jobDetails.environment" :key="index" class="tag-blue">{{ tag }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="footer-buttons">
      <button class="btn resume-btn" @click="openResumeModal">
        <u-icon name="checkmark-circle" color="#FF7373" size="36"></u-icon>
        <text>投递简历</text>
      </button>
      <button class="btn chat-btn" @click="openChatModal">
        <u-icon name="chat" color="#fff" size="36"></u-icon>
        <text>在线沟通</text>
      </button>
    </view>

    <!-- 投递简历弹窗 -->
    <u-modal v-model="showResumeModal" title="选择简历" :show-cancel-button="false" confirm-text="确定"
      @confirm="confirmResume">
      <view class="resume-list">
        <view v-if="resumes.length === 0" class="empty-state">
          <text class="empty-text">暂无附件简历</text>
        </view>
        <view v-else v-for="resume in resumes" :key="resume.id" class="resume-item" @click="selectedResume = resume">
          <view class="resume-info">
            <u-icon name="file-text-fill" color="#E53A34" size="48"></u-icon>
            <view class="resume-details">
              <text class="name">{{ resume.name }}</text>
              <text class="date">更新于{{ resume.updated_at }}</text>
            </view>
          </view>
          <u-icon v-if="selectedResume.id === resume.id" name="checkmark-circle-fill" color="#FF7373"
            size="40"></u-icon>
          <u-icon v-else name="circle" color="#d1d1d1" size="40"></u-icon>
        </view>
      </view>
    </u-modal>

    <!-- 在线沟通弹窗 -->
    <u-modal v-model="showChatModal" title="在线沟通" :show-cancel-button="true" confirm-text="发送" cancel-text="取消"
      @confirm="sendChatMessage" @cancel="showChatModal = false">
      <view class="chat-input-wrapper">
        <textarea v-model="chatMessage" class="chat-input" placeholder="请输入想要咨询的问题.." />
      </view>
    </u-modal>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app'
import request from '@/request'
import { getDictionaryText, getCertificationTexts } from './utils/jobDictionary'

import { useResumeStore } from './stores/resumeStore'
import { getAreaNameFromLocal } from './utils/resumeUtils'

let cv_id = null
const showResumeModal = ref(false);
const showChatModal = ref(false);
const selectedResume = ref(null);
const chatMessage = ref('');
const jobId = ref(null);
const jobRawData = ref(null); // 存储原始职位数据，用于API调用

const jobDetails = reactive({
  title: '',
  type: '',
  salary: '',
  company: '',
  location: '',
  experience: '',
  education: '',
  benefits: [],
  address: '',
  requirements: {
    position: '',
    gender: '',
    age: '',
    education: '',
    major: ''
  },
  certifications: [],
  responsibilities: [],
  environment: []
});

const resumes = reactive([]);

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    jobId.value = options.id
    fetchJobDetails()
  }
})

const fetchJobDetails = () => {
  if (!jobId.value) return


  // 调用招聘职位详情API接口
  request({
    url: '/api/recruitment/job/detail',
    data: {
      id: jobId.value
    }
  }).then(async res => {
    // 格式化API返回的数据
    await formatJobDetails(res)
  }).catch(async error => {
    console.error('API接口调用失败:', error)
  })
}

// 格式化职位详情数据
const formatJobDetails = async (data) => {
  // 保存原始数据用于API调用
  jobRawData.value = data;
  // 解析福利待遇JSON字符串
  let benefits = []
  try {
    benefits = JSON.parse(data.welfare_subsidy || '[]')
  } catch (e) {
    benefits = []
  }

  // 解析工作环境JSON字符串
  let environment = []
  try {
    environment = JSON.parse(data.working_environment || '[]')
  } catch (e) {
    environment = []
  }

  // 使用字典数据获取证书要求
  const certifications = await getCertificationTexts(data.certification_requirements)

  // 使用字典数据获取各种映射值
  const genderText = await getDictionaryText('gender', data.gender)
  const educationText = await getDictionaryText('educational_require', data.educational_require)
  const employmentText = await getDictionaryText('employment_form', data.employment_form)

  // 获取区域名称
  const areaName = getAreaNameFromLocal(data.work_location_area_code)

  // 填充数据
  jobDetails.title = data.name || ''
  jobDetails.type = employmentText || ''
  jobDetails.salary = `${data.salary_benefits_start}-${data.salary_benefits_end}元/月`
  jobDetails.company = data.school_name || '' // 可以根据school_id获取具体机构名称
  jobDetails.location = areaName
  jobDetails.experience = `${data.work_experience}年经验`
  jobDetails.education = educationText || '不限'
  jobDetails.benefits = benefits
  jobDetails.address = data.working_address || ''

  jobDetails.requirements = {
    position: data.name || '',
    gender: genderText || '不限',
    age: `${data.age_requirements}岁以下`,
    education: educationText || '不限',
    major: data.professional_requirements || ''
  }

  jobDetails.certifications = certifications
  jobDetails.responsibilities = data.job_description ? [data.job_description] : []
  jobDetails.environment = environment
}

onMounted(async () => {

  const resumeStore = useResumeStore()
  const res = await resumeStore.fetchPersonalInfo()
  cv_id = res.id
  // 如果没有通过onLoad获取到id，则使用默认逻辑
  if (!jobId.value) {
    fetchJobDetails()
  }
});

const goBack = () => {
  uni.navigateBack();
};

// 获取附件简历列表
const fetchAttachedResumes = async () => {
  try {
    uni.showLoading({ title: '加载中...' });

    const response = await request({
      url: '/api/recruitment/cv/getAttached',
      method: 'GET'
    });

    if (response && response.data) {
      // 清空现有数据并添加新数据
      resumes.splice(0, resumes.length, ...response.data);

      // 如果有简历，默认选择第一个
      if (resumes.length > 0) {
        selectedResume.value = resumes[0];
      }
    }

    uni.hideLoading();
    return true;
  } catch (error) {
    uni.hideLoading();
    console.error('获取附件简历失败:', error);
    uni.showToast({
      title: '获取简历失败',
      icon: 'none'
    });
    return false;
  }
};

const openResumeModal = async () => {
  if (!cv_id) {
    uni.showModal({
      title: '提示',
      content: '您还没有更新个人信息，是否前往更新？',
      confirmText: '去更新',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/job/profile'
          });
        }
      }
    });
    return;
  }
  // 先获取附件简历列表
  const success = await fetchAttachedResumes();

  if (success) {
    if (resumes.length === 0) {
      // 没有附件简历，提示用户先上传
      uni.showModal({
        title: '提示',
        content: '您还没有上传附件简历，是否前往上传？',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/job/attachment'
            });
          }
        }
      });
    } else {
      // 有简历，显示选择弹窗
      showResumeModal.value = true;
    }
  }
};

// 统一的投递/沟通API调用函数
const saveDelivery = async (params) => {
  try {
    const response = await request({
      url: '/api/recruitment/cv/saveDelivery',
      method: 'POST',
      data: params
    });

    return response;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};

const confirmResume = async () => {
  if (!selectedResume.value?.id) {
    uni.showToast({ title: '请选择简历', icon: 'none' });
    return;
  }

  try {
    uni.showLoading({ title: '投递中...' });
    console.log(selectedResume.value)
    // 准备API参数
    const params = {
      job_id: jobId.value,
      cv_attached_id: selectedResume.value.id,
      cv_id: cv_id,
      info_type: '3' // 3=附件简历
    };

    // 如果有school_id，添加到参数中
    if (jobRawData.value && jobRawData.value.school_id) {
      params.school_id = jobRawData.value.school_id;
    }

    await saveDelivery(params);

    uni.hideLoading();
    uni.showToast({ title: '投递成功', icon: 'success' });
    showResumeModal.value = false;

  } catch (error) {
    uni.hideLoading();
    console.error('投递简历失败:', error);
    uni.showToast({
      title: '投递失败，请重试',
      icon: 'none'
    });
  }
};

const openChatModal = () => {
  if (!cv_id) {
    uni.showModal({
      title: '提示',
      content: '您还没有更新个人信息，是否前往更新？',
      confirmText: '去更新',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/job/profile'
          });
        }
      }
    });
    return;
  }
  showChatModal.value = true;
};

const sendChatMessage = async () => {
  if (!chatMessage.value.trim()) {
    uni.showToast({ title: '请输入问题', icon: 'none' });
    return;
  }

  try {
    uni.showLoading({ title: '发送中...' });

    // 准备API参数
    const params = {
      job_id: jobId.value,
      ask_content: chatMessage.value.trim(),
      info_type: '1', // 1=文本
      cv_id: cv_id
    };

    // 如果有school_id，添加到参数中
    if (jobRawData.value && jobRawData.value.school_id) {
      params.school_id = jobRawData.value.school_id;
    }

    await saveDelivery(params);

    uni.hideLoading();
    uni.showToast({ title: '发送成功', icon: 'success' });
    chatMessage.value = '';
    showChatModal.value = false;

  } catch (error) {
    uni.hideLoading();
    console.error('发送消息失败:', error);
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none'
    });
  }
};
</script>

<style lang="scss" scoped>
.job-detail-page {
  background: #F6F6F6;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-view-content {
  flex: 1;
  overflow-y: auto;
}

.content {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #262937;
  }

  .type {
    background-color: #FEF2E8;
    color: #FC8C24;
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
  }
}

.salary {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF7373;
}

.company {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 20rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;

  .tag-grey {
    background-color: #F0F0F0;
    color: #666;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
  }

  .tag-blue {
    background-color: #FFE8E8;
    color: #FF7373;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #262937;
  margin-bottom: 24rpx;
}

.section-subtitle {
  font-size: 28rpx;
  color: #333;
  margin-top: 24rpx;
  margin-bottom: 16rpx;
}

.address-container {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .address-text {
    font-size: 28rpx;
    color: #333;
  }
}

.requirements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx 32rpx;
  font-size: 28rpx;
  color: #555;
}

.requirement-full-width {
  grid-column: span 2;
}

.description-content {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

.footer-buttons {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #F0F0F0;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

  .btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
  }

  .resume-btn {
    background: #FFE8E8;
    color: #FF7373;
    border: 1rpx solid #FF7373;
  }

  .chat-btn {
    background: #FF7373;
    color: #fff;
  }
}

.resume-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 40rpx 20rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.resume-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 16rpx;
  cursor: pointer;
}

.resume-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.resume-details {
  display: flex;
  flex-direction: column;

  .name {
    font-size: 28rpx;
    color: #333;
  }

  .date {
    font-size: 24rpx;
    color: #999;
  }
}

.chat-input-wrapper {
  display: flex;
  align-items: center;
  margin: 32rpx 48rpx; // top: 16px, left/right: 24px, bottom: 16px
  gap: 16rpx; // 8px
}

.chat-input {
  flex: 1;
  height: auto; // 高度自适应
  min-height: 80rpx; // 最小高度
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 24rpx 32rpx; // 12px 16px
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.5;
}

// 响应式调整
@media (max-width: 767px) {
  .chat-input-wrapper {
    margin: 32rpx; // 16px
  }
}
</style>