/**
 * @fileoverview 职位列表管理Hook
 * 统一管理职位列表的加载、搜索、筛选、分页等逻辑
 */

import { ref, computed } from 'vue'
import { fetchJobList } from '../services/jobApiService'
import { useLoading } from './useLoading'
import { useErrorHandler } from './useErrorHandler'
import { formatJobData } from '../utils/dataTransform'

/**
 * 职位列表管理Hook
 * @param {Object} [options] - 配置选项
 * @param {number} [options.pageSize=10] - 每页数量
 * @returns {Object} 职位列表状态和方法
 */
export function useJobList(options = {}) {
  const { pageSize = 10 } = options

  // 状态管理
  const jobList = ref([])
  const currentPage = ref(1)
  const hasMore = ref(true)
  const searchParams = ref({})
  const requestId = ref(0)

  // Hooks
  const { loading, withLoading } = useLoading()
  const { withErrorHandling, showErrorToast } = useErrorHandler()

  // 计算属性
  const isEmpty = computed(() => jobList.value.length === 0)
  const isFirstPage = computed(() => currentPage.value === 1)

  /**
   * 加载职位列表
   * @param {Object} [params] - 查询参数
   * @param {boolean} [isLoadMore=false] - 是否为加载更多
   * @returns {Promise<void>}
   */
  const loadJobList = async (params = {}, isLoadMore = false) => {
    // 如果是新的搜索，重置页码和列表
    if (!isLoadMore) {
      currentPage.value = 1
      jobList.value = []
      hasMore.value = true
    }

    // 合并搜索参数
    const finalParams = {
      page: currentPage.value,
      per_page: pageSize,
      ...searchParams.value,
      ...params
    }

    // 生成请求ID，用于防止过期请求
    const currentRequestId = ++requestId.value

    const result = await withErrorHandling(
      () => withLoading(
        () => fetchJobList(finalParams),
        { 
          showUniLoading: isFirstPage.value,
          loadingText: '加载中...' 
        }
      ),
      { defaultMessage: '加载职位列表失败' }
    )

    // 检查请求是否过期
    if (currentRequestId !== requestId.value) {
      console.log('请求已过期，忽略结果')
      return
    }

    if (result) {
      try {
        const jobData = result.data || []
        
        // 格式化职位数据
        const formattedJobs = await Promise.all(
          jobData.map(job => formatJobData(job))
        )

        if (isLoadMore) {
          jobList.value.push(...formattedJobs)
        } else {
          jobList.value = formattedJobs
        }

        // 更新分页信息
        hasMore.value = result.current_page < result.last_page
        
        if (hasMore.value) {
          currentPage.value++
        }

      } catch (error) {
        console.error('格式化职位数据失败:', error)
        showErrorToast('数据处理失败')
      }
    }
  }

  /**
   * 搜索职位
   * @param {string} keyword - 搜索关键词
   * @param {Object} [filters] - 筛选条件
   * @returns {Promise<void>}
   */
  const searchJobs = async (keyword = '', filters = {}) => {
    searchParams.value = {
      keyword: keyword.trim(),
      ...filters
    }
    
    await loadJobList(searchParams.value, false)
  }

  /**
   * 加载更多职位
   * @returns {Promise<void>}
   */
  const loadMore = async () => {
    if (!hasMore.value || loading.value) {
      return
    }

    await loadJobList(searchParams.value, true)
  }

  /**
   * 刷新职位列表
   * @returns {Promise<void>}
   */
  const refresh = async () => {
    await loadJobList(searchParams.value, false)
  }

  /**
   * 重置搜索条件
   * @returns {Promise<void>}
   */
  const resetSearch = async () => {
    searchParams.value = {}
    await loadJobList({}, false)
  }

  /**
   * 更新搜索参数
   * @param {Object} params - 新的搜索参数
   * @param {boolean} [autoSearch=true] - 是否自动搜索
   * @returns {Promise<void>}
   */
  const updateSearchParams = async (params, autoSearch = true) => {
    searchParams.value = {
      ...searchParams.value,
      ...params
    }

    if (autoSearch) {
      await loadJobList(searchParams.value, false)
    }
  }

  /**
   * 获取职位详情
   * @param {string} jobId - 职位ID
   * @returns {Object|null} 职位详情
   */
  const getJobById = (jobId) => {
    return jobList.value.find(job => job.id === jobId) || null
  }

  /**
   * 移除职位（用于已投递等场景）
   * @param {string} jobId - 职位ID
   */
  const removeJob = (jobId) => {
    const index = jobList.value.findIndex(job => job.id === jobId)
    if (index > -1) {
      jobList.value.splice(index, 1)
    }
  }

  return {
    // 状态
    jobList,
    loading,
    hasMore,
    isEmpty,
    isFirstPage,
    searchParams,

    // 方法
    loadJobList,
    searchJobs,
    loadMore,
    refresh,
    resetSearch,
    updateSearchParams,
    getJobById,
    removeJob
  }
}
