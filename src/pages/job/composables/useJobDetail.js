/**
 * @fileoverview 职位详情管理Hook
 * 统一管理职位详情的获取、投递、沟通等逻辑
 */

import { ref, reactive } from 'vue'
import { fetchJobDetail, saveDelivery, fetchAttachedResumes } from '../services/jobApiService'
import { useLoading } from './useLoading'
import { useErrorHandler } from './useErrorHandler'
import { formatJobDetails } from '../utils/dataTransform'

/**
 * 职位详情管理Hook
 * @returns {Object} 职位详情状态和方法
 */
export function useJobDetail() {
  // 状态管理
  const jobDetails = reactive({
    title: '',
    type: '',
    salary: '',
    company: '',
    location: '',
    experience: '',
    education: '',
    benefits: [],
    address: '',
    requirements: {
      position: '',
      gender: '',
      age: '',
      education: '',
      major: ''
    },
    certifications: [],
    responsibilities: [],
    environment: []
  })

  const jobRawData = ref(null)
  const resumes = ref([])
  const selectedResume = ref(null)

  // Hooks
  const { loading, withLoading } = useLoading()
  const { withErrorHandling, showErrorToast, showSuccessToast, showConfirm } = useErrorHandler()

  /**
   * 获取职位详情
   * @param {string} jobId - 职位ID
   * @returns {Promise<void>}
   */
  const fetchDetails = async (jobId) => {
    if (!jobId) {
      showErrorToast('职位ID不能为空')
      return
    }

    const result = await withErrorHandling(
      () => withLoading(
        () => fetchJobDetail(jobId),
        { showUniLoading: true, loadingText: '加载中...' }
      ),
      { defaultMessage: '获取职位详情失败' }
    )

    if (result) {
      try {
        // 保存原始数据
        jobRawData.value = result
        
        // 格式化详情数据
        const formattedDetails = await formatJobDetails(result)
        Object.assign(jobDetails, formattedDetails)
      } catch (error) {
        console.error('格式化职位详情失败:', error)
        showErrorToast('数据处理失败')
      }
    }
  }

  /**
   * 获取附件简历列表
   * @returns {Promise<boolean>} 是否成功
   */
  const fetchResumes = async () => {
    const result = await withErrorHandling(
      () => withLoading(
        () => fetchAttachedResumes(),
        { showUniLoading: true, loadingText: '加载中...' }
      ),
      { defaultMessage: '获取简历列表失败' }
    )

    if (result && result.data) {
      resumes.value = result.data
      
      // 默认选择第一个简历
      if (resumes.value.length > 0) {
        selectedResume.value = resumes.value[0]
      }
      
      return true
    }
    
    return false
  }

  /**
   * 投递简历
   * @param {string} jobId - 职位ID
   * @param {string} cvId - 简历ID
   * @returns {Promise<boolean>} 是否成功
   */
  const deliverResume = async (jobId, cvId) => {
    if (!selectedResume.value?.id) {
      showErrorToast('请选择简历')
      return false
    }

    const params = {
      job_id: jobId,
      cv_attached_id: selectedResume.value.id,
      cv_id: cvId,
      info_type: '3' // 3=附件简历
    }

    // 如果有school_id，添加到参数中
    if (jobRawData.value?.school_id) {
      params.school_id = jobRawData.value.school_id
    }

    const result = await withErrorHandling(
      () => withLoading(
        () => saveDelivery(params),
        { showUniLoading: true, loadingText: '投递中...' }
      ),
      { defaultMessage: '投递失败，请重试' }
    )

    if (result) {
      showSuccessToast('投递成功')
      return true
    }
    
    return false
  }

  /**
   * 发送咨询消息
   * @param {string} jobId - 职位ID
   * @param {string} cvId - 简历ID
   * @param {string} message - 咨询内容
   * @returns {Promise<boolean>} 是否成功
   */
  const sendMessage = async (jobId, cvId, message) => {
    if (!message.trim()) {
      showErrorToast('请输入问题')
      return false
    }

    const params = {
      job_id: jobId,
      ask_content: message.trim(),
      info_type: '1', // 1=文本
      cv_id: cvId
    }

    // 如果有school_id，添加到参数中
    if (jobRawData.value?.school_id) {
      params.school_id = jobRawData.value.school_id
    }

    const result = await withErrorHandling(
      () => withLoading(
        () => saveDelivery(params),
        { showUniLoading: true, loadingText: '发送中...' }
      ),
      { defaultMessage: '发送失败，请重试' }
    )

    if (result) {
      showSuccessToast('发送成功')
      return true
    }
    
    return false
  }

  /**
   * 处理投递简历流程
   * @param {string} jobId - 职位ID
   * @param {string} cvId - 简历ID
   * @returns {Promise<void>}
   */
  const handleResumeDelivery = async (jobId, cvId) => {
    if (!cvId) {
      const confirmed = await showConfirm({
        title: '提示',
        content: '您还没有更新个人信息，是否前往更新？',
        confirmText: '去更新'
      })
      
      if (confirmed) {
        uni.navigateTo({ url: '/pages/job/profile' })
      }
      return
    }

    // 获取简历列表
    const success = await fetchResumes()
    
    if (success) {
      if (resumes.value.length === 0) {
        const confirmed = await showConfirm({
          title: '提示',
          content: '您还没有上传附件简历，是否前往上传？'
        })
        
        if (confirmed) {
          uni.navigateTo({ url: '/pages/job/attachment' })
        }
      } else {
        // 有简历，可以直接投递或显示选择弹窗
        return true
      }
    }
    
    return false
  }

  /**
   * 处理在线沟通流程
   * @param {string} jobId - 职位ID
   * @param {string} cvId - 简历ID
   * @param {string} message - 消息内容
   * @returns {Promise<boolean>} 是否成功
   */
  const handleChatMessage = async (jobId, cvId, message) => {
    if (!cvId) {
      const confirmed = await showConfirm({
        title: '提示',
        content: '您还没有更新个人信息，是否前往更新？',
        confirmText: '去更新'
      })
      
      if (confirmed) {
        uni.navigateTo({ url: '/pages/job/profile' })
      }
      return false
    }

    return await sendMessage(jobId, cvId, message)
  }

  return {
    // 状态
    jobDetails,
    jobRawData,
    resumes,
    selectedResume,
    loading,

    // 方法
    fetchDetails,
    fetchResumes,
    deliverResume,
    sendMessage,
    handleResumeDelivery,
    handleChatMessage
  }
}
