/**
 * @fileoverview 错误处理Hook
 * 统一管理错误处理逻辑，提供统一的错误提示
 */

import { ref } from 'vue'

/**
 * 错误处理Hook
 * @returns {Object} 错误状态和处理方法
 */
export function useErrorHandler() {
  const error = ref(null)
  const hasError = ref(false)

  /**
   * 处理API错误
   * @param {Error} err - 错误对象
   * @param {Object} [options] - 配置选项
   * @param {boolean} [options.showToast=true] - 是否显示错误提示
   * @param {string} [options.defaultMessage='操作失败，请重试'] - 默认错误消息
   * @param {boolean} [options.logError=true] - 是否记录错误日志
   */
  const handleError = (err, options = {}) => {
    const {
      showToast = true,
      defaultMessage = '操作失败，请重试',
      logError = true
    } = options

    error.value = err
    hasError.value = true

    if (logError) {
      console.error('错误详情:', err)
    }

    if (showToast) {
      const message = getErrorMessage(err, defaultMessage)
      showErrorToast(message)
    }
  }

  /**
   * 获取错误消息
   * @param {Error} err - 错误对象
   * @param {string} defaultMessage - 默认消息
   * @returns {string} 错误消息
   */
  const getErrorMessage = (err, defaultMessage) => {
    if (!err) return defaultMessage

    // 处理API错误响应
    if (err.response && err.response.data) {
      const { data } = err.response
      if (data.message) return data.message
      if (data.msg) return data.msg
      if (data.error) return data.error
    }

    // 处理网络错误
    if (err.code === 'NETWORK_ERROR') {
      return '网络连接失败，请检查网络设置'
    }

    // 处理超时错误
    if (err.code === 'TIMEOUT') {
      return '请求超时，请重试'
    }

    // 处理其他错误
    if (err.message) {
      return err.message
    }

    return defaultMessage
  }

  /**
   * 显示错误提示
   * @param {string} message - 错误消息
   */
  const showErrorToast = (message) => {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 显示成功提示
   * @param {string} message - 成功消息
   */
  const showSuccessToast = (message) => {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 1500
    })
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
    hasError.value = false
  }

  /**
   * 包装异步函数，自动处理错误
   * @template T
   * @param {() => Promise<T>} asyncFn - 异步函数
   * @param {Object} [options] - 错误处理选项
   * @returns {Promise<T|null>} 异步函数的结果，出错时返回null
   */
  const withErrorHandling = async (asyncFn, options = {}) => {
    try {
      clearError()
      const result = await asyncFn()
      return result
    } catch (err) {
      handleError(err, options)
      return null
    }
  }

  /**
   * 显示确认对话框
   * @param {Object} options - 对话框选项
   * @param {string} options.title - 标题
   * @param {string} options.content - 内容
   * @param {string} [options.confirmText='确定'] - 确认按钮文本
   * @param {string} [options.cancelText='取消'] - 取消按钮文本
   * @returns {Promise<boolean>} 用户是否确认
   */
  const showConfirm = (options) => {
    const {
      title = '提示',
      content,
      confirmText = '确定',
      cancelText = '取消'
    } = options

    return new Promise((resolve) => {
      uni.showModal({
        title,
        content,
        confirmText,
        cancelText,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  /**
   * 显示操作表
   * @param {Object} options - 操作表选项
   * @param {string[]} options.itemList - 选项列表
   * @param {string} [options.title] - 标题
   * @returns {Promise<number|null>} 选中的索引，取消时返回null
   */
  const showActionSheet = (options) => {
    const { itemList, title } = options

    return new Promise((resolve) => {
      uni.showActionSheet({
        itemList,
        title,
        success: (res) => {
          resolve(res.tapIndex)
        },
        fail: () => {
          resolve(null)
        }
      })
    })
  }

  return {
    error,
    hasError,
    handleError,
    clearError,
    withErrorHandling,
    showErrorToast,
    showSuccessToast,
    showConfirm,
    showActionSheet
  }
}
