/**
 * @fileoverview 加载状态管理Hook
 * 统一管理加载状态，提供统一的加载提示
 */

import { ref } from 'vue'

/**
 * 加载状态管理Hook
 * @returns {Object} 加载状态和方法
 */
export function useLoading() {
  const loading = ref(false)

  /**
   * 包装异步函数，自动管理加载状态
   * @template T
   * @param {() => Promise<T>} asyncFn - 异步函数
   * @param {Object} [options] - 配置选项
   * @param {boolean} [options.showUniLoading=false] - 是否显示uni-app的loading
   * @param {string} [options.loadingText='加载中...'] - 加载提示文本
   * @returns {Promise<T>} 异步函数的结果
   */
  const withLoading = async (asyncFn, options = {}) => {
    const { showUniLoading = false, loadingText = '加载中...' } = options
    
    if (loading.value) {
      console.warn('已有加载中的请求，跳过重复请求')
      return
    }

    try {
      loading.value = true
      
      if (showUniLoading) {
        uni.showLoading({ title: loadingText })
      }

      const result = await asyncFn()
      return result
    } catch (error) {
      console.error('异步操作失败:', error)
      throw error
    } finally {
      loading.value = false
      
      if (showUniLoading) {
        uni.hideLoading()
      }
    }
  }

  /**
   * 手动设置加载状态
   * @param {boolean} state - 加载状态
   */
  const setLoading = (state) => {
    loading.value = state
  }

  return {
    loading,
    withLoading,
    setLoading
  }
}

/**
 * 多个加载状态管理Hook
 * @param {string[]} keys - 加载状态的键名数组
 * @returns {Object} 多个加载状态和方法
 */
export function useMultipleLoading(keys = []) {
  const loadingStates = ref({})
  
  // 初始化加载状态
  keys.forEach(key => {
    loadingStates.value[key] = false
  })

  /**
   * 包装异步函数，管理指定键的加载状态
   * @template T
   * @param {string} key - 加载状态的键名
   * @param {() => Promise<T>} asyncFn - 异步函数
   * @param {Object} [options] - 配置选项
   * @returns {Promise<T>} 异步函数的结果
   */
  const withLoading = async (key, asyncFn, options = {}) => {
    const { showUniLoading = false, loadingText = '加载中...' } = options
    
    if (loadingStates.value[key]) {
      console.warn(`${key} 已有加载中的请求，跳过重复请求`)
      return
    }

    try {
      loadingStates.value[key] = true
      
      if (showUniLoading) {
        uni.showLoading({ title: loadingText })
      }

      const result = await asyncFn()
      return result
    } catch (error) {
      console.error(`${key} 异步操作失败:`, error)
      throw error
    } finally {
      loadingStates.value[key] = false
      
      if (showUniLoading) {
        uni.hideLoading()
      }
    }
  }

  /**
   * 手动设置指定键的加载状态
   * @param {string} key - 加载状态的键名
   * @param {boolean} state - 加载状态
   */
  const setLoading = (key, state) => {
    if (loadingStates.value.hasOwnProperty(key)) {
      loadingStates.value[key] = state
    }
  }

  /**
   * 获取指定键的加载状态
   * @param {string} key - 加载状态的键名
   * @returns {boolean} 加载状态
   */
  const isLoading = (key) => {
    return loadingStates.value[key] || false
  }

  /**
   * 检查是否有任何加载状态为true
   * @returns {boolean} 是否有加载中的状态
   */
  const hasAnyLoading = () => {
    return Object.values(loadingStates.value).some(loading => loading)
  }

  return {
    loadingStates,
    withLoading,
    setLoading,
    isLoading,
    hasAnyLoading
  }
}
