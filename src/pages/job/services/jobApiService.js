/**
 * @fileoverview 职位相关API服务层
 * 统一管理所有职位相关的API调用，消除重复代码
 */

import request from '@/request'

/**
 * @typedef {Object} JobListParams
 * @property {number} page - 页码
 * @property {number} per_page - 每页数量
 * @property {string} school_id - 学校ID
 * @property {string} [keyword] - 搜索关键词
 * @property {string} [work_location_province_code] - 省份编码
 * @property {string} [work_location_city_code] - 城市编码
 * @property {string} [roles_id] - 岗位ID，逗号分隔
 * @property {string} [employment_form] - 用工形式
 * @property {string} [educational_require] - 学历要求
 * @property {number} [salary_benefits_start] - 最低薪资
 * @property {number} [salary_benefits_end] - 最高薪资
 * @property {string} [welfare_subsidy] - 福利待遇JSON字符串
 */

/**
 * @typedef {Object} JobItem
 * @property {string} id - 职位ID
 * @property {string} name - 职位名称
 * @property {string} employment_form - 用工形式编码
 * @property {number} salary_benefits_start - 最低薪资
 * @property {number} salary_benefits_end - 最高薪资
 * @property {string} work_location_area_code - 工作地区编码
 * @property {number} work_experience - 工作经验要求
 * @property {string} educational_require - 学历要求编码
 * @property {string} welfare_subsidy - 福利待遇JSON字符串
 * @property {string} school_name - 学校名称
 * @property {string} working_address - 工作地址
 * @property {string} school_logo - 学校logo
 */

/**
 * @typedef {Object} JobListResponse
 * @property {JobItem[]} data - 职位列表
 * @property {number} current_page - 当前页码
 * @property {number} last_page - 最后一页
 */

/**
 * @typedef {Object} JobDetail
 * @property {string} id - 职位ID
 * @property {string} name - 职位名称
 * @property {string} employment_form - 用工形式编码
 * @property {number} salary_benefits_start - 最低薪资
 * @property {number} salary_benefits_end - 最高薪资
 * @property {string} school_name - 学校名称
 * @property {string} work_location_area_code - 工作地区编码
 * @property {number} work_experience - 工作经验要求
 * @property {string} educational_require - 学历要求编码
 * @property {string} welfare_subsidy - 福利待遇JSON字符串
 * @property {string} working_address - 工作地址
 * @property {string} working_environment - 工作环境JSON字符串
 * @property {string} certification_requirements - 证书要求，逗号分隔
 * @property {string} gender - 性别要求编码
 * @property {number} age_requirements - 年龄要求
 * @property {string} professional_requirements - 专业要求
 * @property {string} job_description - 职位描述
 * @property {string} school_id - 学校ID
 */

/**
 * 职位API服务类
 */
export class JobApiService {
  /**
   * 获取职位列表
   * @param {JobListParams} params - 查询参数
   * @returns {Promise<JobListResponse>} 职位列表响应
   */
  static async getJobList(params) {
    try {
      const response = await request({
        url: '/api/recruitment/job/filterList',
        method: 'GET',
        data: params
      })
      return response
    } catch (error) {
      console.error('获取职位列表失败:', error)
      throw error
    }
  }

  /**
   * 获取职位详情
   * @param {string} jobId - 职位ID
   * @returns {Promise<JobDetail>} 职位详情
   */
  static async getJobDetail(jobId) {
    try {
      const response = await request({
        url: '/api/recruitment/job/detail',
        method: 'GET',
        data: { id: jobId }
      })
      return response
    } catch (error) {
      console.error('获取职位详情失败:', error)
      throw error
    }
  }

  /**
   * 获取招聘字典数据
   * @returns {Promise<Object>} 字典数据
   */
  static async getDictionary() {
    try {
      const response = await request({
        url: '/api/recruitment/job/getSelect',
        method: 'GET'
      })
      return response.data || response
    } catch (error) {
      console.error('获取招聘字典数据失败:', error)
      throw error
    }
  }
}

/**
 * 简历投递参数
 * @typedef {Object} DeliveryParams
 * @property {string} job_id - 职位ID
 * @property {string} [cv_attached_id] - 附件简历ID
 * @property {string} cv_id - 简历ID
 * @property {string} info_type - 信息类型 1=文本 3=附件简历
 * @property {string} [ask_content] - 咨询内容
 * @property {string} [school_id] - 学校ID
 */

/**
 * 简历相关API服务类
 */
export class ResumeApiService {
  /**
   * 投递简历或发送咨询
   * @param {DeliveryParams} params - 投递参数
   * @returns {Promise<Object>} 投递结果
   */
  static async saveDelivery(params) {
    try {
      const response = await request({
        url: '/api/recruitment/cv/saveDelivery',
        method: 'POST',
        data: params
      })
      return response
    } catch (error) {
      console.error('投递简历失败:', error)
      throw error
    }
  }

  /**
   * 获取附件简历列表
   * @returns {Promise<Object>} 附件简历列表
   */
  static async getAttachedResumes() {
    try {
      const response = await request({
        url: '/api/recruitment/cv/getAttached',
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取附件简历失败:', error)
      throw error
    }
  }

  /**
   * 获取个人信息
   * @returns {Promise<Object>} 个人信息
   */
  static async getPersonalInfo() {
    try {
      const response = await request({
        url: '/api/recruitment/cv/getInfo',
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取个人信息失败:', error)
      throw error
    }
  }

  /**
   * 保存个人信息
   * @param {Object} personalData - 个人信息数据
   * @returns {Promise<Object>} 保存结果
   */
  static async savePersonalInfo(personalData) {
    try {
      const response = await request({
        url: '/api/recruitment/cv/saveInfo',
        method: 'POST',
        data: personalData
      })
      return response
    } catch (error) {
      console.error('保存个人信息失败:', error)
      throw error
    }
  }

  /**
   * 获取教育经历
   * @returns {Promise<Object>} 教育经历列表
   */
  static async getEducationHistory() {
    try {
      const response = await request({
        url: '/api/recruitment/cv/getEducational',
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取教育经历失败:', error)
      throw error
    }
  }

  /**
   * 保存教育经历
   * @param {Object} educationData - 教育经历数据
   * @returns {Promise<Object>} 保存结果
   */
  static async saveEducationExperience(educationData) {
    try {
      const response = await request({
        url: '/api/recruitment/cv/saveEducational',
        method: 'POST',
        data: educationData
      })
      return response
    } catch (error) {
      console.error('保存教育经历失败:', error)
      throw error
    }
  }

  /**
   * 删除教育经历
   * @param {string|number} educationId - 教育经历ID
   * @returns {Promise<Object>} 删除结果
   */
  static async deleteEducationExperience(educationId) {
    try {
      const response = await request({
        url: '/api/recruitment/cv/delEducational',
        method: 'GET',
        data: { id: educationId }
      })
      return response
    } catch (error) {
      console.error('删除教育经历失败:', error)
      throw error
    }
  }

  /**
   * 获取工作经历
   * @returns {Promise<Object>} 工作经历列表
   */
  static async getWorkHistory() {
    try {
      const response = await request({
        url: '/api/recruitment/cv/getWork',
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取工作经历失败:', error)
      throw error
    }
  }

  /**
   * 保存工作经历
   * @param {Object} workData - 工作经历数据
   * @returns {Promise<Object>} 保存结果
   */
  static async saveWorkExperience(workData) {
    try {
      const response = await request({
        url: '/api/recruitment/cv/saveWork',
        method: 'POST',
        data: workData
      })
      return response
    } catch (error) {
      console.error('保存工作经历失败:', error)
      throw error
    }
  }

  /**
   * 删除工作经历
   * @param {string|number} workId - 工作经历ID
   * @returns {Promise<Object>} 删除结果
   */
  static async deleteWorkExperience(workId) {
    try {
      const response = await request({
        url: '/api/recruitment/cv/delWork',
        method: 'GET',
        data: { id: workId }
      })
      return response
    } catch (error) {
      console.error('删除工作经历失败:', error)
      throw error
    }
  }
}
