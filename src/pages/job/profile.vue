<template>
    <view class="profile-page">
        <u-navbar :is-back="true" title="个人信息" title-color="#262937" title-size="36" :border-bottom="false"
            @back="goBack">
        </u-navbar>

        <scroll-view scroll-y class="scroll-view-content">
            <view class="content">
                <u-form :model="profile" ref="formRef" label-position="left" label-width="200" :errorType="['toast']">
                    <view class="card">
                        <u-form-item label="头像" prop="avatar" :border-bottom="false" label-width="auto">
                            <template #right>
                                <view class="avatar-section" @click="chooseAvatar">
                                    <image class="avatar" :src="profile.avatar" />
                                    <u-icon name="camera-fill" color="#fff" size="28" class="camera-icon"></u-icon>
                                </view>
                            </template>
                        </u-form-item>
                    </view>

                    <view class="card">
                        <u-form-item label="姓名" prop="name">
                            <u-input v-model="profile.name" type="text" :border="false" input-align="right"
                                placeholder="请输入姓名" />
                        </u-form-item>
                        <u-form-item label="性别" prop="gender" right-icon="arrow-right">
                            <SelectPicker v-model="profile.gender" :options="genderOptions" placeholder="请选择性别"
                                @change="onGenderChange" />
                        </u-form-item>
                        <u-form-item label="出生日期" prop="birth_date" right-icon="arrow-right">
                            <DatePicker v-model="profile.birth_date" placeholder="请选择出生日期" @change="onBirthDateChange" />
                        </u-form-item>
                        <u-form-item label="开始工作时间" prop="start_working_date" right-icon="arrow-right">
                            <DatePicker v-model="profile.start_working_date" placeholder="请选择开始工作时间"
                                @change="onStartWorkingDateChange" />
                        </u-form-item>
                        <u-form-item label="学历" prop="educational_require" right-icon="arrow-right">
                            <SelectPicker v-model="profile.educational_require" :options="educationOptions" placeholder="请选择学历"
                                @change="onEducationChange" />
                        </u-form-item>
                        <u-form-item label="工作地点" prop="work_location_province_code" right-icon="arrow-right">
                            <AreaPicker v-model="workLocationCodes" placeholder="请选择工作地点"
                                @change="onWorkLocationChange" />
                        </u-form-item>
                        <u-form-item label="手机号码" prop="mobile" :border-bottom="false">
                            <u-input v-model="profile.mobile" type="number" :border="false" input-align="right"
                                placeholder="请输入手机号" />
                        </u-form-item>
                    </view>


                </u-form>
            </view>
        </scroll-view>




        <BottomButton :buttons="[{ type: 'save', text: '保存' }]" @click="onBottomButtonClick" />
    </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useResumeStore } from './stores/resumeStore';
import { storeToRefs } from 'pinia';
import { onReady } from '@dcloudio/uni-app';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import {
    getGenderOptions,
    getEducationOptions,
    getCertificationOptions
} from './utils/jobDictionary.js';
import DatePicker from './components/DatePicker.vue';
import AreaPicker from './components/AreaPicker.vue';
import BottomButton from './components/BottomButton.vue';
import SelectPicker from './components/SelectPicker.vue';

const resumeStore = useResumeStore();
// 使用原始数据用于编辑
const { personalInfoRaw } = storeToRefs(resumeStore);

const goBack = () => {
    uni.navigateBack();
};

const formRef = ref(null);

const profile = reactive({
    id: '',
    avatar: '',
    name: '',
    gender: '',
    birth_date: '',
    start_working_date: '',
    educational_require: '',
    mobile: '',
    work_location_province_code: '',
    work_location_city_code: '',
    work_location_area_code: '',

});

// 工作地点选择器相关状态
const workLocationCodes = ref([]);

onMounted(async () => {
    // 初始化字典选项
    try {
        const [genderOpts, educationOpts] = await Promise.all([
            getGenderOptions(),
            getEducationOptions()
        ]);

        genderOptions.value = genderOpts;
        educationOptions.value = educationOpts;
    } catch (error) {
        console.error('初始化字典选项失败:', error);
        // 不再降级处理
        genderOptions.value = [];
        educationOptions.value = [];
    }

    // 从API加载个人信息
    try {
        await resumeStore.fetchPersonalInfo();

        // 同步数据到表单 - 直接使用接口字段名称
        if (personalInfoRaw.value && personalInfoRaw.value.id) {
            const rawData = personalInfoRaw.value;
            Object.keys(profile).forEach(key => {
                profile[key] = rawData[key] || ''
            })




            // 设置工作地点代码数组
            workLocationCodes.value = [
                profile.work_location_province_code,
                profile.work_location_city_code,
                profile.work_location_area_code
            ].filter(code => code);
        }
    } catch (err) {
        console.error('加载个人信息失败:', err);
        uni.showToast({
            title: '加载个人信息失败',
            icon: 'none',
            duration: 2000
        });
    }
});

const rules = {
    // 移除所有校验规则，改为在提交时手动校验
};

const genderOptions = ref([]);
const educationOptions = ref([]);

const onGenderChange = (value) => {
    profile.gender = value;
};

const onEducationChange = (value) => {
    profile.educational_require = value;
};

const onBirthDateChange = (date) => {
    profile.birth_date = date;
};

const onStartWorkingDateChange = (date) => {
    profile.start_working_date = date;
};



// 工作地点选择器方法
const onWorkLocationChange = (codes) => {
    // 更新profile中的地区编码 - 直接使用接口字段名称
    profile.work_location_province_code = codes[0] || '';
    profile.work_location_city_code = codes[1] || '';
    profile.work_location_area_code = codes[2] || '';
};

const chooseAvatar = async () => {
    try {
        const res = await uni.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: ['album', 'camera']
        });

        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            const filePath = res.tempFilePaths[0];

            // 显示上传提示
            uni.showLoading({
                title: '上传中...'
            });

            try {
                // 获取文件信息
                const fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);

                // 上传到OBS
                const uploadedUrl = await OBSupload(filePath, fileExtension, fileName);

                // 更新头像URL
                profile.avatar = uploadedUrl;

                uni.hideLoading();
                uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                });
            } catch (uploadError) {
                uni.hideLoading();
                console.error('头像上传失败:', uploadError);
                uni.showToast({
                    title: '上传失败',
                    icon: 'error'
                });
            }
        }
    } catch (error) {
        console.error('选择图片失败:', error);
        uni.showToast({
            title: '选择图片失败',
            icon: 'error'
        });
    }
};

const onBottomButtonClick = (type) => {
    if (type === 'save') {
        save();
    }
};

const save = async () => {
    // 手动校验必填字段
    if (!profile.avatar) {
        uni.showToast({
            title: '请上传头像',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.name) {
        uni.showToast({
            title: '请输入姓名',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 验证姓名长度和字符
    if (profile.name.length > 60) {
        uni.showToast({
            title: '姓名不能超过60个字符',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 验证姓名只能包含中文、英文、数字、点号和间隔号
    const nameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9.·]+$/;
    if (!nameRegex.test(profile.name)) {
        uni.showToast({
            title: '姓名只能包含中文、英文、数字、点号(.)和间隔号(·)',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.gender) {
        uni.showToast({
            title: '请选择性别',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.birth_date) {
        uni.showToast({
            title: '请选择出生日期',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.start_working_date) {
        uni.showToast({
            title: '请选择开始工作时间',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.educational_require) {
        uni.showToast({
            title: '请选择学历',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.work_location_province_code) {
        uni.showToast({
            title: '请选择工作地点',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!profile.mobile) {
        uni.showToast({
            title: '请输入手机号',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 验证手机号格式
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (!mobileRegex.test(profile.mobile)) {
        uni.showToast({
            title: '请输入正确的手机号格式',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 验证出生日期不能大于当前日期
    const currentDate = new Date();
    const birthDate = new Date(profile.birth_date);
    if (birthDate > currentDate) {
        uni.showToast({
            title: '出生日期不能大于当前日期',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 验证开始工作时间必须在出生日期和当前日期之间
    const startWorkingDate = new Date(profile.start_working_date);
    if (startWorkingDate < birthDate) {
        uni.showToast({
            title: '开始工作时间不能早于出生日期',
            icon: 'none',
            duration: 2000
        });
        return;
    }
    if (startWorkingDate > currentDate) {
        uni.showToast({
            title: '开始工作时间不能大于当前日期',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    try {
        // 显示加载提示
        uni.showLoading({
            title: '保存中...'
        });
        if(!profile.id){
            delete profile.id;
        }
        // 准备保存数据 - 直接使用接口字段名称
        const saveData = { ...profile };

        // 调用API保存个人信息
        await resumeStore.savePersonalInfo(saveData);

        // 保存成功后重新加载个人信息数据，确保数据一致性
        await resumeStore.fetchPersonalInfo();

        uni.hideLoading();
        uni.showToast({
            title: '保存成功',
            icon: 'success'
        });

        setTimeout(() => {
            uni.navigateBack();
        }, 1500);

    } catch (error) {
        uni.hideLoading();
        console.error('保存个人信息失败:', error);
    }
};

onReady(() => {
    formRef.value.setRules(rules);
});
</script>

<style lang="scss" scoped>
.profile-page {
    background: #F6F6F6;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.scroll-view-content {
    flex: 1;
    overflow-y: auto;
}

.content {
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.card {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    padding: 0 32rpx;
}

.card+.card {
    margin-top: 20rpx;
}

.avatar-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;

    .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
    }

    .camera-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        padding: 6rpx;
    }
}

.picker-value {
    text-align: right;
    width: 100%;
    color: #262937;
    font-size: 28rpx;
    line-height: normal;
}

:deep(uni-picker) {
    width: 100%;
}

.picker-value.placeholder {
    color: #c0c4cc;
}



// /deep/ .u-form-item__body {
//     padding: 20rpx 0;
// }
// /deep/ .u-form-item--right {
//     .u-input__input {
//         text-align: right;
//     }
// }</style>
