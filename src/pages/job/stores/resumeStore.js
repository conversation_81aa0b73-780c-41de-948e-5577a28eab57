// src/pages/job/stores/resumeStore.js
import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/request.js'
import {
  transformPersonalInfo,
  buildEducationDisplay,
  buildWorkDisplay,
} from '../utils/resumeUtils.js'

export const useResumeStore = defineStore('resume', () => {
  // 加载状态
  const isLoading = ref(false)
  const isEducationLoading = ref(false)
  const isWorkLoading = ref(false)

  // 错误状态
  const error = ref(null)
  const countsRaw = ref({
    communicate_count: 0,
    delivery_count: 0,
    waiting_interview_count: 0,
    already_interview_count: 0
  })
  // 显示用的个人信息（经过转换的数据）
  const personalInfo = ref({
    avatar: '',
    name: '',
    gender: '',
    age: '',
    phone: '',
    degree: '',
    experience: '',
    jobStatus: '',
    strengths: '',
    certifications: [],
    id: '',
  })

  // 编辑用的个人信息（原始API数据）
  const personalInfoRaw = ref({
    id: null,
    avatar: '',
    name: '',
    gender: '',
    birth_date: '',
    start_working_date: '',
    educational_require: '',
    mobile: '',
    work_location_province_code: '',
    work_location_city_code: '',
    work_location_area_code: '',
    personal_advantages: '',
    certification_requirements: '',
  })

  // 显示用的教育经历
  const educationHistory = ref([])

  // 编辑用的教育经历（原始API数据）
  const educationHistoryRaw = ref([])

  // 显示用的工作经历
  const workHistory = ref([])

  // 编辑用的工作经历（原始API数据）
  const workHistoryRaw = ref([])


  // API调用方法

  /**
   * 获取个人信息
   */
  async function fetchPersonalInfo() {
    try {
      isLoading.value = true
      error.value = null

      const data = await request({
        url: '/api/recruitment/cv/getInfo',
        method: 'GET'
      })

      if (data) {
        // 保存原始数据用于编辑
        Object.keys(personalInfoRaw.value).forEach(key => {
          personalInfoRaw.value[key] = data[key]
        })

        Object.keys(countsRaw.value).forEach(key => {
          countsRaw.value[key] = data[key]
        })
        // 转换数据用于显示
        const personalInfoData = await transformPersonalInfo(data)
        Object.assign(personalInfo.value, personalInfoData)
      }

      return data
    } catch (err) {
      console.error('获取个人信息失败:', err)
      error.value = err
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取教育经历
   */
  async function fetchEducationHistory() {
    try {
      isEducationLoading.value = true
      error.value = null

      const data = await request({
        url: '/api/recruitment/cv/getEducational',
        method: 'GET'
      })

      if (data && data.data) {
        // 保存原始数据用于编辑
        educationHistoryRaw.value = [...data.data]

        // 转换数据用于显示
        const educationHistoryData = data.data.map(item => {
          return buildEducationDisplay(item)
        })
        educationHistory.value = educationHistoryData
      }

      return data
    } catch (err) {
      console.error('获取教育经历失败:', err)
      error.value = err
      throw err
    } finally {
      isEducationLoading.value = false
    }
  }

  /**
   * 获取工作经历
   */
  async function fetchWorkHistory() {
    try {
      isWorkLoading.value = true
      error.value = null

      const data = await request({
        url: '/api/recruitment/cv/getWork',
        method: 'GET'
      })

      if (data && data.data) {
        // 保存原始数据用于编辑
        workHistoryRaw.value = [...data.data]

        // 转换数据用于显示 - 处理异步buildWorkDisplay
        const workHistoryData = await Promise.all(
          data.data.map(item => buildWorkDisplay(item))
        )
        workHistory.value = workHistoryData
      }

      return data
    } catch (err) {
      console.error('获取工作经历失败:', err)
      error.value = err
      throw err
    } finally {
      isWorkLoading.value = false
    }
  }

  /**
   * 加载所有简历数据
   */
  async function loadResumeData() {
    try {
      // 并行加载所有数据
      await Promise.allSettled([
        fetchPersonalInfo(),
        fetchEducationHistory(),
        fetchWorkHistory()
      ])
    } catch (err) {
      console.error('加载简历数据失败:', err)
    }
  }

  /**
   * 根据ID获取教育经历详情（原始数据）
   */
  function getEducationRawById(id) {
    return educationHistoryRaw.value.find(item => item.id == id)
  }

  /**
   * 根据ID获取工作经历详情（原始数据）
   */
  function getWorkExperienceRawById(id) {
    return workHistoryRaw.value.find(item => item.id == id)
  }

  /**
   * 保存单个工作经验（直接使用接口字段名称）
   * @param {Object} workData - 工作经验数据（接口格式）
   */
  async function saveWorkExperience(workData) {
    try {
      // 直接使用传入的数据，不做任何转换
      const result = await request({
        url: '/api/recruitment/cv/saveWork',
        method: 'POST',
        data: workData
      })

      console.log('保存工作经验成功:', result)
      return result
    } catch (err) {
      console.error('保存工作经验失败:', err)
      throw err
    }
  }

  /**
   * 删除工作经验
   * @param {string|number} workId - 工作经验ID
   */
  async function deleteWorkExperience(workId) {
    try {
      const result = await request({
        url: '/api/recruitment/cv/delWork',
        method: 'GET',
        data: { id: workId }
      })

      console.log('删除工作经验成功:', result)
      return result
    } catch (err) {
      console.error('删除工作经验失败:', err)
      throw err
    }
  }

  /**
   * 保存单个教育经历（直接使用接口字段名称）
   * @param {Object} educationData - 教育经历数据（接口格式）
   */
  async function saveEducationExperience(educationData) {
    try {
      // 直接使用传入的数据，不做任何转换
      const result = await request({
        url: '/api/recruitment/cv/saveEducational',
        method: 'POST',
        data: educationData
      })

      console.log('保存教育经历成功:', result)
      return result
    } catch (err) {
      console.error('保存教育经历失败:', err)
      throw err
    }
  }

  /**
   * 删除教育经历
   * @param {string|number} educationId - 教育经历ID
   */
  async function deleteEducationExperience(educationId) {
    try {
      const result = await request({
        url: '/api/recruitment/cv/delEducational',
        method: 'GET',
        data: { id: educationId }
      })

      console.log('删除教育经历成功:', result)
      return result
    } catch (err) {
      console.error('删除教育经历失败:', err)
      throw err
    }
  }

  /**
   * 保存个人信息（直接使用接口字段名称）
   * @param {Object} personalData - 个人信息数据（接口格式）
   */
  async function savePersonalInfo(personalData) {
    try {
      // 直接使用传入的数据，不做任何转换
      const result = await request({
        url: '/api/recruitment/cv/saveInfo',
        method: 'POST',
        data: personalData
      })

      console.log('保存个人信息成功:', result)
      return result
    } catch (err) {
      console.error('保存个人信息失败:', err)
      throw err
    }
  }

  return {
    // 显示数据
    personalInfo,
    educationHistory,
    workHistory,
    countsRaw,


    // 编辑数据（原始数据）
    personalInfoRaw,
    educationHistoryRaw,
    workHistoryRaw,

    // 状态
    isLoading,
    isEducationLoading,
    isWorkLoading,
    error,

    // 方法
    fetchPersonalInfo,
    fetchEducationHistory,
    fetchWorkHistory,
    loadResumeData,
    getEducationRawById,
    getWorkExperienceRawById,
    saveWorkExperience,
    deleteWorkExperience,
    saveEducationExperience,
    deleteEducationExperience,
    savePersonalInfo
  }
})
