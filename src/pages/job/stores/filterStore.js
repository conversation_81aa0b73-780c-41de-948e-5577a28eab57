import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getCitiesByProvinceCode, getDistrictsByCityCode } from '../utils/resumeUtils'

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export const useFilterStore = defineStore('job-filter', () => {
  // 将静态选项数据移入 store，作为单一数据源
  const benefitOptions = ref([
    { label: '五险一金', value: '1' },
    { label: '带薪年假', value: '2' },
    { label: '节日福利', value: '3' },
    { label: '绩效奖金', value: '4' },
    { label: '员工培训', value: '5' },
    { label: '定期体检', value: '6' },
    { label: '交通便利', value: '7' },
  ])

  // 创建一个 value -> label 的映射，方便转换
  const benefitMap = computed(() => {
    return benefitOptions.value.reduce((map, option) => {
      map[option.value] = option.label
      return map
    }, {})
  })

  // 地区相关状态
  const locationState = ref({
    provinceCode: '',     // 当前选中的省份编码
    cityCode: '',         // 当前选中的城市编码
    availableDistricts: [], // 当前城市下所有可选的区县列表
    selectedDistricts: []   // 当前选中的区县编码数组
  })

  // 筛选条件 (前端UI使用)
  const filterConditions = ref({})

  // API 格式的筛选条件 (提交给后端使用)
  const apiFilters = ref({})
  
  // 搜索关键词
  const keyword = ref('')

  // 当前选中的地区信息 (从搜索页面传入，用于显示)
  const currentLocation = ref({
    codes: [], // 地区编码数组 [省编码, 市编码, 区编码]
    names: []  // 地区名称数组 [省名称, 市名称, 区名称]
  })

  // 搜索状态管理
  const searchState = ref({
    lastRequestParams: null,  // 上次请求参数，用于防重复
    requestId: 0             // 请求ID，用于取消过期请求
  })

  // 防重复请求的参数比较
  const isSameRequest = (newParams) => {
    if (!searchState.value.lastRequestParams) return false
    return JSON.stringify(newParams) === JSON.stringify(searchState.value.lastRequestParams)
  }

  // 设置省份
  const setProvince = (provinceCode) => {
    locationState.value.provinceCode = provinceCode
    locationState.value.cityCode = ''
    locationState.value.availableDistricts = []
    locationState.value.selectedDistricts = []
    updateLocationApiFilters()
  }

  // 设置城市
  const setCity = (cityCode) => {
    locationState.value.cityCode = cityCode
    // 获取该城市下所有区县
    locationState.value.availableDistricts = getDistrictsByCityCode(cityCode)
    locationState.value.selectedDistricts = []
    updateLocationApiFilters()
  }

  // 设置选中的区县
  const setSelectedDistricts = (districtCodes) => {
    locationState.value.selectedDistricts = districtCodes
    // 区县变化不影响API参数，只影响内部状态
  }

  // 添加区县到选中列表
  const addDistrict = (districtCode) => {
    if (!locationState.value.selectedDistricts.includes(districtCode)) {
      locationState.value.selectedDistricts.push(districtCode)
      // 区县变化不影响API参数，只影响内部状态
    }
  }

  // 从选中列表移除区县
  const removeDistrict = (districtCode) => {
    const index = locationState.value.selectedDistricts.indexOf(districtCode)
    if (index > -1) {
      locationState.value.selectedDistricts.splice(index, 1)
      // 区县变化不影响API参数，只影响内部状态
    }
  }

  // 搜索变化通知回调
  let searchChangeCallback = null
  
  // 设置搜索变化回调
  const setSearchChangeCallback = (callback) => {
    searchChangeCallback = callback
  }

  // 防抖触发搜索
  const debouncedTriggerSearch = debounce(() => {
    if (searchChangeCallback) {
      const currentParams = { 
        ...apiFilters.value,
        keyword: keyword.value 
      }
      
      // 防重复请求
      if (!isSameRequest(currentParams)) {
        searchState.value.lastRequestParams = currentParams
        searchState.value.requestId++
        searchChangeCallback(currentParams, searchState.value.requestId)
      }
    }
  }, 300)

  // 更新API筛选条件（只包含省市code）
  const updateLocationApiFilters = () => {
    const formattedFilters = { ...apiFilters.value }

    // 只添加省市编码，不包含区县
    if (locationState.value.provinceCode) {
      formattedFilters.work_location_province_code = locationState.value.provinceCode
    } else {
      delete formattedFilters.work_location_province_code
    }

    if (locationState.value.cityCode) {
      formattedFilters.work_location_city_code = locationState.value.cityCode
    } else {
      delete formattedFilters.work_location_city_code
    }

    apiFilters.value = formattedFilters
    debouncedTriggerSearch() // 触发防抖搜索
  }

  // 更新筛选条件（只处理筛选页面的字段）
  const updateFilters = (newFilters) => {
    // 检查筛选条件是否真的有变化
    const oldFilters = filterConditions.value
    const hasChanges = JSON.stringify(oldFilters) !== JSON.stringify(newFilters)
    
    filterConditions.value = newFilters
    
    // 如果没有变化，直接返回，不触发 apiFilters 更新
    if (!hasChanges) {
      return
    }
    
    const formattedFilters = { ...apiFilters.value } // 保留省市编码

    // roles_id: 转换为逗号分隔的字符串
    if (newFilters.positions && newFilters.positions.length > 0) {
      formattedFilters.roles_id = newFilters.positions.join(',')
    } else {
      delete formattedFilters.roles_id
    }

    // 区县筛选（来自筛选页面）
    if (newFilters.areas && newFilters.areas.length > 0) {
      formattedFilters.work_location_area_code = newFilters.areas.join(',')
    } else {
      delete formattedFilters.work_location_area_code
    }

    // employment_form
    if (newFilters.employmentType) {
      formattedFilters.employment_form = newFilters.employmentType
    } else {
      delete formattedFilters.employment_form
    }

    // educational_require
    if (newFilters.education) {
      formattedFilters.educational_require = newFilters.education
    } else {
      delete formattedFilters.educational_require
    }

    // salary_range 转换为 salary_benefits_start 和 salary_benefits_end
    if (newFilters.salary) {
      const salaryMap = {
        '1': { salary_benefits_end: 3000 },
        '2': { salary_benefits_start: 3001, salary_benefits_end: 4000 },
        '3': { salary_benefits_start: 4001, salary_benefits_end: 5000 },
        '4': { salary_benefits_start: 5001, salary_benefits_end: 6000 },
        '5': { salary_benefits_start: 6001 },
      }
      const salaryRange = salaryMap[newFilters.salary]
      if (salaryRange) {
        Object.assign(formattedFilters, salaryRange)
      }
    } else {
      delete formattedFilters.salary_benefits_start
      delete formattedFilters.salary_benefits_end
    }

    // welfare_subsidy: 转换为 label 数组的 JSON 字符串
    if (newFilters.benefits && newFilters.benefits.length > 0) {
      const benefitLabels = newFilters.benefits.map(val => benefitMap.value[val]).filter(Boolean)
      if (benefitLabels.length > 0) {
        try {
          formattedFilters.welfare_subsidy = JSON.stringify(benefitLabels)
        } catch (e) {
          console.error('福利待遇JSON转换失败:', e)
        }
      }
    } else {
      delete formattedFilters.welfare_subsidy
    }
    
    apiFilters.value = formattedFilters
    debouncedTriggerSearch() // 触发防抖搜索
  }

  // 更新当前地区信息（用于显示）
  const updateCurrentLocation = (locationData) => {
    currentLocation.value = locationData
    
    // 同时更新地区状态
    if (locationData.codes && locationData.codes.length > 0) {
      if (locationData.codes[0]) {
        setProvince(locationData.codes[0])
      }
      if (locationData.codes[1]) {
        setCity(locationData.codes[1])
      }
      if (locationData.codes[2]) {
        setSelectedDistricts([locationData.codes[2]])
      }
    }
  }

  // 设置搜索关键词
  const setKeyword = (newKeyword) => {
    keyword.value = newKeyword
    debouncedTriggerSearch() // 触发防抖搜索
  }

  // 重置筛选条件
  const resetFilters = () => {
    filterConditions.value = {}
    locationState.value = {
      provinceCode: '',
      cityCode: '',
      availableDistricts: [],
      selectedDistricts: []
    }
    apiFilters.value = {}
    keyword.value = ''
  }

  return {
    // 静态选项数据
    benefitOptions,
    
    // 地区相关状态
    locationState,
    
    // 筛选条件
    filterConditions,
    apiFilters,
    
    // 搜索关键词
    keyword,
    
    // 显示用的地区信息
    currentLocation,
    
    // 搜索状态
    searchState,
    
    // 地区管理方法
    setProvince,
    setCity,
    setSelectedDistricts,
    addDistrict,
    removeDistrict,
    
    // 筛选条件管理方法
    updateFilters,
    updateCurrentLocation,
    resetFilters,
    
    // 搜索关键词管理
    setKeyword,
    
    // 搜索优化方法
    setSearchChangeCallback,
    isSameRequest
  }
})
