<template>
    <view class="resume-page">
        <u-navbar :is-back="true" title="在线简历" title-color="#262937" title-size="36" :border-bottom="false"
            @back="goBack" />
        <scroll-view scroll-y class="scroll-view-content">
            <view class="content">
                <!-- User Info -->
                <PersonalInfoCard :personal-info="personalInfo" @edit="gotoProfile" />

                <!-- Job Status -->
                <JobStatusSection :job-status="personalInfo?.status" :job-status-options="jobStatusOptions"
                    @change="onJobStatusChange" />

                <view class="section-card">
                    <view class="section-item">
                        <view class="section-header-with-action">
                            <view class="section-header-left">
                                <view class="section-title-bar"></view>
                                <text class="section-title">个人优势</text>
                            </view>
                            <u-icon name="edit-pen" color="#FF7373" size="32" @click="openAdvantageEditor"></u-icon>
                        </view>
                        <view class="section-content">
                            <text class="placeholder" :class="{ 'has-content': personalInfo.strengths }">{{ personalInfo.strengths || '请填写个人优势' }}</text>
                        </view>
                    </view>
                </view>

                <view class="section-card">
                    <view class="section-item">
                        <view class="section-header">
                            <view class="section-header-left">
                                <view class="section-title-bar"></view>
                                <text class="section-title">持证情况</text>
                            </view>
                        </view>
                        <view class="section-content-with-arrow" @click="openCertificationPicker">
                            <template v-if="personalInfo.certifications.length === 0">
                                <text class="placeholder">请选择持证情况</text>
                            </template>
                            <view class="cert-tags" v-else>
                                <text v-for="(cert, index) in personalInfo.certifications" :key="index"
                                    class="cert-tag">{{ cert }}</text>
                            </view>
                            <u-icon name="arrow-right" color="#BCC0CC" size="28"></u-icon>
                        </view>
                    </view>
                </view>

                <!-- Education History -->
                <view class="section-card">
                    <view class="section-item">
                        <view class="section-header-with-action">
                            <view class="section-header-left">
                                <view class="section-title-bar"></view>
                                <text class="section-title">教育经历</text>
                            </view>
                            <u-icon name="plus-circle-fill" color="#FF7373" size="40" @click="addEducation"></u-icon>
                        </view>
                        <view class="timeline">
                            <!-- 加载状态 -->
                            <view v-if="isEducationLoading" class="loading-container">
                                <u-loading mode="circle"></u-loading>
                                <text class="loading-text">加载中...</text>
                            </view>
                            <!-- 教育经历列表 -->
                            <view v-else-if="educationHistory && educationHistory.length > 0">
                                <view v-for="(item, index) in educationHistory" :key="index" class="timeline-item"
                                    @click="editEducation(item)">
                                    <view class="timeline-content">
                                        <view class="timeline-header">
                                            <view>
                                                <text class="date">{{ item.startDate }} - {{ item.endDate }}</text>
                                            </view>
                                            <u-icon name="arrow-right" color="#BCC0CC" size="28"
                                                class="timeline-arrow"></u-icon>
                                        </view>
                                        <view class="school">{{ item.school }}</view>
                                        <view class="timeline-body">
                                            <view>所学专业：{{ item.major }}</view>
                                            <view>在校经历：{{ item.description }}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <!-- 空状态 -->
                            <view v-else class="empty-state">
                                <text class="empty-text">暂无教育经历</text>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- Work History -->
                <view class="section-card">
                    <view class="section-item">
                        <view class="section-header-with-action">
                            <view class="section-header-left">
                                <view class="section-title-bar"></view>
                                <text class="section-title">工作经历</text>
                            </view>
                            <u-icon name="plus-circle-fill" color="#FF7373" size="40"
                                @click="addWorkExperience"></u-icon>
                        </view>
                        <view class="timeline">
                            <!-- 加载状态 -->
                            <view v-if="isWorkLoading" class="loading-container">
                                <u-loading mode="circle"></u-loading>
                                <text class="loading-text">加载中...</text>
                            </view>
                            <!-- 工作经历列表 -->
                            <view v-else-if="workHistory && workHistory.length > 0">
                                <view v-for="(item, index) in workHistory" :key="index" class="timeline-item"
                                    @click="editWorkExperience(item)">
                                    <view class="timeline-content">
                                        <view class="timeline-header">
                                            <view>
                                                <text class="date">{{ item.startDate }} - {{ item.endDate }}</text>
                                            </view>
                                            <u-icon name="arrow-right" color="#BCC0CC" size="28"
                                                class="timeline-arrow"></u-icon>
                                        </view>
                                        <view class="company">{{ item.company }}</view>
                                        <view class="timeline-body">
                                            <view>职位名称：{{ item.position }}</view>
                                            <view>工作地址：{{ item.location }}</view>
                                            <view>工作内容：{{ item.content }}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <!-- 空状态 -->
                            <view v-else class="empty-state">
                                <text class="empty-text">暂无工作经历</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 个人优势编辑弹窗 -->
        <u-popup v-model="showAdvantageEditor" mode="center" border-radius="14" width="600rpx">
            <view class="advantage-popup">
                <view class="popup-header">
                    <text class="popup-title">编辑个人优势</text>
                    <u-icon name="close" color="#909399" size="32" @click="showAdvantageEditor = false"></u-icon>
                </view>
                <view class="popup-content">
                    <textarea v-model="tempAdvantage" placeholder="请填写个人优势" 
                        class="advantage-textarea" maxlength="500" />
                </view>
                <view class="popup-footer">
                    <button class="popup-btn" @click="confirmAdvantage">确定</button>
                </view>
            </view>
        </u-popup>

        <!-- 持证情况多选弹窗 -->
        <u-popup v-model="showCertificationPicker" mode="bottom" border-radius="14">
            <view class="certification-popup">
                <view class="popup-header">
                    <text class="popup-title">选择持证情况</text>
                    <text class="popup-close" @click="showCertificationPicker = false">取消</text>
                </view>
                <view class="popup-content">
                    <view v-for="cert in certificationOptions" :key="cert.value" class="cert-option-item"
                        @click="toggleCertification(cert.value)">
                        <text class="cert-option-label">{{ cert.label }}</text>
                        <view class="cert-checkbox" :class="{ 'checked': tempCertifications.includes(cert.value) }">
                            <u-icon v-if="tempCertifications.includes(cert.value)" name="checkbox-mark" color="#fff"
                                size="24"></u-icon>
                        </view>
                    </view>
                </view>
                <view class="popup-footer">
                    <button class="popup-btn" @click="confirmCertifications">确定</button>
                </view>
            </view>
        </u-popup>

        <view class="footer-buttons">
            <button class="btn attachment-btn" @click="gotoAttachment">
                <u-icon name="plus" color="#FF7373" size="28"></u-icon>
                <text>附件简历管理</text>
            </button>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useResumeStore } from './stores/resumeStore';
import PersonalInfoCard from './components/PersonalInfoCard.vue';
import JobStatusSection from './components/JobStatusSection.vue';
import { getJobStatusOptions, getCertificationOptions } from './utils/jobDictionary.js';
import { onShow } from '@dcloudio/uni-app';

// 直接使用resumeStore
const resumeStore = useResumeStore();
const { personalInfo, educationHistory, workHistory, isEducationLoading, isWorkLoading } = storeToRefs(resumeStore);

// 个人优势编辑相关
const showAdvantageEditor = ref(false);
const tempAdvantage = ref('');

// 持证情况选择相关
const showCertificationPicker = ref(false);
const certificationOptions = ref([]);
const tempCertifications = ref([]);

const goBack = () => {
    uni.navigateBack();
};

const gotoProfile = () => {
    uni.navigateTo({
        url: '/pages/job/profile'
    });
};

// 跳转到附件简历管理
const gotoAttachment = () => {
    uni.navigateTo({
        url: '/pages/job/attachment'
    });
};

// 添加教育经历
const addEducation = () => {
    uni.navigateTo({
        url: '/pages/job/education?mode=add'
    });
};

// 编辑教育经历
const editEducation = (item) => {
    uni.navigateTo({
        url: `/pages/job/education?mode=edit&educationId=${item.id}`
    });
};

// 添加工作经历
const addWorkExperience = () => {
    uni.navigateTo({
        url: '/pages/job/work-experience?mode=add'
    });
};

// 编辑工作经历
const editWorkExperience = (item) => {
    uni.navigateTo({
        url: `/pages/job/work-experience?mode=edit&workExperienceId=${item.id}`
    });
};

// 个人优势编辑方法
const openAdvantageEditor = () => {
    const { personalInfoRaw } = storeToRefs(resumeStore);
    tempAdvantage.value = personalInfoRaw.value.personal_advantages || '';
    showAdvantageEditor.value = true;
};

const confirmAdvantage = async () => {
    try {
        uni.showLoading({ title: '保存中...' });
        
        // 使用与profile页面相同的保存方法
        const { personalInfoRaw } = storeToRefs(resumeStore);
        const saveData = { ...personalInfoRaw.value };
        saveData.personal_advantages = tempAdvantage.value;
        
        await resumeStore.savePersonalInfo(saveData);
        await resumeStore.fetchPersonalInfo();
        showAdvantageEditor.value = false;
        
        uni.hideLoading();
        uni.showToast({
            title: '保存成功',
            icon: 'success'
        });
    } catch (error) {
        uni.hideLoading();
        console.error('保存个人优势失败:', error);
        uni.showToast({
            title: '保存失败',
            icon: 'none'
        });
    }
};

// 持证情况编辑方法
const openCertificationPicker = () => {
    // 从personalInfoRaw中获取当前的持证情况ID数组
    const { personalInfoRaw } = storeToRefs(resumeStore);
    const certificationRequirements = personalInfoRaw.value.certification_requirements || '';
    
    if (certificationRequirements) {
        tempCertifications.value = certificationRequirements
            .split(',')
            .map(id => parseInt(id.trim()))
            .filter(id => !isNaN(id));
    } else {
        tempCertifications.value = [];
    }
    
    showCertificationPicker.value = true;
};

const toggleCertification = (value) => {
    const index = tempCertifications.value.indexOf(value);
    if (index > -1) {
        tempCertifications.value.splice(index, 1);
    } else {
        tempCertifications.value.push(value);
    }
};

const confirmCertifications = async () => {
    try {
        uni.showLoading({ title: '保存中...' });
        
        // 使用与profile页面相同的保存方法
        const { personalInfoRaw } = storeToRefs(resumeStore);
        const saveData = { ...personalInfoRaw.value };
        const certificationString = tempCertifications.value.join(',');
        saveData.certification_requirements = certificationString;
        
        await resumeStore.savePersonalInfo(saveData);
        await resumeStore.fetchPersonalInfo();
        showCertificationPicker.value = false;
        
        uni.hideLoading();
        uni.showToast({
            title: '保存成功',
            icon: 'success'
        });
    } catch (error) {
        uni.hideLoading();
        console.error('保存持证情况失败:', error);
        uni.showToast({
            title: '保存失败',
            icon: 'none'
        });
    }
};

// 求职状态选项
const jobStatusOptions = ref([]);

const onJobStatusChange = async (value) => {
    if (!personalInfo.value.id) {
        uni.showToast({
            title: '请先完善个人信息',
            icon: 'none'
        });
        return;
    }
    try {
        const request = (await import('@/request.js')).default;
        await request({
            url: '/api/recruitment/cv/updateStatus',
            method: 'POST',
            data: { status: value }
        });

        await resumeStore.fetchPersonalInfo();

        uni.showToast({
            title: '更新成功',
            icon: 'success'
        });
    } catch (error) {
        console.error('更新求职状态失败:', error);
        uni.showToast({
            title: '更新失败',
            icon: 'none'
        });
    }
};

// 页面加载时获取简历数据
onShow(async () => {
    try {
        uni.showLoading({ title: '加载中...' });

        // 初始化求职状态选项
        jobStatusOptions.value = getJobStatusOptions();
        
        // 初始化持证情况选项
        try {
            certificationOptions.value = await getCertificationOptions();
        } catch (error) {
            console.error('初始化持证情况选项失败:', error);
            certificationOptions.value = [];
        }

        // 加载简历数据
        await resumeStore.loadResumeData();
        uni.hideLoading();
        if (!personalInfo.value.id) {
            uni.showModal({
                content: '请先完善个人信息',
                confirmText: '去完善',
                showCancel: false,
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '/pages/job/profile'
                        });
                    }
                }
            });
        }

    } catch (error) {
        uni.hideLoading();
        console.error('加载数据失败:', error);
        uni.showToast({
            title: '加载失败',
            icon: 'none'
        });
    }
});


</script>

<style lang="scss" scoped>
.resume-page {
    background: #F6F6F6;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.scroll-view-content {
    flex: 1;
    overflow-y: auto;
}

.content {
    gap: 20rpx;
    display: flex;
    flex-direction: column;
    padding: 20rpx;
}

.card {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
}


.section-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
}

.section-item {

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }


    .section-header-with-action {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;
    }

    .section-header-left {
        display: flex;
        align-items: center;
    }

    .section-title-bar {
        width: 6rpx;
        height: 30rpx;
        background: #FF7373;
        border-radius: 3rpx;
        margin-right: 12rpx;
    }

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #262937;
    }
}

.section-content-with-arrow {
    display: flex;
    align-items: center;
}

.placeholder {
    color: #BCC0CC; // 灰色
    flex: 1;
    margin-right: 16rpx;

    &.has-content {
        color: #262937; // 黑色
    }
}

.cert-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    flex: 1;
    justify-content: flex-start;

    .cert-tag {
        background-color: #FFF0F0;
        color: #FF7373;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
    }
}

.timeline {
    position: relative;

    .timeline-item {
        position: relative;
        padding-left: 30rpx;
        padding-bottom: 40rpx;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 12rpx;
            width: 16rpx;
            height: 16rpx;
            background: #FF7373;
            border-radius: 50%;
            z-index: 1;
        }

        &:not(:last-child):after {
            content: '';
            position: absolute;
            left: 7rpx;
            top: 12rpx;
            bottom: 0;
            width: 2rpx;
            background: #EAEAEA;
        }

        &:last-child {
            padding-bottom: 0;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-arrow {
            margin-left: 20rpx;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 28rpx;
            color: #262937;
            font-weight: bold;
            margin-bottom: 16rpx;

            .location {
                margin-left: 16rpx;
                font-weight: normal;
                color: #595E72;
            }
        }

        .school,
        .company {
            font-size: 28rpx;
            color: #262937;
            font-weight: bold;
            margin-bottom: 16rpx;
        }

        .timeline-body {
            font-size: 26rpx;
            color: #595E72;
            line-height: 1.6;
        }
    }
}


.footer-buttons {
    display: flex;
    padding: 20rpx;
    background: #fff;
    gap: 20rpx;
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #F0F0F0;

    .btn {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
    }

    .attachment-btn {
        background: #FFF0F0;
        color: #FF7373;
        border: 1px solid #FF7373;
        width: 100%;
    }
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
}

.loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
}

/* 空状态样式 */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
}

/* 弹窗样式 */
.advantage-popup,
.certification-popup {
    background-color: #fff;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 32rpx;
    border-bottom: 1rpx solid #e5e5e5;
}

.popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #303133;
}

.popup-close {
    font-size: 28rpx;
    color: #909399;
}

.popup-content {
    padding: 20rpx 32rpx;
    max-height: 600rpx;
    overflow-y: auto;
}

.advantage-textarea {
    width: auto;
    min-height: 200rpx;
    padding: 20rpx;
    border: 1rpx solid #e5e5e5;
    border-radius: 8rpx;
    font-size: 28rpx;
    line-height: 1.5;
    resize: none;
    outline: none;
    background-color: #fff;
    color: #303133;
    
    &::placeholder {
        color: #c0c4cc;
    }
    
    &:focus {
        border-color: #FF7373;
    }
}

.cert-option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;

    &:active {
        background-color: #f5f5f5;
    }
}

.cert-option-label {
    font-size: 30rpx;
    color: #303133;
}

.cert-checkbox {
    width: 36rpx;
    height: 36rpx;
    border: 2rpx solid #dcdfe6;
    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;

    &.checked {
        background-color: #FF7373;
        border-color: #FF7373;
    }
}

.popup-footer {
    padding: 20rpx 32rpx 40rpx;
}

.popup-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: #FF7373;
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
    border: none;
    margin: 0;
}
</style>