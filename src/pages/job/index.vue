<template>
  <scroll-view scroll-y style="height: 100vh;" @scrolltolower="loadMore">
    <view class="job-page">
      <!-- uView-UI 导航栏 -->
      <u-navbar :is-back="true" title="招聘入职" title-color="#262937" title-size="36" @back="goBack" />

      <!-- 页面内容 -->
      <view class="page-content">

        <!-- 实际内容 -->
        <!-- 搜索区域 -->
        <JobSearch @search="handleSearch" @locationChange="handleLocationChange" />

        <!-- 功能卡片 -->
        <JobFunctionCards @cardClick="handleCardClick" />

        <!-- 职位列表 -->
        <JobList :jobList="jobList" :loading="loading" :hasMore="hasMore" @click="handleJobClick" @filter="handleFilter"
          @loadMore="loadMore" />
      </view>
    </view>
  </scroll-view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { useFilterStore } from './stores/filterStore'
import { useJobList } from './composables/useJobList'
import { useErrorHandler } from './composables/useErrorHandler'
import JobSearch from './components/JobSearch.vue'
import JobFunctionCards from './components/JobFunctionCards.vue'
import JobList from './components/JobList.vue'
import { initJobDictionary } from './utils/jobDictionary'
import { getAreaNamesByCodes } from './utils/addressUtils'
import { useUserStore } from "@/stores/userInfo.js"

const userInfo = useUserStore()
const store = useFilterStore()
const { showErrorToast } = useErrorHandler()

// 当前显示的地区名称
const currentLocation = ref('北京')

// 使用职位列表Hook
const {
  jobList,
  loading,
  hasMore,
  searchJobs,
  loadMore: loadMoreJobs,
  updateSearchParams
} = useJobList({ pageSize: 10 })

onLoad(async () => {
  try {
    // 初始化字典数据
    await initJobDictionary()

    // 初始化搜索参数
    await updateSearchParams({
      school_id: userInfo.school_id
    })
  } catch (error) {
    console.error('页面初始化失败:', error)
    showErrorToast('页面初始化失败')
  }
})

// 设置搜索变化回调
store.setSearchChangeCallback(async (params, requestId) => {
  try {
    await searchJobs(params.keyword || '', {
      school_id: userInfo.school_id,
      ...params
    })
  } catch (error) {
    console.error('搜索失败:', error)
  }
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 处理搜索
const handleSearch = (keyword) => {
  // 使用 store 的统一搜索机制
  store.setKeyword(keyword)
}

// 处理地点变化
const handleLocationChange = (codes) => {
  console.log('地区编码变化:', codes)

  try {
    // 使用工具函数获取地区名称
    const names = getAreaNamesByCodes(codes)

    // 更新 store 中的地区信息
    store.currentLocation.codes = codes || []
    store.currentLocation.names = names

    // 更新当前显示的地区名称
    currentLocation.value = names.length > 1 ? names[1] : (names[0] || '选择城市')

    // 设置省市编码到API参数中
    if (codes && codes.length >= 1) {
      if (codes[0]) {
        store.setProvince(codes[0])
      }
      if (codes[1]) {
        store.setCity(codes[1])
      }
    }
  } catch (error) {
    console.error('处理地区变化失败:', error)
    showErrorToast('地区设置失败')
  }
}

// 处理功能卡片点击
const handleCardClick = (type) => {
  switch (type) {
    case 'intention':
      uni.navigateTo({ url: '/pages/job/intention' })
      break
    case 'resume':
      uni.navigateTo({ url: '/pages/job/resume' })
      break
    case 'my-jobs':
      uni.navigateTo({ url: '/pages/job/my-jobs' })
      break
  }
}

// 处理职位点击
const handleJobClick = (job) => {
  uni.navigateTo({ url: `/pages/job/detail?id=${job.id}` })
}

// 处理筛选按钮点击
const handleFilter = () => {
  uni.navigateTo({ url: '/pages/job/filter' })
}

// 加载职位列表
const loadJobList = (requestId = null) => {
  if (loading.value) return

  loading.value = true
  status.value = 'loading'

  if (page === 1) {
    uni.showLoading({ title: '加载中...' })
  }
  
  // 调用招聘职位列表API接口
  request({
    url: '/api/recruitment/job/filterList',
    data: {
      page,
      per_page: 10,
      school_id: userInfo.school_id,
      keyword: store.keyword,
      ...store.apiFilters
    }
  }).then(async res => {
    // 检查请求是否过期
    if (requestId && requestId !== store.searchState.requestId) {
      console.log('请求已过期，忽略结果')
      return
    }
    // 处理分页响应格式
    const jobData = res.data || []

    // 转换数据格式以适配JobCard组件（异步处理）
    const formattedJobs = await Promise.all(jobData.map(job => formatJobData(job)))

    if (page === 1) {
      jobList.value = formattedJobs
      uni.hideLoading()
    } else {
      jobList.value.push(...formattedJobs)
    }

    // 根据分页信息判断是否还有更多数据
    if (res.current_page >= res.last_page) {
      hasMore.value = false
      status.value = 'nomore'
    } else {
      hasMore.value = true
      status.value = 'loadmore'
    }

  }).catch(error => {
    console.error('加载职位列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    if (page === 1) {
      uni.hideLoading()
    }
  }).finally(() => {
    loading.value = false
  })
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    page++
    loadJobList()
  }
}

// 触底加载更多
onReachBottom(() => {
  loadMore()
})



// 格式化职位数据以适配JobCard组件
const formatJobData = async (job) => {
  console.log('格式化职位数据:', job)

  // 解析福利待遇JSON字符串
  let benefits = []
  try {
    benefits = JSON.parse(job.welfare_subsidy || '[]')
  } catch (e) {
    benefits = []
  }

  // 使用字典数据获取就业形式和学历要求
  const employmentType = await getDictionaryText('employment_form', job.employment_form)
  const educationRequire = await getDictionaryText('educational_require', job.educational_require)

  // 获取区县名称
  const areaName = getAreaNameFromLocal(job.work_location_area_code)

  const formattedJob = {
    id: job.id,
    title: job.name,
    type: employmentType || '全职',
    salaryRange: `${job.salary_benefits_start}-${job.salary_benefits_end}元/月`,
    area: areaName,
    experience: `${job.work_experience}年经验`,
    education: educationRequire || '不限',
    benefits: benefits.slice(0, 4), // 最多显示4个福利标签
    company: job.school_name, // 可以根据school_id获取具体机构名称
    address: job.working_address || '待定',
    logo: job.school_logo, // API中没有距离信息
  }

  console.log('格式化后的职位数据:', formattedJob)
  return formattedJob
}


</script>

<style lang="scss" scoped>
.job-page {
  background: #F6F6F6;
  min-height: 100vh;
}

.page-content {
  padding-bottom: 40rpx;
}
</style>
