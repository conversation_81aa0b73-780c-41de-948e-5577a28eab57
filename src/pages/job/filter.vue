<template>
  <view class="resume-page">
    <u-navbar
      :is-back="true"
      title="筛选条件"
      title-color="#262937"
      title-size="36"
      @back="goBack"
    />
    <scroll-view scroll-y class="scroll-view-content">
      <view class="content">
        <view class="section-card">
          <FilterSection 
            title="岗位选择" 
            icon="bag-fill" 
            :options="positionOptions" 
            v-model="selectedPositions" 
            multiple 
          />
        </view>
        <view class="section-card">
          <FilterSection 
            title="区域选择" 
            icon="map-fill" 
            :options="areaOptions" 
            v-model="selectedAreas" 
            multiple 
          />
        </view>
        <view class="section-card">
          <FilterSection 
            title="用工形式" 
            icon="account-fill" 
            :options="employmentTypeOptions" 
            v-model="selectedEmploymentTypes" 
          />
        </view>
        <view class="section-card">
          <FilterSection 
            title="学历要求" 
            icon="level" 
            :options="educationOptions" 
            v-model="selectedEducation" 
          />
        </view>
        <view class="section-card">
          <FilterSection 
            title="薪资范围" 
            icon="rmb-circle-fill" 
            :options="salaryOptions" 
            v-model="selectedSalary" 
          />
        </view>
        <view class="section-card">
          <FilterSection 
            title="福利待遇" 
            icon="heart-fill" 
            :options="store.benefitOptions" 
            v-model="selectedBenefits" 
            multiple 
          />
        </view>
      </view>
    </scroll-view>
    <view class="footer-buttons">
      <button class="btn attachment-btn" @click="resetFilters">重置</button>
      <button class="btn save-btn" @click="confirmFilters">确定</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import FilterSection from './components/FilterSection.vue'
import { useFilterStore } from './stores/filterStore'
import { 
  getRoleOptions, 
  getEmploymentFormOptions, 
  getEducationOptions 
} from './utils/jobDictionary'

const store = useFilterStore()

const goBack = () => {
  uni.navigateBack()
}

// 筛选条件数据
const positionOptions = ref([])
const areaOptions = ref([])
const employmentTypeOptions = ref([])
const educationOptions = ref([])
const salaryOptions = ref([
  { label: '3000以下', value: '1' },
  { label: '3001-4000', value: '2' },
  { label: '4001-5000', value: '3' },
  { label: '5001-6000', value: '4' },
  { label: '6000以上', value: '5' },
])
// 选中的筛选条件
const selectedPositions = ref([])
const selectedAreas = ref([])
const selectedEmploymentTypes = ref('')
const selectedEducation = ref('')
const selectedSalary = ref('')
const selectedBenefits = ref([])

// 重置筛选
const resetFilters = () => {
  selectedPositions.value = []
  selectedAreas.value = []
  selectedEmploymentTypes.value = ''
  selectedEducation.value = ''
  selectedSalary.value = ''
  selectedBenefits.value = []
  
  // 只重置UI状态，不立即更新store（避免触发接口刷新）
  // 用户需要点击"确定"才会应用重置
}

// 初始化地区选项数据
const initAreaOptions = () => {
  // 从 store 获取当前城市下的所有区县
  const availableDistricts = store.locationState.availableDistricts
  
  // 转换为选项格式
  areaOptions.value = availableDistricts.map(district => ({
    label: district.name,
    value: district.code
  }))
}

// 初始化筛选选项数据
const initFilterOptions = async () => {
  try {
    // 并行获取所有字典选项
    const [roleOpts, employmentOpts, educationOpts] = await Promise.all([
      getRoleOptions(),
      getEmploymentFormOptions(), 
      getEducationOptions()
    ]);
    
    // 设置选项数据
    positionOptions.value = roleOpts;
    employmentTypeOptions.value = employmentOpts;
    educationOptions.value = educationOpts;
    
  } catch (error) {
    console.error('初始化筛选选项失败:', error)
    // 不再降级处理
    positionOptions.value = [];
    employmentTypeOptions.value = [];
    educationOptions.value = [];
  }
}

// 页面加载时初始化数据
onLoad(async () => {
  // 初始化筛选选项数据
  await initFilterOptions()
  
  // 初始化地区选项数据（同步）
  initAreaOptions()

  // 从 store 回显数据
  const currentFilters = store.filterConditions
  selectedPositions.value = currentFilters.positions || []
  selectedAreas.value = currentFilters.areas || []
  selectedEmploymentTypes.value = currentFilters.employmentType || ''
  selectedEducation.value = currentFilters.education || ''
  selectedSalary.value = currentFilters.salary || ''
  selectedBenefits.value = currentFilters.benefits || []
})

// 确认筛选
const confirmFilters = () => {
  const filters = {
    positions: selectedPositions.value,
    areas: selectedAreas.value,
    employmentType: selectedEmploymentTypes.value,
    education: selectedEducation.value,
    salary: selectedSalary.value,
    benefits: selectedBenefits.value,
  }
  
  store.updateFilters(filters)
  
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.resume-page {
    background: #F6F6F6;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.scroll-view-content {
    flex: 1;
    overflow-y: auto;
}

.content {
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.section-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
}

.footer-buttons {
    display: flex;
    padding: 20rpx;
    background: #fff;
    gap: 20rpx;
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #F0F0F0;

    .btn {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
    }

    .attachment-btn {
        background: #FFF0F0;
        color: #FF7373;
        border: 1px solid #FF7373;
    }

    .save-btn {
        background: #FF7373;
        color: #fff;
    }
}
</style>
