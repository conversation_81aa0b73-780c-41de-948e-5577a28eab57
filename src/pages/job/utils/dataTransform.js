/**
 * @fileoverview 数据转换工具函数
 * 统一处理API数据到UI数据的转换逻辑
 */

import { getDictionaryText, getCertificationTexts } from './jobDictionary'
import { getAreaNameFromLocal } from './addressUtils'

/**
 * @typedef {Object} FormattedJobItem
 * @property {string} id - 职位ID
 * @property {string} title - 职位标题
 * @property {string} type - 用工形式
 * @property {string} salaryRange - 薪资范围
 * @property {string} area - 工作地区
 * @property {string} experience - 工作经验要求
 * @property {string} education - 学历要求
 * @property {string[]} benefits - 福利待遇列表
 * @property {string} company - 公司名称
 * @property {string} address - 工作地址
 * @property {string} logo - 公司logo
 */

/**
 * 格式化职位数据以适配JobCard组件
 * @param {Object} job - 原始职位数据
 * @returns {Promise<FormattedJobItem>} 格式化后的职位数据
 */
export async function formatJobData(job) {
  if (!job) {
    throw new Error('职位数据不能为空')
  }

  try {
    // 解析福利待遇JSON字符串
    let benefits = []
    try {
      benefits = JSON.parse(job.welfare_subsidy || '[]')
    } catch (e) {
      console.warn('解析福利待遇失败:', e)
      benefits = []
    }

    // 并行获取字典数据
    const [employmentType, educationRequire] = await Promise.all([
      getDictionaryText('employment_form', job.employment_form).catch(() => '全职'),
      getDictionaryText('educational_require', job.educational_require).catch(() => '不限')
    ])

    // 获取区县名称
    const areaName = getAreaNameFromLocal(job.work_location_area_code) || '待定'

    return {
      id: job.id,
      title: job.name || '职位名称',
      type: employmentType,
      salaryRange: formatSalaryRange(job.salary_benefits_start, job.salary_benefits_end),
      area: areaName,
      experience: formatExperience(job.work_experience),
      education: educationRequire,
      benefits: benefits.slice(0, 4), // 最多显示4个福利标签
      company: job.school_name || '公司名称',
      address: job.working_address || '待定',
      logo: job.school_logo || ''
    }
  } catch (error) {
    console.error('格式化职位数据失败:', error)
    // 返回默认数据，避免页面崩溃
    return {
      id: job.id || '',
      title: job.name || '职位名称',
      type: '全职',
      salaryRange: '面议',
      area: '待定',
      experience: '不限',
      education: '不限',
      benefits: [],
      company: job.school_name || '公司名称',
      address: '待定',
      logo: ''
    }
  }
}

/**
 * 格式化薪资范围
 * @param {number} start - 最低薪资
 * @param {number} end - 最高薪资
 * @returns {string} 格式化后的薪资范围
 */
export function formatSalaryRange(start, end) {
  if (!start && !end) return '面议'
  if (!start) return `${end}元以下/月`
  if (!end) return `${start}元以上/月`
  if (start === end) return `${start}元/月`
  return `${start}-${end}元/月`
}

/**
 * 格式化工作经验要求
 * @param {number} experience - 工作经验年限
 * @returns {string} 格式化后的经验要求
 */
export function formatExperience(experience) {
  if (!experience || experience === 0) return '经验不限'
  if (experience === 1) return '1年经验'
  return `${experience}年经验`
}

/**
 * 格式化职位详情数据
 * @param {Object} data - 原始职位详情数据
 * @returns {Promise<Object>} 格式化后的职位详情
 */
export async function formatJobDetails(data) {
  if (!data) {
    throw new Error('职位详情数据不能为空')
  }

  try {
    // 解析JSON字符串
    const benefits = parseJsonSafely(data.welfare_subsidy, [])
    const environment = parseJsonSafely(data.working_environment, [])

    // 并行获取字典数据
    const [
      genderText,
      educationText,
      employmentText,
      certifications
    ] = await Promise.all([
      getDictionaryText('gender', data.gender).catch(() => '不限'),
      getDictionaryText('educational_require', data.educational_require).catch(() => '不限'),
      getDictionaryText('employment_form', data.employment_form).catch(() => '全职'),
      getCertificationTexts(data.certification_requirements).catch(() => [])
    ])

    // 获取区域名称
    const areaName = getAreaNameFromLocal(data.work_location_area_code) || '待定'

    return {
      title: data.name || '',
      type: employmentText,
      salary: formatSalaryRange(data.salary_benefits_start, data.salary_benefits_end),
      company: data.school_name || '',
      location: areaName,
      experience: formatExperience(data.work_experience),
      education: educationText,
      benefits: benefits,
      address: data.working_address || '',
      requirements: {
        position: data.name || '',
        gender: genderText,
        age: formatAgeRequirement(data.age_requirements),
        education: educationText,
        major: data.professional_requirements || '不限'
      },
      certifications: certifications,
      responsibilities: data.job_description ? [data.job_description] : [],
      environment: environment
    }
  } catch (error) {
    console.error('格式化职位详情失败:', error)
    throw error
  }
}

/**
 * 格式化年龄要求
 * @param {number} age - 年龄要求
 * @returns {string} 格式化后的年龄要求
 */
export function formatAgeRequirement(age) {
  if (!age || age === 0) return '年龄不限'
  return `${age}岁以下`
}

/**
 * 安全解析JSON字符串
 * @param {string} jsonString - JSON字符串
 * @param {*} defaultValue - 默认值
 * @returns {*} 解析结果或默认值
 */
export function parseJsonSafely(jsonString, defaultValue = null) {
  if (!jsonString) return defaultValue
  
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return defaultValue
  }
}

/**
 * 格式化个人信息显示数据
 * @param {Object} apiData - API返回的个人信息
 * @param {string} genderLabel - 性别标签
 * @param {string} degreeLabel - 学历标签
 * @param {string[]} certifications - 证书列表
 * @returns {Object} 格式化后的个人信息
 */
export function formatPersonalInfo(apiData, genderLabel, degreeLabel, certifications) {
  return {
    avatar: apiData.avatar || '',
    name: apiData.name || '',
    gender: genderLabel || '',
    age: calculateAge(apiData.birth_date),
    phone: apiData.mobile || '',
    degree: degreeLabel || '',
    experience: calculateWorkExperience(apiData.start_working_date),
    status: apiData.status || '',
    strengths: apiData.personal_advantages || '',
    certifications: certifications || [],
    id: apiData.id || ''
  }
}

/**
 * 计算年龄
 * @param {string} birthDate - 出生日期 YYYY-MM-DD
 * @returns {string} 年龄字符串
 */
export function calculateAge(birthDate) {
  if (!birthDate) return ''

  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age > 0 ? `${age}岁` : ''
}

/**
 * 计算工作经验
 * @param {string} startWorkingDate - 开始工作日期 YYYY-MM-DD
 * @returns {string} 工作经验字符串
 */
export function calculateWorkExperience(startWorkingDate) {
  if (!startWorkingDate) return ''

  const startDate = new Date(startWorkingDate)
  const today = new Date()
  const years = today.getFullYear() - startDate.getFullYear()
  const months = today.getMonth() - startDate.getMonth()

  let totalMonths = years * 12 + months
  if (today.getDate() < startDate.getDate()) {
    totalMonths--
  }

  if (totalMonths < 12) {
    return totalMonths > 0 ? `${totalMonths}个月工作经验` : '应届生'
  } else {
    const experienceYears = Math.floor(totalMonths / 12)
    const remainingMonths = totalMonths % 12
    if (remainingMonths === 0) {
      return `${experienceYears}年工作经验`
    } else {
      return `${experienceYears}年${remainingMonths}个月工作经验`
    }
  }
}
