/**
 * @fileoverview 地址处理工具函数
 * 处理省市区地址相关的数据转换和查询
 */

import provincesData from './lib/address/provinces.json'
import citysData from './lib/address/citys.json'
import areasData from './lib/address/areas.json'

// 创建地址编码到名称的映射，提高查找性能
const provinceCodeMap = new Map(provincesData.map(p => [p.code, p.name]))
const cityCodeMap = new Map()
const districtCodeMap = new Map()

// 初始化城市和区县映射
provincesData.forEach((province, provinceIndex) => {
  const cities = citysData[provinceIndex] || []
  cities.forEach((city, cityIndex) => {
    cityCodeMap.set(city.code, city.name)

    const districts = areasData[provinceIndex]?.[cityIndex] || []
    districts.forEach(district => {
      districtCodeMap.set(district.code, district.name)
    })
  })
})

/**
 * 根据地区编码获取地区名称（使用本地静态数据）
 * @param {string} code - 地区编码
 * @returns {string} 地区名称
 */
export function getAreaNameFromLocal(code) {
  if (!code) return ''

  // 先查找省份
  if (provinceCodeMap.has(code)) {
    return provinceCodeMap.get(code)
  }

  // 再查找城市
  if (cityCodeMap.has(code)) {
    return cityCodeMap.get(code)
  }

  // 最后查找区县
  if (districtCodeMap.has(code)) {
    return districtCodeMap.get(code)
  }

  return ''
}

/**
 * 根据省份编码获取该省份下的所有城市
 * @param {string} provinceCode - 省份编码，例如：'110000'
 * @returns {Array} 城市列表数组
 */
export function getCitiesByProvinceCode(provinceCode) {
  if (!provinceCode) return []
  
  const provinceIndex = provincesData.findIndex(p => p.code === provinceCode)
  if (provinceIndex === -1) return []
  
  return citysData[provinceIndex] || []
}

/**
 * 根据城市编码获取该城市下的所有区县
 * @param {string} cityCode - 城市编码，例如：'110100'
 * @returns {Array} 区县列表数组
 */
export function getDistrictsByCityCode(cityCode) {
  if (!cityCode) return []
  
  // 从城市编码推导省份编码
  const provinceCode = cityCode.substring(0, 2) + '0000'
  const provinceIndex = provincesData.findIndex(p => p.code === provinceCode)
  if (provinceIndex === -1) return []
  
  const cities = citysData[provinceIndex] || []
  const cityIndex = cities.findIndex(c => c.code === cityCode)
  if (cityIndex === -1) return []
  
  return areasData[provinceIndex]?.[cityIndex] || []
}

/**
 * 根据地区编码数组获取对应的地区名称数组
 * @param {Array} codes - 地区编码数组，例如：['410000', '410100', '410101']
 * @returns {Array} 地区名称数组，例如：['河南省', '郑州市', '中原区']
 */
export function getAreaNamesByCodes(codes) {
  if (!codes || codes.length === 0) return []
  
  const names = []

  // 获取省份名称
  if (codes[0]) {
    const province = provincesData.find(p => p.code === codes[0])
    if (province) names.push(province.name)
  }

  // 获取城市名称
  if (codes[1] && names.length > 0) {
    const provinceIndex = provincesData.findIndex(p => p.code === codes[0])
    if (provinceIndex !== -1) {
      const city = citysData[provinceIndex]?.find(c => c.code === codes[1])
      if (city) names.push(city.name)
    }
  }

  // 获取区县名称
  if (codes[2] && names.length === 2) {
    const provinceIndex = provincesData.findIndex(p => p.code === codes[0])
    const cityIndex = citysData[provinceIndex]?.findIndex(c => c.code === codes[1])
    if (provinceIndex !== -1 && cityIndex !== -1) {
      const district = areasData[provinceIndex]?.[cityIndex]?.find(d => d.code === codes[2])
      if (district) names.push(district.name)
    }
  }

  return names
}

/**
 * 获取所有省份列表
 * @returns {Array} 省份列表
 */
export function getAllProvinces() {
  return provincesData.map(province => ({
    code: province.code,
    name: province.name
  }))
}

/**
 * 根据省份编码获取省份信息
 * @param {string} provinceCode - 省份编码
 * @returns {Object|null} 省份信息
 */
export function getProvinceByCode(provinceCode) {
  return provincesData.find(p => p.code === provinceCode) || null
}

/**
 * 根据城市编码获取城市信息
 * @param {string} cityCode - 城市编码
 * @returns {Object|null} 城市信息
 */
export function getCityByCode(cityCode) {
  if (!cityCode) return null

  const provinceCode = cityCode.substring(0, 2) + '0000'
  const provinceIndex = provincesData.findIndex(p => p.code === provinceCode)
  if (provinceIndex === -1) return null

  const cities = citysData[provinceIndex] || []
  return cities.find(c => c.code === cityCode) || null
}

/**
 * 根据区县编码获取区县信息
 * @param {string} districtCode - 区县编码
 * @returns {Object|null} 区县信息
 */
export function getDistrictByCode(districtCode) {
  if (!districtCode) return null

  const provinceCode = districtCode.substring(0, 2) + '0000'
  const cityCode = districtCode.substring(0, 4) + '00'
  
  const provinceIndex = provincesData.findIndex(p => p.code === provinceCode)
  if (provinceIndex === -1) return null

  const cities = citysData[provinceIndex] || []
  const cityIndex = cities.findIndex(c => c.code === cityCode)
  if (cityIndex === -1) return null

  const districts = areasData[provinceIndex]?.[cityIndex] || []
  return districts.find(d => d.code === districtCode) || null
}

/**
 * 构建完整的地址字符串
 * @param {string} provinceCode - 省份编码
 * @param {string} cityCode - 城市编码
 * @param {string} districtCode - 区县编码
 * @param {string} [separator=' '] - 分隔符
 * @returns {string} 完整地址字符串
 */
export function buildFullAddress(provinceCode, cityCode, districtCode, separator = ' ') {
  const parts = []

  if (provinceCode) {
    const provinceName = getAreaNameFromLocal(provinceCode)
    if (provinceName) parts.push(provinceName)
  }

  if (cityCode) {
    const cityName = getAreaNameFromLocal(cityCode)
    if (cityName) parts.push(cityName)
  }

  if (districtCode) {
    const districtName = getAreaNameFromLocal(districtCode)
    if (districtName) parts.push(districtName)
  }

  return parts.join(separator)
}

/**
 * 验证地区编码的有效性
 * @param {string} code - 地区编码
 * @returns {boolean} 是否有效
 */
export function isValidAreaCode(code) {
  if (!code) return false
  return getAreaNameFromLocal(code) !== ''
}

/**
 * 获取地区编码的层级类型
 * @param {string} code - 地区编码
 * @returns {'province'|'city'|'district'|'unknown'} 层级类型
 */
export function getAreaCodeLevel(code) {
  if (!code) return 'unknown'

  if (provinceCodeMap.has(code)) return 'province'
  if (cityCodeMap.has(code)) return 'city'
  if (districtCodeMap.has(code)) return 'district'
  
  return 'unknown'
}
