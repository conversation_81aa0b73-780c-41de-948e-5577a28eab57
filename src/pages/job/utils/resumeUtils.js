// 简历数据转换工具函数
import { getCertificationTexts, getDictionaryText } from './jobDictionary.js'
import provincesData from './lib/address/provinces.json'
import citysData from './lib/address/citys.json'
import areasData from './lib/address/areas.json'

// 创建地址编码到名称的映射，提高查找性能
const provinceCodeMap = new Map(provincesData.map(p => [p.code, p.name]))
const cityCodeMap = new Map()
const districtCodeMap = new Map()

// 初始化城市和区县映射
provincesData.forEach((province, provinceIndex) => {
  const cities = citysData[provinceIndex] || []
  cities.forEach((city, cityIndex) => {
    cityCodeMap.set(city.code, city.name)

    const districts = areasData[provinceIndex]?.[cityIndex] || []
    districts.forEach(district => {
      districtCodeMap.set(district.code, district.name)
    })
  })
})

/**
 * 根据地区编码获取地区名称（使用本地静态数据）
 * @param {string} code - 地区编码
 * @returns {string} - 地区名称
 */
export function getAreaNameFromLocal(code) {
  if (!code) return ''

  // 先查找省份
  if (provinceCodeMap.has(code)) {
    return provinceCodeMap.get(code)
  }

  // 再查找城市
  if (cityCodeMap.has(code)) {
    return cityCodeMap.get(code)
  }

  // 最后查找区县
  if (districtCodeMap.has(code)) {
    return districtCodeMap.get(code)
  }

  return ''
}

/**
 * 根据省份编码获取该省份下的所有城市
 * @param {string} provinceCode - 省份编码，例如：'110000'
 * @returns {Array} - 城市列表数组
 */
export function getCitiesByProvinceCode(provinceCode) {
  if (!provinceCode) return []
  
  const provinceIndex = provincesData.findIndex(p => p.code === provinceCode)
  if (provinceIndex === -1) return []
  
  return citysData[provinceIndex] || []
}

/**
 * 根据城市编码获取该城市下的所有区县
 * @param {string} cityCode - 城市编码，例如：'110100'
 * @returns {Array} - 区县列表数组
 */
export function getDistrictsByCityCode(cityCode) {
  if (!cityCode) return []
  
  // 从城市编码推导省份编码
  const provinceCode = cityCode.substring(0, 2) + '0000'
  const provinceIndex = provincesData.findIndex(p => p.code === provinceCode)
  if (provinceIndex === -1) return []
  
  const cities = citysData[provinceIndex] || []
  const cityIndex = cities.findIndex(c => c.code === cityCode)
  if (cityIndex === -1) return []
  
  return areasData[provinceIndex]?.[cityIndex] || []
}

/**
 * 根据地区编码数组获取对应的地区名称数组
 * @param {Array} codes - 地区编码数组，例如：['410000', '410100', '410101']
 * @returns {Array} - 地区名称数组，例如：['河南省', '郑州市', '中原区']
 */
export function getAreaNamesByCodes(codes) {
  if (!codes || codes.length === 0) return []
  
  const names = []

  // 获取省份名称
  if (codes[0]) {
    const province = provincesData.find(p => p.code === codes[0])
    if (province) names.push(province.name)
  }

  // 获取城市名称
  if (codes[1] && names.length > 0) {
    const provinceIndex = provincesData.findIndex(p => p.code === codes[0])
    if (provinceIndex !== -1) {
      const city = citysData[provinceIndex]?.find(c => c.code === codes[1])
      if (city) names.push(city.name)
    }
  }

  // 获取区县名称
  if (codes[2] && names.length === 2) {
    const provinceIndex = provincesData.findIndex(p => p.code === codes[0])
    const cityIndex = citysData[provinceIndex]?.findIndex(c => c.code === codes[1])
    if (provinceIndex !== -1 && cityIndex !== -1) {
      const district = areasData[provinceIndex]?.[cityIndex]?.find(d => d.code === codes[2])
      if (district) names.push(district.name)
    }
  }

  return names
}


/**
 * 计算年龄，显示为"XX岁"
 * @param {string} birthDate - 出生日期 YYYY-MM-DD
 * @returns {string}
 */
export function calculateAge(birthDate) {
  if (!birthDate) return ''

  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age > 0 ? `${age}岁` : ''
}

/**
 * 计算工作经验年限，显示为"X年工作经验"
 * @param {string} startWorkingDate - 开始工作日期 YYYY-MM-DD
 * @returns {string}
 */
export function calculateWorkExperience(startWorkingDate) {
  if (!startWorkingDate) return ''

  const startDate = new Date(startWorkingDate)
  const today = new Date()
  const years = today.getFullYear() - startDate.getFullYear()
  const months = today.getMonth() - startDate.getMonth()

  let totalMonths = years * 12 + months
  if (today.getDate() < startDate.getDate()) {
    totalMonths--
  }

  if (totalMonths < 12) {
    return totalMonths > 0 ? `${totalMonths}个月工作经验` : '应届生'
  } else {
    const experienceYears = Math.floor(totalMonths / 12)
    const remainingMonths = totalMonths % 12
    if (remainingMonths === 0) {
      return `${experienceYears}年工作经验`
    } else {
      return `${experienceYears}年${remainingMonths}个月工作经验`
    }
  }
}

/**
 * 统一的证书解析工具
 * @param {string} certificationRequirements - 逗号分隔的证书ID字符串
 * @param {string} type - 返回类型 'array' | 'text' | 'tags'
 * @returns {Promise<Array|string>} - 证书数据
 */
export async function processCertifications(certificationRequirements, type = 'array') {
  if (!certificationRequirements) return type === 'array' ? [] : ''

  try {
    const certificationTexts = await getCertificationTexts(certificationRequirements)

    switch (type) {
      case 'text':
        return certificationTexts.join('、')
      case 'tags':
        return certificationTexts.map(text => ({ text, type: 'cert' }))
      case 'array':
      default:
        return certificationTexts
    }
  } catch (error) {
    console.error('解析证书失败:', error)
    return type === 'array' ? [] : ''
  }
}


/**
 * 格式化日期显示
 * @param {string} dateStr - 日期字符串 YYYY-MM-DD
 * @param {string} format - 格式化类型 'YYYY-MM' | 'YYYY-MM-DD' | 'YYYY.MM'
 * @returns {string} - 格式化后的日期
 */
export function formatDateDisplay(dateStr, format = 'YYYY-MM') {
  if (!dateStr) return ''

  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'YYYY.MM':
      return `${year}.${month}`
    case 'YYYY-MM':
    default:
      return `${year}-${month}`
  }
}

/**
 * 转换个人信息API数据为store格式
 * @param {Object} apiData - API返回的个人信息数据
 * @returns {Promise<Object>}
 */
export async function transformPersonalInfo(apiData) {
  if (!apiData) return {}

  // 并行获取字典数据
  const [genderLabel, degreeLabel, certifications] = await Promise.all([
    getDictionaryText('gender', apiData.gender).catch(() => ''),
    getDictionaryText('educational_require', apiData.educational_require).catch(() => ''),
    processCertifications(apiData.certification_requirements)
  ])

  return buildPersonalInfoDisplay(apiData, genderLabel, degreeLabel, certifications)
}

/**
 * 构建个人信息显示数据
 * @param {Object} apiData - API数据
 * @param {string} genderLabel - 性别标签
 * @param {string} degreeLabel - 学历标签
 * @param {Array} certifications - 证书数组
 * @returns {Object}
 */
function buildPersonalInfoDisplay(apiData, genderLabel, degreeLabel, certifications) {
  return {
    avatar: apiData.avatar || '',
    name: apiData.name || '',
    gender: genderLabel,
    age: calculateAge(apiData.birth_date),
    phone: apiData.mobile || '',
    degree: degreeLabel,
    experience: calculateWorkExperience(apiData.start_working_date),
    status: apiData.status,
    strengths: apiData.personal_advantages || '',
    certifications: certifications,
    id: apiData.id || '',
  }
}

/**
 * 构建教育经历显示数据
 * @param {Object} item - 教育经历数据
 * @returns {Object}
 */
export function buildEducationDisplay(item) {
  return {
    id: item.id,
    school: item.school_name || '',
    major: item.major_name || '',
    degree: item.educational_require || '',
    startDate: formatDateDisplay(item.school_online_start),
    endDate: formatDateDisplay(item.school_online_end),
    description: item.school_experience || ''
  }
}

/**
 * 构建工作经历显示数据
 * @param {Object} item - 工作经历数据
 * @returns {Object}
 */
export function buildWorkDisplay(item) {
  // 获取完整的省市区地址
  let workLocation = '待定'

  if (item.work_location_province_code || item.work_location_city_code || item.work_location_area_code) {
    // 使用本地静态数据获取省市区名称
    const provinceName = item.work_location_province_code ? getAreaNameFromLocal(item.work_location_province_code) : ''
    const cityName = item.work_location_city_code ? getAreaNameFromLocal(item.work_location_city_code) : ''
    const areaName = item.work_location_area_code ? getAreaNameFromLocal(item.work_location_area_code) : ''

    // 组合有效的地址部分
    const locationParts = [provinceName, cityName, areaName]
      .filter(name => name && name !== '待定')

    // 组合完整地址
    if (locationParts.length > 0) {
      workLocation = locationParts.join(' ')
    }
  }

  return {
    id: item.id,
    company: item.workplace_name || '',
    position: item.job_title || '',
    startDate: formatDateDisplay(item.work_date_start),
    endDate: formatDateDisplay(item.work_date_end),
    content: item.work_content || '',
    location: workLocation
  }
}




/**
 * 将前端日期格式转换为API要求的格式
 * @param {string} dateStr - 前端日期格式 YYYY-MM、YYYY-MM-DD 或 YYYY.MM
 * @param {boolean} needDateTime - 是否需要完整的日期时间格式 (YYYY-MM-DD HH:MM:SS)
 * @param {boolean} isEndDate - 是否为结束日期（仅在needDateTime为true时有效）
 * @returns {string} - API格式 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
 */
export function formatDateForAPI(dateStr, needDateTime = false, isEndDate = false) {
  if (!dateStr) return ''

  // 如果已经是完整的日期时间格式，直接返回
  if (dateStr.includes(' ') && dateStr.includes(':')) {
    return dateStr
  }

  // 处理"至今"等特殊文本，转换为当前日期
  if (isCurrentDateKeyword(dateStr)) {
    return formatCurrentDate(needDateTime, isEndDate)
  }

  // 解析日期字符串
  const parsedDate = parseDateString(dateStr)
  if (!parsedDate) return ''

  const { year, month, day } = parsedDate
  const baseDate = `${year}-${month}-${day || '01'}`

  return needDateTime ? `${baseDate} ${isEndDate ? '23:59:59' : '00:00:00'}` : baseDate
}

/**
 * 判断是否为当前日期关键词
 * @param {string} dateStr - 日期字符串
 * @returns {boolean}
 */
function isCurrentDateKeyword(dateStr) {
  return ['至今', '现在', 'now'].includes(dateStr.toLowerCase())
}

/**
 * 格式化当前日期
 * @param {boolean} needDateTime - 是否需要时间
 * @param {boolean} isEndDate - 是否为结束日期
 * @returns {string}
 */
function formatCurrentDate(needDateTime, isEndDate) {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const baseDate = `${year}-${month}-${day}`
  return needDateTime ? `${baseDate} ${isEndDate ? '23:59:59' : '00:00:00'}` : baseDate
}

/**
 * 解析日期字符串
 * @param {string} dateStr - 日期字符串
 * @returns {Object|null} - 解析结果 {year, month, day}
 */
function parseDateString(dateStr) {
  // YYYY.MM 格式
  if (dateStr.includes('.')) {
    const parts = dateStr.split('.')
    return {
      year: parts[0],
      month: parts[1].padStart(2, '0'),
      day: null
    }
  }

  // YYYY-MM-DD 格式
  if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const parts = dateStr.split('-')
    return {
      year: parts[0],
      month: parts[1],
      day: parts[2]
    }
  }

  // YYYY-MM 格式
  if (dateStr.match(/^\d{4}-\d{2}$/)) {
    const parts = dateStr.split('-')
    return {
      year: parts[0],
      month: parts[1],
      day: null
    }
  }

  // 其他格式尝试解析
  try {
    const date = new Date(dateStr)
    if (!isNaN(date.getTime())) {
      return {
        year: date.getFullYear(),
        month: String(date.getMonth() + 1).padStart(2, '0'),
        day: String(date.getDate()).padStart(2, '0')
      }
    }
  } catch (error) {
    console.error('日期格式转换失败:', dateStr, error)
  }

  return null
}