/**
 * @fileoverview 日期处理工具函数
 * 统一处理日期格式化、转换、验证等操作
 */

/**
 * 格式化日期显示
 * @param {string} dateStr - 日期字符串 YYYY-MM-DD
 * @param {string} [format='YYYY-MM'] - 格式化类型
 * @returns {string} 格式化后的日期
 */
export function formatDateDisplay(dateStr, format = 'YYYY-MM') {
  if (!dateStr) return ''

  const date = new Date(dateStr)
  if (isNaN(date.getTime())) return ''

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'YYYY.MM':
      return `${year}.${month}`
    case 'YYYY.MM.DD':
      return `${year}.${month}.${day}`
    case 'MM/DD/YYYY':
      return `${month}/${day}/${year}`
    case 'YYYY年MM月':
      return `${year}年${month}月`
    case 'YYYY年MM月DD日':
      return `${year}年${month}月${day}日`
    case 'YYYY-MM':
    default:
      return `${year}-${month}`
  }
}

/**
 * 将前端日期格式转换为API要求的格式
 * @param {string} dateStr - 前端日期格式
 * @param {boolean} [needDateTime=false] - 是否需要完整的日期时间格式
 * @param {boolean} [isEndDate=false] - 是否为结束日期
 * @returns {string} API格式的日期
 */
export function formatDateForAPI(dateStr, needDateTime = false, isEndDate = false) {
  if (!dateStr) return ''

  // 如果已经是完整的日期时间格式，直接返回
  if (dateStr.includes(' ') && dateStr.includes(':')) {
    return dateStr
  }

  // 处理"至今"等特殊文本
  if (isCurrentDateKeyword(dateStr)) {
    return formatCurrentDate(needDateTime, isEndDate)
  }

  // 解析日期字符串
  const parsedDate = parseDateString(dateStr)
  if (!parsedDate) return ''

  const { year, month, day } = parsedDate
  const baseDate = `${year}-${month}-${day || '01'}`

  return needDateTime ? `${baseDate} ${isEndDate ? '23:59:59' : '00:00:00'}` : baseDate
}

/**
 * 判断是否为当前日期关键词
 * @param {string} dateStr - 日期字符串
 * @returns {boolean}
 */
function isCurrentDateKeyword(dateStr) {
  const keywords = ['至今', '现在', 'now', '目前', '当前']
  return keywords.includes(dateStr.toLowerCase().trim())
}

/**
 * 格式化当前日期
 * @param {boolean} needDateTime - 是否需要时间
 * @param {boolean} isEndDate - 是否为结束日期
 * @returns {string}
 */
function formatCurrentDate(needDateTime, isEndDate) {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const baseDate = `${year}-${month}-${day}`
  return needDateTime ? `${baseDate} ${isEndDate ? '23:59:59' : '00:00:00'}` : baseDate
}

/**
 * 解析日期字符串
 * @param {string} dateStr - 日期字符串
 * @returns {Object|null} 解析结果 {year, month, day}
 */
function parseDateString(dateStr) {
  if (!dateStr) return null

  // YYYY.MM 格式
  if (dateStr.includes('.')) {
    const parts = dateStr.split('.')
    if (parts.length >= 2) {
      return {
        year: parts[0],
        month: parts[1].padStart(2, '0'),
        day: parts[2] ? parts[2].padStart(2, '0') : null
      }
    }
  }

  // YYYY-MM-DD 格式
  if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const parts = dateStr.split('-')
    return {
      year: parts[0],
      month: parts[1],
      day: parts[2]
    }
  }

  // YYYY-MM 格式
  if (dateStr.match(/^\d{4}-\d{2}$/)) {
    const parts = dateStr.split('-')
    return {
      year: parts[0],
      month: parts[1],
      day: null
    }
  }

  // YYYY/MM/DD 格式
  if (dateStr.includes('/')) {
    const parts = dateStr.split('/')
    if (parts.length === 3) {
      return {
        year: parts[2] || parts[0], // 支持 MM/DD/YYYY 和 YYYY/MM/DD
        month: parts[0].length === 4 ? parts[1].padStart(2, '0') : parts[0].padStart(2, '0'),
        day: parts[0].length === 4 ? parts[2].padStart(2, '0') : parts[1].padStart(2, '0')
      }
    }
  }

  // 其他格式尝试解析
  try {
    const date = new Date(dateStr)
    if (!isNaN(date.getTime())) {
      return {
        year: String(date.getFullYear()),
        month: String(date.getMonth() + 1).padStart(2, '0'),
        day: String(date.getDate()).padStart(2, '0')
      }
    }
  } catch (error) {
    console.warn('日期格式转换失败:', dateStr, error)
  }

  return null
}

/**
 * 验证日期字符串是否有效
 * @param {string} dateStr - 日期字符串
 * @returns {boolean} 是否有效
 */
export function isValidDate(dateStr) {
  if (!dateStr) return false
  
  const date = new Date(dateStr)
  return !isNaN(date.getTime())
}

/**
 * 获取两个日期之间的月份差
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {number} 月份差
 */
export function getMonthsDifference(startDate, endDate) {
  if (!startDate || !endDate) return 0

  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0

  const yearDiff = end.getFullYear() - start.getFullYear()
  const monthDiff = end.getMonth() - start.getMonth()
  
  return yearDiff * 12 + monthDiff
}

/**
 * 获取两个日期之间的年份差
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {number} 年份差
 */
export function getYearsDifference(startDate, endDate) {
  const months = getMonthsDifference(startDate, endDate)
  return Math.floor(months / 12)
}

/**
 * 格式化时间段
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @param {string} [format='YYYY-MM'] - 日期格式
 * @returns {string} 格式化后的时间段
 */
export function formatDateRange(startDate, endDate, format = 'YYYY-MM') {
  const start = formatDateDisplay(startDate, format)
  const end = endDate && !isCurrentDateKeyword(endDate) 
    ? formatDateDisplay(endDate, format) 
    : '至今'
  
  return `${start} - ${end}`
}

/**
 * 获取当前日期字符串
 * @param {string} [format='YYYY-MM-DD'] - 日期格式
 * @returns {string} 当前日期
 */
export function getCurrentDate(format = 'YYYY-MM-DD') {
  return formatDateDisplay(new Date().toISOString(), format)
}

/**
 * 比较两个日期的大小
 * @param {string} date1 - 日期1
 * @param {string} date2 - 日期2
 * @returns {number} -1: date1 < date2, 0: date1 = date2, 1: date1 > date2
 */
export function compareDates(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return 0
  
  if (d1 < d2) return -1
  if (d1 > d2) return 1
  return 0
}

/**
 * 检查日期是否在指定范围内
 * @param {string} date - 要检查的日期
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} 是否在范围内
 */
export function isDateInRange(date, startDate, endDate) {
  const target = new Date(date)
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (isNaN(target.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
    return false
  }
  
  return target >= start && target <= end
}
