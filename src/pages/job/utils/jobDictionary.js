import request from '@/request'

// 字典数据缓存
let dictionaryCache = null
let cacheTime = null
let isLoading = false
const CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

/**
 * 获取招聘字典数据
 * @returns {Promise<Object>} 字典数据对象
 */
export const getJobDictionary = async () => {
  // 检查缓存是否有效
  if (dictionaryCache && cacheTime && (Date.now() - cacheTime < CACHE_DURATION)) {
    return dictionaryCache
  }

  // 如果正在加载中，等待加载完成
  if (isLoading) {
    return new Promise((resolve) => {
      const checkCache = () => {
        if (dictionaryCache && !isLoading) {
          resolve(dictionaryCache)
        } else {
          setTimeout(checkCache, 100)
        }
      }
      checkCache()
    })
  }

  isLoading = true

  try {
    const response = await request({
      url: '/api/recruitment/job/getSelect',
      method: 'GET'
    })

    // 根据实际返回结构处理数据
    // response 结构: { code: 200, message: "", data: {...} }
    const data = response.data || response

    // 缓存数据
    dictionaryCache = data
    cacheTime = Date.now()

    return dictionaryCache
  } catch (error) {
    console.error('获取招聘字典数据失败:', error)
  } finally {
    isLoading = false
  }
}

/**
 * 根据字典类型和值获取显示文本
 * @param {string} type - 字典类型 (status, gender, employment_form, educational_require, certification_requirements)
 * @param {string|number} value - 字典值
 * @returns {Promise<string>} 显示文本
 */
export const getDictionaryText = async (type, value) => {
  if (!value) return ''
  const dictionary = await getJobDictionary()

  if (!dictionary[type]) {
    return String(value)
  }

  return dictionary[type][String(value)] || String(value)
}

/**
 * 根据角色ID获取角色信息
 * @param {string|number} roleId - 角色ID
 * @returns {Promise<Object|null>} 角色信息对象
 */
export const getRoleInfo = async (roleId) => {
  const dictionary = await getJobDictionary()

  if (!dictionary.roles || !Array.isArray(dictionary.roles)) {
    return null
  }

  return dictionary.roles.find(role => role.id == roleId) || null
}

/**
 * 获取持证要求文本列表
 * @param {string} certificationIds - 逗号分隔的持证要求ID字符串 (如: "1,2,3")
 * @returns {Promise<Array<string>>} 持证要求文本数组
 */
export const getCertificationTexts = async (certificationIds) => {
  if (!certificationIds) {
    return []
  }

  const dictionary = await getJobDictionary()
  const ids = certificationIds.split(',').map(id => id.trim())
  const texts = []

  for (const id of ids) {
    const text = dictionary.certification_requirements?.[id]
    if (text) {
      texts.push(text)
    }
  }

  return texts
}

/**
 * 初始化字典数据（应用启动时调用）
 */
export const initJobDictionary = async () => {
  try {
    await getJobDictionary()
    console.log('招聘字典数据初始化成功')
  } catch (error) {
    console.error('招聘字典数据初始化失败:', error)
  }
}

/**
 * 获取性别选项 (用于选择器)
 * @returns {Promise<Array>} 选择器格式的选项数组
 */
export const getGenderOptions = async () => {
  const dictionary = await getJobDictionary()
  if (!dictionary.gender) return []
  return Object.entries(dictionary.gender).map(([key, value]) => ({
    value: parseInt(key),
    label: value
  }))
}

/**
 * 获取学历选项 (用于选择器)
 * @returns {Promise<Array>} 选择器格式的选项数组
 */
export const getEducationOptions = async () => {
  const dictionary = await getJobDictionary()
  if (!dictionary.educational_require) return []
  return Object.entries(dictionary.educational_require).map(([key, value]) => ({
    value: parseInt(key),
    label: value
  }))
}

/**
 * 获取证书选项 (用于选择器)
 * @returns {Promise<Array>} 选择器格式的选项数组
 */
export const getCertificationOptions = async () => {
  const dictionary = await getJobDictionary()
  if (!dictionary.certification_requirements) return []
  return Object.entries(dictionary.certification_requirements).map(([key, value]) => ({
    value: parseInt(key),
    label: value
  }))
}

/**
 * 获取用工形式选项 (用于选择器)
 * @returns {Promise<Array>} 选择器格式的选项数组
 */
export const getEmploymentFormOptions = async () => {
  const dictionary = await getJobDictionary()
  if (!dictionary.employment_form) return []
  return Object.entries(dictionary.employment_form).map(([key, value]) => ({
    value: key,
    label: value
  }))
}

/**
 * 获取岗位角色选项 (用于选择器)
 * @returns {Promise<Array>} 选择器格式的选项数组
 */
export const getRoleOptions = async () => {
  const dictionary = await getJobDictionary()
  if (!dictionary.roles || !Array.isArray(dictionary.roles)) return []
  return dictionary.roles.map(role => ({
    value: String(role.id),
    label: role.title || role.name
  }))
}

/**
 * 获取求职状态选项 (用于选择器)
 * @returns {Array} 选择器格式的选项数组
 */
export const getJobStatusOptions = () => {
  return [
    { value: 1, label: '离职-随时到岗' },
    { value: 2, label: '在职-月内到岗' },
    { value: 3, label: '在职-考虑机会' }
  ]
}
