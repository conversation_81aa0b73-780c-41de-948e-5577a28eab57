<template>
    <view class="attachment-page">
        <u-navbar :is-back="true" title="附件简历管理" title-color="#262937" title-size="36" :border-bottom="false"
            @back="goBack">
        </u-navbar>

        <view class="content">
            <!-- 文件列表 -->
            <view v-if="fileList.length > 0" class="file-list">
                <view v-for="(file, index) in fileList" :key="file.id" class="file-item">
                    <view class="file-icon">
                        <u-icon name="file-text" color="#E53E3E" size="48"></u-icon>
                    </view>
                    <view class="file-info">
                        <view class="file-name">{{ file.name }}</view>
                        <view class="file-details">{{ file.size }} | 更新于{{ file.updateTime }}</view>
                    </view>
                    <view class="file-actions" @click="showActionSheet(index)">
                        <u-icon name="more-dot-fill" color="#BCC0CC" size="28"></u-icon>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-state">
                <u-icon name="folder" color="#BCC0CC" size="80"></u-icon>
                <text class="empty-text">暂无附件简历</text>
                <text class="empty-desc">点击下方上传按钮添加附件简历</text>
            </view>

            <!-- 上传区域 -->
            <view class="upload-area" @click="uploadFile">
                <view class="upload-icon">
                    <u-icon name="plus" color="#BCC0CC" size="48"></u-icon>
                </view>
                <view class="upload-text">点击或上传附件</view>
                <view class="upload-desc">支持多文件上传附件简历</view>
            </view>
        </view>

        <!-- 操作菜单 -->
        <u-action-sheet v-model="showActions" :list="actionList" @click="handleAction"></u-action-sheet>

        <!-- 重命名弹窗 -->
        <u-modal v-model="showRenameModal" title="重命名" :show-cancel-button="true" @confirm="confirmRename"
            @cancel="cancelRename">
            <view class="rename-content">
                <u-input v-model="newFileName" placeholder="请输入新的文件名" maxlength="50" :border="true" />
            </view>
        </u-modal>

        <!-- 删除确认弹窗 -->
        <u-modal v-model="showDeleteModal" title="删除确认" content="确定要删除这个文件吗？删除后无法恢复。" :show-cancel-button="true"
            @confirm="confirmDelete" @cancel="cancelDelete"></u-modal>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { storeToRefs } from 'pinia';
import { useResumeStore } from './stores/resumeStore';

const resumeStore = useResumeStore();
const { personalInfo } = storeToRefs(resumeStore);

const goBack = () => {
    uni.navigateBack();
};

// 文件列表数据
const fileList = ref([]);
const error = ref(null);

// 格式化文件大小
const formatFileSize = (sizeStr) => {
    if (!sizeStr) return '0KB';
    // 如果已经是格式化的字符串，直接返回
    if (typeof sizeStr === 'string' && sizeStr.includes('KB')) {
        return sizeStr;
    }
    // 如果是数字，转换为KB
    const size = parseInt(sizeStr);
    if (size < 1024) {
        return size + 'KB';
    } else {
        return (size / 1024).toFixed(1) + 'MB';
    }
};

// 获取附件简历列表
const fetchAttachmentList = async () => {
    try {
        uni.showLoading({ title: '加载中...' });
        error.value = null;

        const request = (await import('@/request.js')).default;
        const response = await request({
            url: '/api/recruitment/cv/getAttached',
            method: 'GET'
        });

        if (response) {
            // 转换API数据格式为页面需要的格式
            fileList.value = response.data.map(item => ({
                id: item.id,
                name: item.name, // 清理文件名
                size: formatFileSize(item.size),
                updateTime: item.updated_at,
                url: item.url,
                _raw: item // 保留原始数据
            }));
        } else {
            throw new Error(response.message || '获取附件列表失败');
        }

    } catch (err) {
        console.error('获取附件列表失败:', err);
        error.value = err.message || '获取附件列表失败';
        uni.showToast({
            title: error.value,
            icon: 'none'
        });
    } finally {
        uni.hideLoading();
    }
};


// 操作相关
const showActions = ref(false);
const currentFileIndex = ref(-1);
const actionList = ref([
    {
        text: '重命名',
        color: '#FF7373'
    },
    {
        text: '删除',
        color: '#E53E3E'
    }
]);

// 重命名相关
const showRenameModal = ref(false);
const newFileName = ref('');

// 删除相关
const showDeleteModal = ref(false);

// 显示操作菜单
const showActionSheet = (index) => {
    currentFileIndex.value = index;
    showActions.value = true;
};

// 处理操作菜单点击
const handleAction = (item) => {

    if (item === 0) {
        const currentFileName = fileList.value[currentFileIndex.value].name;
        console.log('当前文件名:', currentFileName);

        // 正确移除文件扩展名，支持各种文件类型
        const lastDotIndex = currentFileName.lastIndexOf('.');
        let nameWithoutExtension;

        if (lastDotIndex > 0) {
            // 有扩展名，移除扩展名
            nameWithoutExtension = currentFileName.substring(0, lastDotIndex);
        } else {
            // 没有扩展名，使用整个文件名
            nameWithoutExtension = currentFileName;
        }

        console.log('移除扩展名后:', nameWithoutExtension);
        newFileName.value = nameWithoutExtension;
        showRenameModal.value = true;
    } else if (item === 1) {
        showDeleteModal.value = true;
    }
    showActions.value = false;
};

// 重命名附件简历到服务器
const renameAttachmentOnServer = async (attachmentId, newName, url, size) => {
    try {
        const request = (await import('@/request.js')).default;
        const response = await request({
            url: '/api/recruitment/cv/saveAttached',
            method: 'POST',
            data: {
                id: attachmentId,
                cv_id: personalInfo.value.id,
                name: newName,
                url: url,
                size: size
            }
        });

        return response;
    } catch (error) {
        console.error('重命名附件简历失败:', error);
        throw error;
    }
};

// 验证文件名
const validateFileName = (fileName) => {
    if (!fileName || !fileName.trim()) {
        return '请输入文件名';
    }

    const trimmedName = fileName.trim();

    if (trimmedName.length > 50) {
        return '文件名长度不能超过50个字符';
    }

    // 检查是否包含非法字符
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(trimmedName)) {
        return '文件名不能包含特殊字符 < > : " / \\ | ? *';
    }

    return null;
};

// 确认重命名
const confirmRename = async () => {
    const validationError = validateFileName(newFileName.value);
    if (validationError) {
        uni.showToast({
            title: validationError,
            icon: 'none'
        });
        return;
    }

    const currentFile = fileList.value[currentFileIndex.value];
    console.log('当前文件对象:', currentFile);

    // 正确提取扩展名
    const lastDotIndex = currentFile.name.lastIndexOf('.');
    const originalExtension = lastDotIndex > 0 ? currentFile.name.substring(lastDotIndex) : '';
    console.log('从文件名提取的扩展名:', originalExtension);

    // 如果没有扩展名，尝试从URL中获取
    let finalExtension = originalExtension;
    if (!finalExtension && currentFile.url) {
        console.log('文件URL:', currentFile.url);
        const urlLastDotIndex = currentFile.url.lastIndexOf('.');
        console.log('URL中最后一个点的位置:', urlLastDotIndex);
        if (urlLastDotIndex > 0) {
            const urlExtension = currentFile.url.substring(urlLastDotIndex);
            console.log('从URL提取的原始扩展名:', urlExtension);
            // 只取扩展名部分，去掉可能的查询参数
            finalExtension = urlExtension.split('?')[0];
            console.log('处理后的URL扩展名:', finalExtension);
        }
    }

    // 如果还是没有扩展名，默认使用.pdf
    if (!finalExtension) {
        finalExtension = '.pdf';
        console.log('使用默认扩展名:', finalExtension);
    }

    console.log('最终使用的扩展名:', finalExtension);

    const newFullName = newFileName.value.trim() + finalExtension;
    console.log('新的完整文件名:', newFullName);
    console.log('即将发送的参数:', {
        id: currentFile.id,
        name: newFullName,
        url: currentFile.url,
        size: currentFile._raw.size
    });

    try {
        uni.showLoading({ title: '重命名中...' });

        // 调用API重命名
        await renameAttachmentOnServer(
            currentFile.id,
            newFullName,
            currentFile.url,
            currentFile._raw.size, // 使用原始数据中的size
            '1' // cv_id，默认为1
        );

        // 更新本地数据
        fileList.value[currentFileIndex.value].name = newFullName;
        fileList.value[currentFileIndex.value].updateTime = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).replace(/\//g, '.').replace(/,/g, '');

        uni.hideLoading();
        uni.showToast({
            title: '重命名成功',
            icon: 'success'
        });

    } catch (error) {
        uni.hideLoading();
        console.error('重命名失败:', error);
        uni.showToast({
            title: '重命名失败',
            icon: 'error'
        });
    }

    showRenameModal.value = false;
    newFileName.value = '';
};

// 取消重命名
const cancelRename = () => {
    showRenameModal.value = false;
    newFileName.value = '';
};

// 删除附件简历
const deleteAttachmentFromServer = async (attachmentId) => {
    try {
        const request = (await import('@/request.js')).default;
        const response = await request({
            url: '/api/recruitment/cv/delAttached',
            method: 'GET',
            data: {
                id: attachmentId
            }
        });

        return response;
    } catch (error) {
        console.error('删除附件简历失败:', error);
        throw error;
    }
};

// 确认删除
const confirmDelete = async () => {
    try {
        uni.showLoading({ title: '删除中...' });

        const currentFile = fileList.value[currentFileIndex.value];
        if (currentFile && currentFile._raw && currentFile._raw.id) {
            // 调用API删除
            await deleteAttachmentFromServer(currentFile._raw.id);

            // 重新获取附件列表
            await fetchAttachmentList();
        } else {
            // 如果没有ID，直接从本地列表删除（用于新上传但未保存的文件）
            fileList.value.splice(currentFileIndex.value, 1);
        }

        showDeleteModal.value = false;
        uni.hideLoading();
        uni.showToast({
            title: '删除成功',
            icon: 'success'
        });

    } catch (error) {
        uni.hideLoading();
        console.error('删除失败:', error);
        uni.showToast({
            title: '删除失败',
            icon: 'error'
        });
        showDeleteModal.value = false;
    }
};

// 取消删除
const cancelDelete = () => {
    showDeleteModal.value = false;
};

// 保存附件简历到服务器
const saveAttachmentToServer = async (name, url, size) => {
    try {
        const request = (await import('@/request.js')).default;
        const response = await request({
            url: '/api/recruitment/cv/saveAttached',
            method: 'POST',
            data: {
                cv_id: personalInfo.value.id,
                name: name,
                url: url,
                size: size
            }
        });

        return response;
    } catch (error) {
        console.error('保存附件简历失败:', error);
        throw error;
    }
};

// 上传文件
const uploadFile = () => {
    uni.chooseFile({
        count: 5,
        type: 'file',
        extension: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
        success: async (res) => {
            if (res.tempFiles && res.tempFiles.length > 0) {
                uni.showLoading({ title: '上传中...' });

                try {
                    const uploadPromises = res.tempFiles.map(async (file) => {
                        // 获取文件信息
                        const fileName = file.name;
                        const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);
                        const fileSize = Math.round(file.size / 1024); // 转换为KB

                        // 上传到OBS
                        const uploadedUrl = await OBSupload(file.path, fileExtension, fileName);

                        // 保存到服务器
                        await saveAttachmentToServer(fileName, uploadedUrl, fileSize.toString());

                        return {
                            name: fileName,
                            url: uploadedUrl,
                            size: fileSize
                        };
                    });

                    // 等待所有文件上传完成
                    await Promise.all(uploadPromises);

                    // 重新获取附件列表
                    await fetchAttachmentList();

                    uni.hideLoading();
                    uni.showToast({
                        title: '上传成功',
                        icon: 'success'
                    });

                } catch (error) {
                    uni.hideLoading();
                    console.error('上传失败:', error);
                    uni.showToast({
                        title: '上传失败',
                        icon: 'error'
                    });
                }
            }
        },
        fail: (err) => {
            console.log('选择文件失败:', err);
            uni.showToast({
                title: '选择文件失败',
                icon: 'none'
            });
        }
    });
};

onLoad(() => {
    // 页面加载时获取附件列表
    fetchAttachmentList();
});
</script>

<style lang="scss" scoped>
.attachment-page {
    background: #F6F6F6;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
    padding: 20rpx;
}

.file-list {
    padding-bottom: 40rpx;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
}

.file-icon {
    margin-right: 24rpx;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 32rpx;
    color: #262937;
    font-weight: 500;
    margin-bottom: 8rpx;
}

.file-details {
    font-size: 24rpx;
    color: #858997;
}

.file-actions {
    padding: 10rpx;
}

.upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 40rpx;
    background: #fff;
    border-radius: 16rpx;
    border: 2rpx dashed #E0E0E0;
}

.upload-icon {
    margin-bottom: 24rpx;
}

.upload-text {
    font-size: 32rpx;
    color: #262937;
    margin-bottom: 12rpx;
}

.upload-desc {
    font-size: 24rpx;
    color: #858997;
}

.rename-content {
    padding: 32rpx;
}

.rename-tips {
    display: flex;
    align-items: flex-start;
    gap: 12rpx;
    padding: 24rpx;
    background-color: #FFF7ED;
    border-radius: 12rpx;
    border-left: 6rpx solid #FF7373;

    text {
        font-size: 24rpx;
        color: #666666;
        line-height: 1.5;
        flex: 1;
    }
}

.rename-tips {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #858997;
    line-height: 1.4;
}

// 空状态样式
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
}

.empty-text {
    margin-top: 20rpx;
    font-size: 32rpx;
    color: #858997;
}

.empty-desc {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #BCC0CC;
}
</style>
