<template>
  <view class="my-jobs-page">
    <u-navbar :is-back="true" title="我的求职" title-color="#262937" title-size="36" :border-bottom="false">
    </u-navbar>

    <view class="tabs-container">
      <u-tabs :list="tabList" :is-scroll="false" v-model="currentTab" @change="handleTabChange" active-color="#FF7373"
        inactive-color="#999999"></u-tabs>
      <!-- Tab切换loading条 -->
      <view v-if="isTabSwitching" class="tab-loading-bar">
        <view class="loading-progress"></view>
      </view>
    </view>

    <scroll-view scroll-y class="scroll-view-content" @scrolltolower="loadMore">
      <view class="content">
        <view v-if="isTabSwitching" class="loading-state">
          <u-loading mode="circle" size="50"></u-loading>
          <text class="loading-text">切换中...</text>
        </view>
        <view v-else-if="jobApplications.length === 0" class="empty-state">
          <u-empty text="暂无投递记录" mode="list"></u-empty>
        </view>
        <view v-else>
          <view v-for="item in jobApplications" :key="item.id" class="card" @click="handleCardClick(item)">
            <view class="card-header">
              <text class="job-title">{{ item.job_name }}</text>
              <text v-if="item.status !== undefined && item.status !== null"
                :class="['status', getStatusClass(item.status)]">{{ getStatusText(item.status, item.reply_content)
                }}</text>
            </view>
            <view class="card-body">
              <text class="company-name">{{ item.school_name }}</text>
              <text class="info-text">{{ item.timeLabel }}：{{ item.timeValue }}</text>
              <text v-if="item.content" class="content-text">留言：{{ item.content }}</text>
            </view>
          </view>

          <!-- 加载更多 -->
          <view v-if="pagination.current_page < pagination.last_page" class="load-more">
            <view v-if="isLoading" class="loading-more">
              <u-loading mode="circle" size="30"></u-loading>
              <text>加载中...</text>
            </view>
            <view v-else class="load-more-btn" @click="loadMore">
              <text>加载更多</text>
            </view>
          </view>

          <view v-else-if="jobApplications.length > 0" class="no-more">
            <text>没有更多数据了</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 在线沟通弹窗 -->
    <u-popup v-model="showChatModal" @close="closeChatModal" mode="center" border-radius="20" width="600rpx">
      <view class="chat-popup-content">
        <!-- 弹窗头部 -->
        <view class="chat-popup-header">
          <text class="chat-popup-title">在线沟通</text>
          <view class="chat-popup-close" @click="closeChatModal">
            <text>✕</text>
          </view>
        </view>

        <!-- 职位信息 -->
        <view v-if="currentChatItem" class="chat-job-info">
          <text class="chat-job-title">{{ currentChatItem.job_name }}</text>
          <text class="chat-company-name">{{ currentChatItem.school_name }}</text>
        </view>

        <!-- 消息记录 -->
        <view class="chat-messages-container">
          <view v-if="!currentChatItem.ask_content && !currentChatItem.reply_content" class="chat-empty">
            <text>暂无消息记录</text>
          </view>
          <view v-else class="chat-messages">
            <!-- 显示提问内容 -->
            <view v-if="currentChatItem.ask_content" class="chat-message-item">
              <view class="message-content user-message">
                <text>{{ currentChatItem.ask_content }}</text>
              </view>
            </view>
            <!-- 显示回复内容 -->
            <view v-if="currentChatItem.reply_content" class="chat-message-item">
              <view class="message-content reply-message">
                <text>{{ currentChatItem.reply_content }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 消息输入 -->
        <view class="chat-input-wrapper">
          <textarea v-model="chatMessage" class="chat-input" placeholder="请输入想要咨询的问题.." />
        </view>

        <!-- 弹窗底部按钮 -->
        <view class="chat-popup-footer">
          <view class="chat-popup-btn cancel" @click="closeChatModal">
            <text>取消</text>
          </view>
          <view class="chat-popup-btn confirm" @click="sendChatMessage">
            <text>发送</text>
          </view>
        </view>
      </view>
    </u-popup>

  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import request from '@/request.js';
import { useResumeStore } from './stores/resumeStore';
import { storeToRefs } from 'pinia';

const resumeStore = useResumeStore();
const { countsRaw } = storeToRefs(resumeStore);

const currentTab = ref(0);
const isLoading = ref(false);
const isTabSwitching = ref(false); // 专门用于tab切换的loading状态

// 聊天相关状态
const showChatModal = ref(false);
const chatMessage = ref('');
const currentChatItem = ref(null);

const tabList = reactive([
  { name: '在线沟通', count: 0, status: 0 },
  { name: '已投简历', count: 0, status: 1 },
  { name: '待面试', count: 0, status: 2 },
  { name: '已面试', count: 0, status: 3 },
]);

const jobApplications = reactive([]);
const pagination = reactive({
  current_page: 1,
  last_page: 1,
  total: 0,
  per_page: 10
});

// 加载个人信息并更新tab计数
const loadPersonalInfo = async () => {
  try {
    await resumeStore.fetchPersonalInfo();
    // 更新tab计数
    tabList[0].count = countsRaw.value.communicate_count || 0;
    tabList[1].count = countsRaw.value.delivery_count || 0;
    tabList[2].count = countsRaw.value.waiting_interview_count || 0;
    tabList[3].count = countsRaw.value.already_interview_count || 0;
  } catch (error) {
    console.error('获取个人信息失败:', error);
  }
};

// 获取求职列表数据
const fetchJobApplications = async (page = 1) => {
  try {
    // 只有在加载更多时才设置isLoading，tab切换时使用isTabSwitching
    if (page > 1) {
      isLoading.value = true;
    }

    // 获取当前tab的status
    const status = tabList[currentTab.value].status;

    // 调用统一接口，传入status参数
    const response = await request({
      url: '/api/recruitment/cv/getDelivery',
      method: 'GET',
      data: {
        page,
        per_page: pagination.per_page,
        status // 传入status参数: 0=在线沟通,1=已投简历,2=待面试,3=已面试
      }
    });

    if (response && response.data) {
      // 更新分页信息
      pagination.current_page = response.current_page || response.data.current_page || 1;
      pagination.last_page = response.last_page || response.data.last_page || 1;
      pagination.total = response.total || response.data.total || 0;
      pagination.per_page = response.per_page || response.data.per_page || 10;

      // 获取数据数组
      const dataList = response.data.data || response.data || [];

      // 根据不同的status设置不同的timeLabel
      const timeLabels = {
        0: '沟通时间',
        1: '投递时间',
        2: '面试时间',
        3: '面试时间'
      };

      // 转换数据格式
      const formattedData = dataList.map(item => ({
        ...item,
        timeLabel: timeLabels[status],
        timeValue: item.updated_at || item.interview_time || item.created_at
      }));

      // 如果是第一页，替换数据；否则追加数据
      if (page === 1) {
        jobApplications.splice(0, jobApplications.length, ...formattedData);
      } else {
        jobApplications.push(...formattedData);
      }
    } else {
      // 如果接口无数据，清空列表
      if (page === 1) {
        jobApplications.splice(0, jobApplications.length);
      }
    }
  } catch (error) {
    console.error('获取求职列表失败:', error);
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
    // 如果接口失败且是第一页，清空列表
    if (page === 1) {
      jobApplications.splice(0, jobApplications.length);
    }
  } finally {
    // 只有在加载更多时才重置isLoading
    if (page > 1) {
      isLoading.value = false;
    }
  }
};

// tab切换处理
const handleTabChange = async (index) => {
  if (isTabSwitching.value) return; // 防止重复点击

  currentTab.value = index;
  isTabSwitching.value = true;

  try {
    // 重置分页信息
    pagination.current_page = 1;
    pagination.last_page = 1;
    // 重新获取数据
    await fetchJobApplications(1);
  } finally {
    isTabSwitching.value = false;
  }
};

// 加载更多数据
const loadMore = () => {
  // 如果正在加载或已经到最后一页，则不加载
  if (isLoading.value || pagination.current_page >= pagination.last_page) {
    return;
  }
  fetchJobApplications(pagination.current_page + 1);
};

// TODO 更改已回复，未回复样式
// 状态样式类
const getStatusClass = (status) => {
  // 根据面试状态返回对应的样式类
  const statusMap = {
    0: 'status-default',      // 未邀约
    1: 'status-invited',      // 邀约面试
    2: 'status-cancelled',    // 取消面试
    3: 'status-absent',       // 未参加面试
    4: 'status-passed',       // 面试通过
    5: 'status-failed',       // 面试未通过
    6: 'status-trial',        // 试用
    7: 'status-abandoned',    // 放弃入职
    8: 'status-trial-failed', // 试用未通过
    9: 'status-employed',     // 正式入职
    10: 'status-not-interested' // 不感兴趣
  };
  return statusMap[status] || 'status-default';
};

// 获取状态文本
const getStatusText = (status, reply_content) => {
  if (currentTab.value === 0) {
    return reply_content ? '已回复' : '未回复';
  }
  const statusTextMap = {
    0: '未邀约',
    1: '邀约面试',
    2: '取消面试',
    3: '未参加面试',
    4: '面试通过',
    5: '面试未通过',
    6: '试用',
    7: '放弃入职',
    8: '试用未通过',
    9: '正式入职',
    10: '不感兴趣'
  };
  return statusTextMap[status] || '未知状态';
};

// 处理卡片点击
// 处理卡片点击
const handleCardClick = (item) => {
  if (currentTab.value === 0) {
    // 在线沟通tab，打开聊天弹窗
    openChatModal(item);
  } else {
    // 其他tab跳转到职位详情
    viewJobDetail(item);
  }
};

// 查看职位详情
const viewJobDetail = (item) => {
  const jobId = item.job_id || item.id;
  uni.navigateTo({
    url: `/pages/job/detail?id=${jobId}`
  });
};

// 打开聊天弹窗
const openChatModal = (item) => {
  currentChatItem.value = item;
  showChatModal.value = true;
  chatMessage.value = '';
};

// 获取消息记录
// 发送聊天消息
const sendChatMessage = async () => {
  if (!chatMessage.value.trim()) {
    uni.showToast({ title: '请输入问题', icon: 'none' });
    return;
  }

  if (!currentChatItem.value) {
    uni.showToast({ title: '会话信息错误', icon: 'none' });
    return;
  }

  try {
    uni.showLoading({ title: '发送中...' });

    // 准备API参数
    const params = {
      job_id: currentChatItem.value.job_id,
      ask_content: chatMessage.value.trim(),
      info_type: '1', // 1=文本
      cv_id: currentChatItem.value.cv_id || personalInfoRaw.value?.id
    };

    // 如果有school_id，添加到参数中
    if (currentChatItem.value.school_id) {
      params.school_id = currentChatItem.value.school_id;
    }

    await request({
      url: '/api/recruitment/cv/saveDelivery',
      method: 'POST',
      data: params
    });

    uni.hideLoading();
    uni.showToast({ title: '发送成功', icon: 'success' });

    // 清空输入框并关闭弹窗
    chatMessage.value = '';
    showChatModal.value = false;

    // 重新加载列表数据以获取最新状态
    await fetchJobApplications(1);

  } catch (error) {
    uni.hideLoading();
    console.error('发送消息失败:', error);
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none'
    });
  }
};

// 关闭聊天弹窗
const closeChatModal = () => {
  showChatModal.value = false;
  currentChatItem.value = {
    ask_content: '',
    reply_content: ''
  };
};

onMounted(async () => {
  // 先加载个人信息获取计数
  await loadPersonalInfo();
  // 再加载求职列表
  fetchJobApplications(1);
});
</script>

<style lang="scss" scoped>
.my-jobs-page {
  background: #F6F6F6;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.tabs-container {
  background: #fff;
  position: relative;
}

.tab-loading-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #f0f0f0;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #FF7373, #FF9999);
  width: 30%;
  animation: loading-slide 1.5s ease-in-out infinite;
}

@keyframes loading-slide {
  0% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(250%);
  }

  100% {
    transform: translateX(-100%);
  }
}

.scroll-view-content {
  flex: 1;
  overflow-y: auto;
}

.content {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.job-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

// 状态样式
.status-default {
  color: #999;
}

.status-invited {
  color: #2196F3; // 蓝色 - 邀约面试
}

.status-cancelled {
  color: #9E9E9E; // 灰色 - 取消面试
}

.status-absent {
  color: #FF9800; // 橙色 - 未参加面试
}

.status-passed {
  color: #4CAF50; // 绿色 - 面试通过
}

.status-failed {
  color: #F44336; // 红色 - 面试未通过
}

.status-trial {
  color: #00BCD4; // 青色 - 试用
}

.status-abandoned {
  color: #795548; // 棕色 - 放弃入职
}

.status-trial-failed {
  color: #E91E63; // 粉红色 - 试用未通过
}

.status-employed {
  color: #8BC34A; // 浅绿色 - 正式入职
}

.status-not-interested {
  color: #607D8B; // 蓝灰色 - 不感兴趣
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 8rpx;

  .company-name {
    font-size: 26rpx;
    color: #666;
    font-weight: 500;
  }

  .info-text {
    font-size: 24rpx;
    color: #999;
  }

  .content-text {
    font-size: 24rpx;
    color: #666;
    background: #f5f5f5;
    padding: 16rpx;
    border-radius: 8rpx;
    margin-top: 8rpx;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-text {
    margin-top: 20rpx;
    color: #999;
    font-size: 28rpx;
  }
}

.empty-state {
  padding-top: 100rpx;
}

.load-more {
  padding: 40rpx 0;
  text-align: center;

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    color: #999;
    font-size: 28rpx;
  }

  .load-more-btn {
    color: #FF7373;
    font-size: 28rpx;
    padding: 20rpx;
  }
}

.no-more {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

// 聊天弹窗样式
.chat-popup-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.chat-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .chat-popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .chat-popup-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f5f5f5;

    text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.chat-job-info {
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .chat-job-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
  }

  .chat-company-name {
    font-size: 24rpx;
    color: #666;
  }
}

.chat-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 24rpx;
}

.chat-empty {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 24rpx;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.chat-message-item {
  .message-content {
    background: #f5f5f5;
    padding: 16rpx;
    border-radius: 8rpx;
    margin-bottom: 12rpx;

    text {
      font-size: 24rpx;
      color: #333;
      line-height: 1.4;
    }

    &.user-message {
      background: #e3f2fd;
      border-left: 4rpx solid #2196f3;

      &::before {
        content: "我的提问：";
        font-size: 20rpx;
        color: #2196f3;
        font-weight: bold;
        display: block;
        margin-bottom: 8rpx;
      }
    }

    &.reply-message {
      background: #f3e5f5;
      border-left: 4rpx solid #9c27b0;

      &::before {
        content: "机构回复：";
        font-size: 20rpx;
        color: #9c27b0;
        font-weight: bold;
        display: block;
        margin-bottom: 8rpx;
      }
    }
  }

  .message-resume {
    display: flex;
    align-items: center;
    gap: 12rpx;
    background: #fff5f5;
    padding: 16rpx;
    border-radius: 8rpx;
    border: 1rpx solid #ffe0e0;

    text {
      font-size: 24rpx;
      color: #FF7373;
    }
  }
}

.chat-input-wrapper {
  padding: 20rpx 24rpx 16rpx;
  border-top: 1rpx solid #f0f0f0;

  .chat-input {
    width: auto;
    height: 120rpx;
    padding: 16rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    font-size: 24rpx;
    line-height: 1.4;
    resize: none;
    background: #fafafa;
  }
}

.chat-popup-footer {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;

  .chat-popup-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    font-size: 28rpx;

    &.cancel {
      background: #f5f5f5;
      color: #666;
    }

    &.confirm {
      background: #FF7373;
      color: #fff;
    }
  }
}
</style>