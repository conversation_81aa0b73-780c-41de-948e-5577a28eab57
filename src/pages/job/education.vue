<template>
    <view class="education-form-page">
        <u-navbar :is-back="true" :title="pageTitle" title-color="#262937" title-size="36" :border-bottom="false"
            @back="goBack">
        </u-navbar>

        <view class="scroll-view-content">
            <view class="content">
                <u-form :model="educationData" ref="formRef" label-position="left" label-width="200"
                    :errorType="['toast']">
                    <u-form-item label="院校名称" prop="school_name">
                        <u-input v-model="educationData.school_name" type="text" :border="false" input-align="right"
                            placeholder="请输入院校名称" />
                    </u-form-item>
                    <u-form-item label="学历" prop="educational_require" right-icon="arrow-right">
                        <SelectPicker v-model="educationData.educational_require" :options="degreeOptions"
                            placeholder="请选择学历" @change="onDegreeChange" />
                    </u-form-item>
                    <u-form-item label="专业" prop="major_name">
                        <u-input v-model="educationData.major_name" type="text" :border="false" input-align="right"
                            placeholder="请输入专业名称" maxlength="50" />
                    </u-form-item>
                    <u-form-item label="在校时间段" prop="duration" right-icon="arrow-right">
                        <DatePicker v-model="durationData" mode="duration" placeholder="请选择在校时间段"
                            duration-title="选择在校时间段" @change="onDurationChange" />
                    </u-form-item>
                    <u-form-item label="在校经历" prop="school_experience" :border-bottom="false">
                        <u-input v-model="educationData.school_experience" type="textarea" height="120rpx"
                            :auto-height="true" :border="false" input-align="right" placeholder="请输入在校经历"
                            maxlength="500" />
                    </u-form-item>
                </u-form>
            </view>
        </view>


        <!-- Footer Buttons -->
        <BottomButton :buttons="getFooterButtons()" @click="onBottomButtonClick" />
    </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
import { useResumeStore } from './stores/resumeStore';
import { getEducationOptions } from './utils/jobDictionary.js';
import DatePicker from './components/DatePicker.vue';
import BottomButton from './components/BottomButton.vue';
import SelectPicker from './components/SelectPicker.vue';

// Page mode and data
const pageMode = ref('add'); // 'add' or 'edit'
const educationId = ref(null);

// Computed page title
const pageTitle = computed(() => {
    return pageMode.value === 'edit' ? '编辑教育经历' : '添加教育经历';
});

const goBack = () => {
    uni.navigateBack();
};

const formRef = ref(null);

// Form data
const educationData = reactive({
    id: null,
    cv_id: '',
    school_name: '',
    educational_require: '',
    major_name: '',
    school_online_start: '',
    school_online_end: '',
    school_experience: ''
});

// Validation rules
const rules = {
    school_name: [{ required: true, message: '请输入院校名称', trigger: ['blur', 'change'] }],
    educational_require: [{
        validator: (rule, value) => {
            return value !== '';
        }, message: '请选择学历', trigger: ['blur', 'change']
    }],
    major_name: [{ required: true, message: '请选择专业', trigger: ['blur', 'change'] }],
    school_online_start: [{ required: true, message: '请选择开始时间', trigger: ['blur', 'change'] }],
    school_online_end: [{ required: true, message: '请选择结束时间', trigger: ['blur', 'change'] }],
    school_experience: [{ required: true, message: '请输入在校经历', trigger: ['blur', 'change'] }],
};

// Options
const degreeOptions = ref([]);
const durationData = ref({});

const onDegreeChange = (value) => {
    educationData.educational_require = value;
};

const onDurationChange = (duration) => {
    if (duration && duration.startDate && duration.endDate) {
        educationData.school_online_start = duration.startDate;
        educationData.school_online_end = duration.endDate;
    }
};

const getFooterButtons = () => {
    if (pageMode.value === 'add') {
        return [
            { type: 'cancel', text: '取消' },
            { type: 'save', text: '保存' }
        ];
    } else {
        return [
            { type: 'delete', text: '删除' },
            { type: 'save', text: '保存' }
        ];
    }
};

const onBottomButtonClick = (type) => {
    switch (type) {
        case 'cancel':
            cancel();
            break;
        case 'delete':
            deleteEducation();
            break;
        case 'save':
            save();
            break;
    }
};

// Utility functions
// const formatDuration = () => {
//     // 已由DatePicker组件处理
// };

const resumeStore = useResumeStore();

// Load education data for editing
const loadEducationData = (id) => {
    // 使用store方法获取原始教育经历数据
    const rawData = resumeStore.getEducationRawById(id);

    if (rawData) {
        Object.keys(educationData).forEach(key => {
            educationData[key] = rawData[key] || ''
        })

        // 设置日期数据
        durationData.value = {
            startDate: rawData.school_online_start || '',
            endDate: rawData.school_online_end || ''
        };
    }
};

// Actions
const cancel = () => {
    uni.navigateBack();
};

const save = async () => {
    console.log('educationData', educationData)
    formRef.value.validate(async (valid) => {
        if (valid) {
            if (!educationData.school_online_start && !educationData.school_online_end) {
                uni.showToast({
                    title: '请选择在校时间段',
                    icon: 'none'
                })
                return
            }
            try {
                // 显示加载提示
                uni.showLoading({
                    title: '保存中...'
                });

                // 准备保存数据 - 直接使用接口字段名称
                const saveData = { ...educationData };
                if (pageMode.value === 'add') {
                    delete saveData.id
                    saveData.cv_id = resumeStore.personalInfo.id
                }
                // 调用API保存教育经历
                await resumeStore.saveEducationExperience(saveData);

                // 保存成功后重新加载教育经历数据，确保数据一致性
                await resumeStore.fetchEducationHistory();

                uni.hideLoading();
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);

            } catch (error) {
                uni.hideLoading();
                console.error('保存教育经历失败:', error);
            }
        } else {
            console.log('验证失败');
        }
    });
};

// 删除教育经历
const deleteEducation = () => {
    uni.showModal({
        title: '删除确认',
        content: '确定要删除这条教育经历吗？',
        success: async (res) => {
            if (res.confirm) {
                try {
                    // 显示加载提示
                    uni.showLoading({
                        title: '删除中...'
                    });

                    // 调用API删除教育经历
                    await resumeStore.deleteEducationExperience(educationId.value);

                    // 删除成功后重新加载教育经历数据，确保数据一致性
                    await resumeStore.fetchEducationHistory();

                    uni.hideLoading();
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });

                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);

                } catch (error) {
                    uni.hideLoading();
                    console.error('删除教育经历失败:', error);
                    uni.showToast({
                        title: '删除失败',
                        icon: 'error'
                    });
                }
            }
        }
    });
};

// Lifecycle
onLoad((options) => {
    // Set page mode and education ID from URL parameters
    if (options.mode) {
        pageMode.value = options.mode;
    }
    if (options.educationId) {
        educationId.value = options.educationId;
        loadEducationData(options.educationId);
    }

    // Initialize component data
    durationData.value = {
        startDate: educationData.school_online_start,
        endDate: educationData.school_online_end
    };

    // 初始化字典选项
    initOptions();
});

// 初始化选项数据
const initOptions = async () => {
    try {
        const educationOpts = await getEducationOptions();
        degreeOptions.value = educationOpts;
    } catch (error) {
        console.error('初始化学历选项失败:', error);
        // 不再降级处理
        degreeOptions.value = [];
    }
};

onReady(() => {
    formRef.value.setRules(rules);
});
</script>

<style lang="scss" scoped>
.education-form-page {
    background: #F6F6F6;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.scroll-view-content {
    flex: 1;
    overflow-y: scroll;
}

.content {
    margin: 20rpx;
    padding: 0 32rpx;
    background: #fff;
    border-radius: 16rpx;
}

.picker-value {
    text-align: right;
    width: 100%;
    color: #262937;
    font-size: 28rpx;
}

:deep(uni-picker) {
    width: 100%;
}

.picker-value.placeholder {
    color: #c0c4cc;
}
</style>
