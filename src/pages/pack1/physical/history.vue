<template>
  <view class="full-loading" v-if="loading"><u-loading size="48"></u-loading></view>
  <view class="pages" v-else>
    <u-navbar :border-bottom="false" :custom-back="backFun" :background="{ backgroundColor: '#F1F5F8' }" title="预约成功">
    </u-navbar>
    <view class="section">
      <view class="topstatus"
        :class="{ inactive: info.status == 2 || info.status == 3, activ: info.status == 1 || info.status == 4 }">

      </view>
      <view class="top">
        <image v-if="info.status == 0" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/success.png"
          class="icon">
        </image>
        <image v-else-if="info.status == 1 || info.status == 4"
          src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/suasdas.png" class="icon">
        </image>
        <image v-else src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/yqyuquy.png" class="icon">
        </image>
        <view class="in">
          <view class="inl">
            <view class="t1">
              {{ info.status == 0 ? '预约成功' :
      info.status == 1 ? '已检查' :
        info.status == 2 ? '已取消' : info.status == 3 ?
          '已过期' :
          '已签到'
              }}
            </view>
            <view class="t2" v-if="info.status == 0">
              待检查
            </view>
          </view>
          <view class="msg">
            {{ info.status == 0 ? '如无法按照预约时间前往，请提前取消预约' :
      info.status == 1 ? '已检查' :
        info.status == 2 ? '取消成功' : ""
            }}
          </view>
        </view>
      </view>
      <view class="content">
        <view class="code">
          {{ info.code }}
        </view>
        <view class="msg">
          预约码
        </view>
        <view class="row">
          <view class="l1">
            体检人
          </view>
          <view class="l2">
            {{ info.bm_student.name }}
          </view>
        </view>
        <view class="row">
          <view class="l1">
            预约体检时间
          </view>
          <view class="l2">
            {{ info.order_time }} {{ info.order_period.order_period }}
          </view>
        </view>
        <view class="address" @click="goDaohang">
          <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
          </image>
          <view class="add-content">
            <view class="lefec">
              <view class="t1">{{ info.hospital.name }}</view>
              <text class="distance">{{ info.hospital.address }}</text>
            </view>
            <view class="rsec">
              <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
              </image>
            </view>
          </view>
        </view>
        <!-- <view class="tips">
          有效期至2023年12月31日
        </view> -->
      </view>
    </view>
    <view class="notes">
      <text class="notee">
        温馨提示: \n
        1.请按照预约时间前往门诊。\n
        2.如无法按预约时间前往，请提前至少半小时取消。\n
        3.体检时请携带有效身份证件、社保卡前往门诊。\n</text>
    </view>
    <template v-if="info.status == 0 || info.status == 1 || info.status == 2">
      <view class="footer-zhanwei"></view>
      <view class="footer">
        <view class="footer-content">
          <template v-if="info.status == 0">
            <view class="btn btn2" @click="cel">取消预约</view>
            <view class="btn btn3" @click="goCha" v-if="info.is_sign == 1">查看既往史</view>
            <view class="btn btn3" @click="goAddT" v-else>填写既往史</view>
          </template>
          <template v-if="info.status == 1">
            <view class="btn" @click="goCha" v-if="info.is_sign == 1">查看既往史</view>
            <view class="btn" @click="goAddT" v-else>填写既往史</view>
          </template>
          <view class="btn" v-if="info.status == 2" @click="cel">删除预约</view>
        </view>
      </view>
    </template>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad, onUnload } from '@dcloudio/uni-app';
import request from "@/request.js";
let id = '';
const info = ref({
  bm_student_id: 13,
  code: "48369517",
  id: 5,
  bm_student: {
    name: "",
  },
  hospital: {
    name: "",
    address: "",
    lat: "",
    lng: ""
  },
  order_period: {
    order_period: ""
  },
  is_sign: 0
})
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.hospital.lat),
    longitude: Number(info.value.hospital.lng),
    name: info.value.hospital.name,
    address: info.value.hospital.address,
    success: function () {
      console.log('success');
    }
  });
}
let type = 1;
onLoad((option) => {
  id = option.id;
  type = option.type || 1;
  getData();
  uni.$on("physical_sign", () => {
    getData();
  })
})
onUnload(() => {
  uni.$off("physical_sign")
})
const loading = ref(true);
function getData() {
  uni.showLoading()
  loading.value = true;
  request({
    url: "/api/healthservices/examination/detail",
    data: {
      id
    }
  }).then(res => {
    info.value = res;
    loading.value = false;
    uni.hideLoading();
  })
}
function goAddT() {
  uni.navigateTo({
    url: `./sign?id=${info.value.id}`,
  });
}
function goCha() {
  uni.setStorageSync("phy_sign_ifno", info.value)
  uni.navigateTo({
    url: `./sign?id=${info.value.id}&isS=1`,
  });
}
function cel() {
  if (info.value.status == 0) {
    uni.showModal({
      title: "确定取消预约？",
      success: function (res) {
        if (res.confirm) {
          request({
            url: '/api/healthservices/examination/cancel',
            method: 'post',
            data: {
              id
            }
          }).then(res => {
            uni.showToast({
              title: "取消成功",
              icon: "none"
            })
            getData();
          })
        }
      }
    })
  } else {
    uni.showModal({
      title: "确定删除预约？",
      success: function (res) {
        if (res.confirm) {
          request({
            url: '/api/healthservices/examination/delete',
            method: 'post',
            data: {
              id
            }
          }).then(res => {
            uni.showToast({
              title: "删除成功",
              icon: "none"
            })
            uni.navigateBack();
          })
        }
      }
    })
  }
}
function backFun() {
  if (type == 1) {
    uni.navigateBack();
  } else {
    uni.navigateBack({
      delta: 4
    })
  }
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .notes {
    width: 690rpx;
    margin: 20rpx auto;

    .notee {
      font-size: 26rpx;
      font-weight: 400;
      color: #7A7E8F;
      line-height: 48rpx;
    }
  }

  .section1 {
    padding: 30rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #3D414E;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      height: 68rpx;

      .l1 {
        font-size: 28rpx;
        font-weight: 400;
        color: #262937;
      }
    }
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;

    .topstatus {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 10rpx;
      background: #1975FF;
    }

    .topstatus.inactive {

      background: #2A2D3C;
    }

    .topstatus.activ {

      background: #00CB46;
    }

    .top {
      width: 690rpx;
      height: 176rpx;
      background: linear-gradient(90deg, #F9FCFF 0%, #F0F9FF 49%, #FFFFFF 100%);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 30rpx;

      .icon {
        width: 72rpx;
        height: 72rpx;
      }

      .in {
        padding-left: 30rpx;
        flex: 1;

        .inl {
          display: flex;
          flex-direction: row;
          justify-content: space-between;

          .t1 {
            font-size: 48rpx;
            font-weight: 600;
            color: #262937;
          }

          .t2 {
            font-size: 28rpx;
            font-weight: 500;
            color: #1975FF;
          }

          .t2.inactive {
            color: #2A2D3C;
          }
        }

        .msg {
          font-size: 26rpx;
          font-weight: 400;
          color: #AEB0BC;
        }
      }
    }

    .content {
      padding: 30rpx;

      .code {
        font-size: 66rpx;
        font-weight: bold;
        color: #2A2D3C;
        text-align: center;
      }

      .msg {
        font-size: 28rpx;
        font-weight: 400;
        color: #AEB0BC;
        text-align: center;
      }

      .row {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        height: 60rpx;

        .l1 {
          font-size: 28rpx;
          font-weight: 400;
          color: #262937;
        }

        .l2 {
          font-size: 28rpx;
          font-weight: 600;
          color: #262937;
        }
      }

      .address {
        width: 630rpx;
        height: 132rpx;
        position: relative;
        border-radius: 16rpx;
        overflow: hidden;
        margin-top: 40rpx;

        .back {
          position: absolute;
          width: 630rpx;
          height: 132rpx;
          left: 0;
          top: 0;
          z-index: 1;
        }

        .add-content {
          width: 630rpx;
          height: 132rpx;
          display: flex;
          flex-direction: row;
          position: relative;
          z-index: 2;
          align-items: center;
          justify-content: space-between;
          padding: 0 30rpx;

          .lefec {
            .t1 {
              font-size: 28rpx;
              font-weight: 500;
              color: #262937;
              line-height: 40rpx;
            }

            .distance {
              font-size: 24rpx;
              font-weight: 400;
              color: #787C8D;
            }
          }

          .rsec {
            width: 40rpx;
            height: 40rpx;

            .right {
              width: 40rpx;
              height: 40rpx;
            }
          }
        }
      }

      .tips {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
        line-height: 34rpx;
        margin-top: 40rpx;
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;



    .btn {
      width: 100%;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }

    .btn2 {
      width: 50%;
      background: #FFFFFF;
      color: #617EFB;
    }

    .btn3 {
      width: 50%;
    }
  }
}
</style>