<template>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive
      :background="{ backgroundColor: 'linear-gradient(180deg, rgba(241,245,248,0), rgba(241,245,248,1))' }"
      title="体检中心地图">
    </u-navbar>
    <map id="map" :style="{ width: '750rpx', height: '100vh' }" :latitude="currentLocal.citylatitude"
      :longitude="currentLocal.citylongitude" :markers="covers" @markertap="onMarkertap" scale="11">
    </map>
  </view>
  <view class="dingwei" @click="backLoacl">
    <view class="d1">
      <view class="d2">
      </view>
    </view>
  </view>
  <view class="pop" :style="{ top: popTop }">
    <template v-if="topState > 0">
      <image class="showup" @click="changeTop" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/down.png">
      </image>
    </template>
    <view class="pop-top">
      <view class="warp">
        <image class="icon1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/loc.png">
        </image>
        <nj-address @change="lochange">
          <view class="text">
            {{ currentLocal.region }}
          </view>
        </nj-address>
        <image class="icon2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/xiala.png">
        </image>
      </view>
      <view class="hr"></view>
      <u-search bg-color="#F1F5F8" style="width: 500rpx;" :adjustPosition="false" :showLeft="false" @focus="onFocus"
        height="80" @clear="onClear" @search="search" shape="square" :show-action="false" placeholder="门诊名称"
        v-model="keyword"></u-search>
      <view @click="changeTop" v-if="isSearch" class="quxiao">取消</view>
    </view>
    <view class="search" v-if="showSearchRes">
      <view class="ti">搜索结果:</view>
      <scroll-view class="scoll" scroll-y v-if="list.length">
        <view class="sitem" v-for="item in list" :key="item.id" @click="showdetailInfo(item)">
          <view class="sitem-info">
            <view class="name">{{ item.name }}</view>
            <view class="addressa">
              <template v-if="!currentLocal.noLocation">
                <text class="t1" v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2) }}km·</text>
                <text v-else class="t1">{{ item.distance * 1000 }}m·</text>
              </template>
              <text class="t1">{{ item.area_name }}|{{ item.address }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <u-empty text="未找到机构" v-else></u-empty>
    </view>
    <view class="info-section" v-if="showInfo">
      <view class="info-top" @click="intoDetail(info.id)">
        <view class="name">
          <view class="namess">{{ info.name }} </view>
          <u-icon size="20" color="#797E8E" name="arrow-right"></u-icon>
        </view>
        <view class="lavel">服务时间：{{ info.servicetime }} </view>
        <view class="lavel">门诊地址：{{ info.address }} </view>
        <view class="lavel">门诊电话：{{ info.contact }} </view>
      </view>
      <view class="footer">
        <view class="left">
          <view class="bb" @click="callPhone">
            <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone1.png">

            </image>
            <view class="t1">
              电话
            </view>
          </view>
          <view class="bb" @click="goDaohang">
            <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/navigation1.png">

            </image>
            <view class="t1">
              导航
            </view>
          </view>
        </view>
        <view class="btn" @click="intoForm">预约入托体检</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, nextTick, computed } from "vue";
import { useLocalStore } from "@/stores/useLocalStore.js";
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request.js";
const currentLocal = useLocalStore();

onLoad(() => {
  getList();
})
const info = ref({
  name: "西安市莲湖区卫生服务中心",
  distance: 100,
  address: "莲湖区丰镐路2号民航小区",
  lng: '',
  lat: '',
  servicetime: "",
  contact: ""
});

function callPhone() {
  uni.makePhoneCall({
    phoneNumber: info.value.contact, //仅为示例，并非真实的电话号码
  });
}
function backLoacl() {
  let mapRef = uni.createMapContext("map");
  mapRef.moveToLocation({
    latitude: currentLocal.latitude,
    longitude: currentLocal.longitude
  })
}
function lochange(e) {
  currentLocal.province = e.provinceCode;
  currentLocal.city = e.city;
  currentLocal.areaCode = e.areaCode;
  currentLocal.region = e.area;
  getJingwei(e.areaCode, currentLocal.city + currentLocal.region)
  getList();
}
function getJingwei(code, name) {
  request({
    url: `/api/city/getlatlng`,
    data: {
      city: code,
      address: name
    }
  }).then(res => {
    let location = res?.result?.location;
    if (location) {
      let mapRef = uni.createMapContext("map");
      mapRef.moveToLocation({
        latitude: Number(location.lat),
        longitude: Number(location.lng)
      })
    }
  })
}
const showInfo = ref(false);
async function onMarkertap(e) {
  if (e.detail.markerId == 9999) return
  let data = list.value.find(item => item.id == e.detail.markerId);
  info.value = data;
  mapMoveCurrent();
  await nextTick();
  showInfo.value = true;
}
const popTop = computed(() => {
  if (isSearch.value) {
    return '15vh';
  } else if (showInfo.value) {
    return 'calc(100vh - 560rpx)';
  } else {
    return 'calc(100vh - 224rpx)'
  }
})
const topState = computed(() => {
  if (isSearch.value) {
    return 2;
  } else if (showInfo.value) {
    return 1;
  } else {
    return 0
  }
})
function mapMoveCurrent() {
  let mapRef = uni.createMapContext("map");
  mapRef.moveToLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng)
  })
}
function changeTop() {
  if (isSearch.value) {
    showSearchRes.value = false;
    isSearch.value = false;
  } else if (showInfo.value) {
    showInfo.value = false;
  }
}
function onFocus() {
  isSearch.value = true;
  showInfo.value = false;
  showSearchRes.value = false;
}
const list = ref([])
function getList() {
  uni.showLoading();
  return request({
    url: `/api/healthservices/hospitalList`,
    data: {
      page: 1,
      per_page: 30,
      name: keyword.value,
      area: currentLocal.areaCode,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    list.value = res;
    uni.hideLoading();
  })
}
const covers = computed(() => {
  let arr = list.value.map(item => {
    let iconPath = item.id == info.value.id ? 'https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/mapa.png' : 'https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/map.png'
    let width = item.id == info.value.id ? 50 : 32;
    let height = item.id == info.value.id ? 62 : 41;
    return {
      id: item.id,
      latitude: Number(item.lat),
      longitude: Number(item.lng),
      width,
      height,
      iconPath,
    }
  })
  arr.push({
    id: 9999,
    latitude: Number(currentLocal.latitude),
    longitude: Number(currentLocal.longitude),
    width: 24,
    height: 32,
    iconPath: 'https://obs.tuoyupt.com/yjy_mini/front_img/school/mylo.png',
  })
  return arr
});
const isSearch = ref(false);
const showSearchRes = ref(false);
const keyword = ref("");
function onClear() {
  getList();
}
function showdetailInfo(item) {
  showSearchRes.value = false;
  info.value = item;
  showInfo.value = true;
  isSearch.value = false;
  mapMoveCurrent();
}
function search() {
  getList()
    .then(() => {
      showSearchRes.value = true;
    })
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng),
    name: info.value.name,
    address: info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
function intoDetail(id) {
  if (id) {
    uni.navigateTo({
      url: `./detail?id=${id}`,
    });
  }
}
function intoForm() {
  uni.showLoading();
  request({
    url: "/api/healthservices/examination/getTimeList",
    data: {
      hospital_id: info.value.id,
      week: 0
    }
  }).then(res => {
    uni.hideLoading();
    uni.setStorageSync("physical_hospital_info", info.value);
    uni.navigateTo({
      url: `./book`,
    });
  }).catch(() => {
    uni.hideLoading();
  })
}
</script>
<style scoped lang="scss">
.dingwei {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.09);
  position: fixed;
  z-index: 2;
  right: 30rpx;
  top: calc(100vh - 324rpx);

  .d1 {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid #262937;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .d2 {
      width: 8rpx;
      height: 8rpx;
      background: #262937;
      border-radius: 50%;
    }
  }
}

.pop {
  width: 750rpx;
  height: 85vh;
  background: #FFFFFF;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  position: fixed;
  top: calc(100vh - 224rpx);
  left: 0;
  padding: 64rpx 30rpx 0;
  z-index: 3;
  transition: top 0.5s ease;

  .showup {
    width: 56rpx;
    height: 12rpx;
    top: 16rpx;
    left: 348rpx;
    position: absolute;
  }

  .pop-top {
    width: 690rpx;
    height: 80rpx;
    background: #F1F5F8;
    border-radius: 24rpx;
    display: flex;
    flex-direction: row;
    align-items: center;

    .hr {
      width: 2rpx;
      height: 32rpx;
      background: #AEB0BC;
    }

    .quxiao {
      width: 106rpx;
      text-align: center;
      height: 36rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #1975FF;
      background: #F1F5F8;
    }

    .warp {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      width: 188rpx;
      height: 80rpx;

      .icon1 {
        width: 24rpx;
        height: 24rpx;
        margin-right: 12rpx;
      }

      .text {
        width: 78rpx;
        font-size: 26rpx;
        font-weight: 500;
        color: #262937;
      }

      .icon2 {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .search {
    width: 100%;
    height: calc(100% - 100rpx);

    .ti {
      height: 108rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #787C8D;
      line-height: 108rpx;
    }

    .scoll {
      width: 100%;
      height: calc(100% - 108rpx);

      .sitem {
        display: flex;
        flex-direction: row;
        width: 100%;
        margin-bottom: 30rpx;

        .sitem-info {
          flex: 1;
          padding-left: 20rpx;

          .name {
            font-size: 32rpx;
            font-weight: 600;
            color: #262937;
          }

          .addressa {
            font-size: 26rpx;
            font-weight: 400;
            color: #7A7E8F;
            line-height: 36rpx;
            margin-top: 16rpx;
          }
        }
      }
    }
  }

  .info-top {
    padding-top: 40rpx;

    .name {
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;

      .namess {
        font-size: 40rpx;
        font-weight: 500;
        color: #262937;
        max-width: 540rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .lavel {
      font-size: 28rpx;
      font-weight: 400;
      color: #787C8D;
      margin-top: 8rpx;
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    margin-top: 20rpx;

    .left {
      display: flex;
      flex-direction: row;
      flex: 1;
      justify-content: space-around;

      .bb {
        text-align: center;

        .i1 {
          width: 40rpx;
          height: 40rpx;
        }

        .t1 {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
          line-height: 34rpx;
        }
      }
    }

    .btn {
      width: 346rpx;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }
}
</style>