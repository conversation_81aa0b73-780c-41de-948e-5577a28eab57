<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" 
    :title="isS?'查看既往史':'填写既往史'">
    </u-navbar>
    <view class="section">
      <view class="title">
        既往史信息
      </view>
      <uni-data-checkbox :multiple="true" :disabled="isS" v-model="history_ill" :localdata="list"></uni-data-checkbox>
      <view class="title title2">
        过敏史信息（没有请填写无）
      </view>
      <u-input :disabled="isS" v-model="history_allergic" maxlength="100" type="textarea" :border="true" :height="100"
        :auto-height="true" />
    </view>

    <view class="section">
      <view class="title">
        家长签名
      </view>
      <view class="tip">
        本人声明: 我作为家长保证提供的以上信息真实、有效。如提供虚假信息，愿承担一切法律责任。
      </view>
      <view style="width: 630rpx ;height: 356rpx;" class="sign">
        <view class="text">签名区域</view>
        <template v-if="isS">
          <image :src="image" style="width: 630rpx ;height: 356rpx;"></image>
        </template>
        <template v-else> <l-signature disableScroll ref="signatureRef" penColor="#000000" :penSize="5"
            :openSmooth="true"></l-signature></template>
      </view>
    </view>
    <view class="footer-zhanwei"></view>
    <view class="footer" v-if="!isS">
      <view class="footer-content">
        <view class="btn btn1" @click="reset">重新签名</view>
        <view class="btn" @click="comfirm">确定</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import LSignature from "../components/l-signature/l-signature.vue";
import UniDataCheckbox from "../components/uni-checkbox.vue";
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import { OBSupload } from '@/utils/obs/obs-upload.js';
const history_ill = ref([])

const history_allergic = ref('')
const list = ref([
  {
    text: '先心',
    value: 1,
  },
  {
    text: '癫痫',
    value: 2
  },
  {
    text: '高热惊厥',
    value: 3
  },
  {
    text: '哮喘',
    value: 4
  },
  {
    text: '结核接触史',
    value: 5
  },
  {
    text: '其他',
    value: 6
  },
  {
    text: '无',
    value: 7
  }
])
const isS = ref(false)
const image = ref('')
onLoad((option) => {
  if (option.isS == 1) {
    isS.value = true;
    let data = uni.getStorageSync("phy_sign_ifno")
    history_ill.value = data.history_ill.split(',').map(item => Number(item));
    history_allergic.value = data.history_allergic;
    image.value = data.sign_image;
  }
})
const signatureRef = ref(null);
function reset() {
  signatureRef.value.clear();
}
let url = ''
function comfirm() {
  if (history_ill.value.length == 0) {
    uni.showToast({
      title: "请选择既往史",
      icon: "none"
    })
    return
  }
  if (history_allergic.value == '') {
    uni.showToast({
      title: "请填写过敏史",
      icon: "none"
    })
    return
  }
  signatureRef.value.canvasToTempFilePath({
    success: (res) => {
      if (res.isEmpty) {
        uni.showToast({
          title: "请签名",
          icon: "none"
        })
      } else {
        url = res.tempFilePath
        uni.showModal({
          title: "确认签名？",
          content: "确认后不可修改",
          success(res) {
            if (res.confirm) {
              submit();
            }
          }
        })
      }
    }
  })
}
function submit() {
  uni.showLoading();
  let fileName = url.substring(url.lastIndexOf("/") + 1);
  let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
  OBSupload(url, fileExtension, fileName)
    .then(sign_image => {
      uni.hideLoading();
      let data = {
        history_ill: history_ill.value.join(','),
        history_allergic: history_allergic.value,
        sign_image
      }
      uni.$emit("physical_sign");
      uni.setStorageSync("physical_sign", data);
      uni.navigateBack();
    })

}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .section {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #3D414E;
      margin-bottom: 20rpx;
    }

    .title2 {
      margin-top: 20rpx;
    }

    .tip {
      font-size: 28rpx;
      font-weight: 600;
      color: #ca8838;
    }

    .sign {
      border: 1rpx dashed #666;
      margin-top: 20rpx;
      position: relative;

      .text {
        position: absolute;
        left: 50%;
        top: 50%;
        color: #999999;
        font-size: 24rpx;
        transform: translateX(-50%) translateY(-50%);
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;

    .btn {
      width: 50%;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }

    .btn1 {
      background: #FFFFFF;
      color: #617EFB;
    }
  }
}
</style>