<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="服务预约">
    </u-navbar>
    <view class="section">
      <view class="s1">
        <image class="addicon" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/addd.png"></image>
        <text>入托体检套餐</text>
      </view>
      <view class="s2">
        <view class="left">
          <view class="t1">
            {{ hospital_info.name }}
          </view>
          <view class="t2">
            <text v-if="hospital_info.distance >= 1">距你{{ (hospital_info.distance * 1).toFixed(2)
            }}km</text>
            <text v-else>距你{{ hospital_info.distance * 1000 }}m</text>·{{ hospital_info.address }}
          </view>
        </view>
        <image class="icon1" @click="goDaohang" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/daoaoao.png">
        </image>
        <image class="icon1" @click="callPhone" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/phoneee.png">
        </image>
      </view>
    </view>
    <view class="section">
      <view class="row" @click="showChilPop">
        <view class="l1">
          <u-icon name="man-add-fill" size="32" color="#000000"></u-icon>
          <text class="ll1">选择婴幼儿</text>
        </view>
        <view class="l2">
          <text>{{ child_info.name ? child_info.name : "请选择婴幼儿" }}</text>
          <u-icon name="arrow-right" color="#797E8E"></u-icon>
        </view>
      </view>
      <view class="row row1" @click="showSchoolPop">
        <view class="l1">
          <u-icon name="home-fill" size="32" color="#000000"></u-icon>
          <text class="ll1">选择托育机构</text>
        </view>
        <view class="l2">
          <text class="school_name">{{ school_info.name ? school_info.name : "请选择托育机构" }}</text>
          <u-icon name="arrow-right" color="#797E8E"></u-icon>
        </view>
      </view>
    </view>
    <view class="section">
      <view class="tt1">
        预约日期
      </view>
      <view class="dates">
        <view class="btn before" :class="{ dis: week == 0 }" @click="getBefore">
          <u-icon name="play-left-fill"></u-icon>
        </view>
        <view class="btn next" @click="getNext" :class="{ dis: week > 2 }">
          <u-icon name="play-right-fill"></u-icon>
        </view>
        <view class="scroll">
          <view class="date" @click="changeDay(item)"
            :class="{ active: order_info.order_time == item.day, dis: item.status !== 1 || dayDis(item) }"
            v-for="(item, index) in timeList" :key="item.day">
            <view class="t1">{{ index == 0 && week == 0 ? '今天' : dayjs(item.day).format("MM.DD") }}</view>
            <view class="t2" :class="{
              ta: item.status == 1,
              tl: item.status == 3
            }">
              {{ dayDis(item) ? '限制' :
                item.status == 0 || item.status == -1 ? '无号' : item.status == 1 ? '可约' : item.status == 2 ? '约满' : '停诊'
              }}
            </view>
          </view>
        </view>
      </view>
      <view class="tt1">
        上午
      </view>
      <template v-if="periodListS.length">
        <view class="times">
          <view class="time" @click="changePeriod(item)" :class="{
            active: order_info.id == item.id,
            dis: item.per_num == 0
          }" v-for="(item) in periodListS" :key="item.id">
            <view class="timet">{{ item.order_period }}</view>
            <view class="timet">剩余{{ item.per_num }}</view>
          </view>
        </view>
      </template>
      <view v-else class="empty">无可预约时段</view>
      <view class="tt1">
        下午
      </view>
      <template v-if="periodListX.length">
        <view class="times">
          <view class="time" @click="changePeriod(item)" :class="{
            active: order_info.id == item.id,
            dis: item.per_num == 0
          }" v-for="(item) in periodListX" :key="item.id">
            <view class="timet">{{ item.order_period }}</view>
            <view class="timet">剩余{{ item.per_num }}</view>
          </view>
        </view>
      </template>
      <view v-else class="empty">无可预约时段</view>
      <view class="btnb" :class="{ disable: hasSign }" @click="goAddT">{{ hasSign ? '已填写' : '填写既往史' }}</view>
    </view>
    <view class="section">
      <view class="tt1 tt5">
        上传化验单
      </view>
      <u-upload width="180rpx" height="180rpx" max-count="6" @on-list-change="imgChange" multiple deletable
        :auto-upload="false">
      </u-upload>
      <view>请上传3个月以内的化验单，支持上传jpg、png等文件格式</view>
    </view>
    <view class="section">
      <!-- <view class="tt1 tt2">
        项目介绍
      </view>
      <view class="tt3">
        共包含16项检查
      </view>
      <view class="items">
        <view class="it1">
          体检项目（5项）
        </view>
        <view class="des">
          体格检查、血常规、肝功、视力筛查、口腔涂氟
        </view>
      </view>
      <view class="items">
        <view class="it1">
          自选体检项目（5项）
        </view>
        <u-collapse :head-style="headStyle" :body-style="bodyStyle">
          <u-collapse-item :title="item.head" v-for="(item, index) in itemList" :key="index">
            {{ item.body }}
          </u-collapse-item>
        </u-collapse>
      </view> -->
      <view class="tt1 tt4">
        检查须知
      </view>
      <text class="notee">
        1、儿童体检当天早晨请一定不要吃饭和喝水，更不要喝饮料或奶之类的饮品，以免影响检查结果；抽完血，再吃东西，家长可以给孩子带点牛奶、面包等简单食物。\n
        2、为确保测量结果的准确性，测量身高体重时家长需为儿童脱去外套、鞋帽等。\n
        3、生病服药的儿童，请将服药情况及时告知体检医生。\n
        4、儿童既往病史和过敏史请如实告知体检医生。\n
        5、请家长为孩子多准备一套裤子，孩子哭闹厉害时，尤其是年龄小的宝宝会有尿裤子的现象，避免宝宝不适可以及时更换。\n
        6、家长可以带一些小玩具，应对孩子不配合体检的现象；提前告知孩子有抽血项目，可能会有一点点疼，但会很快过去，需要孩子配合医生，多鼓励孩子。\n
        7、抽血时，宝宝往往控制不住会缩胳膊，请家长配合医生控制好孩子的手臂，以免针头误伤孩子；抽完血后，请家长按压抽血部位至少5分钟。\n
      </text>
    </view>
    <view class="footer-zhanwei"></view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn" @click="submit">确认预约</view>
      </view>
    </view>
  </view>
  <u-popup v-model="popShow" border-radius="24" mode="bottom" safe-area-inset-bottom>
    <view class="pop">
      <view class="item" v-for="item in child_list" :key="item.id">
        <view class="top">
          <image class="avter" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/avter.png">
          </image>
          <view class="info">
            <view class="t1">
              <view class="name">{{ item.name }}</view>
              <view class="sex">{{ item.sex == 1 ? '男' : "女" }}</view>
            </view>
            <view class="arr">
              {{ item.age }}
            </view>
          </view>
          <view class="right" @click="checkStudent(item)">
            <view v-if="item.id == child_info.id" class="checked">
              <u-icon name="checkbox-mark" color="#FFFFFF" size="24"></u-icon>
            </view>
            <view v-else class="nocheck"></view>
          </view>
        </view>
      </view>
      <view class="btng" @click="confirmChangeChild">
        确定
      </view>
    </view>
  </u-popup>
  <u-popup v-model="popShow1" border-radius="24" mode="bottom" safe-area-inset-bottom>
    <view class="pop">
      <view>
        <u-search height="80" v-model="searchKey" shape="square" @custom="getChool" @search="getChool"
          :show-action="true" placeholder="机构名称"></u-search>
      </view>
      <scroll-view scroll-y class="scroll">
        <template v-if="school_list.length">
          <view class="item" v-for="item in school_list" :key="item.id">
            <view class="top">
              <view class="name namee">{{ item.name }}</view>
              <view class="right" @click="checkSchool(item)">
                <view v-if="item.id == school_info.id" class="checked">
                  <u-icon name="checkbox-mark" color="#FFFFFF" size="24"></u-icon>
                </view>
                <view v-else class="nocheck"></view>
              </view>
            </view>
          </view>
        </template>
        <u-empty v-else text="暂无签约机构"></u-empty>
      </scroll-view>
      <view class="btns">
        <view class="btnsn" @click="addSchool">
          添加
        </view>
        <view class="btnsn" @click="popShow1 = false">
          确定
        </view>
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, computed } from "vue";
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
import request from "@/request.js";
const hospital_info = ref({
  name: "",
  distance: 0,
  address: "",
  lat: "",
  lng: ""
})
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: hospital_info.value.contact,
  });
}
function goAddT() {
  uni.navigateTo({
    url: `./sign`,
  });
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(hospital_info.value.lat),
    longitude: Number(hospital_info.value.lng),
    name: hospital_info.value.name,
    address: hospital_info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
let imgList = []
function imgChange(e) {
  imgList = e
}
const popShow = ref(false);
const child_list = ref([]);
const child_info = ref({
  id: 0,
  name: ''
});
const order_info = ref({
  order_period: "",
  order_time: "",
  id: ""
})
const hasSign = ref(false);
let hasShowRet = false;
onLoad(() => {
  hospital_info.value = uni.getStorageSync('physical_hospital_info');
  getTime();
  getChool();
  uni.$on("physical_sign", () => {
    hasSign.value = true;
  })
})
onShow(() => {
  if (child_info.value.id == "") {
    getChild();
  } else {
    checkYuyue();
  }
})
const timeList = ref([]);
const periodList = ref([]);
const periodListS = computed(() => {
  return periodList.value.filter(item => {
    let t = item.order_period.substr(0, 2)
    if (Number(t) < 12) {
      return true
    } else { return false }
  })
})
const periodListX = computed(() => {
  return periodList.value.filter(item => {
    let t = item.order_period.substr(0, 2)
    if (Number(t) < 12) {
      return false
    } else { return true }
  })
})
const week = ref(0)
function getBefore() {
  week.value--;
  getTime();
}
function getNext() {
  week.value++;
  getTime();
}
function getTime() {
  uni.showLoading();
  periodList.value = [];
  request({
    url: "/api/healthservices/examination/getTimeList",
    data: {
      hospital_id: hospital_info.value.id,
      week: week.value
    }
  }).then(res => {
    timeList.value = res.time;
    let first = res.time.find(item => item.status == 1);
    if (first) {
      periodList.value = first.period;
      order_info.value.order_time = first.day;
    }
    uni.hideLoading();
  })
}
function changeDay(item) {
  order_info.value.order_time = item.day;
  periodList.value = item.period;
}
function changePeriod(item) {
  order_info.value.id = item.id;
  order_info.value.order_period = item.order_period;
}
function checkStudent(item) {
  child_info.value = item;
}
let currenInfo = "";
function showChilPop() {
  currenInfo = JSON.parse(JSON.stringify(child_info.value));
  popShow.value = true;
}
let minDay = ref("");
let maxDay = ref("");
function confirmChangeChild() {
  if (child_info.value.id !== currenInfo.id) {
    checkYuyue()
  }
  popShow.value = false;
}
let canNext = true;
function checkYuyue() {
  canNext = false;
  request({
    url: "/api/healthservices/examination/getobject",
    data: {
      hospital_id: hospital_info.value.id,
      idcode: child_info.value.id_card
    }
  }).then(res => {
    canNext = true;
    if (res.examination_school && res.examination_school?.id) {
      let now = dayjs();
      if (now.isAfter(res.examination_school.end_time, 'day')) {
        uni.showModal({
          title: "提示",
          content: `宝宝已超过规定体检时间，请直接带宝宝到${hospital_info.value.name}线下预约体检`,
          showCancel: false,
          success() {
            uni.navigateBack({
              delta: 2
            })
          }
        })
        return
      }
      school_info.value = {
        id: res.examination_school.school_id,
        name: res.examination_school.name,
      };
      canChange.value = false;
      uni.setStorageSync("school_packInfo", res);
      if (!hasShowRet) {
        uni.showModal({
          title: "提示",
          content: "机构已为宝宝预约，请重新选择套餐",
          showCancel: false,
          success() {
            hasShowRet = true;
            uni.navigateTo({
              url: `./detail?id=${hospital_info.value.id}&isM=1`
            })
          }
        })
      } else {
        if (minDay.value !== res.examination_school.start_time && maxDay.value != res.examination_school.end_time) {
          minDay.value = res.examination_school.start_time;
          maxDay.value = res.examination_school.end_time;
          order_info.value = {
            order_period: "",
            order_time: "",
            id: ""
          }
          uni.showModal({
            title: "提示",
            content: "机构已为宝宝预约，已限制预约日期",
            showCancel: false,
          })
        }
      }
    } else {
      uni.removeStorageSync("school_packInfo");
      canChange.value = true;
      minDay.value = "";
      maxDay.value = "";
    }
  })

}
const canChange = ref(true);
function dayDis(item) {
  if (minDay.value && maxDay.value) {
    if (dayjs(item.day).isSameOrAfter(minDay.value, 'day') && dayjs(item.day).isSameOrBefore(maxDay.value, 'day')) {
      return false;
    } else {
      return true;
    }
  } else {
    return false;
  }
}
function showSchoolPop() {
  if (canChange.value) {
    popShow1.value = true;
  }
}
function getChild() {
  request({
    goLogin: true,
    url: "/api/healthservices/user/list",
  }).then(res => {
    child_list.value = res;
    if (res.length == 1) {
      if (child_info.value.id == "") {
        child_info.value = res[0];
        checkYuyue();
      } else {
        checkYuyue();
      }
    }
    if (res.length == 0) {
      uni.showModal({
        title: "提示",
        content: "您尚未添加婴幼儿信息，无法预约！",
        confirmText: "去添加",
        cancelText: "返回",
        success(data) {
          if (data.confirm) {
            if (Boolean(userInfo.master_hospital_id)) {
              uni.navigateTo({
                url: "/pages/pack/children/add"
              })
            } else {
              uni.showToast({
                title: "请先完善个人信息",
                mask: true,
                duration: 1500,
                icon: "none"
              })
              setTimeout(() => {
                uni.navigateTo({
                  url: "/pages/pack/children/profile"
                })
              }, 1500)
            }
          } else {
            uni.navigateBack();
          }
        }
      })
    }
  })
}
async function submit() {
  if (!canNext) return;
  if (child_info.value.id == 0) {
    uni.showToast({
      title: "请选择婴幼儿",
      icon: "none"
    })
    return
  }
  if (school_info.value.name == "") {
    uni.showToast({
      title: "请选择托育机构",
      icon: "none"
    })
    return
  }
  if (order_info.value.order_time == '') {
    uni.showToast({
      title: "请选择预约日期",
      icon: "none"
    })
    return
  }
  if (order_info.value.id == '') {
    uni.showToast({
      title: "请选择预约时间",
      icon: "none"
    })
    return
  }
  if (new Date().getTime() >= new Date(order_info.value.order_time + ' ' + order_info.value.order_period.split('~')[0]).getTime()) {
    uni.showToast({
      title: "预约时间不能小于当前时间",
      icon: "none"
    })
    return
  }
  if (!hasSign.value) {
    uni.showToast({
      title: "请填写既往史",
      icon: "none"
    })
    return
  }
  let imgs = [];
  if (imgList.length) {
    let requestArr = []
    imgList.forEach(item => {
      let fileName = item.file.name;
      let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      requestArr.push(OBSupload(item.url, fileExtension, fileName))
    })
    imgs = await Promise.all(requestArr);
    uni.setStorageSync("physical_laboratory_report", imgs.join(","));
  } else {
    uni.removeStorageSync("physical_laboratory_report");
  }
  uni.setStorageSync("physical_order_info", order_info.value);
  uni.setStorageSync("physical_child_info", child_info.value);
  uni.setStorageSync("physical_school_info", school_info.value);
  uni.navigateTo({
    url: `./confirm`,
  });
}

// const headStyle = {
//   'font-size': '26rpx',
//   color: '#262937'
// }
// const bodyStyle = {
//   'font-size': '26rpx',
//   color: '#262937'
// }
// const itemList = ref([{
//   head: "骨密度",
//   body: "只要我们正确择取一个合适的参照物乃至稍降一格去看待他人，值得赏识的东西便会扑面而来",
//   open: false,
// }, {
//   head: "体脂分析",
//   body: "学会欣赏，实际是一种积极生活的态度，是生活的调味品，会在欣赏中发现生活的美",
//   open: false,
// }, {
//   head: "注意力测评",
//   body: "但是据说雕刻大卫像所用的这块大理石，曾被多位雕刻家批评得一无是处，有些人认为这块大理石采凿得不好，有些人嫌它的纹路不够美",
//   open: false,
// }])
const school_list = ref([])
const school_info = ref({
  id: "",
  name: ""
})
const popShow1 = ref(false);
function checkSchool(item) {
  school_info.value = item;
  if (item.service_ids.includes(2)) {
    uni.showModal({
      title: "提示",
      content: "请家长联系机构统一预约",
      cancelText: "重选机构",
      confirmText: "确定",
      showCancel: true,
      success(res) {
        if (res.confirm) {
          uni.navigateBack();
        } else {
          school_info.value = {
            id: "",
            name: ""
          }
        }
      }
    })

  }
}
const searchKey = ref('')
function getChool() {
  request({
    url: "/api/healthservices/examination/schoollist",
    method: "post",
    data: {
      hospital_id: hospital_info.value.id,
      school_name: searchKey.value
    }
  }).then(res => {
    school_list.value = res.data;
  })
}
function addSchool() {
  uni.showModal({
    title: '添加托育机构',
    editable: true,
    success(res) {
      if (res.confirm) {
        if (res.content == '') {
          uni.showToast({
            icon: "none",
            title: "请输入机构名称",
            duration: 1000
          })
        } else {
          school_info.value = {
            id: "",
            name: res.content
          }
        }
      }
    }
  })
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .section {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;
    position: relative;
    z-index: 1;

    .s2 {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 40rpx;

      .icon1 {
        width: 64rpx;
        height: 64rpx;
        margin-left: 40rpx;
      }

      .left {
        flex: 1;
        max-width: 400rpx;

        .t1 {
          font-size: 32rpx;
          font-weight: 500;
          color: #262937;
          line-height: 44rpx;
        }

        .t2 {
          font-size: 26rpx;
          font-weight: 400;
          color: #7A7E8F;
          line-height: 36rpx;
          margin-top: 8rpx;
        }
      }
    }

    .s1 {
      font-size: 40rpx;
      font-weight: 600;
      color: #000000;
      border-bottom: 1rpx solid #EEE;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-bottom: 40rpx;

      .addicon {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .row1 {
      margin-top: 20rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .l1 {
        font-size: 32rpx;
        font-weight: 400;
        color: #797D8D;
        width: 200prx;
        flex-shrink: 0;

        .ll1 {
          margin-left: 12rpx;
        }
      }

      .l2 {
        font-size: 32rpx;
        font-weight: 600;
        color: #000000;
        display: flex;
        flex-direction: row;
        align-items: center;

        .school_name {
          max-width: 9em;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: inline-block;
        }
      }
    }

    .tt1 {
      font-size: 32rpx;
      font-weight: 600;
      color: #292D3B;
      line-height: 32rpx;
    }

    .tt4 {
      margin-top: 40rpx;
      margin-bottom: 20rpx;
    }

    .tt5 {
      margin-bottom: 20rpx;
    }

    .notee {
      font-size: 26rpx;
      font-weight: 400;
      color: #7A7E8F;
      line-height: 48rpx;
    }

    .tt2 {

      color: #1975FF;
    }

    .tt3 {
      font-size: 26rpx;
      font-weight: 400;
      color: #2D303E;
      margin-top: 16rpx;
      margin-bottom: 40rpx;
    }

    .dates {
      width: 100%;
      margin: 28rpx 0;
      position: relative;

      .btn {
        position: absolute;
        width: 60rpx;
        height: 100%;
        top: 0;
        line-height: 96rpx;
        color: #1975FF;
      }

      .before {
        left: -55rpx;
        text-align: right;
      }

      .next {
        right: -55rpx;
        text-align: left;
      }

      .btn.dis {
        color: #AEB0BC;
        pointer-events: none;
      }

      .scroll {
        width: 100%;
        display: flex;
        flex-direction: row;
        height: 96rpx;
        justify-content: space-between;
        padding: 0 10rpx;

        .date {
          width: 74rpx;
          height: 96rpx;
          background: rgba(25, 117, 255, 0);
          border-radius: 8rpx;
          border: 1rpx solid #EEEEEE;
          text-align: center;
          flex-shrink: 0;

          .t1 {
            font-size: 26rpx;
            font-weight: 400;
            color: #7A7E8F;
            line-height: 48rpx;
          }

          .t2 {
            font-size: 26rpx;
            font-weight: 500;
            color: #7A7E8F;
            line-height: 48rpx;
          }

          .ta {
            color: #1975FF;
          }

          .tl {
            color: rgb(0, 208, 93);
          }
        }



        .date.active {
          background: #1975FF;

          .t1,
          .t2 {
            color: #FFFFFF;
          }
        }
      }
    }

    .empty {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
      height: 80rpx;
      line-height: 80rpx;
    }

    .times {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 28rpx 0;
      flex-wrap: wrap;
      justify-content: space-between;

      &::after {
        content: " ";
        width: 194rpx;
      }

      .time {
        width: 194rpx;
        height: 96rpx;
        background: rgba(25, 117, 255, 0);
        border-radius: 8rpx;
        border: 1rpx solid #EEEEEE;
        text-align: center;
        margin-bottom: 20rpx;

        .timet {
          font-size: 26rpx;
          font-weight: 500;
          color: #7A7E8F;
          line-height: 48rpx;
        }
      }

      .time.active {
        .timet {
          background: #1975FF;
          color: #FFFFFF;
        }
      }
    }

    .items {
      width: 630rpx;
      background: #F1F5F8;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 16rpx;

      .it1 {
        font-size: 32rpx;
        font-weight: 600;
        color: #262937;
      }

      .des {
        margin-top: 46rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #262937;
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.dis {
  pointer-events: none;
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 3;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;

    .btn {
      width: 690rpx;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }
}

.btnb {
  width: 100%;
  height: 80rpx;
  border-radius: 16rpx;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  background: #617EFB;
  line-height: 80rpx;
}

.btnb.disable {
  opacity: 0.5;
  pointer-events: none;
}

.pop {
  width: 100%;
  padding: 30rpx 30rpx 0 30rpx;
  background: #FFFFFF;

  .scroll {
    width: 100%;
    height: 500rpx;
  }

  .name {
    font-size: 32rpx;
    font-weight: 600;
    color: #282C39;
    line-height: 56rpx;
  }

  .namee {
    flex: 1;
  }

  .btns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-top: 12rpx;

    .btnsn {
      width: 45%;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }

  .btng {
    width: 100%;
    height: 80rpx;
    border-radius: 16rpx;
    text-align: center;
    font-size: 28rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    background: #617EFB;
    line-height: 80rpx;
  }

  .top {
    width: 100%;
    height: 128rpx;
    display: flex;
    flex-direction: row;
    margin: 0 auto 20rpx;
    border-bottom: 1rpx solid #eee;
    align-items: center;

    .avter {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .info {
      padding-left: 24rpx;
      flex: 1;

      .t1 {
        display: flex;
        flex-direction: row;
        align-items: center;


        .sex {
          width: 32rpx;
          height: 32rpx;
          background: #D6EDFE;
          border-radius: 6rpx;
          text-align: center;
          line-height: 32rpx;
          font-size: 20rpx;
          font-weight: 400;
          color: #0275FF;
          margin-left: 10rpx;
        }
      }


      .arr {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
        margin-top: 8rpx;
      }
    }

    .right {
      .nocheck {
        width: 48rpx;
        height: 48rpx;
        background: #FFFFFF;
        border: 1rpx solid #AEB0BC;
        border-radius: 50%;
        margin-right: 24rpx;
      }

      .checked {
        width: 48rpx;
        height: 48rpx;
        background: #617EFB;
        border-radius: 50%;
        color: #FFFFFF;
        text-align: center;
        font-size: 12rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
      }
    }
  }
}
</style>