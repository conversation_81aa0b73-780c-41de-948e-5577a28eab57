<template>
  <view class="top">
    <image class="backimg" src="https://obs.tuoyupt.com/miniprogram/index/atopback4.png">
    </image>
  </view>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive :background="{ backgroundColor: 'rgba(255,255,255,0)' }" title="">
      <view class="warp">
        <image class="icon1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/loc.png">
        </image>
        <nj-address @change="lochange">
          <view class="text">
            {{ currentLocal.city }}·{{ currentLocal.region }}
          </view>
        </nj-address>
        <image class="icon2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/xiala.png">
        </image>
      </view>
    </u-navbar>
    <view class="top-zhanwei"></view>
    <view class="topfix">
      <view class="top-t">
        <image class="icon" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/ttttt.png">
        </image>
        <view class="t2">
          ｜入托后，根据托育机构指定体检中心体检
        </view>
      </view>
      <view class="btns">
        <view class="bb" @click="goRouter(0)">
          <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/ic1.svg">

          </image>
          <view class="t1">
            体检预约
          </view>
        </view>
        <view class="bb" @click="goRouter(1)">
          <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/ic3.svg">

          </image>
          <view class="t1">
            历史预约
          </view>
        </view>
        <view class="bb" @click="goRouter(2)">
          <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/ic2.svg">

          </image>
          <view class="t1">
            体检报告
          </view>
        </view>
      </view>
    </view>
    <view class="stit">
      <u-search height="80" @click="goSearch" shape="square" :show-action="false" placeholder="体检中心"
        disabled></u-search>
      <view class="section" @click="goMap">
        <view class="t1">附近体检中心</view>
        <view class="t2">
          地图
        </view>
        <image class="map" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/map.png">
        </image>
      </view>
    </view>
    <template v-if="list.length">
      <view class="item" @click="intoDetail(item.id)" v-for="(item, index) in list" :key="index">
        <u-image width="64rpx" height="64rpx" border-radius="32" mode="aspectFill"
          src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/jigoua.png"></u-image>
        <view class="msg">
          <view class="name">{{ item.name }}</view>
          <view class="address">
            <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2) }}km·</text>
              <text v-else class="distance">{{ item.distance * 1000 }}m·</text>
            </template>
            <text>{{ item.area_name }} | {{ item.address }}</text>
          </view>
        </view>
      </view>
      <u-loadmore margin-top="40" :status="status" :load-text="loadText" />
    </template>
    <view class="mno-data" v-else>
      <image src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png" mode="aspectFit" />
      <view class="t1">抱歉</view>
      <view class="t2">该区域暂无体检中心</view>
    </view>
    <view class="zhanwei"></view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';
import { onReachBottom, onLoad,onShow } from '@dcloudio/uni-app';
import request from "@/request.js";
import { useLocalStore } from "@/stores/useLocalStore.js"
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
userInfo.refrsh();
let loadText = {
  loadmore: '轻轻上拉',
  loading: '努力加载中',
  nomore: '已经加载全部'
}

function intoDetail(id) {
  if (!Boolean(userInfo.id)) {
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
    return
  }
  if (id) {
    uni.navigateTo({
      url: `./detail?id=${id}`,
    });
  }
}

function lochange(e) {
  currentLocal.province = e.provinceCode;
  currentLocal.city = e.city;
  currentLocal.areaCode = e.areaCode;
  currentLocal.region = e.area;
  getJingwei(e.areaCode, currentLocal.city + currentLocal.region)
}
function getJingwei(code, name) {
  request({
    url: `/api/city/getlatlng`,
    data: {
      city: code,
      address: name
    }
  }).then(res => {
    let location = res?.result?.location;
    if (location) {
      currentLocal.citylatitude = location.lat;
      currentLocal.citylongitude = location.lng;
    }
  })
}

let page = 1;
const keyword = ref("");
const currentLocal = useLocalStore();
watch(() => currentLocal.areaCode, () => {
  getList();
}, {
  immediate: true
})
let list = ref([])
let status = ref('loadmore');
let hasMore = true;
function getList() {
  page = 1;
  uni.showLoading()
  request({
    url: `/api/healthservices/hospitalList`,
    data: {
      page,
      per_page: 10,
      name: keyword.value,
      area: currentLocal.areaCode,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    list.value = res;
    if (res.length < 10) {
      hasMore = false;
      status.value = 'nomore';
    } else {
      hasMore = true;
      status.value = 'loadmore';
    }
    uni.hideLoading();
  })
}
onReachBottom(() => {
  if (hasMore) {
    status.value = 'loading';
    page++;
    request({
      url: `/api/healthservices/hospitalList`,
      data: {
        page,
        per_page: 10,
        name: keyword.value,
        area: currentLocal.areaCode,
        lng: currentLocal.longitude,
        lat: currentLocal.latitude
      }
    }).then(res => {
      list.value.push(...res);
      if (res.length < 10) {
        hasMore = false;
        status.value = 'nomore';
      } else {
        hasMore = true;
        status.value = 'loadmore';
      }
    })
  }
})
function goRouter(index) {
  if (!Boolean(userInfo.id)) {
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
    return
  }
  if (index == 0) {
    uni.navigateTo({
      url: "./his-list"
    })
  }
  if (index == 1) {
    uni.navigateTo({
      url: "./book-list"
    })
  }
  if (index == 2) {
    uni.navigateTo({
      url: "./list"
    })
  }
}
function goMap() {
  uni.navigateTo({
    url: "./map"
  })
}
function goSearch() {
  uni.navigateTo({
    url: "./search"
  })
}

onLoad(async () => {
  uni.showLoading()
  try {
    let location = await uni.getLocation({
      type: "gcj02",
    });
    if (location.errMsg == "getLocation:ok") {
      currentLocal.latitude = location.latitude;
      currentLocal.longitude = location.longitude;
      currentLocal.noLocation = false;
      let res = await request({ url: `/api/city/name?lat=${location.latitude}&lng=${location.longitude}` });
      let { province, city, district, adcode } = res.result.addressComponent;
      currentLocal.province = province;
      currentLocal.city = city;
      currentLocal.region = district;
      currentLocal.areaCode = adcode;
    } else {
      currentLocal.noLocation = true;
    }
    uni.hideLoading();
  } catch (error) {
    currentLocal.noLocation = true;
    uni.hideLoading();
    return false;
  }
})
</script>

<style lang="scss" scoped>
.pages {
  background-color: #FFFFFF;

  .top-zhanwei {
    width: 750rpx;
    height: 368rpx;
  }

  .stit {
    position: sticky;
    top: 200rpx;
    z-index: 3;
    width: 750rpx;
    padding: 30rpx 30rpx 0;
    background: #FFFFFF;
  }

  .topfix {
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    padding: 30rpx 30rpx 0;
    background: linear-gradient(180deg, #FFEBDC 0%, #FFFFFF 100%);
    position: relative;
    z-index: 2;


    .btns {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      margin: 40rpx 0 0;

      .bb {
        text-align: center;

        .i1 {
          width: 96rpx;
          height: 96rpx;
        }

        .t1 {
          font-size: 26rpx;
          font-weight: 600;
          color: #2A2D3C;
          margin-top: 12rpx;
        }
      }
    }

    .top-t {
      display: flex;
      flex-direction: row;
      line-height: 40rpx;

      .icon {
        width: 152rpx;
        height: 40rpx;
      }


      .t2 {
        font-size: 24rpx;
        font-weight: 400;
        color: #50556A;
      }
    }
  }

  .section {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 96rpx;
    border-bottom: 1rpx solid #EEE;

    .t1 {
      font-size: 32rpx;
      font-weight: 600;
      color: #262937;
    }

    .t2 {
      font-size: 26rpx;
      font-weight: 400;
      color: #797E8E;
      margin-left: auto;
    }

    .map {
      width: 48rpx;
      height: 48rpx;
      margin-left: 8rpx;
    }
  }



  .item {
    width: 100%;
    height: 156rpx;
    display: flex;
    background: #fff;
    margin-top: 30rpx;
    flex-direction: row;
    padding: 0 30rpx;

    .msg {
      padding-left: 16rpx;
      flex: 1;

      .name {
        font-size: 32rpx;
        font-weight: 500;
        color: #262937;
        line-height: 44rpx;
        width: 100%;
      }

      .address {
        width: 100%;
        display: flex;
        font-size: 26rpx;
        font-weight: 400;
        color: #7A7E8F;
        margin-top: 16rpx;
      }
    }
  }
}

.zhanwei {
  height: calc(env(safe-area-inset-bottom));
}

.mno-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20%;
  flex-direction: column;
  background: #fff;

  image {
    width: 160rpx;
    height: 160rpx;
    margin: 196rpx 0 24rpx;
  }

  .t1 {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #262937;
    margin-bottom: 20rpx;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 400;
    color: #aeb0bc;
    line-height: 44rpx;
  }

  .btn {
    width: 240rpx;
    height: 80rpx;
    background: #1975FF;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 48rpx;
    margin-bottom: 100rpx;
  }
}

.top {
  width: 750rpx;
  height: 400rpx;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 1;

  .backimg {
    width: 750rpx;
    height: 420rpx;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.warp {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 24rpx;

  .icon1 {
    width: 40rpx;
    height: 40rpx;
    margin-right: 14rpx;
  }

  .text {
    font-size: 34rpx;
    font-weight: 600;
    color: #000000;
  }

  .icon2 {
    width: 32rpx;
    height: 32rpx;
  }
}
</style>
