<template>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive :background="{ backgroundColor: 'rgba(255,255,255,0))' }" title="">
    </u-navbar>
    <swiper class="swiper-wrap" :indicator-dots="swiperOption.indicatorDots" :autoplay="swiperOption.autoplay"
      :interval="swiperOption.interval" :duration="swiperOption.duration">
      <swiper-item v-for="(item, index) in swiperList" :key="index">
        <view class="swiper-item">
          <image :src="item" mode="aspectFill" />
        </view>
      </swiper-item>
    </swiper>
    <view class="body">
      <view class="info-top">
        <view class="left" @click="intoDetail(info.id)">
          <view class="name">
            <view class="namess">{{ info.name }} </view>
          </view>
          <view class="tag">
            <text class="text">公立</text>
          </view>
          <view class="time">
            {{ info.servicetime }}
          </view>
        </view>
        <image class="phone" @click="callPhone" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone.png">
        </image>
      </view>
      <view class="address" @click="goDaohang">
        <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
        </image>
        <view class="add-content">
          <view class="lefec">
            <view class="t1">{{ info.address }}</view>
            <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="info.distance >= 1">距你{{ (info.distance * 1).toFixed(2)
                }}km</text>
              <text v-else class="distance">距你{{ info.distance * 1000 }}m</text>
            </template>
          </view>
          <view class="rsec">
            <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
            </image>
          </view>
        </view>
      </view>
      <view class="titiii">
        基础套餐
      </view>
      <template v-if="base.length">
        <view class="infoss" v-for="item in base" :key="item.id">
          <view class="left">
            <view class="t1">{{ item.name }}</view>
            <view class="t2">{{ item.object_list.map(item => item.name).join("+") }}</view>
            <view class="t2">费用：{{ item.price }}</view>
          </view>
          <u-checkbox v-model="item.checked" @change="checkChange(item.checked, item)"></u-checkbox>
        </view>
      </template>
      <view v-else class="empty">暂未设置基础套餐</view>
      <view class="titiii">
        附加项目
      </view>
      <template v-if="extra.length">
        <view class="infoss" v-for="item in extra" :key="item.id">
          <view class="left">
            <view class="t1">{{ item.name }}</view>
            <view class="t2">{{ item.object }}</view>
            <view class="t2">费用：{{ item.price }}</view>
          </view>
          <u-checkbox v-model="item.checked"></u-checkbox>
        </view>
      </template>
      <view v-else class="empty">暂无附加项目</view>
      <template v-if="isM">
        <view class="titiii">
          机构已预约基础套餐
        </view>
        <template v-if="schoolBase.length">
          <view class="infoss" v-for="item in schoolBase" :key="item.id">
            <view class="left">
              <view class="t1">{{ item.name }}</view>
              <view class="t2">{{ item.object.map(item => item.name).join("+") }}</view>
              <view class="t2">费用：{{ item.price }}</view>
            </view>
            <u-checkbox v-model="checked" :disabled="true"></u-checkbox>
          </view>
        </template>
        <view v-else class="empty">未设置</view>
        <view class="titiii">
          机构已预约附加项目
        </view>
        <template v-if="schoolObject.length">
          <view class="infoss" v-for="item in schoolObject" :key="item.id">
            <view class="left">
              <view class="t1">{{ item.name }}</view>
              <view class="t2">{{ item.object }}</view>
              <view class="t2">费用：{{ item.price }}</view>
            </view>
            <u-checkbox v-model="item.checked" :disabled="true"></u-checkbox>
          </view>
        </template>
        <view v-else class="empty">未设置</view>
      </template>
      <view style="height: 20rpx;"></view>
      <u-button type="primary" @click="goBook">确定</u-button>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { useLocalStore } from "@/stores/useLocalStore.js";
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request.js";
const currentLocal = useLocalStore();
const checked = ref(true)
let id = '';
const info = ref({
  name: "",
  distance: 0,
  address: "",
  lng: '',
  lat: '',
  servicetime: "",
  contact: ""
})
const swiperList = ref([])
let swiperOption = ref({
  indicatorDots: false,
  autoplay: false,
  interval: 2000,
  duration: 500,
});
function checkChange(v, i) {
  if (!v) {
    let sindex = base.value.findIndex(item => {
      return item.id == i.id;
    })
    for (let index = 0; index < base.value.length; index++) {
      const element = base.value[index];
      if (sindex == index) {
        continue
      } else {
        element.checked = false;
      }
    }
  }
}
const schoolBase = ref([])
const schoolObject = ref([]);
const isM = ref(false);
onLoad((option) => {
  id = option.id;
  if (option.isM == 1) {
    isM.value = true;
    let school_packInfo = uni.getStorageSync("school_packInfo")
    schoolObject.value = school_packInfo.object.map(item => {
      return {
        ...item,
        checked: true
      }
    });
    schoolBase.value = school_packInfo.package.map(item => {
      return {
        ...item,
        checked: true
      }
    });;
    getData();
  } else {
    getData();
  }
})
const base = ref([])
const extra = ref([])
function getPack() {
  request({
    url: "/api/healthservices/package/list",
    data: {
      hospital_id: id,
    }
  }).then(res => {
    if (res && res.list) {
      let b = res.list.filter(item => item.type == 1).map((item) => {
        return {
          ...item,
          checked: false,
        }
      });
      if (b.length) {
        base.value = b;
      }
      let e = res.list.find(item => item.type == 2);
      if (e) {
        extra.value = e.object_list.map((item) => {
          return {
            ...item,
            checked: false,
          }
        });
      }
    }
    uni.hideLoading();
  })
}
function getData() {
  uni.showLoading()
  request({
    url: "/api/healthservices/hospitalDetail",
    data: {
      id,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    try {
      let imgs = JSON.parse(res.images);
      if (imgs.length) {
        swiperList.value = imgs;
      } else {
        swiperList.value.push("https://obs.tuoyupt.com/yjy_mini/front_img/physical/yiyuan.png")
      }
    } catch (error) {
      swiperList.value.push("https://obs.tuoyupt.com/yjy_mini/front_img/physical/yiyuan.png")
    }
    info.value = res;
    getPack();
  })
}
function callPhone() {
  wx.makePhoneCall({
    phoneNumber: info.value.contact,
  });
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng),
    name: info.value.name,
    address: info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
function goBook() {
  if (isM.value) {
    let packInfo = {
      base: base.value.filter(item => item.checked),
      extra: extra.value.filter(item => item.checked)
    }
    uni.setStorageSync("physical_hospital_packInfo", packInfo);
    uni.navigateBack();
  } else {
    uni.showLoading();
    request({
      url: "/api/healthservices/examination/getTimeList",
      data: {
        hospital_id: info.value.id,
        week: 0
      }
    }).then(res => {
      uni.hideLoading();
      let packInfo = {
        base: base.value.filter(item => item.checked),
        extra: extra.value.filter(item => item.checked)
      }
      uni.setStorageSync("physical_hospital_packInfo", packInfo);
      uni.setStorageSync("physical_hospital_info", info.value);
      wx.navigateTo({
        url: `./book`,
      });
    }).catch(() => {
      uni.hideLoading();
    })
  }

}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;


  .swiper-wrap {
    width: 750rpx;
    height: 420rpx;

    .swiper-item {
      display: block;
      height: 420rpx;
      text-align: center;

      image {
        height: 100%;
        display: block;
        width: 100%;
      }
    }
  }

  .body {
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    margin-top: -40rpx;
    padding: 40rpx 30rpx;
    position: relative;
    z-index: 2;

    .info-top {
      display: flex;
      flex-direction: row;
      padding-top: 40rpx;

      .left {
        flex: 1;

        .name {
          width: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: row;

          .namess {
            font-size: 40rpx;
            font-weight: 500;
            color: #262937;
            max-width: 540rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }

        .rate {
          display: flex;
          flex-direction: row;

          .rtt {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .tag {
          display: flex;
          flex-wrap: wrap;
          overflow: hidden;
          margin-top: 14rpx;
          margin-bottom: 14rpx;
          min-height: 40rpx;

          .text {
            font-size: 20rpx;
            color: #262937;
            padding: 0 16rpx;
            margin-right: 8rpx;
            margin-bottom: 8rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            height: 40rpx;
            display: flex;
            align-items: center;
          }
        }

        .time {
          font-size: 28rpx;
          font-weight: 400;
          color: #7A7E8F;
        }
      }

      .phone {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
    }

    .address {
      width: 690rpx;
      height: 132rpx;
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      margin-top: 40rpx;

      .back {
        position: absolute;
        width: 690rpx;
        height: 132rpx;
        left: 0;
        top: 0;
        z-index: 1;
      }

      .add-content {
        width: 690rpx;
        height: 132rpx;
        display: flex;
        flex-direction: row;
        position: relative;
        z-index: 2;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;

        .lefec {
          .t1 {
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
          }

          .distance {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .rsec {
          width: 40rpx;
          height: 40rpx;

          .right {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .titiii {
      font-size: 32rpx;
      font-weight: 500;
      color: #292D3B;
      margin: 40rpx 0;
      font-weight: 600;
    }

    .empty {
      width: 690rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      border: 1rpx solid #EEEEEE;
      padding: 30rpx;
    }

    .infoss {
      width: 690rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      border: 1rpx solid #EEEEEE;
      padding: 30rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 12rpx;

      .left {
        flex: 1;

        .t1 {
          font-size: 32rpx;
          font-weight: 600;
          color: #262937;
        }

        .t2 {
          font-size: 20rpx;
          font-weight: 400;
          color: #262937;
          margin-top: 16rpx;
        }
      }


    }
  }
}
</style>