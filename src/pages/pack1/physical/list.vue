<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="婴幼儿体检记录">
    </u-navbar>
    <template v-if="child_list.length">
      <view class="top">
        <view class="left">
          <view class="t1">
            {{ child_info.name }}
          </view>
          <view class="t2">
            出生日期： {{ child_info.birth }}
          </view>
        </view>
        <view class="btn" @click="selectShow = true">
          <image class="sw" src="https://obs.tuoyupt.com/miniprogram/changzhi/sw.svg"></image>
          <text>切换</text>
        </view>
      </view>
      <u-select @confirm="confirmSelect" value-name="id" label-name="name" v-model="selectShow"
        :list="child_list"></u-select>
      <template v-if="list.length">
        <view class="item" @click="goDeatil(item)" v-for="item in list" :key="item.id">
          <view class="name">{{ item.bm_student.name }}</view>
          <view class="row">
            <view class="label">
              宝宝月龄：
            </view>
            <view class="value">
              {{ item.age }}
            </view>
          </view>
          <view class="row">
            <view class="label">
              体检时间：
            </view>
            <view class="value">
              {{ item.date }}
            </view>
          </view>
          <view class="row">
            <view class="label">
              体检门诊：
            </view>
            <view class="value">
              {{ item.hospital.name }}
            </view>
          </view>
        </view>
      </template>
      <u-empty v-else margin-top="400" text="暂无婴幼儿体检记录"
        src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png"></u-empty>
    </template>
    <u-empty v-else margin-top="400" text="暂未绑定婴幼儿"
      src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png"></u-empty>

  </view>
</template>
<script setup>
import { ref } from "vue";
import { onReachBottom, onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
onLoad(() => {
  getChild();
})
const child_list = ref([]);
const child_info = ref({
  id: 0,
  name: '',
  birth: '',
  id_card: '',
});
const selectShow = ref(false);
function confirmSelect(e) {
  let info = child_list.value.find(item => {
    return item.id == e[0].value;
  })
  child_info.value = info;
  getData();
}
function getChild() {
  request({
    url: "/api/healthservices/user/list",
  }).then(res => {
    child_list.value = res;
    if (res.length) {
      child_info.value = res[0];
      getData();
    }
  })
}
let page = 1;
const list = ref([])
let hasMore = true;
function getData() {
  uni.showLoading();
  request({
    url: "/api/healthservices/examinationreport/list",
    data: {
      per_page: 20,
      page: page,
      student_id:child_info.value.id
    },
    goLogin: true,
  }).then(res => {
    uni.hideLoading();
    if (res.length < 20) {
      hasMore = false;
    } else {
      hasMore = true;
    }
    list.value = res;
  })
}
onReachBottom(() => {
  if (hasMore) {
    page++;
    request({
      url: "/api/healthservices/examinationreport/list",
      data: {
        per_page: 20,
        page: page,
      }
    }).then(res => {
      uni.hideLoading();
      if (res.length < 20) {
        hasMore = false;
      } else {
        hasMore = true;
      }
      list.value.push(...res);
    })
  }
})
function goDeatil(item) {
  uni.navigateTo({
    url: `./report?id=${item.id}`,
  });
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .top {
    width: 100vw;
    height: 206rpx;
    padding: 24rpx 30rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .t1 {
      font-weight: 600;
      font-size: 52rpx;
      color: #000000;
      line-height: 74rpx;
      margin-bottom: 20rpx;
    }

    .t2 {
      font-weight: 400;
      font-size: 26rpx;
      color: #7A7E8E;
      line-height: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn {
      width: 200rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #1975FF;
      line-height: 36rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }

  .item {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;

    .name {
      font-size: 34rpx;
      font-weight: 500;
      color: #3D414E;
      margin-bottom: 40rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }
    }
  }
}
</style>