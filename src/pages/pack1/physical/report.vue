<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="体检报告">
    </u-navbar>
    <view class="section">
      <view class="top">
        <view class="info">
          <view class="t1">
            <view class="name">{{ info.bm_student.name }}</view>
            <view class="sex">{{ info.bm_student.sex == 1 ? '男' : "女" }}</view>
          </view>
          <view class="arr">
            出生日期：{{ info.bm_student.birth }}
          </view>
        </view>
        <view class="right">
          <view class="r1" :class="{ err: info.advise !== 1 }">
            {{ info.advise == 1 ? '体检合格' : '暂缓入园' }}
          </view>
          <view class="r2">
            医师意见
          </view>
        </view>
      </view>
      <view class="infos">
        <view class="row">
          <view class="label">体检门诊：</view>
          <view class="value">{{ info.hospital.name }}</view>
        </view>
        <view class="row">
          <view class="label">体检日期：</view>
          <view class="value">{{ info.date }} {{ info.age }} </view>
        </view>
        <view class="row">
          <view class="label">联系电话：</view>
          <view class="value">{{ info.hospital.contact }}</view>
        </view>
      </view>
    </view>
    <view class="section">
      <view class="tops">
        <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/erkea.png" class="icon">
        </image>
        <view class="tt">
          体格检查
        </view>
        <!-- <view class="arrow">
          <u-icon name="arrow-up"></u-icon>
        </view> -->
      </view>
      <view class="item">
        <view class="r1">
          <view class="r1l">
            体重/kg
          </view>
          <view class="r1r">
            <text class="r1r1">评价：</text>
            <text class="r1r1">{{ info.weight_evaluate?.name }}</text>
          </view>
        </view>
        <view class="r2">
          {{ info.weight ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          <view class="r1l">
            身高/cm
          </view>
          <view class="r1r">
            <text class="r1r1">评价：</text>
            <text class="r1r1">{{ info.height_evaluate?.name ?? '-' }}</text>
          </view>
        </view>
        <view class="r2">
          {{ info.height ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          眼
        </view>
        <view class="r2">
          左眼：{{ info.left_eye ?? '-' }} 右眼：{{ info.right_eye ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          视力
        </view>
        <view class="r2">
          <text class="r22"> 左眼：{{ info.left_vision ?? '-' }}</text>
          <text class="r22"> S值：{{ formatNum(info.left_s_vision) }}</text>
          <text class="r22"> C值：{{ formatNum(info.left_c_vision) }}</text>
        </view>
        <view class="r2">
          <text class="r22"> 右眼：{{ info.right_vision ?? '-' }}</text>
          <text class="r22"> S值：{{ formatNum(info.right_s_vision) }}</text>
          <text class="r22"> C值：{{ formatNum(info.right_c_vision) }}</text>
        </view>
      </view>
      <view class="item">
        <view class="r1">
          牙齿数量
        </view>
        <view class="r2">
          {{ info.tooth_num ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          龋齿数量
        </view>
        <view class="r2">
          <view class="shizi" v-if="info?.decayed_tooth_num?.length">
            <view class="shizirows shizirows1">
              <view class="items items1">
                <view class="span" v-for="(item, index) in tooths.slice(0, 5)" :key="item"
                  :class="{ active: checkTooth(index, 0) }">
                  {{ item }}
                </view>
              </view>
              <view class="items">
                <view class="span" v-for="(item, index) in tooths.slice(5, 10)" :key="item"
                  :class="{ active: checkTooth(index, 1) }">
                  {{ item }}
                </view>
              </view>
            </view>
            <view class="shizirows">
              <view class="items items1">
                <view class="span" v-for="(item, index) in tooths.slice(10, 15)" :key="item"
                  :class="{ active: checkTooth(index, 2) }">
                  {{ item }}
                </view>
              </view>
              <view class="items">
                <view class="span" v-for="(item, index) in tooths.slice(15, 20)" :key="item"
                  :class="{ active: checkTooth(index, 3) }">
                  {{ item }}
                </view>
              </view>
            </view>
          </view>
          <view v-else>
            0
          </view>
        </view>
      </view>
      <view class="item">
        <view class="r1">
          头颅
        </view>
        <view class="r2">
          {{ info.head ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          耳
        </view>
        <view class="r2">
          左：{{ info.left_ear ?? '-' }} 右：{{ info.right_ear ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          胸廓
        </view>
        <view class="r2">
          {{ info.chest ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          皮肤
        </view>
        <view class="r2">
          {{ info.skin ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          脊柱四肢
        </view>
        <view class="r2">
          {{ info.legs ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          咽部
        </view>
        <view class="r2">
          {{ info.pharynx ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          心肺
        </view>
        <view class="r2">
          {{ info.heart ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          肝脾
        </view>
        <view class="r2">
          {{ info.liver ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          外生殖器
        </view>
        <view class="r2">
          {{ info.external ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          其他
        </view>
        <view class="r2">
          {{ info.other ?? '-' }}
        </view>
      </view>
    </view>
    <view class="section">
      <view class="tops">
        <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/erkea.png" class="icon">
        </image>
        <view class="tt">
          辅助检查
        </view>
      </view>
      <view class="item">
        <view class="r1">
          血红蛋白（Hb）
        </view>
        <view class="r2">
          {{ info.hb ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          丙氨酸氨基转移酶（ALT）
        </view>
        <view class="r2">
          {{ info.alt ?? '-' }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          其他
        </view>
        <view class="r2">
          {{ info.support_other ?? '-' }}
        </view>
      </view>
    </view>
    <view class="section" v-if="info.advise == 1">
      <view class="tts">文件下载</view>
      <view class="fitem" v-for="item in files" :key="item.name" @click="down(item)">
        <view class="icon">
          <image class="ic" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/PDF.png">
          </image>
        </view>
        <view class="ins">
          <view>{{ item.name }}</view>
          <view v-if="item.size">{{ Number(item.size) > 1024 ? `${(Number(item.size) / 1024).toFixed(2)}K` :
            `${item.size}Bytes`
            }}</view>
        </view>
        <view class="btn" @click="down">下载</view>
      </view>
      <view class="fitem" v-for="item in files1" :key="item.name" @click="down(item)">
        <view class="icon">
          <image class="ic" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/PDF.png">
          </image>
        </view>
        <view class="ins">
          <view>{{ item.name }}</view>
          <view v-if="item.size">{{ Number(item.size) > 1024 ? `${(Number(item.size) / 1024).toFixed(2)}K` :
            `${item.size}Bytes`
            }}</view>
        </view>
        <view class="btn" @click="down">下载</view>
      </view>
      <view class="fitem" v-for="item in files2" :key="item.name" @click="down(item)">
        <view class="icon">
          <image class="ic" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/PDF.png">
          </image>
        </view>
        <view class="ins">
          {{ item.name }}
        </view>
        <view class="btn" @click="down">下载</view>
      </view>
    </view>
    <view class="footer-zhanwei"></view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
const tooths = ref(['Ⅴ', 'Ⅳ', 'Ⅲ', 'Ⅱ', 'Ⅰ', 'Ⅰ', 'Ⅱ', 'Ⅲ', 'Ⅳ', 'Ⅴ', 'Ⅴ', 'Ⅳ', 'Ⅲ', 'Ⅱ', 'Ⅰ', 'Ⅰ', 'Ⅱ', 'Ⅲ', 'Ⅳ', 'Ⅴ'])
function checkTooth(index, off) {
  let sIndex = index + (off * 5);
  let mIndex = info.value.decayed_tooth_num.findIndex(item => {
    return item == sIndex
  })
  return mIndex > -1;
}
let id = '';
const info = ref({
  "id": -1,
  "bm_student_id": 1,
  "student_id": 1,
  "class_id": 1,
  "school_id": 2,
  "hospital_id": 1,
  "examination_record_id": 1,
  "weight": 0,//体重kg
  "weight_remark": 1,//体重评价:0上，1中，2下
  "height": 0,//身高cm
  "height_remark": 1, //身高评价: 0上，1中，2下
  "skin": "",//皮肤
  "left_eye": "",//左眼
  "right_eye": "",//右眼
  "left_vision": "",//左眼视力
  "right_vision": "",//右眼视力
  "tooth_num": 4,//牙齿数量
  "decayed_tooth_num": [],//龋齿数量
  "head": "",//头颅
  "chest": "",//胸廓
  hb: "",
  left_ear: "",
  right_ear: "",
  support_other: "",
  alt: "",
  "legs": "",//四肢
  "pharynx": "",//咽部
  "heart": "",//心肺
  "liver": "",//肝脾
  "external": "",//外生殖器
  "other": "",//其他
  "result_image": "",//结果图片
  "result": "",//检查结果
  "advise": 1,//医生意见：0暂缓入园，1体检合格
  "doctor_sign": "",//医生签名
  "date": "",//体检日期
  "created_at": "",
  "updated_at": "",
  "age": "",
  "bm_student": {
    "id": 1,
    "name": "",
    "birth": ""
  },
  height_evaluate: {
    name: ""
  },
  weight_evaluate: {
    name: ""
  },
  "hospital": {
    "id": 1,
    "name": ""
  }
})
let age = "";
let sex = "";
onLoad((option) => {
  id = option.id;
  age = option.age;
  sex = option.sex;
  getData();
})
function formatNum(num) {
  if (/^-?\d+(\.\d+)?$/.test(num)) {
    if (num > 0) {
      return '+' + num;
    } else {
      return num
    }
  } else {
    return "-"
  }
}
const files = ref([]);
const files1 = ref([]);
const files2 = ref([]);
function getData() {
  uni.showLoading()
  request({
    url: "/api/healthservices/examinationreport/detail",
    data: {
      id, age, sex
    }
  }).then(res => {
    if (res.decayed_tooth_num) {
      res.decayed_tooth_num = res.decayed_tooth_num.split(",");
    }
    info.value = res;
    if (info.value.advise != 1) {
      uni.showModal({
        title: "提示",
        content: "宝宝体检结果异常，请家长咨询医生",
        showCancel: false
      })
    }
    try {
      if (res.laboratory_report) {
        let laboratory_report = res.laboratory_report?.split(',');
        files2.value = laboratory_report.map(((item) => {
          let fileName = item.substring(item.lastIndexOf("/") + 1);
          let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
          return {
            name: fileName,
            url: item,
            fileExtension
          }
        }))
      }
    } catch (error) {
      console.log(error)
    }
    try {
      if (res.result_image) {
        let imgs = res.result_image?.split(',');
        let sizes = res.result_image_size?.split(',') ?? [];
        files.value = imgs.map(((item, index) => {
          let fileName = item.substring(item.lastIndexOf("/") + 1);
          let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
          let size = sizes[index] ? sizes[index] : "";
          return {
            name: fileName,
            url: item,
            size,
            fileExtension
          }
        }))
      }
      if (res.submit_image) {
        let imgs = res.submit_image?.split(',');
        let sizes = res.submit_image_size?.split(',') ?? [];
        files1.value = imgs.map(((item, index) => {
          let fileName = item.substring(item.lastIndexOf("/") + 1);
          let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
          let size = sizes[index] ? sizes[index] : "";
          return {
            name: fileName,
            url: item,
            size,
            fileExtension
          }
        }))
      }
    } catch (error) {
      console.error(error)
    }
    uni.hideLoading();
  })
}
function down(item) {
  window.open(item.url)
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;
    padding: 30rpx;

    .infos {
      .row {
        display: flex;
        flex-direction: row;
        margin-top: 24rpx;

        .label {
          width: 130rpx;
          height: 36rpx;
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8E;
          line-height: 36rpx;
        }

        .value {
          flex: 1;
          font-size: 26rpx;
          text-align: right;
          font-weight: 400;
          color: #3D414E;
          line-height: 36rpx;
        }
      }
    }

    .tops {
      display: flex;
      flex-direction: row;
      align-items: center;

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 12rpx;
      }

      .tt {
        font-size: 32rpx;
        font-weight: 600;
        color: #282B39;
        flex: 1;
      }
    }

    .item {
      width: 100%;
      padding-bottom: 30rpx;
      border-bottom: 1rpx solid #EEE;
      margin-top: 30rpx;

      .r1 {
        font-size: 28rpx;
        font-weight: 500;
        color: #282B39;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .r1r1 {
          font-size: 24rpx;
          font-weight: 400;
          color: #797D8D;
        }

        .r1r2 {
          font-size: 24rpx;
          font-weight: 400;
          color: #282B39;
          margin-left: 16rpx;
        }

        .ac {
          background: rgba(25, 117, 255, 0);
          border-radius: 8rpx;
          border: 1rpx solid #1975FF;
          width: 40rpx;
          height: 40rpx;
          text-align: center;
          line-height: 40rpx;
          color: #1975FF;
          display: inline-block;
        }
      }

      .r2 {
        font-size: 28rpx;
        font-weight: 400;
        color: #797D8D;

        .r22 {
          display: inline-block;
          min-width: 180rpx;
        }

        .shizi {
          width: 100%;
          height: 80rpx;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin-top: 20rpx;

          .shizirows1 {
            border-bottom: 1rpx solid #ccc;
          }

          .shizirows {
            display: flex;
            flex-direction: row;
            width: 100%;

            .items1 {
              border-right: 1rpx solid #ccc;
            }

            .items {
              display: flex;
              flex-direction: row;
              width: 50%;
              justify-content: space-around;

              .span {
                cursor: pointer;
                color: #ccc;
              }

              .span.active {
                color: #000000;
              }
            }
          }
        }
      }
    }

    .tts {
      font-size: 32rpx;
      font-weight: 600;
      color: #3D414E;
    }

    .fitem {
      width: 100%;
      height: 128rpx;
      background: #FFFFFF;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 20rpx;

      .icon {
        width: 80rpx;
        height: 80rpx;
        background: #F1F5F8;
        border-radius: 16rpx;
        text-align: center;
        line-height: 106rpx;

        .ic {
          width: 48rpx;
          height: 48rpx;
        }
      }

      .ins {
        flex: 1;
        padding-left: 18rpx;
        max-width: 418rpx;
        word-break: break-all;
      }

      .btn {
        width: 96rpx;
        height: 50rpx;
        background: #FFFFFF;
        border-radius: 26rpx;
        border: 2rpx solid #EEEEEE;
        text-align: center;
        line-height: 50rpx;
        color: #797D8D;
        font-size: 24rpx;
      }
    }

    .top {
      width: 100%;
      height: 128rpx;
      display: flex;
      flex-direction: row;
      margin: 0 auto;
      border-bottom: 1rpx solid #eee;

      .avter {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }

      .info {
        padding-left: 24rpx;
        flex: 1;

        .t1 {
          display: flex;
          flex-direction: row;
          align-items: center;

          .name {
            font-size: 32rpx;
            font-weight: 600;
            color: #282C39;
            line-height: 56rpx;
          }

          .sex {
            width: 32rpx;
            height: 32rpx;
            background: #D6EDFE;
            border-radius: 6rpx;
            text-align: center;
            line-height: 32rpx;
            font-size: 20rpx;
            font-weight: 400;
            color: #0275FF;
            margin-left: 10rpx;
          }
        }


        .arr {
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8D;
          margin-top: 8rpx;
        }
      }

      .right {
        text-align: center;

        .r1 {
          font-size: 32rpx;
          font-weight: 600;
          color: #31CF7E;
        }

        .err {
          color: red;
        }

        .r2 {
          font-size: 24rpx;
          font-weight: 400;
          color: #797D8D;
        }
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}
</style>