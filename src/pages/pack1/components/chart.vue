<template>
  <l-echart ref="chartRef" customStyle="height: '900rpx'" @finished="init"></l-echart>
</template>
<script setup>
import LEchart from './lime-echart/l-echart.vue';
import { ref } from "vue";
import * as echarts from 'echarts/core';
import { GridComponent, MarkAreaComponent, TitleComponent } from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { watch } from 'vue';
echarts.use([GridComponent, LineChart, TitleComponent, CanvasRenderer, MarkAreaComponent]);
const props = defineProps({
  data: {
    type: Object,
    default: function () {
      return {
        xData: [],
        yData: [],
        baseDate: [],
        title: "",
        interval: 1,
        min: 40,
        max: 100,
        name: ""
      }
    }
  }
})
const emit = defineEmits(['click'])
const chartRef = ref(null);
function init() {
  chartRef.value.init(echarts, chart => {
    watch(() => props.data, (val) => {
      try {
        let optionb = uni.$u.deepClone(option);
        optionb.series[0].data = val.yData;
        for (let i = 1; i < 7; i++) {
          optionb.series[i].data = val.baseDate[i - 1];
        }
        optionb.title.text = val.title;
        optionb.xAxis.data = val.xData;
        optionb.yAxis.name = val.name;
        optionb.yAxis.min = val.min;
        optionb.yAxis.startValue = val.min;
        optionb.yAxis.max = val.max;
        optionb.yAxis.interval = val.interval;
        chart.setOption(optionb);
      } catch (error) {
        console.error(error)
      }
    }, {
      deep: true,
      immediate: true
    })
  });
}
let option = {
  title: {
    text: ''
  },
  grid: {
    left: 30,
    top: 40,
    right: 30,
    bottom: 20,
  },
  xAxis: {
    type: 'category',
    data: [],
    minInterval: 3,
    axisLine: {
      show: false
    },
    splitLine: {
      show: true
    },
    axisTick: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    min: 45,
    interval: 1,
    startValue: 45,
    position: 'left',
    splitLine: {
      lineStyle: {
        type: "dashed"
      }
    },
  },
  series: [
    {
      data: [],
      type: 'line',
      connectNulls: true,
    },
    {
      data: [],
      type: 'line',
      name: "下",
      symbol: 'none',
      color: "#aaaaaa",
    },
    {
      data: [],
      type: 'line',
      name: "中下",
      symbol: 'none',
      color: "#aaaaaa",
    },
    {
      data: [],
      type: 'line',
      name: "中-",
      symbol: 'none',
      color: "#aaaaaa",
    },
    {
      data: [],
      type: 'line',
      name: "中+",
      symbol: 'none',
      color: "#aaaaaa",
    },
    {
      data: [],
      type: 'line',
      name: "中上",
      symbol: 'none',
      color: "#aaaaaa",
    },
    {
      data: [],
      type: 'line',
      name: "上",
      symbol: 'none',
      color: "#aaaaaa",
    },
    {
      data: [],
      type: 'line',
      name: "上+",
      symbol: 'none',
      color: "#aaaaaa",
    }
  ]
};
</script>