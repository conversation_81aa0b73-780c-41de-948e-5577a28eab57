<template>
    <u-navbar  :border-bottom="false"  title="入托补贴">
    </u-navbar>
    <view v-if="list.length">
        <view class="section" @click="goForm(item)" v-for="item in list" :key="item.id">
            <view class="t1">{{ item.title }}</view>
            <view class="t2">开始时间：{{ item.start_time }} 结束时间：{{ item.end_time }}</view>
        </view>
    </view>
    <u-empty v-else style="margin-top: 20vh;" text="暂无补贴政策"></u-empty>
</template>
<script setup>
import { onMounted, ref } from "vue";
import request from '@/request';
import dayjs from "dayjs";
const list = ref([]);
function getPre() {
    request({
        url: "/api/goveramissionsubsidy/choice",
        method: "get",
    }).then(res => {
        list.value = res;
    })
}
onMounted(() => {
    getPre()
})
const now = dayjs();
function goForm(item) {
    if (now.isBefore(item.end_time) && now.isAfter(item.start_time)) {
        uni.setStorageSync("goveramissionsubsidy", item)
        uni.navigateTo({
            url: "./form"
        })
    } else {
        uni.showToast({
            title: "未在申请时间内",
            icon: "none"
        })
    }
}
</script>
<style lang="scss" scoped>
.section {
    width: 690rpx;
    padding: 30rpx;
    border: 1rpx solid #FF7373;
    border-radius: 24rpx;
    margin: 30rpx auto;

    .t1 {
        font-size: 32rpx;
        color: #000000;
        font-family: 600;
    }

    .t2 {
        font-size: 24rpx;
        color: #666666;
        font-family: 600;
    }
}
</style>