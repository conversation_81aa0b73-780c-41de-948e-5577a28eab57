<template>
  <view class="page">
    <u-navbar :border-bottom="true" title="填报信息">
    </u-navbar>
    <view class="top">
      <u-steps active-color="#FF7373" :list="numList" :current="current"></u-steps>
    </view>
    <u-form :label-style="labelStyle" :rightStyle="rightStyle" :model="formData" label-width="316rpx" ref="formRef">
      <view class="section">
        <view class="fhead">
          <view class="dot"></view>
          <view class="t">申报补贴年度</view>
        </view>
        <u-form-item label="年度补贴范围" prop="year_name">
          {{ range_start_time }}至{{ range_end_time }}
        </u-form-item>
      </view>
      <view class="section">
        <view class="fhead">
          <view class="dot"></view>
          <view class="t">入托婴幼儿信息</view>
        </view>
        <u-form-item required label="姓名" prop="student_name" v-if="isYouke">
          <u-input v-model="formData.student_name" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item required label="姓名" prop="student_name" v-else>
          <view class="item-right">
            <u-input class="right-i" v-model="formData.student_name" :disabled="true" @click="selectStudent" placeholder="请输入" />
            <view class="sousuo" @click="selectStudent">
              <u-icon name="arrow-right" color="#333333"></u-icon>
            </view>
          </view>
        </u-form-item>
        <u-form-item required label="托育机构" prop="school_name">
          <view class="item-right">
            <u-input class="right-i" v-model="formData.school_name" @click="goShoolSea" disabled placeholder="请选择" />
            <view class="sousuo" @click="goShoolSea">
              <u-icon name="arrow-right" color="#333333"></u-icon>
              搜索
            </view>
          </view>
        </u-form-item>
        <u-form-item label="身份证件类型">
          身份证
        </u-form-item>
        <u-form-item required label="身份证件号码" prop="student_card">
          <template v-if="isYouke">
            <u-input v-model="formData.student_card" :disabled="!canEdit" @blur="getBirth" placeholder="请输入" />
          </template>
          <template v-else>
            <u-input v-model="formData.student_card" v-if="!canChangeChild" @click="showError" :disabled="true"
              placeholder="请输入" />
            <u-input v-model="formData.student_card" v-else :disabled="!canEdit" @blur="getBirth" placeholder="请输入" />
          </template>
        </u-form-item>
        <u-form-item required label="出生日期" prop="student_birth">
          <u-input v-model="formData.student_birth" @click="onTimeShow" disabled placeholder="请选择" />
        </u-form-item>
        <u-form-item required label="孩次" prop="child_name">
          <u-input v-model="formData.child_name" @click="onSelectShow1" disabled placeholder="请选择" />
        </u-form-item>
        <u-form-item required label="服务合同" prop="service_contract_s">
          <u-input v-model="formData.service_contract_s" disabled @click="goUplod(1)" placeholder="请上传" />
        </u-form-item>
        <u-form-item required label="入托收费凭证" prop="fee_voucher_s">
          <u-input v-model="formData.fee_voucher_s" disabled @click="goUplod(2)" placeholder="请上传" />
        </u-form-item>
      </view>
      <view class="section">
        <view class="fhead">
          <view class="dot"></view>
          <view class="t">入托补贴发放途径</view>
        </view>
        <u-form-item required label="开户行名称" prop="opening_bank">
          <u-input v-model="formData.opening_bank" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item required label="持卡人姓名" prop="cardholder_name">
          <u-input v-model="formData.cardholder_name" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item required label="银行卡号" prop="bank_card">
          <u-input v-model="formData.bank_card" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
      </view>
    </u-form>
    <view class="btn" @click="goNext">下一页</view>
  </view>
  <u-select @confirm="confirmSelect2" v-model="selectShow2" :list="selectEumn2"></u-select>
  <u-select @confirm="confirmSelect1" v-model="selectShow1" :list="selectEumn1"></u-select>
  <u-picker mode="time" v-model="timeShow" :params="params" @confirm="timeConfirm"></u-picker>
</template>
<script setup>
import { onLoad, onReady } from "@dcloudio/uni-app";
import { computed, ref } from 'vue';
import { useUserStore } from "@/stores/userInfo.js";
import request from '@/request';
import dayjs from "dayjs";
const userInfo = useUserStore();
const range_start_time = ref('');
const range_end_time = ref('');
let range_latest_birth = "";
const formData = ref({
  school_id: "",
  start_time: "",
  end_time: "",
  time: ['2023-09-01', '2024-08-31'],
  year_name: "",
  student_name: "",
  school_name: "",
  student_card: "",
  student_birth: "",
  child: "",
  child_name: "",
  service_contract: "",
  fee_voucher: "",
  service_contract_s: "",
  fee_voucher_s: "",
  opening_bank: "",
  cardholder_name: "",
  bank_card: "",
});
const formRef = ref(null);
const rules = {
  year: [
    {
      required: true,
      message: '请选择补贴年度',
      trigger: 'blur',
    }
  ],
  student_name: [
    {
      required: true,
      message: '请输入学生姓名',
      trigger: 'blur',
    }
  ],
  school_name: [
    {
      required: true,
      message: '请选择托育机构',
      trigger: 'blur',
    }
  ],
  student_card: [
    {
      required: true,
      message: '请输入身份证件号码',
      trigger: 'blur',
    }, {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    }
  ],
  student_birth: [
    {
      required: true,
      message: '请选择出生日期',
      trigger: 'blur',
    }
  ],
  child: [
    {
      required: true,
      message: '请选择孩次',
      trigger: 'blur',
    }
  ],
  service_contract_s: [
    {
      required: true,
      message: '请上传服务合同',
      trigger: 'blur',
    }
  ],
  fee_voucher_s: [
    {
      required: true,
      message: '请上传入托收费凭证',
      trigger: 'blur',
    }
  ],
  opening_bank: [
    {
      required: true,
      message: '请输入开户行',
      trigger: 'blur',
    }
  ],
  cardholder_name: [
    {
      required: true,
      message: '请输入持卡人姓名',
      trigger: 'blur',
    }
  ],
  bank_card: [
    {
      required: true,
      message: '请输入银行卡号',
      trigger: 'blur',
    }, {
      pattern: /^([1-9]{1})(\d{15}|\d{16}|\d{18})$/,
      message: '请输入正确的银行卡号',
      trigger: 'blur',
    }
  ],
}
onReady(() => {
  formRef.value.setRules(rules);
})
const isDetail = ref(0);
const canEdit = ref(true);
const isYouke = ref(true);
const canChangeChild = ref(true);
onLoad(async (option) => {
  uni.$on("childcare-subsidies-upload", (data) => {
    if (data.type == 1) {
      formData.value.service_contract = data.imgs;
      formData.value.service_contract_s = '已上传';
    } else {
      formData.value.fee_voucher = data.imgs;
      formData.value.fee_voucher_s = '已上传';
    }
  })
  uni.$on("selectSchool", (data) => {
    formData.value.school_id = data.id;
    formData.value.school_name = data.name;
    checkCard();
  })
  if (option.isDetail == 1) {
    isDetail.value = 1;
    let data = uni.getStorageSync("childcare-subsidies");
    formData.value.school_id = data.school_id;
    formData.value.school_name = data.school_name;
    formData.value.student_name = data.student_name;
    formData.value.start_time = data.start_time;
    formData.value.end_time = data.end_time;
    formData.value.time = `${formData.value.start_time}至${formData.value.end_time}`
    formData.value.student_card = data.student_card;
    formData.value.student_birth = data.student_birth;
    formData.value.child = data.child;
    if (data.child == 2) {
      formData.value.child_name = '二孩';
    }
    if (data.child == 3) {
      formData.value.child_name = '三孩及以上';
    }
    formData.value.service_contract = data.service_contract;
    formData.value.service_contract_s = "已上传";
    formData.value.fee_voucher = data.fee_voucher;
    formData.value.fee_voucher_s = "已上传";
    formData.value.opening_bank = data.opening_bank;
    formData.value.cardholder_name = data.cardholder_name;
    formData.value.bank_card = data.bank_card;
    canEdit.value = data.approval_status == 0 || data.approval_status == 2 || data.approval_status == 3;
    // 未提交可修改学生和机构信息 提交后不可修改
    canChangeChild.valid = data.is_submit == 1 ? true : false;
    let info = uni.getStorageSync("childcare-amissionsubsidy");
    range_start_time.value = info.range_start_time;
    range_end_time.value = info.range_end_time;
    range_latest_birth = info.range_latest_birth;
    if (userInfo.student_id) {
      isYouke.value = false;
    }
  } else {
    let info = uni.getStorageSync("goveramissionsubsidy");
    range_start_time.value = info.range_start_time;
    range_end_time.value = info.range_end_time;
    range_latest_birth = info.range_latest_birth;
    if (userInfo.student_id) {
      isYouke.value = false;
      request({
        url: `/api/parent/studentphoto/studentdetail/${userInfo.student_id}`
      }).then(res => {
        formData.value.student_card = res.id_code ? res.id_code : '';
        formData.value.student_name = res.name ? res.name : '';
        formData.value.school_name = res.school_name ? res.school_name : '';
        formData.value.school_id = res.school_id ? res.school_id : '';
        getBirth();
      })
    } else {
      isYouke.value = true;
    }
  }
})
const canNext = ref(true);
function checkCard() {
  if (isDetail.value != 1) {
    if (formData.value.student_card && formData.value.school_id) {
      request({
        url: `/api/parent/goveramissionsubsidy/check`,
        data: {
          student_card: formData.value.student_card,
          school_id: formData.value.school_id
        }
      }).then(() => {
        canNext.value = true;
      }).catch(() => {
        canNext.value = false;
      })
    }

  }
}
function getBirth() {
  let pattern1 = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (pattern1.test(formData.value.student_card)) {
    formData.value.student_birth = `${formData.value.student_card.substring(6, 10)}-${formData.value.student_card.substring(10, 12)}-${formData.value.student_card.substring(12, 14)}`;
    checkBirth(true);
  }
}
function showError() {
  uni.showToast({
    title: `婴幼儿信息不可修改`,
    icon: 'none',
    duration: 3000
  })
}
function goShoolSea() {
  if (!canChangeChild.value) {
    uni.showToast({
      title: `机构信息不可修改`,
      icon: 'none',
      duration: 3000
    })
    return
  }
  uni.navigateTo({
    url: `./school`
  })
}
function selectStudent() {
  if (canEdit.value) {
    selectShow2.value = true
  }
}
const selectShow2 = ref(false);
const selectEumn2 = computed(() => {
  return userInfo.student_arr.map(item => {
    return {
      label: item.name,
      value: item.id
    }
  })
})
function confirmSelect2(e) {
  if (!canChangeChild.value) {
    uni.showToast({
      title: `婴幼儿信息不可修改`,
      icon: 'none',
      duration: 3000
    })
    return
  }
  request({
    url: `/api/parent/studentphoto/studentdetail/${e[0].value}`
  }).then(res => {
    formData.value.student_card = res.id_code ? res.id_code : '';
    formData.value.student_name = res.name ? res.name : '';
    formData.value.school_name = res.school_name ? res.school_name : '';
    formData.value.school_id = res.school_id ? res.school_id : '';
    getBirth()
  })
}
const selectShow1 = ref(false);
function onSelectShow1() {
  if (canEdit.value) {
    selectShow1.value = true
  }
}
const selectEumn1 = ref([{
  label: "二孩",
  value: 2
}, {
  label: "三孩及以上",
  value: 3
}])
function confirmSelect1(e) {
  formData.value.child = e[0].value;
  formData.value.child_name = e[0].label;
}
const params = {
  year: true,
  month: true,
  day: true,
  hour: false,
  minute: false,
  second: false
}
// 时间选择
const timeShow = ref(false);
function onTimeShow() {
  if (canEdit.value) {
    timeShow.value = true
  }
}
function checkBirth(checkC = false) {
  let bir = dayjs(formData.value.student_birth);
  if (!bir.isAfter(range_latest_birth, 'day')) {
    uni.showToast({
      title: `出生日期不符合申报范围,仅限${range_latest_birth}后出生申报`,
      icon: 'none',
      duration: 3000
    })
  } else {
    if (checkC) {
      checkCard();
    }
  }
}
function timeConfirm(e) {
  formData.value.student_birth = `${e.year}-${e.month}-${e.day}`;
  checkBirth();
}

function goUplod(type) {
  uni.navigateTo({
    url: `./upload?canEdit=${canEdit.value ? 1 : 0}&type=${type}&imgs=${type == 1 ? formData.value.service_contract : formData.value.fee_voucher}`
  })
}
const current = ref(0);
const labelStyle = {
  paddingLeft: "30rpx"
}
const rightStyle = {
  paddingRight: "30rpx"
}
function goNext() {
  if (isDetail.value != 1 && !canNext.value) {
    uni.showToast({
      title: `该学生已申请`,
      icon: 'none',
      duration: 3000
    })
    return
  }
  formRef.value.validate(valid => {
    if (valid) {
      let bir = dayjs(formData.value.student_birth);
      if (bir.isAfter(range_latest_birth, 'day')) {
        uni.setStorageSync("childcare-subsidies-form1", formData.value)
        uni.navigateTo({
          url: "./form1?isDetail=" + isDetail.value
        })
      } else {
        uni.showToast({
          title: `出生日期不符合申报范围,仅限${range_latest_birth}后出生申报`,
          icon: 'none',
          duration: 3000
        })
      }
    }
  })
}
const numList = [{
  name: '婴幼儿信息'
}, {
  name: '父母信息'
}, {
  name: '证件信息'
}]; 
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #F6F8FB;

  .top {
    width: 750rpx;
    height: 160rpx;
    background: #FFFFFF;
    padding-top: 60rpx;
  }

  .section {
    width: 750rpx;
    background: #FFFFFF;
    margin-top: 24rpx;

    .sousuo {
      width: 120rpx;
      text-align: center;
      color: #2b85e4;
    }

    ::v-deep.u-form-item--left__content--required {
      left: 12rpx;
    }

    .item-right {
      display: flex;
      flex-direction: row;
      width: 100%;

      .right-i {
        flex: 1;
      }
    }

    .fhead {
      padding: 0 30rpx;
      height: 104rpx;
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-bottom: 1rpx solid #EEEEEE;

      .dot {
        width: 8rpx;
        height: 32rpx;
        border-radius: 4rpx;
        background: #FF7373;
      }

      .t {
        font-weight: 600;
        font-size: 32rpx;
        color: #111111;
        margin-left: 24rpx;
      }
    }
  }

  .btn {
    width: 690rpx;
    height: 88rpx;
    background: #FF7373;
    border-radius: 8rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 88rpx;
    text-align: center;
    font-style: normal;
    margin: 64rpx auto;
  }
}
</style>