<template>
  <view class="page">
    <u-navbar :custom-back="goPre" :border-bottom="true" title="填报信息">
    </u-navbar>
    <view class="top">
      <u-steps active-color="#FF7373" :list="numList" :current="current"></u-steps>
    </view>
    <u-form :label-style="labelStyle" :rightStyle="rightStyle" :model="formData" label-width="316rpx" ref="formRef">
      <view class="section">
        <view class="fhead">
          <view class="dot"></view>
          <view class="t">本人信息</view>
        </view>
        <u-form-item required label="姓名" prop="parent_name">
          <u-input v-model="formData.parent_name" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item required label="与孩子关系" prop="parent_relation">
          <u-input v-model="formData.parent_relation" @click="showSelect(1)" disabled placeholder="请选择" />
        </u-form-item>
        <u-form-item required label="手机号码" prop="parent_phone">
          <u-input v-model="formData.parent_phone" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item label="身份证件类型">
          身份证
        </u-form-item>
        <u-form-item required label="身份证件号码" prop="parent_card">
          <u-input v-model="formData.parent_card" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item required label="身份证电子版" prop="parent_ele_card_s">
          <u-input v-model="formData.parent_ele_card_s" disabled @click="goUplod" placeholder="请上传" />
        </u-form-item>
        <view class="signature">
          <view class="tip" v-if="!formData.parent_sig">家长签名</view>
          <image class="imgs" v-if="formData.parent_sig" :src="formData.parent_sig"></image>
          <l-signature disableScroll v-else ref="signatureRef" penColor="#000000" :penSize="5"
            :openSmooth="true"></l-signature>
        </view>
        <view class="b1tns" v-if="canEdit">
          <view class="clear" @click="reset">重新签名</view>
          <view class="clear" v-if="formData.parent_sig">已上传</view>
          <view class="clear" @click="confirm" v-else>确定并上传</view>
        </view>
      </view>
      <view class="section">
        <view class="fhead">
          <view class="dot"></view>
          <view class="t">其他亲属(可选填)</view>
        </view>
        <u-form-item label="姓名">
          <u-input v-model="formData.other_name" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item label="与孩子关系">
          <u-input v-model="formData.other_relation" @click="showSelect(2)" disabled placeholder="请选择" />
        </u-form-item>
        <u-form-item label="手机号码" prop="other_phone">
          <u-input v-model="formData.other_phone" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
        <u-form-item label="身份证件类型">
          身份证
        </u-form-item>
        <u-form-item label="身份证件号码" prop="other_card">
          <u-input v-model="formData.other_card" :disabled="!canEdit" placeholder="请输入" />
        </u-form-item>
      </view>
    </u-form>
    <view class="btns">
      <view class="btn" @click="goPre">上一页</view>
      <view class="btn" @click="goNext">下一页</view>
    </view>
  </view>
  <u-select @confirm="confirmSelect" v-model="selectShow" :list="selectEumn"></u-select>
</template>
<script setup>
import LSignature from "../components/l-signature/l-signature.vue";
import { ref } from 'vue';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { onReady, onLoad } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
const formData = ref({
  parent_name: "",
  parent_relation: "",
  parent_phone: "",
  parent_card: "",
  parent_ele_card_s: "",
  parent_ele_card: "",
  other_name: "",
  other_relation: "",
  other_relation_name: "",
  other_phone: "",
  other_card: "",
  parent_sig: "",
});
let isDetail = 0;
const canEdit = ref(true);
onLoad((option) => {
  uni.$on("childcare-subsidies-card", (data) => {
    formData.value.parent_ele_card = data;
    formData.value.parent_ele_card_s = '已上传';
  })
  formData.value.parent_phone = userInfo.phone ? userInfo.phone : '';
  if (option.isDetail == 1) {
    isDetail = 1;
    let data = uni.getStorageSync("childcare-subsidies");
    formData.value.parent_name = data.parent_name;
    formData.value.parent_relation = data.parent_relation;
    formData.value.parent_phone = data.parent_phone;
    formData.value.parent_card = data.parent_card;
    formData.value.parent_ele_card = data.parent_ele_card;
    formData.value.parent_ele_card_s = "已上传";
    formData.value.other_name = data.other_name;
    formData.value.other_phone = data.other_phone;
    formData.value.other_relation = data.other_relation;
    formData.value.other_card = data.other_card;
    formData.value.parent_sig = data.parent_sig;
    canEdit.value = data.approval_status == 0 || data.approval_status == 2 || data.approval_status == 3;
  }
})
const formRef = ref(null);
const rules = {
  parent_name: [
    {
      required: true,
      message: '请输入姓名',
      trigger: 'blur',
    }
  ],
  parent_relation: [
    {
      required: true,
      message: '请选择与孩子关系',
      trigger: 'blur',
    }
  ],
  parent_phone: [
    {
      required: true,
      message: '请输入手机号',
      trigger: 'blur',
    }, {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号',
      trigger: 'blur',
    }
  ],
  parent_card: [
    {
      required: true,
      message: '请输入身份证号',
      trigger: 'blur',
    }, {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    }
  ],
  other_phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号',
      trigger: 'blur',
    }
  ],
  other_card: [
    {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    }
  ]
}
onReady(() => {
  formRef.value.setRules(rules);
})
function goUplod() {
  uni.navigateTo({
    url: `./card?canEdit=${canEdit.value ? 1 : 0}&imgs=${formData.value.parent_ele_card}`
  })
}
const selectShow = ref(false);
const selectEumn = ref([{
  label: "父亲",
  value: "父亲"
}, {
  label: "母亲",
  value: "母亲"
}, {
  label: "爷爷",
  value: "爷爷"
}, {
  label: "奶奶",
  value: "奶奶"
}, {
  label: "姥爷",
  value: "姥爷"
}, {
  label: "姥姥",
  value: "姥姥"
}, {
  label: "其他",
  value: "其他"
}])
let type = 1;
function showSelect(stype) {
  if (canEdit.value) {
    type = stype;
    selectShow.value = true;
  }
}
function confirmSelect(e) {
  if (type == 1) {
    formData.value.parent_relation = e[0].value;
  }
  if (type == 2) {
    formData.value.other_relation = e[0].value;
  }
}
function goNext() {
  formRef.value.validate(valid => {
    if (valid) {
      if (formData.value.parent_sig == '') {
        uni.showToast({ title: "请签名并上传", icon: "none" })
        return
      }
      uni.setStorageSync("childcare-subsidies-form2", formData.value)
      uni.navigateTo({
        url: "./form2?isDetail=" + isDetail
      })
    }
  })
}
function goPre() {
  uni.setStorageSync("childcare-subsidies-form2", formData.value)
  uni.navigateBack();
}
const current = ref(1);
const labelStyle = {
  paddingLeft: "30rpx"
}
const rightStyle = {
  paddingRight: "30rpx"
}
const numList = [{
  name: '婴幼儿信息'
}, {
  name: '父母信息'
}, {
  name: '证件信息'
}];
const signatureRef = ref(null);
function reset() {
  if (formData.value.parent_sig) {
    uni.showModal({
      title: "确认要重新签名？",
      success(res) {
        if (res.confirm) {
          formData.value.parent_sig = "";
          signatureRef.value.clear();
        }
      }
    })
  } else {
    signatureRef.value.clear();
  }
}
function confirm() {
  signatureRef.value.canvasToTempFilePath({
    success: (res) => {
      if (res.isEmpty) {
        uni.showToast({
          title: "请签名",
          icon: "none"
        })
      } else {
        let url = res.tempFilePath;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        OBSupload(url, fileExtension, fileName)
          .then(res => {
            formData.value.parent_sig = res;
            uni.showToast({
              title: "上传成功"
            })
          })
      }
    }
  })
}
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #F6F8FB;

  .top {
    width: 750rpx;
    height: 160rpx;
    background: #FFFFFF;
    padding-top: 60rpx;
  }

  .section {
    width: 750rpx;
    background: #FFFFFF;
    margin-top: 24rpx;

    ::v-deep.u-form-item--left__content--required {
      left: 12rpx;
    }

    .signature {
      width: 690rpx;
      height: 364rpx;
      border-radius: 8rpx;
      border: 2rpx solid #FF7373;
      margin: 20rpx auto;
      position: relative;

      .imgs {
        width: 690rpx;
        height: 364rpx;
      }

      .tip {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 128rpx;
        height: 44rpx;
        font-weight: 400;
        font-size: 32rpx;
        color: #FF7373;
        line-height: 44rpx;
        text-align: center;
      }
    }

    .b1tns {
      width: 690rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin: 20rpx auto;
      padding-bottom: 20rpx;
    }

    .clear {
      width: 320rpx;
      height: 60rpx;
      background: #FF7373;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #FFFFFF;
      line-height: 60rpx;
      text-align: center;
      font-style: normal;
    }

    .fhead {
      padding: 0 30rpx;
      height: 104rpx;
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-bottom: 1rpx solid #EEEEEE;

      .dot {
        width: 8rpx;
        height: 32rpx;
        border-radius: 4rpx;
        background: #FF7373;
      }

      .t {
        font-weight: 600;
        font-size: 32rpx;
        color: #111111;
        margin-left: 24rpx;
      }
    }
  }

  .btns {
    display: flex;
    flex-direction: row;
    margin: 64rpx auto;
    width: 100%;
    justify-content: space-between;
    padding: 0 30rpx;

    .btn {
      width: 310rpx;
      height: 88rpx;
      background: #FF7373;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      line-height: 88rpx;
      text-align: center;
      font-style: normal;
    }
  }

}
</style>