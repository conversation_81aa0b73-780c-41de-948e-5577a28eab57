<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :custom-back="backIndex" :background="{ backgroundColor: '#FFFFFF' }" title="历史记录">
    </u-navbar>
    <template v-if="list.length">
      <view class="item" v-for="item in list" :key="item.id" @click="goDeatil(item)">
        <view class="row">
          <view class="name">{{ item.student_name }}</view>
          <view class="statu" :class="['color' + item.is_submit]">
            {{ item.is_submit == 1 ? '未提交' : '已提交' }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            申请补贴年度：
          </view>
          <view class="value">
            {{ item.amissionsubsidy?.year }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            入托机构名称：
          </view>
          <view class="value">
            {{ item.school_name }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            最后提交日期：
          </view>
          <view class="value">
            {{ item.updated_at }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            审核状态：
          </view>
          <template v-if="item.is_submit == 2">
            <view v-if="item.area_status == 1" class="value">
              审核通过
            </view>
            <view v-else-if="item.approval_status == 2" class="value">
              审核未通过，可修改再次提交
            </view>
            <view v-else-if="item.approval_status == 3" class="value">
              未审核，可修改再次提交
            </view>
            <view v-else-if="item.approval_status == 1" class="value">
              审核中，不可再修改提交
            </view>
            <view v-else class="value">
              未审核，可修改再次提交
            </view>
          </template>
        </view>
        <view class="extra" v-if="item.approval_status == 2">
          <view class="extra-row">
            <view class="label">
              提交日期：
            </view>
            <view class="value">
              {{ item.approval_time }}
            </view>
          </view>
          <view class="extra-row">
            <view class="label">
              审核日期：
            </view>
            <view class="value">
              {{ item.updated_at }}
            </view>
          </view>
          <view class="extra-row">
            <view class="label">
              驳回原因：
            </view>
            <view class="value">
              {{ item.approval_reason }}
            </view>
          </view>
        </view>
      </view>
    </template>
    <u-empty v-else margin-top="400" text="暂无申请记录"></u-empty>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad, onReachBottom, onShow } from '@dcloudio/uni-app';
import request from "@/request.js";
let back = 0;
onLoad(option => {
  back = option.back;
})
onShow(() => {
  getData();
})
let page = 1;
const list = ref([])
let hasMore = true;
function getData() {
  page = 1;
  uni.showLoading();
  request({
    goLogin: true,
    url: "/api/parent/goveramissionsubsidy/list",
    method: "post",
    data: {
      per_page: 20,
      page: page,
    }
  }).then(res => {
    uni.hideLoading();
    if (res.length < 20) {
      hasMore = false;
    } else {
      hasMore = true;
    }
    list.value = res;
  })
}
onReachBottom(() => {
  if (hasMore) {
    page++;
    request({
      url: "/api/parent/goveramissionsubsidy/list",
      method: "post",
      data: {
        per_page: 20,
        page: page,
      }
    }).then(res => {
      uni.hideLoading();
      if (res.length < 20) {
        hasMore = false;
      } else {
        hasMore = true;
      }
      list.value.push(...res);
    })
  }
})
function backIndex() {
  if (back == 1) {
    uni.navigateBack({
      delta: 3
    })
  } else {
    uni.navigateBack();
  }
}
function goDeatil(item) {
  uni.setStorageSync("childcare-amissionsubsidy", item.amissionsubsidy)
  uni.showLoading();
  request({
    url: "/api/parent/goveramissionsubsidy/detail",
    method: "post",
    data: {
      id: item.id
    }
  }).then(res => {
    uni.hideLoading();
    uni.setStorageSync("childcare-subsidies", res)
    uni.navigateTo({
      url: `./form?isDetail=1`,
    });
  })
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .item {
    width: 750rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    padding: 40rpx 30rpx;

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 30rpx;
      }

      .statu {
        width: 142rpx;
        height: 48rpx;

        border-radius: 24rpx;
        font-weight: 400;
        font-size: 28rpx;

        line-height: 48rpx;
        text-align: center;
      }

      .label {
        font-weight: 500;
        font-size: 28rpx;
        color: #262937;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .colora1 {
        background: #DBFFF6;
        color: #42C68C;
      }

      .colora2 {
        background: #FFE8E8;
        color: #E74848;
      }

      .color1 {
        background: #FFE8E8;
        color: #E74848;
      }

      .color2 {
        background: #DBFFF6;
        color: #42C68C;
      }
    }

    .extra {
      border: 1px solid #eee;
      width: 90%;
      margin: 10px auto;
      padding: 12px;

      .extra-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-top: 16rpx;
      }
    }
  }
}
</style>