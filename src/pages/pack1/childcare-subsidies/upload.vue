<template>
  <view class="page">
    <u-navbar :border-bottom="true" title="资料补充">
    </u-navbar>
    <view class="setion">
      <view class="title">
        {{ title }}
      </view>
      <template v-if="canEdit">
        <u-upload :fileList="fileList" width="200rpx" height="200rpx" max-count="10" @on-list-change="imgChange"
          multiple deletable :auto-upload="false">
        </u-upload>
      </template>
      <view v-else class="files">
        <image class="item" :src="item.url" v-for="item in fileList" :key="item.url"></image>
      </view>
      <view class="btn" @click="submit" v-if="canEdit">上传材料</view>
      <view class="tips">
        支持PNG、JPG、JPEG、PDF,最多可上传10份
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { onLoad } from "@dcloudio/uni-app";
const title = ref("服务合同");
let type = 1;
const canEdit = ref(true)
onLoad((option) => {
  if (option.type == 2) {
    type = 2;
    title.value = "入托收费凭证"
  }
  canEdit.value = option.canEdit == 1;
  let imgs = option.imgs ? option.imgs.split(',') : [];
  fileList.value = imgs.map(item => {
    return {
      url: decodeURIComponent(item)
    }
  })
})
const fileList = ref([])
let imgList = []
function imgChange(e) {
  imgList = e
}
async function submit() {
  if (imgList.length) {
    uni.showLoading();
    let imgs = [];
    if (imgList.length) {
      let requestArr = []
      imgList.filter(item => item.progress == 0).forEach(item => {
        let fileName = item.file.name;
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        requestArr.push(OBSupload(item.url, fileExtension, fileName))
      })
      let flag = false;
      try {
        imgs = await Promise.all(requestArr);
      } catch (error) {
        flag = true;
        uni.showToast({
          title: "图片上传失败",
          icon: "error"
        })
      }
      if (flag)
        return;
      imgs.push(...imgList.filter(item => item.progress !== 0).map(item => item.url));
      uni.$emit("childcare-subsidies-upload", {
        type,
        imgs: imgs.join(",")
      })
      uni.hideLoading();
      uni.navigateBack();
    }
  } else {
    uni.showToast({
      title: "请选择图片",
      icon: "none"
    })
  }
}
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #F6F8FB;

  .setion {
    width: 750rpx;
    background: #FFFFFF;
    padding: 30rpx;

    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #262937;
      line-height: 44rpx;
    }

    .files {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 20rpx;

      .add {
        width: 200rpx;
        height: 200rpx;
        background: #FFFFFF;
        border: 1rpx solid #262937;
        text-align: center;
        line-height: 200rpx;
      }

      .item {
        width: 200rpx;
        height: 200rpx;
        position: relative;
        margin-bottom: 20rpx;

        .close {
          width: 36rpx;
          height: 36rpx;
          position: absolute;
          top: 36rpx;
          right: 36rpx;
          z-index: 2;
        }

        .img {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 140rpx;
          height: 140rpx;
          background: #F6F6F6;
        }
      }
    }

    .btn {
      width: 690rpx;
      height: 96rpx;
      border-radius: 8rpx;
      border: 2rpx solid #FF7373;
      font-weight: 400;
      font-size: 32rpx;
      color: #FF7373;
      line-height: 96rpx;
      text-align: center;
      margin: 32rpx 0;
    }
  }
}
</style>