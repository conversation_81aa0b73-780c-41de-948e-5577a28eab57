<template>
  <view class="page">
    <u-navbar :custom-back="backIndex" :border-bottom="true" :title="title">
    </u-navbar>
    <image class="ic" v-if="type == 1" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/succ1.png">
    </image>
    <image class="ic" v-else src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/succ2.png">
    </image>
    <view class="t1" v-if="type == 1">保存成功！</view>
    <view class="t1" v-else>您已提交成功！</view>
    <!-- <view class="bt1" @click="goInfo">查看提交信息</view> -->
    <view class="bt2" @click="goList">查看申报记录</view>
    <!-- <template v-if="type == 2">
      <view class="t2">
        注：
      </view>
      <view class="t3">
        复审审核通过后及时已短信形式通知您，请您注意查收短信信息。
      </view>
      <view class="t3">
        如有问题，您可以拨打客服电话详细咨询。
        电话：000-0000000
      </view>
    </template> -->
  </view>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
const type = ref(1);
const title = ref('保存成功');
onLoad((option) => {
  type.value = option.type;
  if (type.value == 2) {
    title.value = "提交成功";
  }
})
function goInfo() {

}
function goList() {
  uni.redirectTo({
    url: "./list?back=1"
  })
}
function backIndex() {
  uni.navigateBack({
    delta: 3
  })
}
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #F6F8FB;
  align-items: center;

  .ic {
    width: 238rpx;
    height: 220rpx;
    position: relative;
    left: 12rpx;
    margin-top: 122rpx;
  }

  .t1 {
    width: 600rpx;
    font-weight: 600;
    font-size: 36rpx;
    color: #262937;
    line-height: 50rpx;
    text-align: center;
    margin-top: 58rpx;
  }

  .bt1 {
    width: 600rpx;
    height: 92rpx;
    background: #FF7373;
    border-radius: 8rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 92rpx;
    text-align: center;
    font-style: normal;
    margin-top: 142rpx;
  }

  .bt2 {
    width: 600rpx;
    height: 92rpx;
    background: #FF7373;
    border-radius: 8rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 92rpx;
    text-align: center;
    font-style: normal;
    margin-top: 48rpx;
  }

  .t2 {
    width: 600rpx;
    height: 40rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #080808;
    line-height: 40rpx;
    text-align: left;
    margin-top: 116rpx;
  }

  .t3 {
    width: 600rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #787C8D;
    line-height: 40rpx;
    margin-top: 32rpx;
  }
}
</style>