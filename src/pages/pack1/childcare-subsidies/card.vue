<template>
  <view class="page">
    <u-navbar :border-bottom="true" title="身份证电子版">
    </u-navbar>
    <view class="setion">
      <view class="left">
        <view class="t1">
          头像面
        </view>
        <view class="t2">
          上传您的身份证头像面
        </view>
      </view>
      <view class="photo" @click="chooseFile(1)">
        <image class="imgg" v-if="card1" :src="card1">
        </image>
        <image class="imgg" v-else src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/zzc.png">
        </image>
      </view>
    </view>
    <view class="setion">
      <view class="left">
        <view class="t1">
          国徽面
        </view>
        <view class="t2">
          上传您的身份证国徽面
        </view>
      </view>
      <view class="photo" @click="chooseFile(2)">
        <image class="imgg" v-if="card2" :src="card2">
        </image>
        <image class="imgg" v-else src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/ccc.png">
        </image>
      </view>
    </view>
    <view class="tiops">支持PNG、JPG、JPEG格式</view>
    <view class="btn" @click="submit">上传材料</view>
  </view>
</template>
<script setup>
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
const canEdit = ref(true);
onLoad((option) => {
  let imgs = option.imgs;
  canEdit.value = option.canEdit == 1;
  if (imgs) {
    try {
      imgs = imgs.split(",")
      card1.value = imgs[0];
      card2.value = imgs[1];
    } catch (error) {

    }
  }
})
const card1 = ref('');
let car1Change = false;
const card2 = ref('');
let car2Change = false;
function chooseFile(type) {
  if (canEdit.value) {
    uni.chooseImage({
      count: 1,  //总限制5张
      sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], //从相册选择
      success(res) {
        if (type == 1) {
          card1.value = res.tempFilePaths[0];
          car1Change = true;
        }
        if (type == 2) {
          card2.value = res.tempFilePaths[0];
          car2Change = true;
        }
      }
    })
  }
}
async function submit() {
  if (card1.value == "") {
    uni.showToast({
      title: "请上传身份证头像面",
      icon: "none"
    })
    return
  }
  if (card2.value == "") {
    uni.showToast({
      title: "请上传身份证国徽面",
      icon: "none"
    })
    return
  }
  let p1 = "";
  let p2 = "";
  uni.showLoading()
  if (car1Change) {
    let url = card1.value;
    let fileName = url.substring(url.lastIndexOf("/") + 1);
    let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
    p1 = await OBSupload(url, fileExtension, fileName)
  } else {
    p1 = card1.value
  }
  if (car2Change) {
    let url = card2.value;
    let fileName = url.substring(url.lastIndexOf("/") + 1);
    let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
    p2 = await OBSupload(url, fileExtension, fileName)
  } else {
    p2 = card2.value;
  }
  uni.hideLoading();
  uni.$emit("childcare-subsidies-card", `${p1},${p2}`)
  uni.navigateBack();
}
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #F6F8FB;

  .btn {
    width: 690rpx;
    height: 96rpx;
    border-radius: 8rpx;
    border: 2rpx solid #FF7373;
    font-weight: 400;
    font-size: 32rpx;
    color: #FF7373;
    line-height: 96rpx;
    text-align: center;
    margin: 32rpx auto;

  }

  .setion {
    width: 750rpx;
    background: #FFFFFF;
    padding: 46rpx 30rpx;
    display: flex;
    flex-direction: row;
    margin-top: 20rpx;
    justify-content: space-between;

    .left {
      .t1 {
        font-weight: 400;
        font-size: 32rpx;
        color: #262937;
        line-height: 44rpx;
      }

      .t2 {
        font-weight: 400;
        font-size: 28rpx;
        color: #AEB0BC;
        line-height: 40rpx;
        margin-top: 8rpx;
      }
    }

    .photo {
      width: 234rpx;
      height: 148rpx;
      border: 1rpx dashed #262937;

      .imgg {
        width: 230rpx;
        height: 142rpx;
      }
    }
  }

  .tiops {
    font-weight: 400;
    font-size: 28rpx;
    color: #787C8D;
    line-height: 40rpx;
    text-align: right;
    padding-right: 30rpx;
    margin-top: 30rpx;
  }
}
</style>