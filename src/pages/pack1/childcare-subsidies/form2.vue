<template>
  <view class="page">
    <u-navbar :custom-back="goPre" :border-bottom="true" title="填报信息">
    </u-navbar>
    <view class="top">
      <u-steps active-color="#FF7373" :list="numList" :current="current"></u-steps>
    </view>
    <view class="section">
      <view class="fhead">
        <view class="dot"></view>
        <view class="t">户籍信息</view>
      </view>
      <view class="t1">
        父母双方或一方是否为本市户籍
      </view>
      <view class="btn">
        <view class="b" :class="{ ac: isLoc == 1 }" @click="isLoc = 1">是</view>
        <view class="b" :class="{ ac: isLoc == 2 }" @click="isLoc = 2">否</view>
      </view>
      <template v-if="isLoc == 1">
        <view class="flex">
          <view class="item" @click="chooesimg(1)">
            <text v-if="photo.p1 == ''">户口本首页</text>
            <image v-else mode="aspectFit" :src="photo.p1" class="img"></image>
          </view>
          <view class="item" @click="chooesimg(2)">
            <text v-if="photo.p2 == ''">索引页</text>
            <image v-else mode="aspectFit" :src="photo.p2" class="img"></image>
          </view>
          <view class="item" @click="chooesimg(3)">
            <text v-if="photo.p3 == ''">婴幼儿页</text>
            <image v-else mode="aspectFit" :src="photo.p3" class="img"></image>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="flex">
          <view class="item" @click="chooesimg(1)">
            <text v-if="photo.p1 == ''">户口本首页</text>
            <image v-else mode="aspectFit" :src="photo.p1" class="img"></image>
          </view>
          <view class="item" @click="chooesimg(2)">
            <text v-if="photo.p2 == ''">索引页</text>
            <image v-else mode="aspectFit" :src="photo.p2" class="img"></image>
          </view>
          <view class="item" @click="chooesimg(3)">
            <text v-if="photo.p3 == ''">婴幼儿页</text>
            <image v-else mode="aspectFit" :src="photo.p3" class="img"></image>
          </view>
          <view class="item" @click="chooesimg(4)">
            <text v-if="photo.p4 == ''">本地居住证</text>
            <image v-else mode="aspectFit" :src="photo.p4" class="img"></image>
          </view>
        </view>
      </template>
      <view class="tiops">
        支持PNG、JPG、JPEG图片格式
      </view>
      <view class="flex-mode">
        <u-checkbox v-model="checkValue" shape="circle" active-color="#FF7373" icon-size="32">
          <view class="main-check flex-mode">
            承诺以上填报内容均属实无误，无任何虚假信息。
            愿意对其真实性和准确性承担全部责任</view>
        </u-checkbox>
      </view>
    </view>
    <view class="btns">
      <view class="btn" @click="goPre">上一页</view>
      <view class="btn" :class="{ disable: !canEdit }" @click="save(1)">保存</view>
      <view class="btn" :class="{ disable: !canEdit }" @click="save(2)">提交</view>
    </view>
  </view>

</template>
<script setup>
import request from '@/request';
import { OBSupload } from '@/utils/obs/obs-upload.js';
import { ref } from 'vue';
import { useUserStore } from "@/stores/userInfo.js"
import { onLoad } from "@dcloudio/uni-app";
import dayjs from 'dayjs';
const checkValue = ref(false);
let id = 0;
const canEdit = ref(true);
let approval_status = "";
onLoad((option) => {
  if (option.isDetail == 1) {
    let data = uni.getStorageSync("childcare-subsidies");
    isLoc.value = data.reg_information == 1 ? 1 : 2;
    approval_status = data.approval_status;
    canEdit.value = data.approval_status == 0 || data.approval_status == 2 || data.approval_status == 3;
    id = data.id;
    if (data.reg_information == 1) {
      let imgs = data.reg_img.split(",");
      photo.value.p1 = imgs[0];
      photo.value.p2 = imgs[1];
      photo.value.p3 = imgs[2];
    } else {
      let imgs = data.reg_img.split(",");
      photo.value.p1 = imgs[0];
      photo.value.p2 = imgs[1];
      photo.value.p3 = imgs[2];
      photo.value.p4 = imgs[3];
    }
  }
})
const userInfo = useUserStore();
const isLoc = ref(1);
const photo = ref({
  p1: "",
  p1c: false,
  p2: "",
  p2c: false,
  p3: "",
  p3c: false,
  p4: "",
  p4c: false,
})
function chooesimg(type) {
  uni.chooseImage({
    count: 1,  //总限制5张
    sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
    sourceType: ['album', 'camera'], //从相册选择
    success(res) {
      if (type == 1) {
        photo.value.p1 = res.tempFilePaths[0];
        photo.value.p1c = true;
      }
      if (type == 2) {
        photo.value.p2 = res.tempFilePaths[0];
        photo.value.p2c = true;
      }
      if (type == 3) {
        photo.value.p3 = res.tempFilePaths[0];
        photo.value.p3c = true;
      }
      if (type == 4) {
        photo.value.p4 = res.tempFilePaths[0];
        photo.value.p4c = true;
      }
    }
  })
}
function goPre() {
  uni.setStorageSync("childcare-subsidies-form3", {
    isLoc: isLoc.value,
    photo: photo.value
  })
  uni.navigateBack();
}
async function save(type) {
  if (type == 2) {
    if (!checkValue.value) {
      uni.showToast({
        title: "请阅读以上内容并勾选",
        icon: "none"
      })
      return
    }
  }
  let imgs = ''
  if (isLoc.value == 1) {
    if (photo.value.p1 == "" || photo.value.p2 == "" || photo.value.p3 == "") {
      uni.showToast({
        title: "请将户口材料补充完整",
        icon: "none"
      })
      return
    } else {
      uni.showLoading()
      let p1 = "";
      let p2 = "";
      let p3 = "";;
      if (photo.value.p1c) {
        let url = photo.value.p1;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p1 = await OBSupload(url, fileExtension, fileName)
      } else {
        p1 = photo.value.p1;
      }
      if (photo.value.p2c) {
        let url = photo.value.p2;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p2 = await OBSupload(url, fileExtension, fileName)
      } else {
        p2 = photo.value.p2;
      }
      if (photo.value.p3c) {
        let url = photo.value.p3;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p3 = await OBSupload(url, fileExtension, fileName)
      } else {
        p3 = photo.value.p3;
      }
      imgs = `${p1},${p2},${p3}`;
    }
  } else {
    if (photo.value.p1 == "" || photo.value.p2 == "" || photo.value.p3 == "" || photo.value.p4 == "") {
      uni.showToast({
        title: "请将户口材料补充完整",
        icon: "none"
      })
      return
    } else {
      uni.showLoading()
      let p1 = "";
      let p2 = "";
      let p3 = "";
      let p4 = "";
      if (photo.value.p1c) {
        let url = photo.value.p1;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p1 = await OBSupload(url, fileExtension, fileName)
      } else {
        p1 = photo.value.p1;
      }
      if (photo.value.p2c) {
        let url = photo.value.p2;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p2 = await OBSupload(url, fileExtension, fileName)
      } else {
        p2 = photo.value.p2;
      }
      if (photo.value.p3c) {
        let url = photo.value.p3;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p3 = await OBSupload(url, fileExtension, fileName)
      } else {
        p3 = photo.value.p3;
      }
      if (photo.value.p4c) {
        let url = photo.value.p4;
        let fileName = url.substring(url.lastIndexOf("/") + 1);
        let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        p4 = await OBSupload(url, fileExtension, fileName)
      } else {
        p4 = photo.value.p4;
      }
      imgs = `${p1},${p2},${p3},${p4}`;
    }
  }
  let data1 = uni.getStorageSync("childcare-subsidies-form1")
  let data2 = uni.getStorageSync("childcare-subsidies-form2")
  let info = uni.getStorageSync("goveramissionsubsidy");

  request({
    url: id ? "/api/parent/goveramissionsubsidy/update" : "/api/parent/goveramissionsubsidy/add",
    method: "post",
    data: {
      id: id == 0 ? "" : id,
      subsidy_id: info.id,
      student_id: userInfo.student_id || 105,
      ...data1,
      ...data2,
      approval_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      approval_status: approval_status == 2 || approval_status == 3 ? 3 : 0,
      reg_information: isLoc.value == 1 ? 1 : 0,
      reg_img: imgs,
      is_submit: type,
    }
  }).then(res => {
    uni.hideLoading();
    uni.showToast({
      title: type == 1 ? "保存成功" : "提交成功",
      icon: "none"
    })
    uni.removeStorageSync('childcare-subsidies-form1');
    uni.removeStorageSync('childcare-subsidies-form2');
    setTimeout(() => {
      uni.navigateTo({
        url: `./success?type=${type}`
      })
    }, 1500)
  })
}
const current = ref(2);
const numList = [{
  name: '婴幼儿信息'
}, {
  name: '父母信息'
}, {
  name: '证件信息'
}];
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #F6F8FB;

  .top {
    width: 750rpx;
    height: 160rpx;
    background: #FFFFFF;
    padding-top: 60rpx;
  }

  .section {
    width: 750rpx;
    background: #FFFFFF;
    margin-top: 24rpx;

    ::v-deep.u-form-item--left__content--required {
      left: 12rpx;
    }


    .fhead {
      padding: 0 30rpx;
      height: 104rpx;
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-bottom: 1rpx solid #EEEEEE;

      .dot {
        width: 8rpx;
        height: 32rpx;
        border-radius: 4rpx;
        background: #FF7373;
      }

      .t {
        font-weight: 600;
        font-size: 32rpx;
        color: #111111;
        margin-left: 24rpx;
      }
    }

    .t1 {
      font-weight: 400;
      font-size: 30rpx;
      color: #686C7C;
      line-height: 42rpx;
      padding: 30rpx;
    }

    .btn {
      display: flex;
      flex-direction: row;
      padding: 20rpx 30rpx;

      .b {
        width: 160rpx;
        height: 80rpx;
        border-radius: 8rpx;
        border: 2rpx solid #FF7373;
        font-weight: 500;
        font-size: 32rpx;
        color: #FF7373;
        line-height: 80rpx;
        text-align: center;
        font-style: normal;
        margin-right: 30rpx;
      }

      .ac {
        background: #FF7373;
        color: #FFFFFF;
      }
    }

    .flex {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 0 30rpx;
      margin-top: 30rpx;

      .item {
        width: 330rpx;
        height: 186rpx;
        border-radius: 8rpx;
        border: 2rpx solid #FF7373;
        font-weight: 400;
        font-size: 28rpx;
        color: #FF7373;
        line-height: 186rpx;
        text-align: center;
        font-style: normal;
        margin-bottom: 28rpx;

        .img {
          width: 326rpx;
          height: 180rpx;
        }
      }
    }

    .aitem {
      width: 690rpx;
      height: 388rpx;
      border-radius: 8rpx;
      border: 2rpx solid #FF7373;
      font-weight: 400;
      font-size: 32rpx;
      color: #FF7373;
      line-height: 388rpx;
      text-align: center;
      font-style: normal;
      margin: 20rpx auto;

      .img {
        width: 686rpx;
        height: 382rpx;
      }
    }

    .tiops {
      font-weight: 400;
      font-size: 30rpx;
      color: #686C7C;
      line-height: 42rpx;
      text-align: right;
      font-style: normal;
      padding: 20rpx 30rpx 20rpx;
    }
  }

  .btns {
    display: flex;
    flex-direction: row;
    margin: 64rpx auto;
    width: 100%;
    justify-content: space-between;
    padding: 0 30rpx;

    .btn {
      width: 200rpx;
      height: 88rpx;
      background: #FF7373;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      line-height: 88rpx;
      text-align: center;
      font-style: normal;
    }

    .disable {
      opacity: 0.5;
      pointer-events: none;
    }
  }

}

.flex-mode {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20rpx 20rpx 20rpx;
}
</style>