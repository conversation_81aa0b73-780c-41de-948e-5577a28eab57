<template>
  <view class="page safe-area-inset-bottom">
    <u-navbar backIconColor="#FFFFFF" :border-bottom="false" title-color="#FFFFFF"
      :background="{ backgroundColor: '#FF7373' }" title="入托补贴">
    </u-navbar>
    <view class="top">
      <view class="titl">
        <view>
          3岁以下婴幼儿二孩、三孩
        </view>
        <view>
          入托专项补贴
        </view>
      </view>
      <view class="img">
        <image class="imgimg" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/bak.png"></image>
      </view>
    </view>
    <view class="center">
      <view class="se">
        <view class="title">
          3岁以下婴幼儿二孩、三孩入托专项补贴政策简介
        </view>
        <view class="t">
          为贯彻落实国家关于优化生育政策、促进人口长期均衡发展的相关要求，结合全国各地实际情况，现就做好3岁以下婴幼儿二孩、三孩入托补贴发放工作提出以下通用性指导。凡具有中国国籍或在中华人民共和国境内常驻的3岁以下婴幼儿二孩、三孩家庭，其子女在国内已备案的托育机构接受托育服务，其父母均有资格申请入托补贴。各地可根据本地实际情况进一步细化政策并执行。
        </view>
      </view>
      <view class="btn btn1" @click="showPop">
        去填报
      </view>
      <view class="btn btn2" @click="toList">
        去查询
      </view>
    </view>
    <view class="wtext">
      常见问题
    </view>
    <view class="textss">
      <view class="item">
        <view class="t1">
          <view class="xuhao">
            1
          </view>
          <view class="th">
            入托补贴范围包括那些？
          </view>
        </view>
        <view class="t2">
          <text>
            1、服务合同、入托收费凭证；\n
            2、银行账户信息；\n
            3、父/母身份证电子版；\n
            4、本地户籍需准备户口簿；\n
            5、非本地户籍除户口簿还需准备居住证。\n
          </text>
        </view>
      </view>
      <view class="item">
        <view class="t1">
          <view class="xuhao">
            2
          </view>
          <view class="th">
            入托补助标准是多少？
          </view>
        </view>
        <view class="t2">
          <text>
            按照二孩每人每月300元、三孩每人每月500元的标准进行补助。
          </text>
        </view>
      </view>
      <view class="item">
        <view class="t1">
          <view class="xuhao">
            3
          </view>
          <view class="th">
            入托补贴发放周期？
          </view>
        </view>
        <view class="t2">
          <text>
            入托补贴自2023年9月1日起执行，发放周期为每年9月1日至次年8月31日。婴幼儿家长于每年9月30日前提交补贴申请。
          </text>
        </view>
      </view>
      <view class="item">
        <view class="t1">
          <view class="xuhao">
            4
          </view>
          <view class="th">
            入托补贴发放途径是什么？
          </view>
        </view>
        <view class="t2">
          <text>
            县级卫生健康部门通过惠民惠农一本通将入托补贴发放到婴幼儿父母社会保障卡账户或个人银行账户。
          </text>
        </view>
      </view>
    </view>
    <u-popup v-model="popShow" :mask-close-able="true" height="972rpx" :border-radius="40" mode="center">
      <view class="pop">
        <view class="topba"></view>
        <view class="tt">填报前，您需要提前准备</view>
        <view class="row">
          <image class="icon" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/icon1.png">
          </image>
          <text>1、服务合同、入托收费凭证；</text>
        </view>
        <view class="row">
          <image class="icon" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/icon2.png">
          </image>
          <text>2、银行账户信息；</text>
        </view>
        <view class="row">
          <image class="icon" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/icon3.png">
          </image>
          <text>3、父/母身份证电子版；</text>
        </view>
        <view class="row">
          <image class="icon" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/icon4.png">
          </image>
          <text>4、本地户籍需准备户口簿；</text>
        </view>
        <view class="row">
          <image class="icon" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/icon4.png">
          </image>
          <text>5、非本地户籍除户口簿还需准备居住证。</text>
        </view>
        <view class="btnss" @click="goForm">准备完毕、进入填报</view>
      </view>
    </u-popup>
  </view>
</template>
<script setup>
import { ref } from "vue";
const popShow = ref(false);
function toList() {
  uni.navigateTo({
    url: "./list"
  })
}
function showPop() {
  popShow.value = true;
}
function goForm() {
  popShow.value = false;
  uni.navigateTo({
    url: "./project-list"
  })
}
</script>
<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #FF7373;

  .top {
    position: relative;
    width: 750rpx;
    height: 650rpx;

    .titl {
      position: absolute;
      left: 32rpx;
      top: 38rpx;
      font-size: 56rpx;
      color: #FFFFFF;
      line-height: 90rpx;
      text-shadow: 0px 4px 10px #CB4141;
      text-align: left;
      font-style: normal;
      z-index: 2;
    }

    .img {
      width: 750rpx;
      height: 500rpx;
      position: absolute;
      left: 0;
      bottom: 0;
      background: linear-gradient(180deg, #FF7373 0%, #FAE9E5 100%);

      .imgimg {
        position: absolute;
        top: -44rpx;
        left: 0;
        width: 750rpx;
        height: 588rpx;
      }
    }
  }

  .center {
    width: 686rpx;
    height: 660rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    position: relative;
    margin: 0 auto;
    z-index: 3;
    padding-top: 22rpx;

    .se {
      width: 646rpx;
      height: 600rpx;
      border-radius: 16rpx;
      border: 2rpx solid #EEEEEE;
      margin: 0 auto;
      padding: 32rpx;

      .title {
        font-weight: 500;
        font-size: 36rpx;
        color: #521515;
        line-height: 48rpx;
        text-align: center;
        font-style: normal;
      }

      .t {
        font-weight: 400;
        font-size: 28rpx;
        color: #262937;
        line-height: 46rpx;
        margin-top: 24rpx;
      }
    }

    .btn {
      width: 320rpx;
      height: 80rpx;
      background: linear-gradient(#FFB781 0%, #FF8F32 100%, #FF4F4F 100%);
      box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(255, 94, 0, 0.76);
      border-radius: 40rpx;
      position: absolute;
      text-align: center;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      line-height: 80rpx;
      font-style: normal;
    }

    .btn1 {
      left: 0;
    }

    .btn2 {
      left: 362rpx;
    }
  }

  .wtext {
    width: 144rpx;
    height: 50rpx;
    font-weight: 600;
    font-size: 36rpx;
    color: #FFFFFF;
    line-height: 50rpx;
    text-align: center;
    font-style: normal;
    margin: 80rpx auto 40rpx;
    position: relative;

    &::before {
      content: " ";
      width: 40rpx;
      height: 2rpx;
      background: #FFFFFF;
      position: absolute;
      left: -60rpx;
      top: 24rpx;
    }

    &::after {
      content: " ";
      width: 40rpx;
      height: 2rpx;
      background: #FFFFFF;
      position: absolute;
      right: -60rpx;
      top: 24rpx;
    }
  }

  .textss {
    background: #FFFFFF;
    border-radius: 16rpx;
    margin: 40rpx auto;
    width: 686rpx;
    padding: 30rpx;

    .item {
      width: 622rpx;
      padding-bottom: 32rpx;
      border-bottom: 2rpx solid #EEEEEE;
      margin-bottom: 34rpx;

      .t1 {
        display: flex;
        flex-direction: row;
        align-items: center;

        .xuhao {
          width: 52rpx;
          height: 36rpx;
          background: url(https://obs.tuoyupt.com/miniprogram/childcare-subsidies/xuh.png) 100%/100% no-repeat;
          font-weight: 500;
          font-size: 28rpx;
          color: #FFFFFF;
          line-height: 36rpx;
          text-align: center;
          font-style: normal;
        }

        .th {
          font-weight: 600;
          font-size: 32rpx;
          color: #262937;
          margin-left: 20rpx;
        }
      }

      .t2 {
        margin-top: 16rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #787C8D;
        line-height: 48rpx;
      }
    }
  }
}

.pop {
  width: 700rpx;
  background: #FFFFFF;
  border-radius: 32rpx;

  .topba {
    width: 700rpx;
    height: 102rpx;
    background: linear-gradient(180deg, #FF7373 0%, rgba(255, 231, 231, 0.05) 100%);
  }

  .tt {
    text-align: center;
    font-weight: 600;
    font-size: 34rpx;
    color: #262937;
    line-height: 48rpx;
    margin-top: 8rpx;
  }

  .row {
    display: flex;
    flex-direction: row;
    font-weight: 400;
    font-size: 30rpx;
    color: #262937;
    line-height: 42rpx;
    width: 100%;
    padding: 0 30rpx;
    align-items: center;
    height: 130rpx;

    .icon {
      width: 64rpx;
      height: 64rpx;
      margin-right: 16rpx;
      flex-shrink: 0;
    }
  }

  .btnss {
    width: 634rpx;
    height: 96rpx;
    background: #FF7373;
    box-shadow: 0rpx 8rpx 30rpx 0rpx #FF7373;
    border-radius: 47rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 96rpx;
    text-align: center;
    margin: 0 auto;
  }
}
</style>