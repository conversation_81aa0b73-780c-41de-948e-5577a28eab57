<template>
  <view class="school-detail-page">
    <u-navbar :background="{ background: `rgba(255,255,255,${navBackground})` }" :immersive="true" ref="navbar"
      :is-back="navBackground ? true : false" :border-bottom="navBackground ? true : false" title="">
      <view v-if="navBackground == 1" class="slot-wrap">
        <u-tabs :list="tabList" bg-color="transparent" :show-bar="false" :active-item-style="{ color: '#000' }"
          :is-scroll="false" v-model="current" @change="tabChange"></u-tabs>
      </view>
      <view v-else class="slot-wrap">
        <image @click="backFun" class="back-icon"
          src="https://obs.tuoyupt.com/miniprogram/client/images/back_icon.png" />
      </view>
    </u-navbar>
    <div class="swiper" id="swiper">
      <swiper v-if="schoolData" class="swiper-wrap" circular :indicator-dots="swiperOption.indicatorDots"
        :autoplay="swiperOption.autoplay" :interval="swiperOption.interval" :duration="swiperOption.duration">
        <swiper-item v-for="(item, index) in swiperList" :key="index">
          <view class="swiper-item" v-if="item.type == 1">
            <MVdieo :src="item.url">
            </MVdieo>
          </view>
          <view class="swiper-item" v-else>
            <image :src="item.url" mode="aspectFill" />
          </view>
        </swiper-item>
      </swiper>
    </div>
    <view class="content" v-if="schoolData">
      <view class="name-part">
        <view class="left">
          <view class="name">{{ schoolData.abbreviation }}</view>
          <view class="tag">
            <text v-if="schoolData.free_type">{{ id2name(schoolData.free_type, free_type) }}</text>
            <text v-if="schoolData.school_class">{{ id2name(schoolData.school_class, school_class) }}</text>
            <text v-if="schoolData.is_record != 0">{{ id2name(schoolData.is_record, is_record) }}</text>
            <text v-if="schoolData.school_nature_ids" v-for="(item, index) in str2arr(schoolData.school_nature_ids)"
              :key="index">{{ id2name(item,
                school_nature_ids) }}</text>
          </view>
          <view class="rate">
            <view class="rtt">评分</view>
            <MyRate :current="schoolData.score">
            </MyRate>
          </view>
        </view>
        <image @click="phoneShow = true" class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone.png"
          mode="aspectFit" />
      </view>
      <view class="address" @click="infoMap">
        <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
        </image>
        <view class="add-content">
          <view class="lefec">
            <view class="t1">{{ schoolData.service.address }}</view>
            <!-- <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="info.distance >= 1">距你{{ (info.distance * 1).toFixed(2)
              }}km</text>
              <text v-else class="distance">距你{{ info.distance * 1000 }}m</text>
            </template> -->
          </view>
          <view class="rsec">
            <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
            </image>
          </view>
        </view>
      </view>
      <view class="intro-part" id="introduce" :class="{ 'intro-open': introOpen }">
        {{ schoolData.service.introduce }}
        <view class="close-btn" v-if="!hideOpenBtn" @click="introOpen = false;">收起</view>
        <view class="open-btn" @click="introOpen = true;">展开</view>
      </view>
      <u-line id="msg-part" color="#eee" margin="40rpx 0" />
      <view class="msg-part">
        <view class="title-1">信息</view>
        <u-collapse :accordion="false" :head-style="headStyle">
          <u-collapse-item class="title-2" title="基本信息">
            <view v-if="schoolData.service.build_tuoyu_num || schoolData.service.build_children_num" style="margin: 0"
              class="title-3">建设托位数：</view>
            <view v-if="schoolData.service.build_tuoyu_num || schoolData.service.build_children_num" class="value">{{
              schoolData.service.build_tuoyu_num ||
              schoolData.service.build_children_num
            }}</view>
            <view v-if="schoolData.service.server_type" class="title-3">服务类型：</view>
            <view v-if="schoolData.service.server_type" class="value">
              <template v-for="(item, index) in str2arr(
                schoolData.service.server_type,
                true
              )" :key="index">
                {{ id2name(item, server_type) }}；
              </template>
            </view>
            <view v-if="schoolData.service.feed_type" class="title-3">供餐情况：</view>
            <view v-if="schoolData.service.feed_type" style="margin-bottom: 40rpx" class="value">{{
              id2name(schoolData.service.feed_type, feed_type)
            }}</view>
          </u-collapse-item>
          <u-collapse-item class="title-2" title="设施信息">
            <view v-if="schoolData.service.facility_type" style="margin: 0" class="title-3">设施情况：</view>
            <view v-if="schoolData.service.facility_type" class="value">{{
              id2name(schoolData.service.facility_type, facility_type)
            }}</view>
            <view v-if="schoolData.service.place_type" class="title-3">服务场所性质：</view>
            <view v-if="schoolData.service.place_type" class="value">{{
              id2name(schoolData.service.place_type, place_type)
            }}</view>
            <view v-if="schoolData.service.struct_area || schoolData.service.out_area || schoolData.service.in_area"
              class="title-3">场地面积：</view>
            <view class="value">
              <text v-if="schoolData.service.struct_area">建筑面积{{ schoolData.service.struct_area }}㎡，</text>
              <text v-if="schoolData.service.out_area">户外场地面积{{ schoolData.service.out_area }}㎡，</text>
              <text v-if="schoolData.service.in_area">室内面积{{ schoolData.service.in_area }}㎡，</text>
            </view>
            <view v-if="schoolData.service.classroom_num" class="title-3">教室数量：</view>
            <view v-if="schoolData.service.classroom_num" class="value">{{ schoolData.service.classroom_num }}</view>
            <view v-if="schoolData.service.other_facilitys" class="title-3">其他设备：</view>
            <view v-if="schoolData.service.other_facilitys" class="value">
              <template v-for="(item, index) in str2arr(
                schoolData.service.other_facilitys,
                true
              )" :key="index">
                {{ id2name(item, other_facilitys) }}；
              </template>
            </view>
          </u-collapse-item>
        </u-collapse>
      </view>
      <view style="height: 30rpx;"></view>
      <u-tabs :list="tabList1" :is-scroll="false" v-model="current1" bar-height="6" bar-width="40"
        active-color="#1975FF"></u-tabs>
      <view id="media-part" class="media-part" v-if="current1 == 1">
        <template v-if="remakeList.length">
          <view class="pinglun-item" v-for="item in remakeList" :key="item.user_id">
            <view class="pinglun-top">
              <image :src="item.img_url" v-if="item.img_url" class="pinglun-avter"></image>
              <image src="https://obs.tuoyupt.com/yjy_mini/front_img/school/avter.png" v-else class="pinglun-avter">
              </image>
              <view class="pinglun-info">
                <view class="pinglun-t1">
                  {{ item.user_name }}
                </view>
                <view class="pinglun-t2">
                  {{ item.created_at }}
                </view>
              </view>
              <MyRate :current="item.score ? Number(item.score) : 5">
              </MyRate>
            </view>
            <view class="pinglun-content">
              <view class="text">
                {{ item.content }}
              </view>
              <view class="imgs" v-if="item.images">
                <image mode="aspectFit" @click="showImgs(item, index)"
                  v-for="(img, index) in item.images.split(',').slice(0, 3)" :key="img"
                  :src="img + '?x-image-process=image/resize,m_pad,h_100,w_100,color_F6F6F6'" class="img">
                </image>
                <view v-if="item.images.split(',').length > 3" class="more">共{{ item.images.split(',').length }}张</view>
              </view>
            </view>
          </view>
          <u-loadmore margin-top="40" margin-bottom="40" :status="status1" :load-text="loadText" />
        </template>
        <view class="no-data" v-else>
          <image src="https://obs.tuoyupt.com/miniprogram/client/images/no_data.png" mode="aspectFit" />
          <view class="t2">暂无评论</view>
        </view>
      </view>
      <view id="media-part" class="media-part" v-if="current1 == 0">
        <view class="media-tab">
          <view class="tab" :class="{ on: catId == item.id }" v-for="(item, index) in cat_id" :key="index"
            @click="mediaTab(item.id)">{{ item.name }}
          </view>
        </view>
        <u-waterfall class="media-list" v-model="list" v-if="list.length">
          <template v-slot:left="{ leftList }">
            <view v-for="(item, index) in leftList" :key="index" style="margin-right: 20rpx;">
              <view class="media" @click="preview(item)">
                <view class="tip" v-if="item.type == 0">
                  {{ str2arr(item.image).length }}
                </view>
                <image class="tip" v-else src="https://obs.tuoyupt.com/miniprogram/client/images/play_icon.png" />
                <view class="cover">
                  <image v-if="item.type == 0" :src="str2arr(item.image)[0]" mode="widthFix" lazy-load="true" />
                  <image v-else :src="item.video_face" mode="widthFix" lazy-load="true" />
                </view>
                <view class="name">{{ item.description }}</view>
              </view>
            </view>
          </template>
          <template v-slot:right="{ rightList }">
            <view v-for="(item, index) in rightList" :key="index">
              <view class="media" @click="preview(item)">
                <view class="tip" v-if="item.type == 0">
                  {{ str2arr(item.image).length }}
                </view>
                <image class="tip" v-else src="https://obs.tuoyupt.com/miniprogram/client/images/play_icon.png" />
                <view class="cover">
                  <image v-if="item.type == 0" :src="str2arr(item.image)[0]" mode="widthFix" lazy-load="true" />
                  <image v-else :src="item.video_face" mode="widthFix" lazy-load="true" />
                </view>
                <view class="name">{{ item.description }}</view>
              </view>
            </view>
          </template>
        </u-waterfall>
        <view class="no-data" v-else src="">
          <image src="https://obs.tuoyupt.com/miniprogram/client/images/no_data.png" mode="aspectFit" />
          <view class="t2">该分类暂无机构动态</view>
        </view>
        <u-loadmore v-if="list.length" margin-top="40" margin-bottom="40" :status="status" :load-text="loadText" />
      </view>
    </view>
    <u-action-sheet @click="call" :list="phoneList" :safe-area-inset-bottom="true" v-model="phoneShow"></u-action-sheet>
    <Preview :previewData="previewData" />
  </view>
  <view class="footer-zhanwei"></view>
  <view class="footer">
    <view class="footer-content">
      <view class="left">
        <view class="bb" @click="gotoEvaluate">
          <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/evaluate1.png">

          </image>
          <view class="t1">
            <!-- 评价 -->
          </view>
        </view>
        <view class="bb" @click="phoneShow = true">
          <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone1.png">

          </image>
          <view class="t1">
            <!-- 电话 -->
          </view>
        </view>
        <view class="bb" @click="infoMap">
          <image class="i1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/navigation1.png">

          </image>
          <view class="t1">
            <!-- 导航 -->
          </view>
        </view>
      </view>
      <view v-if="isChangeSchool" class="btn" @click="intoChange">申请转园</view>
      <view v-if="!isChangeSchool" class="btn" @click="intoForm(1)">预约参观</view>
      <view v-if="!isChangeSchool" style="margin-left: 24rpx;" class="btn" @click="intoForm(2)">入园体验</view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from "vue";
import { onLoad, onShow, onReachBottom, onPageScroll } from "@dcloudio/uni-app";
import request from "@/request.js";
import Preview from "@/components/preview.vue";
import MyRate from "./components/rate.vue";
import MVdieo from "./components/mvideo.vue";
import {
  id2name,
  str2arr,
  operate_condition,
  is_record,
  cat_id,
  school_class,
  free_type,
  place_type,
  facility_type,
  server_type,
  other_facilitys,
  school_nature_ids,
  feed_type,
} from "@/utils/dataHandle.js";

const schoolData = ref(null);
const list = ref([]);
const catId = ref("");
const navBackground = ref(0);
const hideOpenBtn = ref(false);
const listLoading = ref(false);
let tabList = ref([{
  name: '机构'
}, {
  name: '信息'
}, {
  name: '动态',
}])
let tabList1 = ref([{
  name: '机构动态'
}, {
  name: '机构评价'
}])
const current1 = ref(0);
let isLogin = ref(false);
let current = ref(0)
let swiperOption = reactive({
  indicatorDots: false,
  autoplay: false,
  interval: 2000,
  duration: 500,
});
let status = ref("loadmore");
let loadText = {
  loadmore: "轻轻上拉",
  loading: "努力加载中",
  nomore: "已经加载全部~",
};
let lastScrollTop = 0;
let phoneShow = ref(false);
let pageData = reactive({
  page: 0,
  per_page: 6,
});
let previewData = reactive({
  show: false,
  type: "image",
  video_url: "",
  image_list: [],
});
let headStyle = {
  height: "80rpx",
  color: "#787C8D",
};

let introOpen = ref(true);
let navbar = ref(null);

const props = defineProps(["id"]);

let swiperList = computed(() => {
  if (!schoolData.value) {
    return [];
  }
  let { head_imgs, head_video } = schoolData.value.service;
  let arr = [];
  if (head_video) {
    arr.push({
      type: 1,
      url: head_video
    });
  }
  if (head_imgs) {
    let imgs = head_imgs.split(",").map(it => {
      return {
        type: 2,
        url: it
      }
    })
    arr = [...arr, ...imgs];
  }
  if (arr.length == 0) {
    arr.push({
      type: 2,
      url: 'https://obs.tuoyupt.com/miniprogram/parentindex/school.png'
    });
  }
  return arr;
});

let phoneList = computed(() => {
  if (!schoolData.value) {
    return [];
  }
  return schoolData.value.service.phones.split(",").map((e) => {
    return { text: e };
  });
});

function backFun() {
  uni.navigateBack();
}

function preview(item) {
  if (item.type == "0") {
    previewData.type = "image";
    previewData.image_list = str2arr(item.image);
  } else if (item.type = "1") {
    previewData.type = "video";
    previewData.video_url = item.video_url;
  }
  previewData.show = true;
}

async function mediaTab(id) {
  if (catId.value == id) {
    return;
  }
  let scrollTop = 0;
  let getSystemInfo = await uni.getSystemInfo();
  uni.createSelectorQuery().select('#first-media').boundingClientRect(function (res) {
    if (res) {
      let top = getSystemInfo.screenHeight - res.height;
      scrollTop = res.top + lastScrollTop - top
      uni.pageScrollTo({
        scrollTop,
        duration: 300,
        success: function () {
          catId.value = id;
          getMediaList(true);
        }
      })
    } else {
      catId.value = id;
      getMediaList(true);
    }
  }).exec()

}
function showImgs(item, index) {
  uni.previewImage({
    current: index,
    urls: item.images.split(',')
  });
}
async function tabChange(index) {
  let scrollTop = 0;
  if (index == 0) {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  } else if (index == 1) {
    uni.createSelectorQuery().select('#msg-part').boundingClientRect(function (res) {
      scrollTop = res.top + lastScrollTop - 68
      uni.pageScrollTo({
        scrollTop,
        duration: 300
      })
    }).exec()
  } else if (index == 2) {
    uni.createSelectorQuery().select('#media-part').boundingClientRect(function (res) {
      scrollTop = res.top + lastScrollTop - 68
      uni.pageScrollTo({
        scrollTop,
        duration: 300
      })
    }).exec()
  }
  console.log(scrollTop);

}
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();


function infoMap() {
  uni.openLocation({
    latitude: Number(schoolData.value.service.lat),
    longitude: Number(schoolData.value.service.lng),
    name: schoolData.value.abbreviation,
    address: schoolData.value.service.address
  });

}

function intoForm(type) {
  uni.setStorageSync("visitSchool", {
    logo: schoolData.value.brand_logo,
    name: schoolData.value.abbreviation,
    id: schoolData.value.id,
  });
  uni.navigateTo({
    url: `/pages/pack1/school/form?type=1&submit_type=${type}`,
  });
}
function intoChange() {
  if(schoolData.value.id == userInfo.school_id) {
    uni.showToast({ icon: "none", title: '您已经在该托育机构' });
    return;
  }
  uni.setStorageSync("changeSchool", {
    name: schoolData.value.abbreviation,
    id: schoolData.value.id,
    address: schoolData.value.service.provicne_name + schoolData.value.service.city_name + schoolData.value.service.area_name + schoolData.value.service.address
  });
  uni.navigateTo({
    url: `/pages/pack1/apply/form?type=2`,
  });
}
function gotoEvaluate() {
  if (!Boolean(userInfo.id)) {
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
    return
  }
  uni.setStorageSync("evaluateSchool", schoolData.value);
  uni.navigateTo({
    url: `/pages/pack1/school/evaluate`,
  });
}

function call(index) {
  let phoneNumber = phoneList.value[index].text;
  console.log(index, phoneNumber);
  uni.makePhoneCall({
    phoneNumber, //仅为示例，并非真实的电话号码
  });
}
const remakeList = ref([])
let remakePage = 0;
const status1 = ref('loadmore')
function getRemakeList(newlist = false) {
  if (newlist) {
    remakePage = 0;
    remakeList.value = [];
    status1.value = "loadmore";
  }
  if (status.value == "nomore" || status.value == "loading") {
    return;
  }
  remakePage += 1;
  status.value == "loading";
  request({
    url: `/api/parent/school/remarkList?page=${remakePage}&per_page=5&school_id=${schoolData.value.id}`,
  }).then((res) => {
    remakeList.value = [...remakeList.value, ...res];
    status1.value = res.length < 5 ? "nomore" : "loadmore";
  });
}
function getMediaList(newlist = false) {
  if (newlist) {
    pageData.page = 0;
    list.value = [];
    status.value = "loadmore";
  }
  if (status.value == "nomore" || status.value == "loading") {
    return;
  }
  pageData.page += 1;
  status.value == "loading";
  let cat = catId.value && `&cat_id=${catId.value}`;
  listLoading.value = true;
  request({
    url: `/api/parent/school/advertise/${schoolData.value.id}?page=${pageData.page}&per_page=${pageData.per_page}${cat}`,
  }).then((res) => {
    list.value = [...list.value, ...res];
    status.value = res.length < pageData.per_page ? "nomore" : "loadmore";
    listLoading.value = false;
  });
}

onPageScroll((e) => {
  lastScrollTop = e.scrollTop;
  if ((e.scrollTop > 60)) {
    navBackground.value = 1;
  } else {
    navBackground.value = 0;
  }
})
onLoad(async (options) => {
  isLogin.value = uni.getStorageSync('token');
  request({
    url: "/api/parent/school/detail/" + options.id,
  }).then((res) => {
    schoolData.value = res;
    setTimeout(() => {
      uni.createSelectorQuery().select('#introduce').boundingClientRect(function (res) {
        if (res && res.height > 48) {
          introOpen.value = false;
        } else {
          hideOpenBtn.value = true;
          introOpen.value = true;
        }
      }).exec()
    }, 1000);
    getMediaList();
    getRemakeList();
  });
  if (options.back == 'home') {
    backHome = true;
  }
  uni.$on("add_remakeList", () => {
    getRemakeList(true);
  })
});
let isChangeSchool = ref(false);
onShow(() => {
  if (schoolData && uni.getStorageSync('visitAdd')) {
    schoolData.value.service.visit_num += 1;
    uni.removeStorageSync('visitAdd');
  }
  if (schoolData && uni.getStorageSync('changeSchool')) {
    isChangeSchool.value = true;
    uni.setStorageSync('changeSchool', '');
  } else {
    isChangeSchool.value = false;
  }
});
onReachBottom((e) => {
  if (current1.value == 0) {
    getMediaList();
  } else {
    getRemakeList();
  }
});
</script>

<style>
.u-mode-center-box {
  background: transparent !important;
}

.no-data,
.u-water-list {
  min-height: 400px;
}

.u-collapse-title {
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
.school-detail-page {
  .slot-wrap {
    position: absulute;
    width: calc(100vw - 38rpx - 24px);
    margin: 0 auto;
    padding-right: calc(168rpx + 24px);
    padding-left: 130rpx;
    left: 0;
    overflow: visible;

    .back-icon {
      width: 56rpx;
      height: 56rpx;
      position: absolute;
      left: 40rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .swiper {
    height: 420rpx;
    position: relative;
  }

  .swiper-wrap {
    height: 420rpx;
  }

  .swiper-item {
    display: block;
    height: 420rpx;
    text-align: center;

    video,
    image {
      height: 100%;
      display: block;
      width: 100%;
    }
  }

  .content {
    margin-top: -200rpx;
    border-radius: 24rpx 24rpx 0 0;
    padding: 0 30rpx;
    position: relative;
    z-index: 9;
    background: #fff;
    min-height: calc(100vh - 420rpx);
    overflow: hidden;

    .name-part {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30rpx;

      .right {
        flex: 0 0 80rpx;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }

      .left {
        flex: auto;

        .name {
          font-size: 40rpx;
          font-weight: 500;
          color: #262937;
        }

        .rate {
          display: flex;
          flex-direction: row;
          margin-top: 12rpx;

          .rtt {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .tag {
          display: flex;
          margin-top: 12rpx;
          flex-wrap: wrap;

          text {
            height: 40rpx;
            background: #f6f6f6;
            border-radius: 8rpx;
            padding: 0 16rpx;
            font-size: 20rpx;
            color: #262937;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16rpx;
            margin-bottom: 8rpx;
          }
        }
      }
    }

    .address {
      width: 690rpx;
      height: 132rpx;
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      margin-top: 40rpx;

      .back {
        position: absolute;
        width: 690rpx;
        height: 132rpx;
        left: 0;
        top: 0;
        z-index: 1;
      }

      .add-content {
        width: 690rpx;
        height: 132rpx;
        display: flex;
        flex-direction: row;
        position: relative;
        z-index: 2;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;

        .lefec {
          .t1 {
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
          }

          .distance {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .rsec {
          width: 40rpx;
          height: 40rpx;

          .right {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .intro-part {
      margin-top: 20rpx;
      height: 88rpx;
      font-size: 28rpx;
      color: #262937;
      line-height: 44rpx;
      position: relative;
      overflow: hidden;
      word-break: break-all;

      .open-btn {
        position: absolute;
        right: 0;
        top: 44rpx;
        width: 300rpx;
        height: 44rpx;
        text-align: right;
        color: #1975FF;
        background-image: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            #ffffff 100%);
      }

      .close-btn {
        text-align: right;
        color: #1975FF;
      }
    }

    .intro-open {
      overflow: visible;
      height: auto;

      .open-btn {
        display: none;
      }
    }

    .title-1 {
      font-size: 32rpx;
      font-weight: 500;
      color: #262937;
      line-height: 32rpx;
      letter-spacing: 1px;
      margin-bottom: 20rpx;
    }

    .title-2 {
      font-size: 28rpx;
      color: #787c8d;
      line-height: 40rpx;
      margin-top: 28rpx;
    }

    .title-3 {
      font-size: 28rpx;
      font-weight: 500;
      color: #515A6E;
      line-height: 44rpx;
      margin-top: 28rpx;
    }

    .value {
      font-size: 28rpx;
      color: #262937;
      line-height: 44rpx;
      margin-top: 14rpx;
    }

    .pinglun-item {
      width: 100%;
      padding-bottom: 40rpx;
      border-bottom: 1rpx solid #EEEEEE;
      margin-top: 40rpx;

      .pinglun-top {
        display: flex;
        flex-direction: row;
        width: 100%;

        .pinglun-avter {
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
        }

        .pinglun-info {
          flex: 1;
          padding-left: 18rpx;
          padding-top: 12rpx;

          .pinglun-t1 {
            font-size: 28rpx;
            font-weight: 500;
            color: #000000;
          }

          .pinglun-t2 {
            font-size: 28rpx;
            font-weight: 400;
            color: #AEB0BC;
            margin-top: 8rpx;
          }
        }
      }

      .pinglun-content {
        margin-top: 40rpx;
        width: 100%;
        padding-left: 82rpx;

        .imgs {
          display: flex;
          flex-direction: row;
          margin-top: 20rpx;
          position: relative;
          width: 100%;

          .img {
            width: 194rpx;
            height: 194rpx;
            border-radius: 16rpx;
            margin-right: 12rpx;
          }

          .more {
            position: absolute;
            width: 96rpx;
            height: 40rpx;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 20rpx;
            border: 2rpx solid rgba(255, 255, 255, 0.3);
            right: 16rpx;
            bottom: 16rpx;
          }
        }

        .text {
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          /* 超出几行省略 */
          overflow: hidden;
          font-size: 28rpx;
          font-weight: 400;
          color: #262937;
          line-height: 44rpx;
          position: relative;
        }
      }
    }

    .media-tab {
      display: flex;
      flex-wrap: wrap;
      margin-top: 30rpx;

      .tab {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #787c8d;
        height: 48rpx;
        background: #f6f6f6;
        border-radius: 8rpx;
        margin-right: 16rpx;
        line-height: 48rpx;
        padding: 0 20rpx;
        margin-bottom: 16rpx;
      }

      .on {
        background: #1975FF;
        color: #fff;
      }
    }

    .media-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .media {
        flex: 0 1 336rpx;
        width: 336rpx;
        margin-top: 40rpx;
        overflow: hidden;
        position: relative;

        .tip {
          width: 40rpx;
          height: 40rpx;
          position: absolute;
          right: 20rpx;
          top: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 24rpx;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
        }

        .cover {
          width: 336rpx;
          border-radius: 12rpx;
          overflow: hidden;
          background-color: #f6f6f6;
          max-height: 392rpx;

          image {
            width: 336rpx;
            display: block;
          }
        }

        .name {
          margin-top: 16rpx;
          font-size: 28rpx;
          color: #262937;
          line-height: 36rpx;
          letter-spacing: 1px;
        }
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;

    .left {
      display: flex;
      flex-direction: row;

      .bb {
        margin-right: 32rpx;
        text-align: center;

        .i1 {
          width: 40rpx;
          height: 40rpx;
        }

        .t1 {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
          line-height: 34rpx;
        }
      }
    }

    .btn {
      width: 346rpx;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }
}
</style>
