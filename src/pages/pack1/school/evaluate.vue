<template>
  <u-navbar :border-bottom="false" :background="{ backgroundColor: '#FFFFFF' }" title="评论">
  </u-navbar>
  <view class="info">
    <u-image v-if="info.brand_logo" width="88rpx" height="88rpx" border-radius="16" mode="aspectFill"
      :src="info.brand_logo"></u-image>
    <u-image v-else width="88rpx" height="88rpx" border-radius="16" mode="aspectFill"
      src="https://obs.tuoyupt.com/miniprogram/parentindex/school1.png"></u-image>
    <view class="name">{{ info.abbreviation }}</view>
  </view>
  <view class="hr"></view>
  <view class="rate">
    <view class="rtt">评分</view>
    <MyRate v-model="score" :disabled="false">
    </MyRate>
  </view>
  <view class="input">
    <u-input height="200" placeholder="谈谈您的看法，帮助大家选择" v-model="content" type="textarea" />
  </view>
  <view class="upload">
    <u-upload width="210rpx" height="210rpx" max-count="6" @on-list-change="imgChange" multiple deletable
      :auto-upload="false">
    </u-upload>
  </view>
  <view class="footers"></view>
  <view class="footer">
    <view class="footer-content">
      <view class="btn" @click="submit">发布</view>
    </view>
  </view>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import { OBSupload } from '@/utils/obs/obs-upload.js';
import request from "@/request.js";
import MyRate from "./components/rate.vue";
onLoad(() => {
  info.value = uni.getStorageSync("evaluateSchool");
})
const score = ref(0)
const content = ref('')
const info = ref({
  entrance_img: "",
  abbreviation: ""
})
let imgList = []
function imgChange(e) {
  imgList = e
}
async function submit() {
  if (score.value == 0) {
    uni.showToast({
      title: "请打分",
      icon: "none"
    })
    return
  }
  if (content.value == '') {
    uni.showToast({
      title: "请输入评价",
      icon: "none"
    })
    return
  }
  uni.showLoading();
  let imgs = [];
  if (imgList.length) {
    let requestArr = []
    imgList.forEach(item => {
      let fileName = item.file.name.substring(item.file.name.lastIndexOf("/") + 1);
      let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      requestArr.push(OBSupload(item.url, fileExtension, fileName))
    })
    imgs = await Promise.all(requestArr);
  }
  request({
    url: `/api/parent/school/remark`,
    method: "post",
    data: {
      content: content.value,
      school_id: info.value.id,
      score: score.value,
      images: imgs.join(",")
    },
  }).then(() => {
    uni.hideLoading();
    uni.showToast({
      title: "评价成功！",
      icon: "none",
      mask: true,
      duration: 1500
    })
    uni.$emit("add_remakeList")
    setTimeout(uni.navigateBack, 1500);
  })
}
</script>
<style scoped lang="scss">
.info {
  width: 750rpx;
  height: 160rpx;
  background: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 30rpx;

  .name {
    margin-left: 32rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #262937;
    line-height: 40rpx;
  }
}

.rate {
  width: 750rpx;
  height: 128rpx;
  background: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 30rpx;

  .rtt {
    margin-right: 32rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #787C8D;
  }
}

.input {
  width: 690rpx;
  background: #F1F5F8;
  border-radius: 24rpx;
  margin: 0 auto;
  padding: 24rpx;
}

.upload {
  width: 694rpx;
  margin: 40rpx auto;
}

.hr {
  width: 694rpx;
  height: 2rpx;
  border: 1rpx solid #EEEEEE;
  margin: 0 auto;
}

.footers {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  bottom: 0;
  left: 0;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;

    .btn {
      width: 690rpx;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }
}
</style>