<template>
  <view class="top">
    <image class="backimg" src="https://obs.tuoyupt.com/miniprogram/index/atopback3.png">
    </image>
  </view>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive :background="{ backgroundColor: 'rgba(255,255,255,0)' }" title="">
      <view class="warp">
        <image class="icon1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/loc.png">
        </image>
        <nj-address @change="lochange">
          <view class="text">
            {{ currentLocal.city }}·{{ currentLocal.region }}
          </view>
        </nj-address>
        <image class="icon2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/xiala.png">
        </image>
      </view>
    </u-navbar>
    <view class="top-zhanwei"></view>
    <view class="topfix">
      <u-search height="80" @clear="getList" @search="getList" shape="square" :show-action="false" placeholder="托育机构"
        v-model="keyword"></u-search>
      <view class="section">
        <view class="t1 flex-ct" @click="searchPopupShow = true;">
          <text>托育机构</text>
          <u-icon style="transform: translate(12rpx, 4rpx);" name="arrow-down-fill" color="#d8d8d8" size="22"></u-icon>
        </view>
        <view class="t2" @click="goMap">
          托育地图
        </view>
        <image class="map" @click="goMap" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/map.png">
        </image>
      </view>
    </view>
    <template v-if="list.length">
      <view class="item" @click="intoDetail(item.id)" v-for="(item, index) in list" :key="index">
        <u-image v-if="item.entrance_img" width="240rpx" height="240rpx" border-radius="16" mode="aspectFill"
          :src="item.entrance_img"></u-image>
        <u-image v-else width="240rpx" height="240rpx" border-radius="16" mode="aspectFill"
          src="https://obs.tuoyupt.com/miniprogram/parentindex/school1.png"></u-image>
        <view class="msg">
          <view class="name">{{ item.abbreviation }}</view>
          <view class="address">
            <view class="address-text">{{ item.address }}</view>
            <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2) }}km</text>
              <text v-else class="distance">{{ item.distance * 1000 }}m</text>
            </template>
          </view>
          <view class="tag">
            <text class="text" v-if="item.free_type">{{ id2name(item.free_type, free_type) }}</text>
            <text class="text" v-if="item.school_class">{{ id2name(item.school_class, school_class) }}</text>
            <text class="text" v-if="item.is_record != 0">{{ id2name(item.is_record, is_record) }}</text>
            <text class="text" v-if="item.school_nature_ids" v-for="(item, index) in str2arr(item.school_nature_ids)"
              :key="index">{{
                id2name(item,
                  school_nature_ids) }}</text>
          </view>
          <view class="rate">
            <view class="rtt">评分</view>
            <MyRate :current="item.score ? Number(item.score) : 5" :disabled="true">
            </MyRate>
          </view>
        </view>
      </view>
      <u-loadmore margin-top="40" :status="status" :load-text="loadText" />
    </template>
    <view class="mno-data" v-else-if="keyword">
      <image class="image" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png" mode="aspectFit" />
      <view class="t1">抱歉</view>
      <view class="t2">未找到相关托育机构</view>
    </view>
    <view class="mno-data" v-else>
      <image class="image" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png" mode="aspectFit" />
      <view class="t1">抱歉</view>
      <view class="t2">该地区暂无托育机构</view>
    </view>
    <view class="zhanwei"></view>
    <u-popup v-model="searchPopupShow" mode="top" border-radius="14">
      <view class="slot-content" :style="{ height: `auto` }">
        <scroll-view scroll-y="true" style="height: 100%">
          <view class="search-title">托育机构服务范围</view>
          <view class="search-list">
            <view @click="filterHandle('school_nature_ids', item.id)"
              :class="{ on: searchData.school_nature_ids == item.id }" class="search-item"
              v-for="(item, index) in school_nature_ids" :key="index">{{ item.name }}</view>
          </view>
          <view class="search-title">托育机构类型</view>
          <view class="search-list">
            <view @click="filterHandle('school_class', item.id)" :class="{ on: searchData.school_class == item.id }"
              class="search-item" v-for="(item, index) in school_class" :key="index">{{ item.name }}</view>
          </view>
          <view class="search-title">托育机构价格范围</view>
          <view class="search-list">
            <view @click="filterHandle('price', item)" :class="{ on: searchData.price == item }" class="search-item"
              v-for="(item, index) in price_arr" :key="index">{{ item }}</view>
          </view>
          <view style="height: 60rpx;"></view>
        </scroll-view>
        <view class="fixed-btn">
          <view class="btn-wrap">
            <view class="btn" @click="searchReset">重置</view>
            <view class="btn confirm" @click="getList">确定</view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup>
import { ref, watch, reactive } from 'vue';
import { onReachBottom, onLoad, onShow } from '@dcloudio/uni-app';
import request from "@/request.js";
import { id2name, str2arr, is_record, school_class, free_type, school_nature_ids } from '@/utils/dataHandle.js';
import { useLocalStore } from "@/stores/useLocalStore.js";
import MyRate from "./components/rate.vue"
let loadText = {
  loadmore: '轻轻上拉',
  loading: '努力加载中',
  nomore: '已经加载全部'
}

const searchData = reactive({
  school_class: '',
  school_nature_ids: [],
  price: ''
});


function intoDetail(id) {
  if (id) {
    uni.navigateTo({
      url: `/pages/pack1/school/detail?id=${id}`,
    });
  }
}

function lochange(e) {
  currentLocal.province = e.provinceCode;
  currentLocal.city = e.city;
  currentLocal.areaCode = e.areaCode;
  currentLocal.region = e.area;
  page = 1;
  getJingwei(e.areaCode, currentLocal.city + currentLocal.region)
}
function getJingwei(code, name) {
  request({
    url: `/api/city/getlatlng`,
    data: {
      city: code,
      address: name
    }
  }).then(res => {
    let location = res?.result?.location;
    if (location) {
      currentLocal.citylatitude = location.lat;
      currentLocal.citylongitude = location.lng;
    }
  })
}
let page = 1;
const keyword = ref("");
const currentLocal = useLocalStore();
let list = ref([])
let status = ref('loadmore');
let hasMore = true;
function getList() {
  searchPopupShow.value = false;
  page = 1;
  uni.showLoading()
  request({
    url: `/api/parent/school/list`,
    data: {
      page,
      per_page: 10,
      order: "distance",
      name: keyword.value,
      area: currentLocal.areaCode,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude,
      ...searchData
    }
  }).then(res => {
    list.value = res;
    if (res.length < 10) {
      hasMore = false;
      status.value = 'nomore';
    } else {
      hasMore = true;
      status.value = 'loadmore';
    }
    uni.hideLoading();
  })
}
let price_arr = ['0-3000', '3001-5000', '5001-7000', '7001-9000', '9001',]
function filterHandle(type, id) {
  if (searchData[type] === id) {
    searchData[type] = ''
  } else {
    searchData[type] = id
  }
}
function searchReset() {
  searchData.name = '';
  searchData.school_class = '';
  searchData.price = '';
  searchData.school_nature_ids = [];
}

onReachBottom(() => {
  if (hasMore) {
    status.value = 'loading';
    page++;
    request({
      url: `/api/parent/school/list`,
      data: {
        page,
        per_page: 10,
        order: "distance",
        name: keyword.value,
        area: currentLocal.areaCode,
        lng: currentLocal.longitude,
        lat: currentLocal.latitude
      }
    }).then(res => {
      list.value.push(...res);
      if (res.length < 10) {
        hasMore = false;
        status.value = 'nomore';
      } else {
        hasMore = true;
        status.value = 'loadmore';
      }
    })
  }
})
function goMap() {
  uni.navigateTo({
    url: "/pages/pack1/school/map"
  })
}
let searchPopupShow = ref(false);
watch(() => currentLocal.areaCode, () => {
  getList();
}, {
  immediate: true
})

onLoad(async () => {
  uni.showLoading()
  try {
    let location = await uni.getLocation({
      type: "gcj02",
    });
    if (location.errMsg == "getLocation:ok") {
      currentLocal.latitude = location.latitude;
      currentLocal.longitude = location.longitude;
      currentLocal.noLocation = false;
      let res = await request({ url: `/api/city/name?lat=${location.latitude}&lng=${location.longitude}` });
      let { province, city, district, adcode } = res.result.addressComponent;
      currentLocal.province = province;
      currentLocal.city = city;
      currentLocal.region = district;
      currentLocal.areaCode = adcode;
    } else {
      currentLocal.noLocation = true;
    }
    uni.hideLoading();
  } catch (error) {
    uni.hideLoading();
    return false;
  }
})
</script>

<style lang="scss" scoped>
.pages {
  background-color: #FFFFFF;

  .top-zhanwei {
    width: 750rpx;
    height: 368rpx;
  }


  .topfix {
    position: sticky;
    top: 200rpx;
    z-index: 3;
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    padding: 30rpx 30rpx 0;
  }

  .section {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 96rpx;
    background: #FFFFFF;
    border-bottom: 1rpx solid #EEE;

    .t1 {
      font-size: 32rpx;
      font-weight: 600;
      color: #262937;
    }

    .t2 {
      font-size: 26rpx;
      font-weight: 400;
      color: #797E8E;
      margin-left: auto;
    }

    .map {
      width: 48rpx;
      height: 48rpx;
      margin-left: 8rpx;
    }
  }



  .item {
    width: 100%;
    height: 240rpx;
    display: flex;
    background: #fff;
    margin-top: 30rpx;
    flex-direction: row;
    padding: 0 30rpx;

    .msg {
      padding-left: 16rpx;
      flex: 1;

      .name {
        font-size: 28rpx;
        font-weight: 500;
        color: #262937;
        margin: 12rpx 0;
        line-height: 28rpx;
        width: 100%;
      }

      .address {
        font-size: 24rpx;
        color: #787C8D;
        width: 100%;
        display: flex;
        justify-content: space-between;

        .address-text {
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          height: 58rpx;
          margin-bottom: 6rpx;
          width: 306rpx;
          line-height: 30rpx;
          overflow: hidden;
        }

        .distance {
          color: #262937;
        }
      }

      .rate {
        display: flex;
        flex-direction: row;

        .rtt {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
        }
      }

      .tag {
        display: flex;
        overflow: hidden;
        margin-top: 14rpx;
        margin-bottom: 14rpx;
        overflow-x: auto;

        .text {
          font-size: 20rpx;
          color: #262937;
          padding: 0 16rpx;
          margin-right: 8rpx;
          margin-bottom: 8rpx;
          background: #F6F6F6;
          border-radius: 8rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          flex-shrink: 0;
        }
      }

      .num {
        font-size: 24rpx;
        color: #FF7373;
      }
    }
  }
}

.zhanwei {
  height: calc(env(safe-area-inset-bottom));
}

.mno-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20%;
  flex-direction: column;
  background: #fff;

  .image {
    width: 160rpx;
    height: 160rpx;
    margin: 196rpx 0 24rpx;
  }

  .t1 {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #262937;
    margin-bottom: 20rpx;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 400;
    color: #aeb0bc;
    line-height: 44rpx;
  }

  .btn {
    width: 240rpx;
    height: 80rpx;
    background: #1975FF;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 48rpx;
    margin-bottom: 100rpx;
  }
}

.slot-content {
  background: #fff;
  padding-bottom: 160rpx;
  position: relative;

  .search-list {
    overflow: hidden;
    padding: 0 30rpx;
  }

  .search-title {
    font-size: 24rpx;
    font-weight: 500;
    color: #111111;
    line-height: 34rpx;
    margin: 40rpx auto 0;
    padding: 0 30rpx;
  }

  .search-item {
    float: left;
    width: calc((100vw - 120rpx) / 3);
    height: 60rpx;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #262937;
    border-radius: 8rpx;
    background: #F6F6F6;
    margin: 28rpx 28rpx 0 0;

    &:nth-of-type(3n) {
      margin-right: 0;
    }
  }

  .on {
    color: #FF7373;
    background: #FEEAEA;
    border: 2rpx solid #FF7373;
  }

  .fixed-btn {
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
    z-index: 99;
    padding-top: 20rpx;
    height: 160rpx;
    background: #fff;
    display: flex;
    justify-content: center;
    box-shadow: 0 2rpx 0rpx #888992;
  }

  .btn-wrap {
    width: 690rpx;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    border: 2rpx solid #FF7373;
    height: 80rpx;

    .btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FF7373;
      background: #fff;
      height: 80rpx;
    }

    .confirm {
      background: #FF7373;
      color: #fff;
    }
  }
}

.top {
  width: 750rpx;
  height: 400rpx;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 1;

  .backimg {
    width: 750rpx;
    height: 420rpx;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.warp {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 24rpx;

  .icon1 {
    width: 40rpx;
    height: 40rpx;
    margin-right: 14rpx;
  }

  .text {
    font-size: 34rpx;
    font-weight: 600;
    color: #000000;
  }

  .icon2 {
    width: 32rpx;
    height: 32rpx;
  }
}
</style>
