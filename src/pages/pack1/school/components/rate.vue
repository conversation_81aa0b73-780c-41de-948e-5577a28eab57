<template>
  <view class="list">
    <view v-for="(item, index) in list" :key="item" @click="change(index)">
      <image class="i" v-if="item.type == 1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/st1.png">
      </image>
      <image class="i" v-else src="https://obs.tuoyupt.com/yjy_mini/front_img/school/st2.png">
      </image>
    </view>
  </view>
</template>
<script setup>
import { computed } from "vue";
const props = defineProps({
  modelValue: {
    type: Number,
    default: -1
  },
  current: {
    type: Number,
    default: 3
  },
  disabled: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['update:modelValue'])
function change(index) {
  if (!props.disabled) {
    let rate = index + 1;
    emit('update:modelValue', rate)
  }
}
const list = computed(() => {
  let active = 0;
  if (props.modelValue != -1) {
    active = props.modelValue;
  } else {
    active = props.current;
  }
  active = Math.round(active)
  let inactive = 5 - active;
  let arr = [];
  for (let index = 0; index < active; index++) {
    arr.push({
      type: 1
    })
  }
  for (let index = 0; index < inactive; index++) {
    arr.push({
      type: 2
    })
  }
  return arr;
})
</script>
<style lang="scss" scoped>
.list {
  display: flex;
  flex-direction: row;

  .i {
    width: 32rpx;
    height: 32rpx;
    margin: 0 8rpx;
  }
}
</style>