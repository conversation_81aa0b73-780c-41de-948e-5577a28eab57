<template>
  <view class="svideo">
  </view>
</template>
<script setup>
import { onMounted, ref } from "vue";
const props = defineProps({
  src: String
})
onMounted(async () => {
  let video = document.createElement("video");
  video.src = props.src;
  video.autoplay = true;
  video.controls = true;
  video.muted = true;
  video.poster = props.src + '?vframe/jpeg/offset/1'
  video.style = "height: 220px;display: block;width: 100%;object-fit: contain;"
  document.querySelector(".svideo").appendChild(video);
  video.play();
})

</script>
<style lang="scss" scoped>
.svideo {
  height: 420rpx;
  display: block;
  width: 100%;
  background: #000;
}
</style>