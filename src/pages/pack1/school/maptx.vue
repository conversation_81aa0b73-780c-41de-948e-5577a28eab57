<template>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive
      :background="{ backgroundColor: 'linear-gradient(180deg, rgba(241,245,248,0), rgba(241,245,248,1))' }"
      title="托育地图">
    </u-navbar>
    <map id="map" :style="{ width: '750rpx', height: '100vh' }" :latitude="currentLocal.citylatitude"
      :longitude="currentLocal.citylongitude" :markers="covers" @markertap="onMarkertap" scale="11">
    </map>
  </view>
  <view class="dingwei" @click="backLoacl">
    <view class="d1">
      <view class="d2">
      </view>
    </view>
  </view>
  <view class="pop" :style="{ top: popTop }" @touchstart="onTouchstart" @touchend="onTouchend">
    <template v-if="topState > 0">
      <view class="topsectionc" @click="changeTop">
        <image class="showup" v-if="topState == 1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/up.png">
        </image>
        <image class="showup" v-if="topState == 2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/down.png">
        </image>
      </view>
    </template>
    <view class="pop-top">
      <view class="warp">
        <image class="icon1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/loc.png">
        </image>
        <nj-address @change="lochange">
          <view class="text">
            {{ currentLocal.region }}
          </view>
        </nj-address>
        <image class="icon2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/xiala.png">
        </image>
      </view>
      <view class="hr"></view>
      <u-search bg-color="#F1F5F8" style="width: 500rpx;" :adjustPosition="false" :showLeft="false" @focus="onFocus"
        height="80" @clear="onClear" @search="search" shape="square" :show-action="false" placeholder="托育机构"
        v-model="keyword"></u-search>
      <view @click="changeTop" v-if="isSearch" class="quxiao">取消</view>
    </view>
    <view class="search" v-if="showSearchRes">
      <view class="ti">搜索结果:</view>
      <scroll-view class="scoll" scroll-y v-if="list.length">
        <view class="sitem" v-for="item in list" :key="item.id" @click="showdetailInfo(item)">
          <u-image v-if="item.entrance_img" width="64rpx" height="64rpx" border-radius="32" mode="aspectFill"
            :src="item.entrance_img"></u-image>
          <u-image v-else width="64rpx" height="64rpx" border-radius="32" mode="aspectFill"
            src="https://obs.tuoyupt.com/yjy_mini/front_img/school/jigou.png"></u-image>
          <view class="sitem-info">
            <view class="name">{{ item.abbreviation }}</view>
            <view class="addressa">
              <template v-if="!currentLocal.noLocation">
                <text class="t1" v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2) }}km·</text>
                <text v-else class="t1">{{ item.distance * 1000 }}m·</text>
              </template>
              <text class="t1">{{ item.area_name }}|{{ item.address }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <u-empty text="未找到机构" v-else></u-empty>
    </view>
    <view class="info-section" v-if="showInfo">
      <view class="info-top">
        <view class="left" @click="intoDetail(info.id)">
          <view class="name">
            <view class="namess">{{ info.abbreviation }} </view>
            <u-icon size="20" color="#797E8E" name="arrow-right"></u-icon>
          </view>
          <view class="tag">
            <text class="text" v-if="info.free_type">{{ id2name(info.free_type, free_type) }}</text>
            <text class="text" v-if="info.school_class">{{ id2name(info.school_class, school_class) }}</text>
            <text class="text" v-if="info.is_record != 0">{{ id2name(info.is_record, is_record) }}</text>
          </view>
          <view class="rate">
            <view class="rtt">评分</view>
            <MyRate :current="info.score ? Number(info.score) : 5">
            </MyRate>
          </view>
        </view>
        <image class="phone" @click="phoneShow = true"
          src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone.png">
        </image>
      </view>
      <view class="address" @click="goDaohang">
        <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
        </image>
        <view class="add-content">
          <view class="lefec">
            <view class="t1">{{ info.address }}</view>
            <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="info.distance >= 1">距你{{ (info.distance * 1).toFixed(2)
                }}km</text>
              <text v-else class="distance">距你{{ info.distance * 1000 }}m</text>
            </template>
          </view>
          <view class="rsec">
            <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
            </image>
          </view>
        </view>
      </view>
      <view class="des" v-if="info.introduce" @click="intoDetail(info.id)">
        {{ info.introduce }}
        <view class="btn">详情</view>
      </view>
    </view>
  </view>
  <u-action-sheet @click="call" :list="phoneList" :safe-area-inset-bottom="true" v-model="phoneShow"></u-action-sheet>
</template>
<script setup>
import { ref, nextTick, computed } from "vue";
import { useLocalStore } from "@/stores/useLocalStore.js";
import { id2name, is_record, school_class, free_type, school_nature_ids } from '@/utils/dataHandle.js';
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request.js";
const currentLocal = useLocalStore();
import MyRate from "./components/rate.vue"
onLoad(() => {
  getList();
})
const info = ref({
  abbreviation: "",
  address: "",
  area: 610400,
  area_name: "",
  city: 610100,
  city_name: "",
  distance: "",
  entrance_img: null,
  free_type: 2,
  id: 0,
  rate: 3,
  is_record: 0,
  lat: "",
  lng: "",
  name: "",
  provicne_name: "",
  province: 610000,
  school_class: 0,
  school_nature_ids: "",
  short_name: "",
  visit_num: 0,
  wechat_name: 0,
  wechatname: "",
  phones: '',
  introduce: ""
});
const phoneShow = ref(false)
let phoneList = computed(() => {
  if (!info.value.phones) {
    return [];
  }
  return info.value.phones.split(",").map((e) => {
    return { text: e };
  });
});
function call(index) {
  let phoneNumber = phoneList.value[index].text;
  console.log(index, phoneNumber);
  uni.makePhoneCall({
    phoneNumber, //仅为示例，并非真实的电话号码
  });
}
function backLoacl() {
  let mapRef = uni.createMapContext("map");
  mapRef.moveToLocation({
    latitude: currentLocal.latitude,
    longitude: currentLocal.longitude
  })
}
function lochange(e) {
  currentLocal.province = e.provinceCode;
  currentLocal.city = e.city;
  currentLocal.areaCode = e.areaCode;
  currentLocal.region = e.area;
  getJingwei(e.areaCode, currentLocal.city + currentLocal.region)
  getList();
}
function getJingwei(code, name) {
  request({
    url: `/api/city/getlatlng`,
    data: {
      city: code,
      address: name
    }
  }).then(res => {
    let location = res?.result?.location;
    if (location) {
      let mapRef = uni.createMapContext("map");
      mapRef.moveToLocation({
        latitude: Number(location.lat),
        longitude: Number(location.lng)
      })
    }
  })
}
function mapMoveCurrent() {
  let mapRef = uni.createMapContext("map");
  mapRef.moveToLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng)
  })
}
const showInfo = ref(false);
const showDetail = ref(false);
async function onMarkertap(e) {
  if (e.detail.markerId == 9999) return
  let data = list.value.find(item => item.id == e.detail.markerId);
  info.value = data;
  mapMoveCurrent();
  await nextTick();
  showInfo.value = true;
}
const popTop = computed(() => {
  if (isSearch.value) {
    return '15vh';
  } else if (showInfo.value) {
    return showDetail.value ? 'calc(100vh - 724rpx)' : 'calc(100vh - 384rpx)';
  } else {
    return 'calc(100vh - 224rpx)'
  }
})
const topState = computed(() => {
  if (isSearch.value) {
    return 2;
  } else if (showInfo.value) {
    return showDetail.value ? 2 : 1;
  } else {
    return 0
  }
})
function changeTop() {
  if (isSearch.value) {
    showSearchRes.value = false;
    isSearch.value = false;
  } else if (showDetail.value) {
    showDetail.value = false;
  } else if (showInfo.value) {
    showDetail.value = true;
  }
}
let startY = 0;
function onTouchstart(e) {
  startY = e.changedTouches[0].pageY
}
function onTouchend(e) {
  let endY = e.changedTouches[0].pageY;
  let diff = endY - startY;

  if (showInfo.value) {
    if (diff > 50) {
      if (showDetail.value) {
        showDetail.value = false;
      }
    } else if (diff < -50) {
      if (!showDetail.value) {
        showDetail.value = true;
      }
    }
  }
}
function onFocus() {
  isSearch.value = true;
  showInfo.value = false;
  showSearchRes.value = false;
}
const list = ref([])
function getList() {
  uni.showLoading();
  return request({
    url: `/api/parent/school/list`,
    data: {
      page: 1,
      per_page: 30,
      order: "distance",
      name: keyword.value,
      area: currentLocal.areaCode,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    list.value = res;
    covers.value = list.value.map(item => {
      return {
        id: item.id,
        latitude: Number(item.lat),
        longitude: Number(item.lng),
        width: 45,
        height: 58,
        iconPath: 'https://obs.tuoyupt.com/miniprogram/parentindex/map1.png'
      }
    })
    uni.hideLoading();
  })
}
const covers = ref([]);
const isSearch = ref(false);
const showSearchRes = ref(false);
const keyword = ref("");
function onClear() {
  getList();
}
function showdetailInfo(item) {
  showSearchRes.value = false;
  info.value = item;
  showInfo.value = true;
  mapMoveCurrent();
}
function search() {
  getList()
    .then(() => {
      showSearchRes.value = true;
    })
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng),
    name: info.value.abbreviation,
    address: info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
function intoDetail(id) {
  if (id) {
    uni.navigateTo({
      url: `/pages/pack1/school/detail?id=${id}`,
    });
  }
}
</script>
<style scoped lang="scss">
.dingwei {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.09);
  position: fixed;
  z-index: 2;
  right: 30rpx;
  top: calc(100vh - 324rpx);

  .d1 {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid #262937;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .d2 {
      width: 8rpx;
      height: 8rpx;
      background: #262937;
      border-radius: 50%;
    }
  }
}

.pop {
  width: 750rpx;
  height: 85vh;
  background: #FFFFFF;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  position: fixed;
  top: calc(100vh - 224rpx);
  left: 0;
  padding: 64rpx 30rpx 0;
  z-index: 3;
  transition: top 0.5s ease;

  .topsectionc {
    top: 0rpx;
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
    text-align: center;
    width: 300rpx;
    height: 45rpx;

    .showup {
      width: 56rpx;
      height: 12rpx;
    }
  }


  .pop-top {
    width: 690rpx;
    height: 80rpx;
    background: #F1F5F8;
    border-radius: 24rpx;
    display: flex;
    flex-direction: row;
    align-items: center;

    .hr {
      width: 2rpx;
      height: 32rpx;
      background: #AEB0BC;
    }

    .quxiao {
      width: 106rpx;
      text-align: center;
      height: 36rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #1975FF;
      background: #F1F5F8;
    }

    .warp {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      width: 188rpx;
      height: 80rpx;

      .icon1 {
        width: 24rpx;
        height: 24rpx;
        margin-right: 12rpx;
      }

      .text {
        width: 78rpx;
        font-size: 26rpx;
        font-weight: 500;
        color: #262937;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }



      .icon2 {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .search {
    width: 100%;
    height: calc(100% - 100rpx);

    .ti {
      height: 108rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #787C8D;
      line-height: 108rpx;
    }

    .scoll {
      width: 100%;
      height: calc(100% - 108rpx);

      .sitem {
        display: flex;
        flex-direction: row;
        width: 100%;
        margin-bottom: 30rpx;

        .sitem-info {
          flex: 1;
          padding-left: 20rpx;

          .name {
            font-size: 32rpx;
            font-weight: 600;
            color: #262937;
          }

          .addressa {
            font-size: 26rpx;
            font-weight: 400;
            color: #7A7E8F;
            line-height: 36rpx;
            margin-top: 16rpx;
          }
        }
      }
    }
  }

  .info-top {
    display: flex;
    flex-direction: row;
    padding-top: 40rpx;

    .left {
      flex: 1;

      .name {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;

        .namess {
          font-size: 40rpx;
          font-weight: 500;
          color: #262937;
          max-width: 540rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .rate {
        display: flex;
        flex-direction: row;

        .rtt {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
        }
      }

      .tag {
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;
        margin-top: 14rpx;
        margin-bottom: 14rpx;
        min-height: 40rpx;

        .text {
          font-size: 20rpx;
          color: #262937;
          padding: 0 16rpx;
          margin-right: 8rpx;
          margin-bottom: 8rpx;
          background: #F6F6F6;
          border-radius: 8rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
        }
      }
    }

    .phone {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }
  }

  .address {
    width: 690rpx;
    height: 132rpx;
    position: relative;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 40rpx;

    .back {
      position: absolute;
      width: 690rpx;
      height: 132rpx;
      left: 0;
      top: 0;
      z-index: 1;
    }

    .add-content {
      width: 690rpx;
      height: 132rpx;
      display: flex;
      flex-direction: row;
      position: relative;
      z-index: 2;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;

      .lefec {
        .t1 {
          font-size: 28rpx;
          font-weight: 500;
          color: #262937;
          line-height: 40rpx;
        }

        .distance {
          font-size: 24rpx;
          font-weight: 400;
          color: #787C8D;
        }
      }

      .rsec {
        width: 40rpx;
        height: 40rpx;

        .right {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
  }

  .des {
    margin-top: 40rpx;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 超出几行省略 */
    overflow: hidden;
    font-size: 28rpx;
    font-weight: 400;
    color: #262937;
    line-height: 44rpx;
    position: relative;

    .btn {
      width: 56rpx;
      height: 44rpx;
      font-size: 28rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #1975FF;
      line-height: 44rpx;
      right: 0;
      bottom: 0;
      position: absolute;
      background: #FFFFFF;

      &::before {
        content: " ";
        position: absolute;
        top: 0;
        left: -150rpx;
        width: 150rpx;
        height: 44rpx;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%);
      }
    }
  }
}
</style>