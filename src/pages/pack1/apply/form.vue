<template>
  <view class="page-complaint-form">
    <u-navbar :title="navTitle + '申请'" :background="{ background: '#FFFFFF' }" :border-bottom="true">
      <template #right>
        <view class="custom-right">
          <u-icon name="order" size="50" @click="handleRightClick"></u-icon>
        </view>
      </template>
    </u-navbar>

    <view class="card-item">
      <view class="header">
        <text class="title">{{ type == 2 ? '原托育机构' : '申请离托' }}信息</text>
      </view>
      <view class="info-item">
        <text class="label">{{ navTitle }}婴幼儿：</text>
        <text class="value">{{ formData.name }}</text>
      </view>
      <view class="info-item">
        <text class="label">年龄：</text>
        <text class="value">{{ formData.age }}</text>
      </view>
      <view class="info-item">
        <text class="label">{{ navTitle }}机构：</text>
        <text class="value">{{ formData.school_name }}</text>
      </view>
      <view class="info-item">
        <text class="label">机构所在地：</text>
        <text class="value">{{ currentLocal }}</text>
      </view>
      <view class="info-item">
        <text class="label">所在班级：</text>
        <text class="value">{{ formData.class_name }}</text>
      </view>
    </view>
    <view class="card-item" v-if="type == 2">
      <view class="header">
        <text class="title">目标托育机构信息</text>
      </view>
      <view class="info-item">
        <text class="label">托育机构名称：</text>
        <text class="value">{{ formData.transfer_school_name }}</text>
      </view>
      <view class="info-item">
        <text class="label">机构所在地：</text>
        <text class="value">{{ formData.transfer_school_address }}</text>
      </view>
    </view>
    <view class="card-item" style="padding-top: 24rpx;" v-if="pageReadonly">
      <view class="info-item">
        <text class="label">{{ navTitle }}日期：</text>
        <text class="value">{{ formData.time }}</text>
      </view>
      <view class="info-item">
        <text class="label">{{ navTitle }}原因：</text>
        <text class="value">{{ formData.reason }}</text>
      </view>
    </view>
    <view class="card-item" style="padding-left: 48rpx;" v-else>
      <u-form ref="formRef" :border-bottom="false" :model="formData" label-position="top">
        <u-form-item required :label="navTitle + '日期：'" prop="time">
          <u-input type="select" v-model="formData.time" placeholder="选择日期" @click="showDate = true" />
        </u-form-item>
        <u-form-item required :label="navTitle + '原因：'" prop="reason">
          <u-input maxlength="800" v-model="formData.reason" type="textarea" height="200rpx" :auto-height="true" />
        </u-form-item>
      </u-form>
    </view>

    <view class="card-item" style="padding-top: 24rpx; " v-if="formData.status == 2 || formData.status == 4">
      <view class="info-item">
        <text class="label">未通过原因：</text>
        <text class="value" style="color: #fa3534;">{{ formData.refuse_reason }}</text>
      </view>
    </view>

    <view class="card-item" style="padding-top: 24rpx; " v-if="formData.status == 1">
      <view class="info-item">
        <text class="label">离园说明：</text>
        <text class="value">{{ formData.leave_tips }}</text>
      </view>
    </view>
    <view class="card-item" style="padding-top: 24rpx; " v-if="formData.status == 3">
      <view class="info-item">
        <text class="label">转园说明：</text>
        <text class="value">{{ formData.leave_tips }}</text>
      </view>
    </view>
    <u-calendar v-model="showDate" mode="date" @change="dateChange" safe-area-inset-bottom :min-date="currentDay"
      max-date="2051-01-01"></u-calendar>
    <view class="fixed-btn" v-if="pageReadonly && formData.status == 0">
      <u-button class="btn1" type="error" @click="delShow = true;" :loading="submiting">撤销申请</u-button>
      <u-modal :show-cancel-button="true" v-model="delShow" @confirm="del" ref="uModal" content="确认撤销该申请吗？"
        :async-close="true"></u-modal>
      <u-button class="btn1" type="primary" @click="pageReadonly = false" :loading="submiting">修改</u-button>
    </view>
    <view class="fixed-btn" v-if="!pageReadonly && formData.status == 0">
      <u-button class="btn1" @click="back" :loading="submiting">返回</u-button>
      <u-modal :show-cancel-button="true" title="" confirm-text="同意协议并申请转园" v-model="changeShow" @confirm="submit"
        ref="uModal" :async-close="true">
        <view class="modal-conent">
          <text class="title">转园协议</text>
          <text class="p">提交转园申请后，系统会自动将婴幼儿原托育机构信息同步至要转入的目标托育机构</text>
          <text class="p">机构信息同步至要转入的目标托育机构</text>
          <text class="p">转园申请同步信息包括</text>
          <text class="p">1、转园原因；</text>
          <text class="p">2、婴幼儿在原机构的检查信息</text>
          <text class="p">3、婴幼儿在原机构的评价信息</text>
          <text class="p">4、婴幼儿基本信息</text>
          <text class="p">5、家长基本信息等</text>
          <text class="p">转园期间系统可保证婴幼儿在托信息的私密性、安全性，确保数据安全</text>
        </view>
      </u-modal>
      <u-button class="btn1" type="primary" v-if="type == 1" @click="submit" :loading="submiting">提交</u-button>
      <u-button class="btn1" type="primary" v-if="type == 2" @click="changeModal" :loading="submiting">提交</u-button>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { reactive, ref } from "vue";
import { OBSupload } from "@/utils/obs/obs-upload.js";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
import dayjs from 'dayjs';
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);
const currentLocal = ref('');
const currentDay = dayjs().add(1, 'day').format('YYYY-MM-DD');
const formRef = ref(null);
let pageReadonly = ref(false);
let pageId = ref('');
let type = ref(1);
let navTitle = ref('');
let baseUrl = ref('');
const formData = reactive({
  time: currentDay,
  reason: "",
  name: '',
  age: '',
  school_name: '',
  class_name: '',
  transfer_school_id: '',
  transfer_school_name: '',
  transfer_school_address: '',
  refuse_reason: '',
  status: '',
  leave_tips: '',
});
const fileList = ref([]);
const imgList = ref([]);
const rules = {
  time: [{ required: true, message: "请选择" + navTitle.value + "日期" }],
  reason: [{ required: true, message: "请输入" + navTitle.value + "原因" }],
};
const showDate = ref(false);
const submiting = ref(false);

const dateChange = (e) => {
  formData.time = e.result;
  showDate.value = false;
};

const getData = async (id) => {
  uni.showLoading();
  const res = await request({
    url: `${baseUrl.value}/detail?id=${id}`,
  });
  if (res) {
    console.log(res);
    formData.name = res.name;
    formData.age = res.age;
    formData.school_name = res.school_name;
    formData.class_name = res.class_name;
    formData.status = res.status;
    formData.refuse_reason = res.leave_refuse_reason || res.refuse_reason || '';
    formData.leave_tips = res.leave_tips;
    if (type.value == 1) {
      formData.reason = res.leave_reason;
      formData.time = res.leave_time;
    }
    if (type.value == 2) {
      formData.reason = res.transfer_reason;
      formData.time = res.transfer_time;
      if (!formData.transfer_school_i) {
        formData.transfer_school_id = res.transfer_school_id;
        formData.transfer_school_name = res.transfer_school_name;
        formData.transfer_school_address = await getSchoolAddress(res.transfer_school_id);
      }
    }
  }
  uni.hideLoading();
};

function getSchoolAddress(id) {
  return new Promise((resole, reject) => {
    request({
      url: `/api/parent/school/detail/${id}`,
    }).then(res => {
      if (res) {
        let { city_name, area_name, address } = res.service;
        resole(city_name + area_name + address)
      }
    })
  })

}
const uploadFiles = async () => {
  try {
    const reqArr = [];
    imgList.value
      .filter((item) => item.progress == 0)
      .forEach((item) => {
        const fileName = item.file.path.substring(
          item.file.path.lastIndexOf("/") + 1
        );
        const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        reqArr.push(OBSupload(item.url, fileExtension, fileName));
      });
    const images = await Promise.all(reqArr);
    return images;
  } catch (error) {
    uni.showToast({
      title: "图片上传失败",
      icon: "error",
    });
    console.error("上传失败:", error);
  }
};
const submit = () => {
  try {
    formRef.value.validate((valid) => {
      if (valid) {
        submitForm();
      }
    });
  } catch (error) { }
};
const changeModal = () => {
  try {
    formRef.value.validate((valid) => {
      if (valid) {
        changeShow.value = true;
      }
    });
  } catch (error) { }
};
const submitForm = async () => {
  uni.showLoading();
  submiting.value = true;
  let data = {
    parent_id: userInfo.parent_id,
    school_id: userInfo.school_id,
    class_id: userInfo.class_id,
    student_id: userInfo.student_id,
  };
  if (pageId) {
    data.id = pageId.value;
  }
  if (type.value == 1) {
    data.leave_reason = formData.reason;
    data.leave_time = formData.time;
  }
  if (type.value == 2) {
    data.transfer_reason = formData.reason;
    data.transfer_time = formData.time;
    data.transfer_school_id = formData.transfer_school_id;
  }
  request({
    url: `${baseUrl.value}/${pageId.value ? 'edit' : 'add'}`,
    method: "post",
    data,
  }).then(res => {
    submiting.value = false;
    changeShow.value = false;
    uni.hideLoading();
    if (res) {
      uni.navigateTo({ url: `/pages/pack1/apply/history?type=${type.value}` });
    }
  }).catch(err => {
    submiting.value = false;
    changeShow.value = false;
    uni.hideLoading();
  })
};
let delShow = ref(false);
let changeShow = ref(false);
const del = async () => {
  uni.showLoading();
  request({
    url: `${baseUrl.value}/del`,
    method: "post",
    data: {
      id: pageId.value
    },
  }).then(res => {
    if (res) {
      back();
    }
    uni.hideLoading();
  }).catch(err => {
    uni.hideLoading();
  })
}
const back = () => {
  uni.navigateBack();
};
const handleRightClick = () => {
  uni.navigateTo({ url: "/pages/pack1/apply/history?type=" + type.value });
}


onLoad(async (options) => {
  type.value = options.type;
  navTitle.value = type.value == 1 ? '离园' : '转园';
  baseUrl.value = type.value == 1 ? '/api/parent/schoolleave' : '/api/parent/schooltransfer';
  if (type.value == 2 && uni.getStorageSync('changeSchool')) {
    console.log(uni.getStorageSync('changeSchool'))
    let school = uni.getStorageSync('changeSchool');
    formData.transfer_school_id = school.id;
    formData.transfer_school_name = school.name;
    formData.transfer_school_address = school.address;
  }
  currentLocal.value = await getSchoolAddress(userInfo.school_id)
  if (options.id) {
    pageId.value = options.id;
    getData(options.id);
    pageReadonly.value = true;
  } else {
    formData.name = userInfo.currentStudent.name;
    formData.age = userInfo.currentStudent.age;
    formData.school_name = userInfo.currentStudent.school_name;
    formData.class_name = userInfo.currentStudent.class_name;
    formData.time = currentDay;
    pageReadonly.value = false;
  }
});
onReady(() => {
  if (pageReadonly.value == false) {
    formRef.value.setRules(rules);
  }
});
</script>

<style lang="scss" scoped>
.page-complaint-form {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: #f6f6f6;
  padding-bottom: 160rpx;

  .custom-right {
    padding: 15rpx;
    margin-right: 30rpx;
  }

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 0 24rpx 24rpx;
    display: flex;
    flex-direction: column;

    .header {
      padding: 24rpx 0;
      border-bottom: 1px solid #eee;
      margin-bottom: 24rpx;

      .title {
        font-size: 28rpx;
        color: #333;
      }
    }

    .info-item {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 80rpx;

      .label {
        width: 260rpx;
        text-align: left;
        font-size: 28rpx;
        color: #666;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #9a9a9a;
        text-align: right;
      }
    }
  }

  .form-wrapper {
    width: 600rpx;
  }

  .btn-wrapper {
    margin: 100rpx 0;
    width: 600rpx;
    display: flex;
    gap: 20rpx;

    .btn {
      flex: 1;
    }
  }
}
</style>
