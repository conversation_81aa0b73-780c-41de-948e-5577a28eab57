<template>
  <u-navbar :title="'指导信息'" :background="{ background: '#FFFFFF' }" :border-bottom="true">
  </u-navbar>
  <u-cell-item hover-class="false" :title-style="{
    width: '70vw',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    whiteSpace: 'nowrap'
  }" v-for="(item, index) in list" :key="index" icon="file-text-fill" :title="getName(item.url)" :arrow="false">
    <u-icon slot="right-icon" size="32" name="download" @click="openPdf(item.url)"></u-icon>
  </u-cell-item>
</template>

<script setup>
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { reactive, ref } from "vue";
import { OBSupload } from "@/utils/obs/obs-upload.js";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);

function getName(name) {
  return name.substring(name.lastIndexOf("/") + 1).substring(13);
}
const signatureRef = ref('');
let baseUrl = ref('');
let list = ref([]);
async function getData(id) {
  uni.showLoading();

  const res = await request({
    url: `${baseUrl.value}/guidancelist?id=${id}`,
  });
  if (res) {
    list.value = res
  }
  uni.hideLoading();
}

function openPdf(url) {
  uni.showLoading({
    mask: true
  })
  uni.downloadFile({
    url,
    success: function (res) {
      var filePath = res.tempFilePath;
      uni.openDocument({
        filePath: filePath,
        showMenu: true,
        success(e) {
          console.log(e)
        },
        fail(e) {
          console.log(e)
        },
        complete() {
          uni.hideLoading()
        }
      });
    },
    fail(e) {
      uni.hideLoading()
      console.log(e)
    }
  });
}


let pageId = ref('');
onLoad(async (options) => {
  baseUrl.value = '/api/parent/nursery';
  if (options.id) {
    pageId.value = options.id;
    await getData(options.id);
  }
});

onReady(() => {
});
</script>

<style lang="scss" scoped>
.content-text {
  flex: 1;
  border: 1rpx solid #D8D8D8;
  margin-bottom: 24rpx;
  background: #fff;
  overflow-y: auto;
  font-size: 32rpx;
  padding: 24rpx;
  overflow-x: hidden;
  border-radius: 8rpx;
  width: 100%;
}

.signature-pad {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: calc(100vh - 44px);
  width: 100vw;
  font-size: 20rpx;
  background-color: #fff;
  padding: 30rpx;
}

.sign-title {
  height: 40rpx;
  margin: 24rpx 0;
  font-size: 32rpx;
  font-weight: bold;
}

// .signature-pad::before,
// .signature-pad::after {
//   position: absolute;
//   z-index: -1;
//   content: '';
//   width: 40%;
//   height: 20rpx;
//   bottom: 20rpx;
//   background: transparent;
//   box-shadow: 0 8rpx 12rpx rgba(0, 0, 0, 0.4);
// }

// .signature-pad::before {
//   left: 20rpx;
//   -webkit-transform: skew(-3deg) rotate(-3deg);
//   transform: skew(-3deg) rotate(-3deg);
// }

// .signature-pad::after {
//   right: 20rpx;
//   -webkit-transform: skew(3deg) rotate(3deg);
//   transform: skew(3deg) rotate(3deg);
// }

.signature-pad--body {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 0 0 400rpx;
  background: #fff;
  border: 1rpx solid #d8d8d8;
  border-radius: 8rpx;
}

.signature-pad--body.empty {
  background-color: #333;
}

.signature-pad--body canvas {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.02) inset;
}

.signature-pad--footer {
  color: #c3c3c3;
  text-align: center;
  font-size: 24rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10rpx;
  flex: 0 0 120rpx;

  .u-btn {
    width: 300rpx;
  }
}
</style>
