<template>
  <view class="page-complaint">
    <u-navbar title="一键申请" :background="{ background: '#FFFFFF' }" :border-bottom="true" />
    <view class="item item1" @click="toHistoryEnter">
      <view class="title">入托申请</view>
      <view class="p">发起入托申请，机构快速响应便捷入托</view>
      <view class="btn" @click.stop="toApplyEnter">发起申请 <img src="./img/4.png" alt=""></view>
    </view>
    <view class="item item2" @click="toHistory(2)">
      <view class="title">一键转园</view>
      <view class="p">在线申请转园，婴幼儿信息实时同步</view>
      <view class="btn" @click.stop="toApply(2)">发起申请 <img src="./img/4.png" alt=""></view>
    </view>
    <view class="item item3" @click="toHistory(1)">
      <view class="title">一键离园</view>
      <view class="p">离园一键申请，保证信息安全快捷离园</view>
      <view class="btn" @click.stop="toApply(1)">发起申请 <img src="./img/4.png" alt=""></view>
    </view>
  </view>
</template>

<script setup>
import { onShow } from "@dcloudio/uni-app";
import { ref } from "vue";
import request from "@/request";
// type 1一键离园 2一键转园
const toHistory = (type) => {
  uni.navigateTo({ url: `/pages/pack1/apply/history?type=${type}` });
};
const toApply = (type) => {
  if (type == 1) {
    uni.navigateTo({ url: "/pages/pack1/apply/form?type=1" });
  } else {
    uni.setStorageSync('changeSchool', '1');
    uni.navigateTo({ url: "/pages/pack1/school/index" });
  }
};

const toHistoryEnter = () => {
  uni.navigateTo({ url: `/pages/pack1/apply/history_enter` });
};
const toApplyEnter = () => {
  uni.navigateTo({ url: "/pages/pack1/apply/form_enter" });
};
onShow(() => { });
</script>

<style lang="scss" scoped>
.page-complaint {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;

  .item {
    position: relative;
    margin: 30rpx 30rpx 0;
    padding: 32rpx;
    height: 248rpx;
    width: 690rpx;
    border-radius: 20rpx;

    .title {
      font-size: 36rpx;
      color: #262937;
      margin-bottom: 10rpx;
      font-weight: bold;
    }

    .p {
      font-size: 26rpx;
      color: #8D7878;
      margin-bottom: 32rpx;
    }

    .btn {
      width: 188rpx;
      height: 60rpx;
      background: #FF8A8A;
      border-radius: 30rpx;
      font-size: 26rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 24rpx;
    }

    .icon {
      width: 24rpx;
      height: 24rpx;
      margin-left: 24rpx;
    }
  }

  .item1 {
    background: url('./img/1.png') no-repeat;
    background-size: 100% 100%;
  }

  .item2 {
    background: url('./img/2.png') no-repeat;
    background-size: 100% 100%;

    .btn {
      background: #76E98B;
    }
  }

  .item3 {
    background: url('./img/3.png') no-repeat;
    background-size: 100% 100%;

    .btn {
      background: #70C1F8;
    }
  }
}
</style>
