<template>
  <view class="page-complaint">
    <u-navbar
      :title="navTitle[type]"
      :background="{ background: '#FFFFFF' }"
      :border-bottom="true"
    />
    <view class="card">
      <view class="header">
        <view class="info"><text>里大牛</text><text class="sex">女</text></view>
        <view class="birth">出生日期：2023-07-07</view>
      </view>
      <view class="item">
        <text class="label">所在班级：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">班级类型：</text>
        <text class="value"></text>
      </view>
      <view class="item">
        <text class="label">入托时间：</text>
        <text class="value"></text>
      </view>
      <view class="item">
        <text class="label">申请时间：</text>
        <text class="value"></text>
      </view>
      <template v-if="type == 1">
        <view class="item">
          <text class="label">离园日期：</text>
          <text class="value"></text>
        </view>
        <view class="item-column">
          <text class="label">离园原因：</text>
          <text class="value"
            >阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算</text
          >
        </view>
        <view class="item-column">
          <text class="label">上传凭证：</text>
          <image
            src="https://obs.tuoyupt.com/miniprogram/pack/m2.webp"
            mode="widthFix"
          />
        </view>
        <view class="item-column">
          <text class="label">机构意见：</text>
          <text class="value"
            >阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算</text
          >
        </view>
        <view class="item">
          <text class="label">机构名称：</text>
          <text class="value">机构名称机构名称机构名称</text>
        </view>
      </template>
      <template v-if="type == 2">
        <view class="item">
          <text class="label">转园日期：</text>
          <text class="value"></text>
        </view>
        <view class="item-column">
          <text class="label">转园原因：</text>
          <text class="value"
            >阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算</text
          >
        </view>
        <view class="item-column">
          <text class="label">目标托育机构机构意见：</text>
          <text class="value"
            >阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算阿松大撒撒打算打算</text
          >
        </view>
      </template>
    </view>
    <view v-if="type == 2" class="card">
      <view class="link">
        <text class="title">目标托育机构信息学</text>
      </view>
      <view class="item">
        <text class="label">机构名称：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">机构地址：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
    </view>
    <view v-if="type == 2" class="card">
      <view class="link">
        <text class="title">原托育机构信息学</text>
      </view>
      <view class="item">
        <text class="label">机构名称：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">机构地址：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">班级类型：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">入托时间：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
    </view>
    <view class="card">
      <view class="link">
        <text class="title">体检信息</text>
        <text class="btn" @click="toCheck">更多></text>
      </view>
      <view class="item">
        <text class="label">体检门诊：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">体检日期：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">联系电话：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">身高/cm：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">体重/kg：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">头围/cm：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">BMI：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">视力：</text>
        <text class="value">左眼：11 右眼：231</text>
      </view>
    </view>
    <view class="card">
      <view class="link">
        <text class="title">疫苗接种信息</text>
        <text class="btn" @click="toVaccines">更多></text>
      </view>
      <view class="item">
        <text class="label">接种信息：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">接种针剂：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">接种门诊：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
    </view>
    <view class="card">
      <view class="link">
        <text class="title">在园信息</text>
      </view>
      <view class="item">
        <text class="label">在托天数：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">生活记录条数：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">晨午晚检条数：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">成长时光条数：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
      <view class="item">
        <text class="label">班级动态条数：</text>
        <text class="value">阿斯顿卢卡斯离开</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import request from "@/request";
const navTitle = {
  1: "一键离园",
  2: "一键转园",
};
const type = ref(1);
const toCheck = () => {
  uni.navigateTo({ url: "/pages/pack1/physical/report?id=129" });
};
const toVaccines=()=>{
    uni.navigateTo({ url: "/pages/pack1/vaccine/record" });
}
onLoad((options) => {
  type.value = options.type;
});
</script>

<style lang="scss" scoped>
.page-complaint {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  background: #f6f6f6;
  .card {
    margin-top: 20rpx;
    padding: 30rpx;
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    .header {
      border-bottom: 1rpx solid #e7e7e7;
      .info {
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;
        font-size: 30rpx;
        font-weight: 500;
        color: #262937;
        line-height: 28rpx;
        .sex {
          margin-left: 20rpx;
          width: 40rpx;
          height: 40rpx;
          line-height: 40rpx;
          text-align: center;
          background-color: #b4fdff;
          color: #1a91ff;
          border-radius: 8rpx;
        }
      }
      .birth {
        margin-bottom: 20rpx;

        font-size: 24rpx;
        color: #aeb0bc;
      }
    }
    .link {
      padding-bottom: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #e7e7e7;
      .btn {
        color: #4095e5;
        &:active {
          opacity: 0.7;
        }
      }
    }
    .item {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      .label {
        color: #aeb0bc;
      }
      .value {
        color: #262937;
      }
    }
    .item-column {
      margin-top: 20rpx;
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      .label {
        color: #aeb0bc;
      }
      .value {
        background-color: #efefef;
        padding: 20rpx;
        border-radius: 20rpx;
        color: #262937;
      }
      image {
        width: 200rpx;
      }
    }
  }
}
</style>
