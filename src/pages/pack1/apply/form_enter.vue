<template>
  <view class="page-complaint-form">
    <u-navbar :title="navTitle + '申请'" :background="{ background: '#FFFFFF' }" :border-bottom="true">
      <template #right>
        <view class="custom-right">
          <u-icon name="order" size="50" @click="handleRightClick"></u-icon>
        </view>
      </template>
    </u-navbar>
    <u-form ref="formRef" :border-bottom="false" :model="formData" label-position="top">
      <view class="card-item" v-if="review_status != 0">
        <view class="flex-ct" style="margin-top: 24rpx;">
          <text style="flex: 1;">当前状态：</text>
          <u-button :type="getType(review_status)" size="mini" shape="square">{{ getName(review_status) }}</u-button>
        </view>
      </view>
      <view class="card-item" style="padding-left: 48rpx;">
        <u-form-item required label="婴幼儿姓名" prop="name">
          <u-input v-model="formData.name" />
        </u-form-item>
        <u-form-item required label="婴幼儿性别" prop="sex">
          <u-radio-group v-model="formData.sex">
            <u-radio name="1">男</u-radio>
            <u-radio name="2">女</u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item required label="出生日期" prop="birth">
          <u-input type="select" v-model="formData.birth" placeholder="选择日期" @click="show['birth'] = true" />
        </u-form-item>
        <u-form-item required label="身份证号" prop="id_card">
          <u-input v-model="formData.id_card" maxlength="18" />
        </u-form-item>
        <u-form-item required label="健康状况" prop="health_status">
          <u-radio-group v-model="formData.health_status">
            <u-radio name="0">健康</u-radio>
            <u-radio name="1">一般</u-radio>
            <u-radio name="2">较差</u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item required label="已接种疫苗" prop="vaccinated">
          <u-checkbox-group @change="formData.vaccinated = vaccines.filter(e => { return e.checked })">
            <u-checkbox v-model="item.checked" v-for="(item, index) in vaccines" :key="index" name="item.id">{{
              item.name }}</u-checkbox>
          </u-checkbox-group>
        </u-form-item>
        <u-form-item label="特殊需求" prop="special_needs">
          <u-input maxlength="800" v-model="formData.special_needs" type="textarea" height="200rpx"
            :auto-height="true" />
        </u-form-item>
      </view>
      <view class="card-item" style="padding-left: 48rpx;">
        <u-form-item required label="选择机构所在区域" prop="region">
          <u-input type="select" v-model="text.region" placeholder="请选择" @click="show['region'] = true" />
        </u-form-item>
        <u-form-item required label="选择意向机构" prop="school_id">
          <u-input type="select" v-model="text.school_id" placeholder="请选择" @click="show['school_id'] = true" />
        </u-form-item>
        <u-form-item required label="选择服务类型" prop="service_type">
          <u-radio-group v-model="formData.service_type">
            <u-radio :name="item.id" v-for="item in service_type">{{ item.name }}</u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item required label="期望入学时间" prop="admission_date">
          <u-input type="select" v-model="formData.admission_date" placeholder="选择日期"
            @click="show['admission_date'] = true" />
        </u-form-item>
      </view>
      <view class="card-item" style="padding-left: 48rpx;">
        <u-form-item required label="家长姓名" prop="parent_name">
          <u-input v-model="formData.parent_name" />
        </u-form-item>
        <u-form-item required label="家长身份证号" prop="parent_idcard">
          <u-input v-model="formData.parent_idcard" maxlength="18" />
        </u-form-item>
        <u-form-item required label="家长手机号" prop="parent_phone">
          <u-input v-model="formData.parent_phone" maxlength="11" />
        </u-form-item>
        <u-form-item required label="家长关系" prop="parent_ties">
          <u-input type="select" v-model="text.parent_ties" placeholder="请选择" @click="show['parent_ties'] = true" />
        </u-form-item>
      </view>
      <view class="card-item" style="padding-left: 48rpx; display: block;">
        <u-form-item class="u-noborder-bottom" required label="上传入托体检报告" prop="check_report" style="display: block;">
        </u-form-item>
        <UploadFile :fileList="formData.check_report" @getNewData="(data) => { formData.check_report = data }" />
      </view>
      <view class="card-item" style="padding-left: 48rpx; display: block;">
        <u-form-item class="u-noborder-bottom" required label="上传其他文件凭证" prop="file_voucher">
        </u-form-item>
        <UploadFile :fileList="formData.file_voucher" @getNewData="(data) => { formData.file_voucher = data }" />
      </view>
    </u-form>
    <u-picker mode="selector" v-model="show['region']" @confirm="selectConfirm" :range="area_arr"
      range-key="name"></u-picker>
    <u-picker mode="selector" v-model="show['school_id']" @confirm="selectConfirm" :range="school_arr"
      range-key="abbreviation"></u-picker>
    <u-picker mode="selector" @confirm="selectConfirm" v-model="show['parent_ties']" :range="parent_arr"
      range-key="name"></u-picker>
    <u-calendar v-model="show['birth']" mode="date" @change="e => { formData.birth = e.result }" safe-area-inset-bottom
      min-date="2011-01-01" max-date="2051-01-01"></u-calendar>
    <u-calendar v-model="show['admission_date']" mode="date" @change="e => { formData.admission_date = e.result }"
      safe-area-inset-bottom :min-date="currentDay" max-date="2051-01-01"></u-calendar>
    <view class="fixed-btn" v-if="pageReadonly && review_status == 0">
      <u-button class="btn1" @click="back" :loading="submiting">返回</u-button>
      <u-button class="btn1" type="primary" @click="pageReadonly = false" :loading="submiting">修改</u-button>
    </view>
    <view class="fixed-btn" v-if="!pageReadonly && review_status == 0">
      <u-button class="btn1" @click="back" :loading="submiting">返回</u-button>
      <u-modal :show-cancel-button="true" title="" confirm-text="确认" v-model="changeShow" @confirm="submit" ref="uModal"
        :async-close="true">
        <view class="modal-conent">
          <text class="title">确认信息</text>
          <text class="p">申请机构：{{ text.school_id }}</text>
          <text class="p">服务类型：{{ id2name(formData.service_type, service_type) }}</text>
          <text class="p">婴幼儿姓名：{{ formData.name }}</text>
          <text class="p">入托时间：{{ formData.admission_date }}</text>
          <text class="p" style="color: #FF7373;">信息提交后若有问题，可联系机构进行修改</text>
        </view>
      </u-modal>
      <u-button class="btn1" type="primary" @click="changeModal">提交</u-button>
    </view>
    <view class="fixed-btn" v-if="pageReadonly && review_status == 0">
      <u-button class="btn1" type="error" @click="delShow = true;" :loading="submiting">撤销申请</u-button>
      <u-modal :show-cancel-button="true" v-model="delShow" @confirm="del" ref="uModal" content="确认撤销该申请吗？"
        :async-close="true"></u-modal>
      <u-button class="btn1" type="primary" @click="pageReadonly = false" :loading="submiting">修改</u-button>
    </view>
    <view class="fixed-btn" v-if="review_status == 1">
      <u-button class="btn1" @click="back" :loading="submiting">返回</u-button>
      <u-button class="btn1" type="primary" @click="toSign" :loading="submiting">查看合同并签署</u-button>
    </view>
    <view class="fixed-btn" v-if="review_status == 2">
      <u-button class="btn1" @click="back" :loading="submiting">返回</u-button>
      <u-button class="btn1" type="primary" @click="toSign" :loading="submiting">查看合同</u-button>
    </view>
    <view class="fixed-btn" v-if="review_status == 3">
      <u-button class="btn1" type="primary" @click="toSign" :loading="submiting">查看合同</u-button>
      <u-button class="btn1" type="primary" @click="toFile" :loading="submiting">查看指导文件</u-button>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { reactive, ref } from "vue";
import { OBSupload } from "@/utils/obs/obs-upload.js";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
import dayjs from 'dayjs';
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);
import { useLocalStore } from "@/stores/useLocalStore.js";
import { id2name, str2arr } from "@/utils/dataHandle";
import UploadFile from '@/components/uploadFile.vue'
const currentLocal = useLocalStore();
const currentDay = dayjs().add(1, 'day').format('YYYY-MM-DD');
const formRef = ref(null);
let pageReadonly = ref(false);
let pageId = ref('');
let type = ref(1);
let navTitle = ref('');
let baseUrl = ref('');
let limitType = ['*'];
let changeShow = ref(false);
const changeModal = () => {
  try {
    formRef.value.validate((valid) => {
      if (valid) {
        changeShow.value = true;
      }
    });
  } catch (error) { }
};
function getType(id) {
  switch (id) {
    case 0:
      return 'warning'
      break;
    case 1:
      return 'info'
      break;
      case2:
      return 'primary'
      break;
    case 3:
      return 'success'
      break;
    case 4:
      return 'default'
      break;
    case 5:
      return 'error'
      break;
    default:
      break;
  }
}

function getName(id) {
  switch (id) {
    case 0:
      return '待审核'
      break;
    case 1:
      return '审核通过待签署合同'
      break;
    case 2:
      return '已签字待审核协议'
      break;
    case 3:
      return '已完成 '
      break;
    case 4:
      return '已撤销'
      break;
    case 5:
      return '审核未通过'
      break;
    default:
      break;
  }
}
const parent_arr = [{ id: 1, name: '爸爸' }, { id: 2, name: '妈妈' }, { id: 3, name: '爷爷' }, { id: 4, name: '奶奶' }, { id: 5, name: '姥姥' }, { id: 6, name: '姥爷' }];
const formData = reactive({
  name: '',
  sex: '1',
  birth: '',
  id_card: '',
  health_status: '0',
  vaccinated: [],
  special_needs: '',
  region: '',
  school_id: '',
  service_type: '',
  admission_date: '',
  parent_name: '',
  parent_idcard: '',
  parent_ties: '',
  check_report: [],
  file_voucher: [],
  parent_phone: '',
});
const review_status = ref(0);
const status_name = ref('');
const rules = {
  name: [{ required: true, trigger: 'change', message: "请输入婴幼儿姓名" }],
  birth: [{ required: true, trigger: 'change', message: "请选择出生日期" }],
  id_card: [{
    required: true,
    message: '请输入身份证号',
    trigger: 'change',
  }, {
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    message: '请输入正确的身份证号',
    trigger: 'change',
  }],
  region: [{ required: true, trigger: 'change', message: "请选择地区" }],
  school_id: [{ required: true, trigger: 'change', message: "请选择意向机构" }],
  service_type: [{ required: true, trigger: 'change', message: "请选择服务类型" }],
  admission_date: [{ required: true, trigger: 'change', message: "请选择期望入学时间" }],
  parent_name: [{ required: true, trigger: 'change', message: "请输入家长姓名" }],
  parent_idcard: [{ required: true, trigger: 'change', message: "请输入家长身份证号" }, {
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    message: '请输入正确的身份证号',
    trigger: 'change',
  }],
  parent_phone: [{ required: true, trigger: 'change', message: "请输入家长手机号" }, {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号',
      trigger: 'change',
    }],
  parent_ties: [{ required: true, trigger: 'change', message: "请选择家长关系" }],
  check_report: [{ required: true, type: 'array', trigger: 'change', message: "请选择入托体检报告" }],
  vaccinated: [{ required: true, type: 'array', trigger: 'change', message: "请选择已接种疫苗" }],
};

let school_arr = ref([]);
const show = reactive({
  region: false,
  parent_ties: false,
  school_id: false,
  birth: false,
  admission_date: false,
});
const text = reactive({
  region: '',
  parent_ties: '',
  school_id: '',
  birth: '',
  admission_date: '',
});
const area_arr = [{ id: "510502", name: "江阳区" }, { id: "510503", name: "纳溪区" }, { id: "510504", name: "龙马潭区" }, { id: "510521", name: "泸县" }, { id: "510522", name: "合江县" }, { id: "510524", name: "叙永县" }, { id: "510525", name: "古蔺县" }]
const submiting = ref(false);
const selectConfirm = (e) => {
  if (show.region) {
    formData.region = area_arr[e[0]].id + '';
    text.region = area_arr[e[0]].name;
    getSchool();
  }
  if (show.school_id) {
    formData.school_id = school_arr.value[e[0]].id + '';
    text.school_id = school_arr.value[e[0]].abbreviation;
  }
  if (show.parent_ties) {
    formData.parent_ties = parent_arr[e[0]].id + '';
    text.parent_ties = parent_arr[e[0]].name;
  }
  // if(show.birth) {
  //   formData[key] = e.result;
  // }
  // show[key] = false;
};

function getSchool() {
  request({
    url: `/api/parent/school/list`,
    data: {
      page: 1,
      per_page: 10000,
      name: '',
      order: 'popular',
      area: formData.region,
    }
  }).then(res => {
    school_arr.value = res;
  })
}


const getData = async (id) => {
  uni.showLoading();
  const res = await request({
    url: `${baseUrl.value}/findnursery?id=${id}`,
  });
  if (res) {
    formData.id = res.id || '';
    formData.name = res.name || '';
    formData.sex = res.sex ? res.sex + '' : '1';
    formData.birth = res.birth || '';
    formData.id_card = res.id_card || '';
    formData.health_status = res.health_status ? res.health_status + '' : '0';
    formData.vaccinated = str2arr(res.vaccinated);
    for (const element1 of formData.vaccinated) {
      for (const element2 of vaccines.value) {
        if (element2.id == element1) {
          element2.checked = true;
        }
      }
    }
    formData.vaccinated = [];
    formData.special_needs = res.special_needs || '';
    formData.region = res.region ? res.region + '' : '';
    text.region = id2name(res.region, area_arr);
    request({
      url: `/api/parent/school/list`,
      data: {
        page: 1,
        per_page: 10000,
        name: '',
        order: 'popular',
        area: formData.region,
      }
    }).then(e => {
      school_arr.value = e;
      text.school_id = id2name(res.school_id, e, 'abbreviation');
    })
    formData.school_id = res.school_id ? res.school_id + '' : '';
    formData.service_type = res.service_type ? res.service_type + '' : '';
    formData.admission_date = res.admission_date || '';
    formData.parent_name = res.parent_name || '';
    formData.parent_idcard = res.parent_idcard || '';
    formData.parent_ties = res.parent_ties ? res.parent_ties + '' : '';
    text.parent_ties = id2name(res.parent_ties, parent_arr);
    formData.check_report = str2arr(res.check_report) || [];
    formData.file_voucher = str2arr(res.file_voucher) || [];
    formData.parent_phone = res.parent_phone ? res.parent_phone + '' : '';
    review_status.value = res.review_status;
  }
  uni.hideLoading();
};

const submit = () => {
  try {
    formRef.value.validate((valid) => {
      console.log(valid, formData);
      if (valid) {
        submitForm();
      }
    });
  } catch (error) { }
};
const submitForm = async () => {
  uni.showLoading();
  submiting.value = true;
  let data = {};
  for (const key in formData) {
    if (Object.prototype.hasOwnProperty.call(formData, key)) {
      const element = formData[key];
      data[key] = element;
    }
  }
  data.vaccinated = data.vaccinated.map(e => { return e.id }).join(',');
  data.check_report = data.check_report.join(',')
  data.file_voucher = data.file_voucher.join(',')
  request({
    url: `${baseUrl.value}/${pageId.value ? 'update' : 'add'}`,
    method: "post",
    data: data,
  }).then(res => {
    submiting.value = false;
    uni.hideLoading();
    changeShow.value = false;
    if (res) {
      uni.navigateTo({ url: `/pages/pack1/apply/history_enter` });
    }
  }).catch(err => {
    submiting.value = false;
    uni.hideLoading();
    changeShow.value = false;
  })
};
let delShow = ref(false);
const toSign = () => {
  uni.navigateTo({ url: `/pages/pack1/apply/sign?id=` + pageId.value });
}
const toFile = () => {
  uni.navigateTo({ url: `/pages/pack1/apply/file?id=` + pageId.value });
}
const del = async () => {
  uni.showLoading();
  request({
    url: `${baseUrl.value}/quash`,
    method: "post",
    data: {
      id: pageId.value
    },
  }).then(res => {
    if (res) {
      back();
    }
    uni.hideLoading();
  }).catch(err => {
    uni.hideLoading();
  })
}
const back = () => {
  uni.navigateBack();
};

const handleRightClick = () => {
  uni.navigateTo({ url: "/pages/pack1/apply/history_enter" });
}
let vaccines = ref([]);
let service_type = ref([]);
function config() {
  request({
    url: `${baseUrl.value}/vaccines`,
  }).then(res => {
    if (res) {
      let arr = [];
      for (const key in res) {
        if (Object.prototype.hasOwnProperty.call(res, key)) {
          const element = res[key];
          element.checked = false;
          arr.push(element)
        }
      }
      vaccines.value = arr;
    }
  }).catch(err => {
  })
  request({
    url: `/api/parent/nursery/typelist`,
  }).then(res => {
    if (res) {
      for (const element of res) {
        element.id += '';
      }
      service_type.value = res;
    }
  }).catch(err => {
  })
}

onLoad(async (options) => {
  navTitle.value = '入托';
  baseUrl.value = '/api/parent/nursery';
  await config();
  if (options.id) {
    pageId.value = options.id;
    await getData(options.id);
    pageReadonly.value = true;
  } else {
    pageReadonly.value = false;
  }
});

onReady(() => {
  if (pageReadonly.value == false) {
    formRef.value.setRules(rules);
  }
});
</script>

<style lang="scss" scoped>
.page-complaint-form {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: #f6f6f6;
  padding-bottom: 160rpx;

  .custom-right {
    padding: 15rpx;
    margin-right: 30rpx;
  }

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 0 24rpx 24rpx;
    display: flex;
    flex-direction: column;

    .header {
      padding: 24rpx 0;
      border-bottom: 1px solid #eee;
      margin-bottom: 24rpx;

      .title {
        font-size: 28rpx;
        color: #333;
      }
    }

    .info-item {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 80rpx;

      .label {
        width: 260rpx;
        text-align: left;
        font-size: 28rpx;
        color: #666;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #9a9a9a;
        text-align: right;
      }
    }
  }

  .form-wrapper {
    width: 600rpx;
  }

  .btn-wrapper {
    margin: 100rpx 0;
    width: 600rpx;
    display: flex;
    gap: 20rpx;

    .btn {
      flex: 1;
    }
  }
}
</style>
