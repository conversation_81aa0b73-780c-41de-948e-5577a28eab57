<template>
  <u-navbar :title="'合同签署'" :background="{ background: '#FFFFFF' }" :border-bottom="true">
  </u-navbar>
  <view ref="signature-pad" class="signature-pad">
    <view class="sign-title">查看合同</view>
    <view ref="content" class="content-text">
      {{ sign_contract }}
    </view>
    <view class="sign-title">在线签字</view>
    <view ref="canvas-wrapper" class="signature-pad--body">
      <image style="width: 100%; width: 100%;" v-if="sign_img" :src="sign_img"></image>
      <l-signature disableScroll v-else ref="signatureRef" penColor="#000000" :penSize="5"
        :openSmooth="true"></l-signature>
    </view>
    <view ref="footer" class="signature-pad--footer" v-if="review_status == 1">
      <u-button ref="clear" @click="clear">清除</u-button>
      <u-button ref="submit" type="primary" @click="submit">确认</u-button>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { reactive, ref } from "vue";
import { OBSupload } from "@/utils/obs/obs-upload.js";
import request from "@/request";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);
import { id2name, str2arr } from "@/utils/dataHandle";
import LSignature from "../components/l-signature/l-signature.vue";

const signatureRef = ref('');
let baseUrl = ref('');
let sign_img = ref('');
let sign_contract = ref('123')
let review_status = ref('');
async function getData(id) {
  uni.showLoading();

  const res = await request({
    url: `${baseUrl.value}/findnursery?id=${id}`,
  });
  if (res) {
    review_status.value = res.review_status;
    if (review_status.value == 1) {
      clear();
    } else {
      sign_img.value = res.sign_img;
    }
  }
  uni.hideLoading();
}

function clear() {
  signatureRef.value.clear();
}

async function submit() {
  uni.showLoading();
  signatureRef.value.canvasToTempFilePath({
    success: async (res) => {
      if (res.isEmpty) {
        uni.showToast({
          title: "请签名",
          icon: "none"
        })
      } else {
        let url = res.tempFilePath;
        OBSupload(url, 'sign', '入托签名')
          .then(async img => {
            const req = await request({
              url: `${baseUrl.value}/signcontract`,
              method: 'post',
              data: {
                id: pageId.value,
                sign_img: img,
                sign_contract: sign_contract.value
              }
            });
            if (req) {
              uni.showToast({
                title: "签订成功",
                icon: "none"
              })
              getData(pageId.value)
            }
          })
      }
    }
  })
}

let pageId = ref('');
let signaturePadInstance = null
onLoad(async (options) => {
  baseUrl.value = '/api/parent/nursery';
  // init();
  if (options.id) {
    pageId.value = options.id;
    await getData(options.id);
  }
});

onReady(() => {
});
</script>

<style lang="scss" scoped>
.content-text {
  flex: 1;
  border: 1rpx solid #D8D8D8;
  margin-bottom: 24rpx;
  background: #fff;
  overflow-y: auto;
  font-size: 32rpx;
  padding: 24rpx;
  overflow-x: hidden;
  border-radius: 8rpx;
  width: 100%;
}

.signature-pad {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: calc(100vh - 44px);
  width: 100vw;
  font-size: 20rpx;
  background-color: #fff;
  padding: 30rpx;
}

.sign-title {
  height: 40rpx;
  margin: 24rpx 0;
  font-size: 32rpx;
  font-weight: bold;
}

// .signature-pad::before,
// .signature-pad::after {
//   position: absolute;
//   z-index: -1;
//   content: '';
//   width: 40%;
//   height: 20rpx;
//   bottom: 20rpx;
//   background: transparent;
//   box-shadow: 0 8rpx 12rpx rgba(0, 0, 0, 0.4);
// }

// .signature-pad::before {
//   left: 20rpx;
//   -webkit-transform: skew(-3deg) rotate(-3deg);
//   transform: skew(-3deg) rotate(-3deg);
// }

// .signature-pad::after {
//   right: 20rpx;
//   -webkit-transform: skew(3deg) rotate(3deg);
//   transform: skew(3deg) rotate(3deg);
// }

.signature-pad--body {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 0 0 400rpx;
  background: #fff;
  border: 1rpx solid #d8d8d8;
  border-radius: 8rpx;
}

.signature-pad--body.empty {
  background-color: #333;
}

.signature-pad--body canvas {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.02) inset;
}

.signature-pad--footer {
  color: #c3c3c3;
  text-align: center;
  font-size: 24rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10rpx;
  flex: 0 0 120rpx;

  .u-btn {
    width: 300rpx;
  }
}
</style>
