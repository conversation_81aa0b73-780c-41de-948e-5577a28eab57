<template>
  <view class="sort-cate-container">
    <view class="sort-header">
      <view
        v-for="(item, index) in tabs"
        :key="index"
        class="sort-tab"
        :class="{ active: activeTab === index }"
        @click="handleTabClick(index)"
      >
        {{ item.name }}
        <u-icon
          name="arrow-down-fill"
          :class="{ 'icon-active': showSelect && index === 1 }"
          :color="activeTab === index ? '#2979ff' : ''"
          size="28"
        ></u-icon>
      </view>
      <view class="sort-content" v-if="showSelect">
        <view class="sort-title">托育机构类型</view>
        <view class="sort-container">
          <view>公办</view>
          <view>公办民营</view>
          <view>民办</view>
          <view>公办公助</view>
        </view>
        <view class="sort-title">托育机构服务范围</view>
        <view class="sort-container">
          <view>综合服务中心</view>
          <view>普惠机构</view>
          <view>示范机构</view>
          <view>用人单位办托</view>
        </view>
        <view class="sort-title">托育机构加个范围</view>
        <view class="sort-container">
          <view class="small active">0-3000元托费</view>
          <view class="small">3001-5000元托费</view>
          <view class="small">5001-7000元托费</view>
          <view class="small">7001-9000元托费</view>
          <view class="small">9001元以上托费</view>
        </view>
        <view class="combin-btn">
          <view class="l-btn" @click="reset">重置</view>
          <view class="r-btn" @click="confirm">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
const emit = defineEmits(['change']);
const tabs = ref([{ name: "全部" }, { name: "筛选" }]);
const activeTab = ref(null);
const showSelect = ref(false);
const handleTabClick = (index) => {
  if (index === 0 && activeTab.value === 0) {
    activeTab.value = null;
    emit('change',{})
    return;
  }
  if (index === 1) {
    activeTab.value = index;
    showSelect.value = !showSelect.value;
    emit('change',{})
    return;
  }
  activeTab.value = index;
  showSelect.value = false;
  emit('change',{})
};
const reset = () => {
  activeTab.value = null;
  showSelect.value = false;
  emit('change',{})
};
const confirm = () => {
  showSelect.value = false;
  emit('change',{})
};
</script>

<style lang="scss">
.sort-header {
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}
.sort-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  &.active {
    color: #2979ff;
    font-weight: bold;
  }
  .icon-active {
    transform: rotate(-180deg);
    transition: all 0.3s ease-in-out;
  }
}

.sort-content {
  position: absolute;
  top: 100%;
  width: 100%;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}
.sort-title {
  font-weight: 600;
}
.sort-container {
  margin: 20rpx 0;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  view {
    height: 60rpx;
    width: 200rpx;
    text-align: center;
    line-height: 60rpx;
    border-radius: 10rpx;
    background-color: #f6f6f6;
    &:last-child {
      margin-right: auto;
    }
    &.small {
      font-size: 20rpx;
    }
    &.active{
      color: #2979ff;
    }
    &:active{
      color: #2979ff;
      opacity: 0.7;
    }
  }
}
.combin-btn {
  margin: 0 auto;
  height: 80rpx;
  width: 580rpx;
  border-radius: 20rpx;
  border: solid 4rpx #ee7b77;
  display: flex;
  overflow: hidden;
  view {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    &:active {
      opacity: 0.7;
    }
  }
  .l-btn {
    color: #ee7b77;
  }
  .r-btn {
    background-color: #ee7b77;
    color: #fff;
  }
}
</style>
