<template>
  <view class="page-complaint">
    <u-navbar :title="navTitle + '申请'" :background="{ background: '#FFFFFF' }" :border-bottom="true" />
    <view class="card-item" v-for="(item, index) in list" :key="index" @click="goDetail(item.id)">
      <view class="header">
        <text class="title">申请{{ navTitle }}机构：{{ item.school_name }}</text>
        <u-button type="warning" size="mini" shape="square" v-if="item.status == 0 || (item.status == 1 && type == 2)">待审核</u-button>
        <u-button type="success" size="mini" shape="square" v-if="(item.status == 1 && type == 1) || item.status == 3">已完成</u-button>
        <u-button type="error" size="mini" shape="square" v-if="item.status == 2 || item.status == 4">未通过</u-button>
      </view>
      <view class="info-item">
        <text class="label">{{ navTitle }}婴幼儿：</text>
        <text class="value">{{ item.name }}</text>
      </view>
      <view class="info-item">
        <text class="label">申请时间：</text>
        <text class="value">{{ type == 1 ? item.leave_time : item.transfer_time }}</text>
      </view>
      <!-- <view class="info-item">
        <text class="label">申请原因：</text>
        <text class="value">{{ type == 1 ? item.leave_reason : item.transfer_reason }}</text>
      </view> -->
      <view class="info-item" v-if="item.status == 2 || item.status == 4">
        <text class="label" style="color: #fa3534;">未通过原因：</text>
        <text class="value" style="color: #fa3534;">{{ item.refuse_reason || item.leave_refuse_reason }}</text>
      </view>
      <view class="footer">
        <text class="title">申请编号：{{ item.number }}</text>
        <text class="status" v-if="item.status == 1 && type == 1">离园成功</text>
        <text class="status" v-if="item.status == 1 && type == 2">入园待审核</text>
        <text class="status" v-if="item.status == 3">转园成功</text>
        <text class="status" v-if="item.status == 2 && type == 2">原机构离园审核未通过</text>
        <text class="status" v-if="item.status == 4 && type == 2">入园审核未通过</text>
      </view>
    </view>
    <view class="page-nodata" v-if="list.length == 0">
      <u-empty text="暂无数据"></u-empty>
    </view>
    <view class="add" @click="goAdd">
      <image class="addi" src="https://obs.tuoyupt.com/aituoyu/img/enrollment/add.svg"></image>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, watch } from "vue";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
console.log('userInfo', userInfo.currentStudent);
import request from "@/request";
const type = ref(1);
const navTitle = ref('');
const list = ref([]);
function getStatusUrl(status) {
  let url = "";
  switch (status) {
    case 0:
      url =
        "https://obs.tuoyupt.com/miniprogram/enrollment_management/daishenhe.png";
      break;
    case 1:
      url =
        "https://obs.tuoyupt.com/miniprogram/enrollment_management/tongguo.png";
      break;
    case 2:
      url =
        "https://obs.tuoyupt.com/miniprogram/enrollment_management/weitonguo.png";
      break;
    case 3:
      url =
        "https://obs.tuoyupt.com/miniprogram/enrollment_management/chexiao.png";
      break;
  }
  return url;
}
function goDetail(id) {
  uni.navigateTo({
    url: `/pages/pack1/apply/form?id=${id}&type=${type.value}`,
  });
}
function goAdd() {
  if (type.value == 1) {
    uni.navigateTo({
      url: `/pages/pack1/apply/form?type=${type.value}`,
    });
  } else {
    uni.setStorageSync('changeSchool', '1');
    uni.navigateTo({ url: "/pages/pack1/school/index" });
  }

}
function goReport() {
  uni.navigateTo({
    url: `/pages/pack1/apply/report?type=${type.value}`,
  });
}

async function getData() {
  uni.showLoading();
  const res = await request({
    url: baseUrl.value + "/list?parent_id=" + userInfo.parent_id,
  });
  console.log(res);
  uni.hideLoading();
  if (res) {
    list.value = res;
  }
}
let baseUrl = ref('');
onLoad((options) => {
  type.value = options.type;
  navTitle.value = type.value == 1 ? '离园' : '转园';
  baseUrl.value = type.value == 1 ? '/api/parent/schoolleave' : '/api/parent/schooltransfer'
});

onShow(() => {
  console.log('onshow')
  getData();
})

</script>

<style lang="scss" scoped>
.page-complaint {
  padding-bottom: env(safe-area-inset-bottom);
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: #f6f6f6;

  .card-item {
    width: 690rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin: 20rpx auto;
    box-sizing: border-box;
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;

    .header {
      padding: 24rpx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }

      .u-btn {
        margin-left: 24rpx;
      }
    }

    .footer {
      padding: 24rpx 0;
      border-top: 1px solid #eee;
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #2979ff;
      line-height: 1.5;

      .title {
        font-size: 28rpx;
      }

      .status {
        flex: 0 0 280rpx;
        text-align: right;
      }
    }

    .info-item {
      display: flex;
      align-items: center;
      height: 80rpx;

      .label {
        text-align: right;
        font-size: 28rpx;
        color: #666;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #9a9a9a;
      }
    }
  }

  .add {
    position: fixed;
    bottom: 140rpx;
    right: 40rpx;
    z-index: 5;

    .addi {
      width: 112rpx;
      height: 112rpx;
    }
  }
}
</style>
