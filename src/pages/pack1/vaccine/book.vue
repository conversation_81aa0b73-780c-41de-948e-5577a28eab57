<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="服务预约">
    </u-navbar>
    <view class="section">
      <view class="s1">
        <view class="row1">
          <image class="addicon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/dasdcc.png"></image>
          <text>{{ vaccine_info.name }}</text>
        </view>
        <view class="row2">
          价格：<text class="price">￥{{ vaccine_info.price }}</text>
        </view>
        <view class="row2">
          生产厂家：{{ vaccine_info.merchant?.name }}
        </view>
        <view class="row2">
          产品规格：{{ vaccine_info.spec }}
        </view>
      </view>
      <view class="s2">
        <view class="left">
          <view class="t1">
            {{ hospital_info.name }}
          </view>
          <view class="t2">
            <text v-if="hospital_info.distance >= 1">距你{{ (hospital_info.distance * 1).toFixed(2)
              }}km</text>
            <text v-else>距你{{ hospital_info.distance * 1000 }}m</text>·{{ hospital_info.address }}
          </view>
        </view>
        <image class="icon1" @click="goDaohang" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/daoaoao.png">
        </image>
        <image class="icon1" @click="callPhone" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/phoneee.png">
        </image>
      </view>
    </view>
    <view class="section">
      <view class="stop" @click="goXZX">
        <image src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/czxzjjja1.png" class="simg">

        </image>
        <view class="stopright">
          预约前请致电门诊确认细节！请携带… <image class="stoprighticon"
            src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/ddsasd.png">
          </image>
        </view>
      </view>
    </view>
    <view class="section">
      <view class="row" @click="popShow = true">
        <view class="l1">
          <u-icon name="man-add-fill" size="32" color="#000000"></u-icon>
          <text class="ll1">选择婴幼儿</text>
        </view>
        <view class="l2">
          <text>{{ child_info.name ? child_info.name : "请选择婴幼儿" }}</text>
          <u-icon name="arrow-right" color="#797E8E"></u-icon>
        </view>
      </view>
    </view>
    <view class="section">
      <view class="tt1">
        预约日期
      </view>
      <view class="dates">
        <view class="btn before" :class="{ dis: week == 0 }" @click="getBefore">
          <u-icon name="play-left-fill"></u-icon>
        </view>
        <view class="btn next" @click="getNext" :class="{ dis: week > 2 }">
          <u-icon name="play-right-fill"></u-icon>
        </view>
        <view class="scroll">
          <view class="date" @click="changeDay(item)"
            :class="{ active: order_info.order_time == item.day, dis: item.status !== 1 }"
            v-for="(item, index) in timeList" :key="item.day">
            <view class="t1">{{ index == 0 && week == 0 ? '今天' : dayjs(item.day).format("MM.DD") }}</view>
            <view class="t2" :class="{
              ta: item.status == 1,
              tl: item.status == 3
            }">
              {{ item.status == 0 || item.status == -1 ? '无号' : item.status == 1 ? '可约' : item.status == 2 ? '约满' : '停诊'
              }}
            </view>
          </view>
        </view>
      </view>
      <view class="tt1">
        上午
      </view>
      <template v-if="periodListS.length">
        <view class="times">
          <view class="time" @click="changePeriod(item)" :class="{
            active: order_info.id == item.id,
            dis: item.per_num == 0
          }" v-for="(item, index) in periodListS" :key="item.id">
            <view class="timet">{{ item.order_period }}</view>
            <view class="timet">剩余{{ item.per_num }}</view>
          </view>
        </view>
      </template>
      <view v-else class="empty">无可预约时段</view>
      <view class="tt1">
        下午
      </view>
      <template v-if="periodListX.length">
        <view class="times">
          <view class="time" @click="changePeriod(item)" :class="{
            active: order_info.id == item.id,
            dis: item.per_num == 0
          }" v-for="(item, index) in periodListX" :key="item.id">
            <view class="timet">{{ item.order_period }}</view>
            <view class="timet">剩余{{ item.per_num }}</view>
          </view>
        </view>
      </template>
      <view v-else class="empty">无可预约时段</view>
    </view>
    <view class="section">
      <view class="tt1 tt4">
        预约须知
      </view>
      <text class="notee">
        预约时，需满足疫苗接种间隔要求、接种计划间隔要求。未满足时，对应的日期则展示“不可约”。请根据页面提示，选择“可预约”的日期进行预约。 </text>
    </view>
    <view class="footer-zhanwei"></view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn" @click="submit">确认预约</view>
      </view>
    </view>
  </view>
  <u-popup v-model="popShow" border-radius="24" mode="bottom" safe-area-inset-bottom>
    <view class="pop">
      <view class="item" v-for="item in child_list" :key="item.id">
        <view class="top">
          <image class="avter" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/avter.png">
          </image>
          <view class="info">
            <view class="t1">
              <view class="name">{{ item.name }}</view>
              <view class="sex">{{ item.sex == 1 ? '男' : "女" }}</view>
            </view>
            <view class="arr">
              {{ item.age }}
            </view>
          </view>
          <view class="right" @click="checkStudent(item)">
            <view v-if="item.id == child_info.id" class="checked">
              <u-icon name="checkbox-mark" color="#FFFFFF" size="24"></u-icon>
            </view>
            <view v-else class="nocheck"></view>
          </view>
        </view>
      </view>
      <view class="btng" @click="popShow = false">
        确定
      </view>
    </view>
  </u-popup>
</template>
<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, computed } from "vue";
import dayjs from "dayjs";
import request from "@/request.js";
import { useUserStore } from "@/stores/userInfo.js";
const userInfo = useUserStore();
const hospital_info = ref({
  name: "",
  distance: 0,
  address: "",
  lat: "",
  lng: ""
})
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: hospital_info.value.contact,
  });
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(hospital_info.value.lat),
    longitude: Number(hospital_info.value.lng),
    name: hospital_info.value.name,
    address: hospital_info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
const popShow = ref(false);
const child_list = ref([]);
const child_info = ref({
  id: 0,
  name: ''
});
const vaccine_info = ref({
  name: "",
  price: "",
  spec: "",
  stock: '',
  merchant: {
    name: ""
  }
})
const order_info = ref({
  order_period: "",
  order_time: "",
  id: ""
})
onLoad(() => {
  hospital_info.value = uni.getStorageSync('vaccine_hospital_info');
  vaccine_info.value = uni.getStorageSync('vaccine_info');
  getTime();
})
onShow(() => {
  getChild();
})
const timeList = ref([]);
const periodList = ref([]);
const periodListS = computed(() => {
  return periodList.value.filter(item => {
    let t = item.order_period.substr(0, 2)
    if (Number(t) < 12) {
      return true
    } else { return false }
  })
})
const periodListX = computed(() => {
  return periodList.value.filter(item => {
    let t = item.order_period.substr(0, 2)
    if (Number(t) < 12) {
      return false
    } else { return true }
  })
})
const week = ref(0)
function getBefore() {
  week.value--;
  getTime();
}
function getNext() {
  week.value++;
  getTime();
}
function getTime() {
  uni.showLoading();
  periodList.value = [];
  request({
    url: "/api/healthservices/vaccine/getTimeList",
    data: {
      hospital_id: hospital_info.value.id,
      week: week.value
    }
  }).then(res => {
    timeList.value = res.time;
    let first = res.time.find(item => item.status == 1);
    if (first) {
      periodList.value = first.period;
      order_info.value.order_time = first.day;
    }
    uni.hideLoading();
  })
}
function changeDay(item) {
  order_info.value.order_time = item.day;
  periodList.value = item.period;
}
function changePeriod(item) {
  order_info.value.id = item.id;
  order_info.value.order_period = item.order_period;
}
function checkStudent(item) {
  child_info.value = item;
}
function getChild() {
  request({
    goLogin: true,
    url: "/api/healthservices/user/list",
  }).then(res => {
    child_list.value = res;
    if (res.length == 1) {
      child_info.value = res[0];
    }
    if (res.length == 0) {
      uni.showModal({
        title: "提示",
        content: "您尚未添加婴幼儿信息，无法预约！",
        confirmText: "去添加",
        cancelText: "返回",
        success(data) {
          if (data.confirm) {
            if (Boolean(userInfo.master_hospital_id)) {
              uni.navigateTo({
                url: "/pages/pack/children/add"
              })
            } else {
              uni.showToast({
                title: "请先完善个人信息",
                mask: true,
                duration: 1500,
                icon: "none"
              })
              setTimeout(() => {
                uni.navigateTo({
                  url: "/pages/pack/children/profile"
                })
              }, 1500)
            }
          } else {
            uni.navigateBack();
          }
        }
      })
    }
  })
}
function submit() {
  if (child_info.value.id == 0) {
    uni.showToast({
      title: "请选择婴幼儿",
      icon: "none"
    })
    return
  }
  if (order_info.value.order_time == '') {
    uni.showToast({
      title: "请选择预约日期",
      icon: "none"
    })
    return
  }
  if (order_info.value.id == '') {
    uni.showToast({
      title: "请选择预约时间",
      icon: "none"
    })
    return
  }
  uni.setStorageSync("vaccine_order_info", order_info.value);
  uni.setStorageSync("vaccine_child_info", child_info.value);
  uni.navigateTo({
    url: `./confirm`,
  });
}
function goXZX() {
  uni.navigateTo({
    url: "./notice"
  })
}
// const headStyle = {
//   'font-size': '26rpx',
//   color: '#262937'
// }
// const bodyStyle = {
//   'font-size': '26rpx',
//   color: '#262937'
// }
// const itemList = ref([{
//   head: "骨密度",
//   body: "只要我们正确择取一个合适的参照物乃至稍降一格去看待他人，值得赏识的东西便会扑面而来",
//   open: false,
// }, {
//   head: "体脂分析",
//   body: "学会欣赏，实际是一种积极生活的态度，是生活的调味品，会在欣赏中发现生活的美",
//   open: false,
// }, {
//   head: "注意力测评",
//   body: "但是据说雕刻大卫像所用的这块大理石，曾被多位雕刻家批评得一无是处，有些人认为这块大理石采凿得不好，有些人嫌它的纹路不够美",
//   open: false,
// }])
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .section {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;

    .stop {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
      height: 35rpx;

      .simg {
        width: 134.4rpx;
        height: 45.5rpx;
      }

      .stopright {
        font-size: 26rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #797E8E;
        line-height: 36rpx;

        .stoprighticon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .s2 {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 40rpx;

      .icon1 {
        width: 64rpx;
        height: 64rpx;
        margin-left: 40rpx;
      }

      .left {
        flex: 1;
        max-width: 400rpx;

        .t1 {
          font-size: 32rpx;
          font-weight: 500;
          color: #262937;
          line-height: 44rpx;
        }

        .t2 {
          font-size: 26rpx;
          font-weight: 400;
          color: #7A7E8F;
          line-height: 36rpx;
          margin-top: 8rpx;
        }
      }
    }

    .s1 {
      border-bottom: 1rpx solid #EEE;
      padding-bottom: 40rpx;

      .row1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 40rpx;
        font-weight: 600;
        color: #000000;
        margin-bottom: 20rpx;

        .addicon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 12rpx;
        }
      }

      .row2 {
        font-size: 26rpx;
        font-weight: 400;
        color: #7A7E8E;
        line-height: 36rpx;

        .price {
          color: #FF0000;
        }
      }
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .l1 {
        font-size: 32rpx;
        font-weight: 400;
        color: #797D8D;

        .ll1 {
          margin-left: 12rpx;
        }
      }

      .l2 {
        font-size: 32rpx;
        font-weight: 600;
        color: #000000;
      }
    }

    .tt1 {
      font-size: 32rpx;
      font-weight: 600;
      color: #292D3B;
      line-height: 32rpx;
    }

    .tt4 {
      margin-top: 20rpx;
      margin-bottom: 20rpx;
    }

    .notee {
      font-size: 26rpx;
      font-weight: 400;
      color: #7A7E8F;
      line-height: 48rpx;
    }

    .tt2 {

      color: #1975FF;
    }

    .tt3 {
      font-size: 26rpx;
      font-weight: 400;
      color: #2D303E;
      margin-top: 16rpx;
      margin-bottom: 40rpx;
    }

    .dates {
      width: 100%;
      margin: 28rpx 0;
      position: relative;

      .btn {
        position: absolute;
        width: 60rpx;
        height: 100%;
        top: 0;
        line-height: 96rpx;
        color: #1975FF;
      }

      .before {
        left: -55rpx;
        text-align: right;
      }

      .next {
        right: -55rpx;
        text-align: left;
      }

      .btn.dis {
        color: #AEB0BC;
        pointer-events: none;
      }

      .scroll {
        width: 100%;
        display: flex;
        flex-direction: row;
        height: 96rpx;
        justify-content: space-between;
        padding: 0 10rpx;

        .date {
          width: 74rpx;
          height: 96rpx;
          background: rgba(25, 117, 255, 0);
          border-radius: 8rpx;
          border: 1rpx solid #EEEEEE;
          text-align: center;
          flex-shrink: 0;

          .t1 {
            font-size: 26rpx;
            font-weight: 400;
            color: #7A7E8F;
            line-height: 48rpx;
          }

          .t2 {
            font-size: 26rpx;
            font-weight: 500;
            color: #7A7E8F;
            line-height: 48rpx;
          }

          .ta {
            color: #1975FF;
          }

          .tl {
            color: rgb(0, 208, 93);
          }
        }



        .date.active {
          background: #1975FF;

          .t1,
          .t2 {
            color: #FFFFFF;
          }
        }
      }
    }

    .empty {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
      height: 80rpx;
      line-height: 80rpx;
    }

    .times {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 28rpx 0;
      flex-wrap: wrap;
      justify-content: space-between;

      &::after {
        content: " ";
        width: 194rpx;
      }

      .time {
        width: 194rpx;
        height: 96rpx;
        background: rgba(25, 117, 255, 0);
        border-radius: 8rpx;
        border: 1rpx solid #EEEEEE;
        text-align: center;
        margin-bottom: 20rpx;

        .timet {
          font-size: 26rpx;
          font-weight: 500;
          color: #7A7E8F;
          line-height: 48rpx;
        }
      }

      .time.active {
        .timet {
          background: #1975FF;
          color: #FFFFFF;
        }
      }
    }

    .items {
      width: 630rpx;
      background: #F1F5F8;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 16rpx;

      .it1 {
        font-size: 32rpx;
        font-weight: 600;
        color: #262937;
      }

      .des {
        margin-top: 46rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #262937;
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.dis {
  pointer-events: none;
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;

    .btn {
      width: 690rpx;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }
}

.pop {
  width: 100%;
  padding: 30rpx;
  background: #FFFFFF;

  .btng {
    width: 100%;
    height: 80rpx;
    border-radius: 16rpx;
    text-align: center;
    font-size: 28rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    background: #617EFB;
    line-height: 80rpx;
  }

  .top {
    width: 100%;
    height: 128rpx;
    display: flex;
    flex-direction: row;
    margin: 0 auto 20rpx;
    border-bottom: 1rpx solid #eee;
    align-items: center;

    .avter {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .info {
      padding-left: 24rpx;
      flex: 1;

      .t1 {
        display: flex;
        flex-direction: row;
        align-items: center;

        .name {
          font-size: 32rpx;
          font-weight: 600;
          color: #282C39;
          line-height: 56rpx;
        }

        .sex {
          width: 32rpx;
          height: 32rpx;
          background: #D6EDFE;
          border-radius: 6rpx;
          text-align: center;
          line-height: 32rpx;
          font-size: 20rpx;
          font-weight: 400;
          color: #0275FF;
          margin-left: 10rpx;
        }
      }


      .arr {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
        margin-top: 8rpx;
      }
    }

    .right {
      .nocheck {
        width: 48rpx;
        height: 48rpx;
        background: #FFFFFF;
        border: 1rpx solid #AEB0BC;
        border-radius: 50%;
        margin-right: 24rpx;
      }

      .checked {
        width: 48rpx;
        height: 48rpx;
        background: #617EFB;
        border-radius: 50%;
        color: #FFFFFF;
        text-align: center;
        font-size: 12rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
      }
    }
  }
}
</style>