<template>
  <view class="top">
    <image class="backimg" src="https://obs.tuoyupt.com/miniprogram/index/atopback1.png">
    </image>
  </view>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive :background="{ backgroundColor: 'rgba(255,255,255,0)' }" title="">
      <view class="warp">
        <image class="icon1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/loc.png">
        </image>
        <nj-address @change="lochange">
          <view class="text">
            {{ currentLocal.city }}·{{ currentLocal.region }}
          </view>
        </nj-address>
        <image class="icon2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/xiala.png">
        </image>
      </view>
    </u-navbar>
    <view class="top-zhanwei"></view>
    <view class="topfix">
      <view class="top-t">
        <image class="icon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/tipssa.png">
        </image>
        <view class="t2">
          ｜接种务必携带疫苗本或建卡建证等相关材料
        </view>
      </view>
      <view class="btns">
        <view class="left">
          <view class="ltop" @click="goRouter(5)">
            <image class="licon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/gsd.png"></image>
            <view class="lbtnt">
              <view class="t1">疫苗预约</view>
              <view class="t2">更多疫苗预约</view>
            </view>
          </view>
          <view class="lbom">
            <view class="lbootm lbootma" @click="goRouter(6)">
              <image class="laicon" src="https://obs.tuoyupt.com/miniprogram/changzhi/yuyu.png"></image>
              <view class="title">预约记录</view>
            </View>
            <view class="lbootm" @click="goRouter(7)">
              <image class="laicon" src="https://obs.tuoyupt.com/miniprogram/changzhi/jiezhong.png"></image>
              <view class="title">接种记录</view>
            </View>
          </view>
        </view>
        <view class="right">
          <view class="rbtn" @click="goRouter(5)">
            <image class="ricon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/addda.png"></image>
            <view class="rbtnt">
              <view class="t1">接种门诊</view>
              <view class="t2">查看附近门诊</view>
            </view>
          </view>
          <view class="rbtn rbtn1" @click="goRouter(4)">
            <image class="ricon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/dsa.png"></image>
            <view class="rbtnt">
              <view class="t1">接种参考</view>
              <view class="t2">疫苗接种类目</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="section" @click="goRouter(1)">
      <view class="stop">
        <view class="stoptitle">
          接种流程
        </view>
        <view class="stopright">
          详情 <image class="stoprighticon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/ddsasd.png">
          </image>
        </view>
      </view>
      <view class="lc">
        <view class="lcy">
          <image class="lcyi" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/lc1.png">
          </image>
          <view class="lcyi1">登记</view>
          <view class="lcyi2">登记室登记、预检、预约</view>
        </view>
        <view class="hr"></view>
        <view class="lcy">
          <image class="lcyi" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/lc2.png">
          </image>
          <view class="lcyi1">接种</view>
          <view class="lcyi2">无禁忌者进行接种</view>
        </view>
        <view class="hr"></view>
        <view class="lcy">
          <image class="lcyi" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/lc3.png">
          </image>
          <view class="lcyi1">观察</view>
          <view class="lcyi2">观察室留观30分钟</view>
        </view>
        <view class="hr"></view>
        <view class="lcy">
          <image class="lcyi" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/lc4.png">
          </image>
          <view class="lcyi1">完成</view>
          <view class="lcyi2">无异常反应即可离开</view>
        </view>
      </view>
    </view>
    <view class="section">
      <view class="stop" @click="goRouter(2)">
        <view class="stoptitle">
          📢家长须知
        </view>
        <view class="stopright">
          协助做好预防接种工作 <image class="stoprighticon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/ddsasd.png">
          </image>
        </view>
      </view>
    </view>
    <view class="section">
      <view class="stop" @click="goRouter(3)">
        <view class="stoptitle">
          ⚠️注意事项
        </view>
        <view class="stopright">
          接种前的知情告知 <image class="stoprighticon" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/ddsasd.png">
          </image>
        </view>
      </view>
    </view>
    <!-- <view class="section">
      <view class="stop">
        <view class="stoptitle">
          疫苗知识
        </view>
      </view>
      <view class="news">
        <view class="newsinfo">
          <view class="newst1">
            秋冬季节，中老年人“疫苗三件套”，赶紧给咱 爸妈安排
          </view>
          <view class="newstime">
            2022-10-28
          </view>
        </view>
        <image class="newsi" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/lc4.png">
        </image>
      </view>
    </view> -->
    <view class="zhanwei"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import request from "@/request.js";
import { useLocalStore } from "@/stores/useLocalStore.js"
import { useUserStore } from "@/stores/userInfo.js"
const userInfo = useUserStore();
function lochange(e) {
  currentLocal.province = e.provinceCode;
  currentLocal.city = e.city;
  currentLocal.areaCode = e.areaCode;
  currentLocal.region = e.area;
  getJingwei(e.areaCode, currentLocal.city + currentLocal.region)
}
function getJingwei(code, name) {
  request({
    url: `/api/city/getlatlng`,
    data: {
      city: code,
      address: name
    }
  }).then(res => {
    let location = res?.result?.location;
    if (location) {
      currentLocal.citylatitude = location.lat;
      currentLocal.citylongitude = location.lng;
    }
  })
}

const currentLocal = useLocalStore();
function goRouter(index) {
  if (!Boolean(userInfo.id)) {
    uni.redirectTo({
      url: "/pages_work/login/main"
    })
    return
  }
  if (index == 1) {
    uni.navigateTo({
      url: "./flow"
    })
  }
  if (index == 2) {
    uni.navigateTo({
      url: "./notice"
    })
  }
  if (index == 3) {
    uni.navigateTo({
      url: "./attention"
    })
  }
  if (index == 4) {
    uni.navigateTo({
      url: "./refer"
    })
  }
  if (index == 5) {
    uni.navigateTo({
      url: "./search"
    })
  }
  if (index == 6) {
    uni.navigateTo({
      url: "./book-list"
    })
  }
  if (index == 7) {
    uni.navigateTo({
      url: "./record"
    })
  }
}
const vaccine_record_count = ref(0)
function getHistory() {
  request({
    url: '/api/healthservices/index',
  }).then(res => {
    vaccine_record_count.value = res.vaccine_record_count;
  })
}
onShow(() => {
  getHistory();
})

onLoad(async () => {
  uni.showLoading()
  try {
    let location = await uni.getLocation({
      type: "gcj02",
    });
    if (location.errMsg == "getLocation:ok") {
      currentLocal.latitude = location.latitude;
      currentLocal.longitude = location.longitude;
      currentLocal.noLocation = false;
      let res = await request({ url: `/api/city/name?lat=${location.latitude}&lng=${location.longitude}` });
      let { province, city, district, adcode } = res.result.addressComponent;
      currentLocal.province = province;
      currentLocal.city = city;
      currentLocal.region = district;
      currentLocal.areaCode = adcode;
    } else {
      currentLocal.noLocation = true;
    }
    uni.hideLoading();
  } catch (error) {
    uni.hideLoading();
    currentLocal.noLocation = true;
    return false;
  }
  uni.hideLoading();
})
</script>

<style lang="scss" scoped>
.pages {
  background-color: #F1F5F8;
  min-height: 100vh;

  .top-zhanwei {
    width: 750rpx;
    height: 368rpx;
  }

  .stit {
    position: sticky;
    top: 200rpx;
    z-index: 3;
    width: 750rpx;
    padding: 30rpx 30rpx 0;
    background: #FFFFFF;
  }

  .topfix {
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    padding: 30rpx 30rpx 20rpx;
    background: linear-gradient(142deg, #FFEBDC 0%, #F1F5F8 100%);
    position: relative;
    z-index: 2;


    .btns {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin: 30rpx 0 0;
      align-items: center;

      .left {
        width: 336rpx;
        height: 308rpx;
        background: linear-gradient(180deg, #1975FF 0%, #36ADFF 100%);
        border-radius: 24rpx;

        .ltop {
          display: flex;
          flex-direction: row;
          height: 144rpx;
          align-items: center;
          padding-left: 30rpx;

          .licon {
            width: 80rpx;
            height: 80rpx;
          }

          .lbtnt {
            padding-left: 16rpx;

            .t1 {
              font-size: 34rpx;
              font-weight: 600;
              color: #FFFFFF;
            }

            .t2 {
              font-size: 24rpx;
              font-weight: 400;
              color: #FFFFFF
            }
          }
        }

        .lbom {
          width: 336rpx;
          height: 152rpx;
          display: flex;
          flex-direction: row;
          background: rgba(255, 255, 255, 0.21);
          border-radius: 24rpx;
          margin-top: 20rpx;

          .lbootma {
            position: relative;

            &::after {
              content: " ";
              position: absolute;
              right: 0rpx;
              top: 44rpx;
              width: 1rpx;
              height: 50rpx;
              background: #FFFFFF;
            }
          }

          .lbootm {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .title {
              font-size: 26rpx;
              font-weight: 500;
              color: #FFFFFF;
              margin-top: 20rpx;
            }

            .laicon {
              width: 40rpx;
              height: 40rpx;
            }
          }
        }
      }

      .right {
        width: 336rpx;
        height: 308rpx;

        .rbtn {
          width: 336rpx;
          height: 144rpx;
          background: #FFFFFF;
          border-radius: 24rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          padding-left: 30rpx;

          .ricon {
            width: 80rpx;
            height: 80rpx;
          }

          .rbtnt {
            padding-left: 16rpx;

            .t1 {
              font-size: 34rpx;
              font-weight: 600;
              color: #272A39;
            }

            .t2 {
              font-size: 24rpx;
              font-weight: 400;
              color: #797D8E;
            }
          }
        }

        .rbtn1 {
          margin-top: 20rpx;
        }
      }
    }

    .top-t {
      display: flex;
      flex-direction: row;
      line-height: 40rpx;

      .icon {
        width: 152rpx;
        height: 40rpx;
      }


      .t2 {
        font-size: 24rpx;
        font-weight: 400;
        color: #50556A;
      }
    }
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 30rpx;
    margin: 20rpx auto;

    .stop {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .stoptitle {
        font-size: 32rpx;
        font-weight: 500;
        color: #292D3B;
        height: 40rpx;
        line-height: 40rpx;
      }

      .stopright {
        font-size: 26rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #797E8E;
        line-height: 36rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 40rpx;

        .stoprighticon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .lc {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 40rpx;

      .hr {
        width: 54rpx;
        height: 0rpx;
        border: 2rpx dashed #E0E0E0;
        position: relative;
        top: 40rpx;
      }

      .lcy {
        text-align: center;

        .lcyi {
          width: 80rpx;
          height: 80rpx;
        }

        .lcyi1 {
          font-size: 26rpx;
          font-weight: 500;
          color: #292D3B;
          line-height: 36rpx;
          margin-top: 8rpx;
        }

        .lcyi2 {
          width: 128rpx;
          height: 56rpx;
          font-size: 20rpx;
          font-weight: 400;
          color: #797D8E;
          line-height: 28rpx;
          margin-top: 8rpx;
        }
      }
    }

    .news {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 26rpx;

      .newsinfo {
        flex: 1;
        padding-right: 66rpx;

        .newst1 {
          font-size: 28rpx;
          font-weight: 600;
          color: #262937;
        }

        .newstime {
          font-size: 20rpx;
          font-weight: 400;
          color: #AEB0BC;
          margin-top: 14rpx;
        }
      }

      .newsi {
        width: 188rpx;
        height: 140rpx;
        border-radius: 16rpx;
      }
    }
  }
}

.zhanwei {
  height: calc(env(safe-area-inset-bottom));
}

.mno-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20%;
  flex-direction: column;
  background: #fff;

  image {
    width: 160rpx;
    height: 160rpx;
    margin: 196rpx 0 24rpx;
  }

  .t1 {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #262937;
    margin-bottom: 20rpx;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 400;
    color: #aeb0bc;
    line-height: 44rpx;
  }

  .btn {
    width: 240rpx;
    height: 80rpx;
    background: #1975FF;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 48rpx;
    margin-bottom: 100rpx;
  }
}

.top {
  width: 750rpx;
  height: 400rpx;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 1;

  .backimg {
    width: 750rpx;
    height: 420rpx;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.warp {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 24rpx;

  .icon1 {
    width: 40rpx;
    height: 40rpx;
    margin-right: 14rpx;
  }

  .text {
    font-size: 34rpx;
    font-weight: 600;
    color: #000000;
  }

  .icon2 {
    width: 32rpx;
    height: 32rpx;
  }
}
</style>
