<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="接种参考">
    </u-navbar>
    <view class="tips">
      一类疫苗是指由政府免费提供给公民的疫苗,公民应按照政府规定接受接种。第二类疫苗是指由公民自费和自愿接种的疫苗。
    </view>
    <view class="btns">
      <view class="btn" :class="{ btna: type == 1 }" @click="type = 1">
        一类疫苗
      </view>
      <view class="btn btn2" :class="{ btna: type == 2 }" @click="type = 2">
        二类疫苗
      </view>
    </view>
    <template v-if="type == 1">
      <view class="list" v-for="item in list" :key="item.id">
        <view class="title">
          {{ item.title }}
        </view>
        <view class="item" v-for="it in item.items" :key="it.index">
          <view class="info">
            <view class="t1">
              {{ it.name }}
            </view>
            <view class="t2">
              第{{ it.index }}剂/共{{ it.total }}剂
            </view>
            <view class="t2">
              {{ it.des }}
            </view>
          </view>
          <!-- <u-icon name="arrow-right"></u-icon> -->
        </view>
      </view>
    </template>
    <template v-else>
      <view class="scetion">
        <view class="t1">
          不同疫苗的选择
        </view>
        <view class="t2">
          我国批准上市的流感疫苗有三种，可以参考表格选择
        </view>
        <view class="t3">
          01未接种过流感疫苗人群
        </view>
        <image class="img" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/refff1.png">

        </image>
        <view class="t2">
          备注：接种两针的需要间隔≥4周才能继续接种
        </view>
        <view class="t3">
          02既往接种过流感疫苗人群
        </view>
        <image class="img" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/refff2.png">

        </image>
        <view class="t2">
          同一流感季，如已接种完全程，无需再重复接种
        </view>
      </view>
    </template>
  </view>
</template>
<script setup>
import { ref } from "vue";
const type = ref(1)
const list = ref([{
  id: 1,
  title: "出生",
  items: [{
    name: "乙肝（酿酒酵母）",
    des: "可预防乙型肝炎",
    index: 1,
    total: 3,
  }, {
    name: "卡介苗",
    des: "可预防儿童结核病，特别是对婴幼儿结核性脑膜炎和栗粒型肺结核有预防作用",
    index: 1,
    total: 1,
  }]
}, {
  id: 2,
  title: "1月龄",
  items: [{
    name: "乙肝（酿酒酵母）",
    des: "可预防乙型肝炎",
    index: 2,
    total: 3,
  }]
}, {
  id: 3,
  title: "2月龄",
  items: [{
    name: "脊灰（灭活salk）",
    des: "可预防脊髓灰质炎，俗称“小儿麻痹症”",
    index: 1,
    total: 4,
  }]
}, {
  id: 4,
  title: "3月龄",
  items: [{
    name: "脊灰（灭活salk）",
    des: "可预防脊髓灰质炎，俗称“小儿麻痹症”",
    index: 2,
    total: 4,
  }, {
    name: "百白破（无细胞）",
    des: "可预防百日咳、白喉、破伤风",
    index: 1,
    total: 4,
  }]
}, {
  id: 5,
  title: "4月龄",
  items: [{
    name: "二价脊灰疫苗",
    des: "可预防脊髓灰质炎，俗称“小儿麻痹症”",
    index: 3,
    total: 4,
  }, {
    name: "百白破（无细胞）",
    des: "可预防百日咳、白喉、破伤风",
    index: 2,
    total: 4,
  }]
}, {
  id: 6,
  title: "5月龄",
  items: [{
    name: "百白破（无细胞）",
    des: "可预防百日咳、白喉、破伤风",
    index: 3,
    total: 4,
  }]
}, {
  id: 7,
  title: "6月龄",
  items: [{
    name: "乙肝（酿酒酵母）",
    des: "可预防乙型肝炎",
    index: 3,
    total: 3,
  }, {
    name: "流脑A群",
    des: "可与法国A群脑膜炎奈瑟球菌引起的流行性脑脊髓膜炎",
    index: 1,
    total: 2,
  }]
}, {
  id: 8,
  title: "8月龄",
  items: [{
    name: "麻风",
    des: "可预防麻疹、风疹",
    index: 1,
    total: 1,
  }, {
    name: "乙脑（减毒）",
    des: "可预防流行性乙型脑炎",
    index: 1,
    total: 2,
  }]
}, {
  id: 9,
  title: "9月龄",
  items: [{
    name: "流脑A群",
    des: "可与法国A群脑膜炎奈瑟球菌引起的流行性脑脊髓膜炎",
    index: 2,
    total: 2,
  }]
}, {
  id: 10,
  title: "18月龄",
  items: [{
    name: "麻腮风",
    des: "可预防麻疹、腮腺炎和风疹",
    index: 1,
    total: 1,
  }, {
    name: "百白破（无细胞）",
    des: "可预防百日咳、白喉、破伤风",
    index: 4,
    total: 4,
  }, {
    name: "甲肝（减毒）",
    des: "可预防甲型肝炎",
    index: 1,
    total: 1,
  }]
}, {
  id: 11,
  title: "2周岁",
  items: [{
    name: "麻风",
    des: "可预防麻疹、风疹",
    index: 1,
    total: 1,
  }, {
    name: "乙脑（减毒）",
    des: "可预防流行性乙型脑炎",
    index: 1,
    total: 2,
  }]
}, {
  id: 12,
  title: "3周岁",
  items: [{
    name: "流脑A+C群",
    des: "可预防A群和C群脑膜炎奈瑟球菌引起的流行性脑脊髓膜炎",
    index: 1,
    total: 2,
  }]
}, {
  id: 13,
  title: "4周岁",
  items: [{
    name: "二价脊灰疫苗",
    des: "可预防脊髓灰质炎，俗称“小儿麻痹症”",
    index: 4,
    total: 4,
  }]
}, {
  id: 14,
  title: "6周岁",
  items: [{
    name: "白破",
    des: "可预防经百白破疫苗全程免以后的白喉、破伤风加强免疫",
    index: 1,
    total: 1,
  }, {
    name: "流脑A+C群",
    des: "可预防A群和C群脑膜炎奈瑟球菌引起的流行性脑脊髓膜炎",
    index: 2,
    total: 2,
  }]
}])

</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;
  padding-bottom: 20rpx;

  .tips {
    width: 690rpx;
    font-size: 30rpx;
    font-weight: 400;
    color: #787D8E;
    line-height: 42rpx;
    margin: 20rpx auto;
  }

  .btns {
    display: flex;
    flex-direction: row;
    margin: 20rpx auto;
    padding-left: 30rpx;

    .btn {
      width: 144rpx;
      height: 64rpx;
      background: rgba(216, 216, 216, 0);
      border-radius: 8rpx;
      border: 2rpx solid #D9D9D9;
      font-size: 28rpx;
      font-weight: 500;
      color: #63676F;
      line-height: 64rpx;
      text-align: center;
    }

    .btn2 {
      margin-left: 20rpx;
    }

    .btna {
      background: #FFFFFF;
      color: #63676F;
      box-shadow: 0rpx 16rpx 18rpx -8rpx rgba(0, 0, 0, 0.11);
      border-radius: 8rpx;
    }
  }

  .scetion {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    padding: 40rpx 30rpx;

    .t1 {
      font-size: 36rpx;
      font-weight: 600;
      color: #2A2D3C;
    }

    .t2 {
      font-size: 26rpx;
      font-weight: 400;
      color: #7A7E8F;
      line-height: 36rpx;
      margin-top: 8rpx;
    }

    .t3 {
      font-size: 32rpx;
      font-weight: 600;
      color: #1975FF;
      line-height: 44rpx;
      margin-top: 40rpx;
    }

    .img {
      width: 630rpx;
      height: 424rpx;
      margin-top: 20rpx;
    }
  }

  .list {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;

    .title {
      width: 630rpx;
      height: 112rpx;
      font-size: 36rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #2A2D3C;
      line-height: 112rpx;
      border-bottom: 1rpx solid #eee;
      margin: 0 auto;
    }

    .item {
      width: 690rpx;
      padding: 30rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-bottom: 1rpx solid #eee;

      .info {
        flex: 1;
      }

      .t1 {
        font-size: 30rpx;
        font-weight: 500;
        color: #3D414E;
        line-height: 42rpx;
      }

      .t2 {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8E;
        line-height: 36rpx;
        margin-top: 8rpx;
      }
    }
  }
}
</style>