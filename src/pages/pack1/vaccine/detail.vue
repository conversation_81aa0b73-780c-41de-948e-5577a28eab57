<template>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive :background="{ backgroundColor: 'rgba(255,255,255,0))' }" title="">
    </u-navbar>
    <swiper class="swiper-wrap" :indicator-dots="swiperOption.indicatorDots" :autoplay="swiperOption.autoplay"
      :interval="swiperOption.interval" :duration="swiperOption.duration">
      <swiper-item v-for="(item, index) in swiperList" :key="index">
        <view class="swiper-item">
          <image :src="item" mode="aspectFill" />
        </view>
      </swiper-item>
    </swiper>
    <view class="body">
      <view class="info-top">
        <view class="left" @click="intoDetail(info.id)">
          <view class="name">
            <view class="namess">{{ info.name }} </view>
          </view>
          <view class="tag">
            <text class="text">公立</text>
          </view>
          <view class="time">
            {{ info.servicetime }}
          </view>
        </view>
        <image class="phone" @click="callPhone" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone.png">
        </image>
      </view>
      <view class="address" @click="goDaohang">
        <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
        </image>
        <view class="add-content">
          <view class="lefec">
            <view class="t1">{{ info.address }}</view>
            <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="info.distance >= 1">距你{{ (info.distance * 1).toFixed(2)
                }}km</text>
              <text v-else class="distance">距你{{ info.distance * 1000 }}m</text>
            </template>
          </view>
          <view class="rsec">
            <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
            </image>
          </view>
        </view>
      </view>
      <view class="titiii">
        疫苗类型
      </view>
      <view class="tabs">
        <view class="tab" :class="{ active: type == 1 }" @click="type = 1">一类疫苗(免费)</view>
        <view class="tab" :class="{ active: type == 2 }" @click="type = 2">二类疫苗(自费)</view>
      </view>
      <template v-if="listClass.length">
        <view class="infoss" @click="goBook(item)" v-for="item in listClass" :key="item.id">
          <view class="tsp">
            <view class="t1">{{ item.name }}</view>
            <view class="t4" v-if="item.price == 0">免费</view>
            <view class="t7" v-else>￥{{ item.price }}</view>
          </view>
          <view class="t5">{{ item.description }}</view>
          <view class="t2" v-if="Number(item.stock) > 0">可预约</view>
          <view class="t3" v-else>无疫苗不可预约</view>
        </view>
      </template>
      <u-empty v-else margin-top="40" text="暂无疫苗"
        src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png"></u-empty>
    </view>
  </view>
</template>
<script setup>
import { ref, computed } from "vue";
import { useLocalStore } from "@/stores/useLocalStore.js";
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request.js";
const currentLocal = useLocalStore();
let id = '';
const info = ref({
  name: "",
  distance: 0,
  address: "",
  lng: '',
  lat: '',
  servicetime: "",
  contact: ""
})
const swiperList = ref([])
let swiperOption = ref({
  indicatorDots: false,
  autoplay: false,
  interval: 2000,
  duration: 500,
});
onLoad((option) => {
  id = option.id;
  getData();
  getVaccineList();
})
const list = ref([])
function getVaccineList() {
  request({
    url: "/api/healthservices/vaccine/list",
    data: {
      hospital_id: id,
    }
  }).then(res => {
    list.value = res;
  })
}
const type = ref(1);
const listClass = computed(() => {
  return list.value.filter(item => item.classify == type.value)
})
function getData() {
  uni.showLoading()
  request({
    url: "/api/healthservices/hospitalDetail",
    data: {
      id,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    info.value = res;
    try {
      let imgs = JSON.parse(res.images);
      if (imgs.length) {
        swiperList.value = imgs;
      } else {
        swiperList.value.push("https://obs.tuoyupt.com/yjy_mini/front_img/physical/yiyuan.png")
      }
    } catch (error) {
      swiperList.value.push("https://obs.tuoyupt.com/yjy_mini/front_img/physical/yiyuan.png")
    }
    uni.hideLoading();
  })
}
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: info.value.contact,
  });
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng),
    name: info.value.name,
    address: info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
function goBook(item) {
  if (!Number(item.stock) > 0) {
    uni.showToast({
      title: "无疫苗不可预约",
      mask: true,
      duration: 1500,
      icon: "none"
    })
    return
  }
  uni.setStorageSync("vaccine_hospital_info", info.value);
  uni.showLoading()
  request({
    url: "/api/healthservices/vaccine/getTimeList",
    data: {
      hospital_id: info.value.id,
      week: 0
    }
  }).then(res => {
    request({
      url: "/api/healthservices/vaccine/detail",
      data: {
        id: item.id,
      }
    }).then(res => {
      uni.hideLoading();
      uni.setStorageSync("vaccine_info", res);
      uni.navigateTo({
        url: `./book`,
      });
    })
  }).catch(() => {
    uni.hideLoading();
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;


  .swiper-wrap {
    width: 750rpx;
    height: 420rpx;

    .swiper-item {
      display: block;
      height: 420rpx;
      text-align: center;

      image {
        height: 100%;
        display: block;
        width: 100%;
      }
    }
  }

  .body {
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    margin-top: -40rpx;
    padding: 40rpx 30rpx;
    position: relative;
    z-index: 2;

    .info-top {
      display: flex;
      flex-direction: row;
      padding-top: 40rpx;

      .left {
        flex: 1;

        .name {
          width: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: row;

          .namess {
            font-size: 40rpx;
            font-weight: 500;
            color: #262937;
            max-width: 540rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }

        .rate {
          display: flex;
          flex-direction: row;

          .rtt {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .tag {
          display: flex;
          flex-wrap: wrap;
          overflow: hidden;
          margin-top: 14rpx;
          margin-bottom: 14rpx;
          min-height: 40rpx;

          .text {
            font-size: 20rpx;
            color: #262937;
            padding: 0 16rpx;
            margin-right: 8rpx;
            margin-bottom: 8rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            height: 40rpx;
            display: flex;
            align-items: center;
          }
        }

        .time {
          font-size: 28rpx;
          font-weight: 400;
          color: #7A7E8F;
        }
      }

      .phone {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
    }

    .address {
      width: 690rpx;
      height: 132rpx;
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      margin-top: 40rpx;

      .back {
        position: absolute;
        width: 690rpx;
        height: 132rpx;
        left: 0;
        top: 0;
        z-index: 1;
      }

      .add-content {
        width: 690rpx;
        height: 132rpx;
        display: flex;
        flex-direction: row;
        position: relative;
        z-index: 2;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;

        .lefec {
          .t1 {
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
          }

          .distance {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .rsec {
          width: 40rpx;
          height: 40rpx;

          .right {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .titiii {
      font-size: 32rpx;
      font-weight: 500;
      color: #292D3B;
      margin: 40rpx 0 0;
      font-weight: 600;
    }

    .tabs {
      width: 100%;
      height: 144rpx;
      display: flex;
      flex-direction: row;
      align-items: center;

      .tab {
        padding: 0 16rpx;
        height: 64rpx;
        background: #F1F5F8;
        border-radius: 32rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #7A7E8F;
        line-height: 64rpx;
        margin-right: 24rpx;
      }

      .active {
        color: #FFFFFF;
        background: #1975FF;
      }
    }

    .infoss {
      width: 690rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      border: 1rpx solid #EEEEEE;
      padding: 30rpx;
      margin-bottom: 20rpx;

      .tsp {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .t1 {
          font-size: 32rpx;
          font-weight: 600;
          color: #262937;
          max-width: 80%;
        }

        .t4 {
          font-size: 32rpx;
          font-weight: 500;
          color: #00CB46;
        }

        .t7 {
          font-size: 32rpx;
          font-weight: 500;
          color: #FF7373;
        }
      }

      .t5 {
        font-size: 26rpx;
        font-weight: 400;
        color: #7A7E8F;
        margin-top: 12rpx;
      }

      .t2 {
        width: fit-content;
        padding: 0 12rpx;
        height: 40rpx;
        background: #E2F3FF;
        border-radius: 8rpx;
        font-size: 20rpx;
        font-weight: 400;
        color: #1975FF;
        line-height: 40rpx;
        text-align: center;
        margin-top: 16rpx;
      }

      .t3 {
        width: fit-content;
        padding: 0 12rpx;
        height: 40rpx;
        background: rgb(249, 195, 193);
        border-radius: 8rpx;
        font-size: 20rpx;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 40rpx;
        text-align: center;
        margin-top: 16rpx;
      }
    }
  }
}
</style>