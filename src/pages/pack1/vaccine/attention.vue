<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#FFFFFF' }" title="">
    </u-navbar>
    <view class="t1">⚠️注意事项</view>
    <view class="t2">家长要将疫苗接种前的知情告知当回事儿!</view>
    <text class="t3">
      我们都知道，疫苗接种前，接种医生需要向家长进行知情告知，家长阅读知情同意书或听取门头知情告知后，均需要签字。关于知情告知这件事儿，儿童家长需要了解的重要信息都有哪些？
    </text>
    <view class="secion">
      <view class="top">
        <image class="iii" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/sdasd.png">
        </image>
        <view class="text">
          每次接种疫苗前都得签署知情同意书吗?
        </view>
      </view>
      <text class="t2">
        是的，这是一项必不可少的流程。我国的《疫苗流通和预防接种管理条例》中规定:
        医疗P生人员在实施接种前，应当告知受种者或者其监护人所接种疫苗的品种、作用、禁忌、不良反应以及注意事项，询问受种者的健康状况以及是否有接种禁忌等情况，如实记录告知和询问情况。受种者或者其监护人应当了解预防接种的相关知识，如实提供受种者的健康状况和接种禁忌等情况。目前，我国接种疫苗前告知，多数地区是以书面形式体现，部分地区实行口头告知，但无论是哪一种形式，告知后均要由受种者或者其法定监护人签字后，才能接种疫苗。
      </text>
    </view>
    <view class="secion">
      <view class="top">
        <image class="iii" src="https://obs.tuoyupt.com/yjy_mini/front_img/vaccine/sdasd.png">
        </image>
        <view class="text">
          家长们需要重点关注知情同意中的哪些内容？
        </view>
      </view>
      <text class="t2">
        1）接种告知中的疫苗与预防接种证中预约疫苗是否一致;\n
        2）要接种的疫苗可以预防哪种传染病;\n
        3）孩子的月 (年) 龄是否在该疫苗接种的月(年) 龄范围内;\n
        4）了解要接种疫苗的禁忌，以及孩子当下的健康状况是否适宜接种;\n
        5）了解疫苗接种后的常见不良反应;\n
      </text>
    </view>
  </view>
</template>
<script setup>
</script>
<style lang="scss" scoped>
.pages {
  min-height: 100vh;
  width: 100vw;
  padding: 30rpx;

  .t1 {
    font-size: 48rpx;
    font-family: AppleColorEmoji;
    color: #000000;
    line-height: 64rpx;
    border-bottom: 1rpx solid #EEEEEE;
    padding-bottom: 20rpx;
  }

  .t2 {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
    line-height: 44rpx;
    margin: 40rpx 0;
  }

  .t3 {
    font-size: 32rpx;
    font-weight: 400;
    color: #282B39;
    line-height: 76rpx;
    margin-top: 40rpx;
  }

  .secion {
    width: 690rpx;
    background: #F1F5F8;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 40rpx;

    .top {
      display: flex;
      flex-direction: row;

      .iii {
        width: 48rpx;
        height: 48rpx;
        margin-right: 30rpx;
      }

      .text {
        font-size: 32rpx;
        font-weight: 500;
        color: #1975FF;
        line-height: 44rpx;
      }
    }

    .t2 {
      font-size: 32rpx;
      font-weight: 400;
      color: #282B39;
      line-height: 76rpx;
    }
  }
}
</style>