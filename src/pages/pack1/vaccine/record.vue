<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="接种记录">
    </u-navbar>
    <view class="top">
      <view class="left">
        <view class="t1">
          {{ child_info.name }}
        </view>
        <view class="t2">
          出生日期： {{ child_info.birth }}
        </view>
        <view class="t2">
          身份证号：{{ child_info.id_card }}
        </view>
      </view>
      <view class="btn" @click="selectShow = true">
        <image class="sw" src="https://obs.tuoyupt.com/miniprogram/changzhi/sw.svg"></image>
        <text>切换</text>
      </view>
    </view>
    <u-select @confirm="confirmSelect" value-name="id" label-name="name" v-model="selectShow"
      :list="child_list"></u-select>
    <template v-if="list.length">
      <view class="item" v-for="item in list" :key="item.id">
        <view class="row">
          <view class="name">{{ item.record_date }}</view>
        </view>
        <view class="row">
          <view class="label">
            接种疫苗：
          </view>
          <view class="value">
            {{ item.vaccine.name }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            接种针剂：
          </view>
          <view class="value">
            第{{ item.injectioning }}剂/共{{ item.vaccine.injection }}剂
          </view>
        </view>
        <view class="row">
          <view class="label">
            接种门诊：
          </view>
          <view class="value">
            {{ item.hospital.name }}
          </view>
        </view>
      </view>
    </template>
    <u-empty v-else margin-top="400" text="暂无接种记录"
      src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png"></u-empty>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onReachBottom, onShow, onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
onLoad(() => {
  getChild();
})
const child_list = ref([]);
const child_info = ref({
  id: 0,
  name: '',
  birth: '',
  id_card: '',
});
function getChild() {
  request({
    goLogin: true,
    url: "/api/healthservices/user/list",
  }).then(res => {
    child_list.value = res;
    if (res.length) {
      child_info.value = res[0];
      getData();
    }
  })
}
let page = 1;
const list = ref([{
  record_date: '2021-09-01',
  injectioning: "",
  vaccine: {
    name: '',
    injection: 0
  },
  hospital: {
    name: ''
  }
}])
let hasMore = true;
function getData() {
  page = 1;
  uni.showLoading();
  request({
    goLogin: true,
    url: "/api/parent/vaccinerecords/list",
    data: {
      student_id: child_info.value.id,
      per_page: 20,
      page: page,
    }
  }).then(res => {
    uni.hideLoading();
    if (res.length < 20) {
      hasMore = false;
    } else {
      hasMore = true;
    }
    list.value = res;
  })
}
onReachBottom(() => {
  if (hasMore) {
    page++;
    request({
      url: "/api/parent/vaccinerecords/list",
      data: {
        student_id: child_info.value.id,
        per_page: 20,
        page: page,
      }
    }).then(res => {
      uni.hideLoading();
      if (res.length < 20) {
        hasMore = true;
      } else {
        hasMore = false;
      }
      list.value.push(...res);
    })
  }
})
const selectShow = ref(false);
function confirmSelect(e) {
  let info = child_list.value.find(item => {
    return item.id == e[0].value;
  })
  child_info.value = info;
  getData();
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .top {
    width: 100vw;
    height: 236rpx;
    padding: 24rpx 30rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .t1 {
      font-weight: 600;
      font-size: 52rpx;
      color: #000000;
      line-height: 74rpx;
      margin-bottom: 20rpx;
    }

    .t2 {
      font-weight: 400;
      font-size: 26rpx;
      color: #7A7E8E;
      line-height: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn {
      width: 200rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #1975FF;
      line-height: 36rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }

  .item {
    width: 100vw;
    margin: 20rpx auto;
    background: #FFFFFF;
    padding: 40rpx 30rpx;
    border-bottom: 1rpx solid #EEE;

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 10rpx;
      }

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .color0 {
        color: #1975FF;
      }

      .color1 {
        color: #00CB46;
      }
    }
  }
}
</style>