<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :custom-back="backFun" :background="{ backgroundColor: '#F1F5F8' }" title="预约成功">
    </u-navbar>
    <view class="section">
      <view class="topstatus"
        :class="{ inactive: info.status == 2 || info.status == 3, activ: info.status == 1 || info.status == 4 }">

      </view>
      <view class="top">
        <image v-if="info.status == 0" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/success.png"
          class="icon">
        </image>
        <image v-else-if="info.status == 1 || info.status == 4"
          src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/suasdas.png" class="icon">
        </image>
        <image v-else src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/yqyuquy.png" class="icon">
        </image>
        <view class="in">
          <view class="inl">
            <view class="t1">
              {{ info.status == 0 ? '预约成功' :
                info.status == 1 ? '已接种' :
                  info.status == 2 ? '已取消' : info.status == 3 ?
                    '已过期' :
                    '已签到'
              }}
            </view>
            <view class="t2" v-if="info.status == 0">
              待接种
            </view>
          </view>
          <view class="msg">
            {{ info.status == 0 ? '如无法按照预约时间前往，请提前取消预约' :
              info.status == 1 ? '已接种' :
                info.status == 2 ? '取消成功' : ""
            }}
          </view>
        </view>
      </view>
      <view class="content">
        <view class="code">
          {{ info.code }}
        </view>
        <view class="msg">
          预约码
        </view>
        <view class="row">
          <view class="l1">
            接种人
          </view>
          <view class="l2">
            {{ info.bm_student.name }}
          </view>
        </view>
        <view class="row">
          <view class="l1">
            预约接种时间
          </view>
          <view class="l2">
            {{ info.order_time }} {{ info.order_period.order_period }}
          </view>
        </view>
        <view class="address" @click="goDaohang">
          <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
          </image>
          <view class="add-content">
            <view class="lefec">
              <view class="t1">{{ info.hospital.name }}</view>
              <text class="distance">{{ info.hospital.address }}</text>
            </view>
            <view class="rsec">
              <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
              </image>
            </view>
          </view>
        </view>
        <!-- <view class="tips">
          有效期至2023年12月31日
        </view> -->
      </view>
    </view>
    <view class="section">
      <view class="content">
        <view class="conteee">预约信息</view>
        <view class="row">
          <view class="l3">
            预约人：
          </view>
          <view class="l4">
            {{ info.bm_student.name }}
          </view>
        </view>
        <view class="row">
          <view class="l3">
            疫苗类型：
          </view>
          <view class="l4">
            {{ info.vaccine.name }}
          </view>
        </view>
        <view class="row">
          <view class="l3">
            接种门诊：
          </view>
          <view class="l4">
            {{ info.hospital.name }}
          </view>
        </view>
        <view class="row">
          <view class="l3">
            接种日期：
          </view>
          <view class="l4">
            {{ info.order_time }}
          </view>
        </view>
        <view class="conteee">订单信息</view>
        <view class="row">
          <view class="l3">
            订单价格：
          </view>
          <view class="l4">
            ￥{{ info.vaccine.price }}
          </view>
        </view>
        <view class="row">
          <view class="l3">
            支付方式：
          </view>
          <view class="l4">
            医院现场支付
          </view>
        </view>
      </view>
    </view>
    <view class="notes">
      <text class="notee">
        温馨提示:\n
        1.请按照预约时间前往接种门诊。\n
        2.如无法按预约时间前往，请提前至少半小时取消。\n
        3.接种时请携带有效身份证件（儿童需携带接种证）前往接种门诊。\n
        4.请在监护人陪同下前往接种疫苗。</text>
    </view>
    <template v-if="info.status == 0 || info.status == 2">
      <view class="footer-zhanwei"></view>
      <view class="footer">
        <view class="footer-content">
          <template v-if="info.status == 0">
            <view class="btn btn2" @click="cel">取消预约</view>
          </template>
          <view class="btn btn2" @click="cel" v-else>删除预约</view>
        </view>
      </view>
    </template>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
let id = '';
const info = ref({
  bm_student_id: 13,
  code: "48369517",
  id: 5,
  order_period: "08:30~09:00",
  order_time: "2024-01-23",
  status: 1,
  user_id: 9,
  bm_student: {
    name: "",
  },
  order_period: {
    order_period: ""
  },
  hospital: {
    name: "",
    address: "",
    lat: "",
    lng: ""
  },
})
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.hospital.lat),
    longitude: Number(info.value.hospital.lng),
    name: info.value.hospital.name,
    address: info.value.hospital.address,
    success: function () {
      console.log('success');
    }
  });
}
let type = 1;
onLoad((option) => {
  id = option.id;
  type = option.type || 1;
  getData();
})
function getData() {
  uni.showLoading()
  request({
    url: "/api/healthservices/vaccine/recorddetail",
    data: {
      id
    }
  }).then(res => {
    info.value = res;
    uni.hideLoading();
  })
}
function cel() {
  if (info.value.status == 0) {
    uni.showModal({
      title: "确定取消预约？",
      success: function (res) {
        if (res.confirm) {
          request({
            url: '/api/healthservices/vaccine/cancel',
            method: 'post',
            data: {
              id
            }
          }).then(res => {
            uni.showToast({
              title: "取消成功",
              icon: "none"
            })
            getData();
          })
        }
      }
    })
  } else {
    uni.showModal({
      title: "确定删除预约？",
      success: function (res) {
        if (res.confirm) {
          request({
            url: '/api/healthservices/vaccine/delete',
            method: 'post',
            data: {
              id
            }
          }).then(res => {
            uni.showToast({
              title: "删除成功",
              icon: "none"
            })
            uni.navigateBack();
          })
        }
      }
    })
  }
}
function backFun() {
  if (type == 1) {
    uni.navigateBack();
  } else {
    uni.navigateBack({
      delta: 4
    })
  }
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .notes {
    width: 690rpx;
    margin: 20rpx auto;

    .notee {
      font-size: 26rpx;
      font-weight: 400;
      color: #7A7E8F;
      line-height: 48rpx;
    }
  }

  .section1 {
    padding: 30rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #3D414E;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      height: 68rpx;

      .l1 {
        font-size: 28rpx;
        font-weight: 400;
        color: #262937;
      }
    }
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;

    .topstatus {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 10rpx;
      background: #1975FF;
    }

    .topstatus.inactive {

      background: #2A2D3C;
    }

    .topstatus.activ {

      background: #00CB46;
    }

    .top {
      width: 690rpx;
      height: 176rpx;
      background: linear-gradient(90deg, #F9FCFF 0%, #F0F9FF 49%, #FFFFFF 100%);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 30rpx;

      .icon {
        width: 72rpx;
        height: 72rpx;
      }

      .in {
        padding-left: 30rpx;
        flex: 1;

        .inl {
          display: flex;
          flex-direction: row;
          justify-content: space-between;

          .t1 {
            font-size: 48rpx;
            font-weight: 600;
            color: #262937;
          }

          .t2 {
            font-size: 28rpx;
            font-weight: 500;
            color: #1975FF;
          }

          .t2.inactive {
            color: #2A2D3C;
          }
        }

        .msg {
          font-size: 26rpx;
          font-weight: 400;
          color: #AEB0BC;
        }
      }
    }

    .content {
      padding: 30rpx;

      .code {
        font-size: 66rpx;
        font-weight: bold;
        color: #2A2D3C;
        text-align: center;
      }

      .conteee {
        height: 48rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: #3D414E;
        line-height: 48rpx;
      }

      .msg {
        font-size: 28rpx;
        font-weight: 400;
        color: #AEB0BC;
        text-align: center;
      }

      .row {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        height: 60rpx;

        .l1 {
          font-size: 28rpx;
          font-weight: 400;
          color: #262937;
        }

        .l2 {
          font-size: 28rpx;
          font-weight: 600;
          color: #262937;
        }

        .l3 {
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8E;
        }

        .l4 {
          font-size: 26rpx;
          font-weight: 600;
          color: #3D414E;
        }
      }

      .address {
        width: 630rpx;
        height: 132rpx;
        position: relative;
        border-radius: 16rpx;
        overflow: hidden;
        margin-top: 40rpx;

        .back {
          position: absolute;
          width: 630rpx;
          height: 132rpx;
          left: 0;
          top: 0;
          z-index: 1;
        }

        .add-content {
          width: 630rpx;
          height: 132rpx;
          display: flex;
          flex-direction: row;
          position: relative;
          z-index: 2;
          align-items: center;
          justify-content: space-between;
          padding: 0 30rpx;

          .lefec {
            .t1 {
              font-size: 28rpx;
              font-weight: 500;
              color: #262937;
              line-height: 40rpx;
            }

            .distance {
              font-size: 24rpx;
              font-weight: 400;
              color: #787C8D;
            }
          }

          .rsec {
            width: 40rpx;
            height: 40rpx;

            .right {
              width: 40rpx;
              height: 40rpx;
            }
          }
        }
      }

      .tips {
        font-size: 24rpx;
        font-weight: 400;
        color: #787C8D;
        line-height: 34rpx;
        margin-top: 40rpx;
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;



    .btn {
      width: 100%;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }

    .btn2 {
      width: 100%;
      background: #FFFFFF;
      color: #617EFB;
    }
  }
}
</style>