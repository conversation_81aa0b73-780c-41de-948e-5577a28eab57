<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F6F6F6' }" title="接种门诊">
    </u-navbar>
    <view class="section">
      <view class="pop-top">
        <view class="warp">
          <image class="icon1" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/loc.png">
          </image>
          <nj-address @change="lochange">
            <view class="text">
              {{ currentLocal.region }}
            </view>
          </nj-address>
          <image class="icon2" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/xiala.png">
          </image>
        </view>
        <view class="hr"></view>
        <u-search style="width: 500rpx;" :showLeft="false" focus @focus="onFocus" height="80" @clear="onClear"
          @search="search" shape="square" :show-action="false" placeholder="门诊" v-model="keyword"></u-search>
      </view>
      <view class="ssection">
        <view class="t1">接种门诊</view>
        <view class="t2" @click="goMap">
          门诊地图
        </view>
        <image @click="goMap" class="map" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/map.png">
        </image>
      </view>

      <template v-if="list.length">
        <view class="item" @click="intoDetail(item.id)" v-for="(item, index) in list" :key="index">
          <u-image width="64rpx" height="64rpx" border-radius="32" mode="aspectFill"
            src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/jigoua.png"></u-image>
          <view class="msg">
            <view class="name">{{ item.name }}</view>
            <view class="address">
              <template v-if="!currentLocal.noLocation">
                <text class="distance" v-if="item.distance >= 1">{{ (item.distance * 1).toFixed(2) }}km·</text>
                <text v-else class="distance">{{ item.distance * 1000 }}m·</text>
              </template>
              <text>{{ item.area_name }} | {{ item.address }}</text>
            </view>
          </view>
        </view>
        <u-loadmore margin-top="40" :status="status" :load-text="loadText" />
      </template>
      <view class="mno-data" v-else>
        <image src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png" mode="aspectFit" />
        <view class="t1">抱歉</view>
        <view class="t2">未找到相关门诊机构</view>
      </view>
    </view>
    <view class="zhanwei"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onReachBottom } from '@dcloudio/uni-app';
import request from "@/request.js";
import { useLocalStore } from "@/stores/useLocalStore.js"
let loadText = {
  loadmore: '轻轻上拉',
  loading: '努力加载中',
  nomore: '已经加载全部'
}
onLoad(() => {
  getList();
})
function lochange(e) {
  currentLocal.province = e.provinceCode;
  currentLocal.city = e.city;
  currentLocal.areaCode = e.areaCode;
  currentLocal.region = e.area;
  getList();
}
function onFocus() {
  showRes.value = false;
}
function onClear() {
  showRes.value = false;
  list.value = []
}
function search() {
  getList();
}
function intoDetail(id) {
  if (id) {
    uni.navigateTo({
      url: `./detail?id=${id}`,
    });
  }
}
const showRes = ref(true);
let page = 1;
const keyword = ref("");
const currentLocal = useLocalStore();
let list = ref([])
let status = ref('loadmore');
let hasMore = true;
function getList() {
  page = 1;
  uni.showLoading()
  request({
    url: `/api/healthservices/hospitalList`,
    data: {
      page,
      per_page: 10,
      name: keyword.value,
      area: currentLocal.areaCode,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    list.value = res;
    if (res.length < 10) {
      hasMore = false;
      status.value = 'nomore';
    } else {
      hasMore = true;
      status.value = 'loadmore';
    }
    showRes.value = true;
    uni.hideLoading();
  })
}
onReachBottom(() => {
  if (hasMore) {
    status.value = 'loading';
    page++;
    request({
      url: `/api/healthservices/hospitalList`,
      data: {
        page,
        per_page: 10,
        name: keyword.value,
        area: currentLocal.areaCode,
        lng: currentLocal.longitude,
        lat: currentLocal.latitude
      }
    }).then(res => {
      list.value.push(...res);
      if (res.length < 10) {
        hasMore = false;
        status.value = 'nomore';
      } else {
        hasMore = true;
        status.value = 'loadmore';
      }
    })
  }
})
function goMap() {
  uni.navigateTo({
    url: "./map"
  })
}
</script>

<style lang="scss" scoped>
.pages {
  background-color: #FFFFFF;

  .top-zhanwei {
    width: 750rpx;
    height: 190rpx;
  }

  .section {
    position: relative;
    z-index: 2;
    padding: 40rpx 30rpx 0;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0 0;

    .ssection {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      height: 96rpx;
      border-bottom: 1rpx solid #EEE;

      .t1 {
        font-size: 32rpx;
        font-weight: 600;
        color: #262937;
      }

      .t2 {
        font-size: 26rpx;
        font-weight: 400;
        color: #797E8E;
        margin-left: auto;
      }

      .map {
        width: 48rpx;
        height: 48rpx;
        margin-left: 8rpx;
      }
    }

    .pop-top {
      width: 690rpx;
      height: 80rpx;
      background: #F1F5F8;
      border-radius: 24rpx;
      display: flex;
      flex-direction: row;
      align-items: center;

      .hr {
        width: 2rpx;
        height: 32rpx;
        background: #AEB0BC;
      }

      .warp {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        width: 188rpx;
        height: 80rpx;

        .icon1 {
          width: 24rpx;
          height: 24rpx;
          margin-right: 12rpx;
        }

        .text {
          width: 78rpx;
          font-size: 26rpx;
          font-weight: 500;
          color: #262937;
        }

        .icon2 {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }


  .item {
    width: 100%;
    height: 156rpx;
    display: flex;
    background: #fff;
    margin-top: 30rpx;
    flex-direction: row;


    .msg {
      padding-left: 16rpx;
      flex: 1;

      .name {
        font-size: 32rpx;
        font-weight: 500;
        color: #262937;
        line-height: 44rpx;
        width: 100%;
      }

      .address {
        width: 100%;
        display: flex;
        font-size: 26rpx;
        font-weight: 400;
        color: #7A7E8F;
        margin-top: 16rpx;
      }
    }
  }
}

.zhanwei {
  height: calc(env(safe-area-inset-bottom));
}

.mno-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20%;
  flex-direction: column;
  background: #fff;

  image {
    width: 160rpx;
    height: 160rpx;
    margin: 196rpx 0 24rpx;
  }

  .t1 {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #262937;
    margin-bottom: 20rpx;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 400;
    color: #aeb0bc;
    line-height: 44rpx;
  }

  .btn {
    width: 240rpx;
    height: 80rpx;
    background: #1975FF;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 48rpx;
    margin-bottom: 100rpx;
  }
}

.top {
  width: 750rpx;
  height: 400rpx;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 1;

  .backimg {
    width: 750rpx;
    height: 420rpx;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.warp {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 24rpx;

  .icon1 {
    width: 40rpx;
    height: 40rpx;
    margin-right: 14rpx;
  }

  .text {
    font-size: 34rpx;
    font-weight: 600;
    color: #000000;
  }

  .icon2 {
    width: 32rpx;
    height: 32rpx;
  }
}
</style>
