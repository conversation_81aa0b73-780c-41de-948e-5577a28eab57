<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="疫苗预约">
    </u-navbar>
    <view class="section">
      <view class="s1">
        <image class="icon" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/avter.png">
        </image>
        <view class="info">
          <view class="t1">{{ child_info.name }}
          </view>
          <view class="t2">
            身份证：{{ child_info.id_card }}
          </view>
        </view>
      </view>
      <view class="s2">
        <image class="icon" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/stime.png"></image>
        <text class="t1">{{ order_info.order_time }} {{ order_info.order_period }}</text>
        <text class="t2">{{ weekText }}</text>
      </view>
      <view class="s2">
        <image class="icon" src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/a2ddr.png"></image>
        <text class="t3">{{ hospital_info.name }}</text>
      </view>
    </view>
    <view class="section">
      <view class="title">{{ vaccine_info.name }}</view>
      <view class="row2">
        生产厂家：{{ vaccine_info.merchant?.name }}
      </view>
      <view class="row2">
        产品规格：{{ vaccine_info.spec }}
      </view>
    </view>
    <view class="section">
      <view class="row row1">
        <view class="label">
          费用
        </view>
        <view class="value">
          ¥{{ vaccine_info.price }}
        </view>
      </view>
      <view class="row">
        <view class="label">
          支付方式
        </view>
        <view class="value1">
          医院现场支付
        </view>
      </view>
    </view>
    <view class="footer-zhanwei"></view>
    <view class="footer">
      <view class="footer-content">
        <view class="btn" @click="submit">确认预约</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref, computed } from "vue";
import request from "@/request.js";
const hospital_info = ref({
  id: 0,
  name: "",
  distance: 0,
  address: "",
})
const order_info = ref({
  order_period: "",
  order_time: "",
  id: ""
})
const child_info = ref({
  id: 0,
  name: "",
  idcode: ""
})
const vaccine_info = ref({
  name: "",
  price: "",
  spec: "",
  stock: '',
  merchant: {
    name: ""
  }
})
function getWeekText(date) {
  date = new Date(`${date.replace(/\-/g, '/')} 00:00:00`);
  let week = date.getDay();
  return '星期' + ['日', '一', '二', '三', '四', '五', '六'][week];
}
const weekText = computed(() => {
  return getWeekText(order_info.value.order_time)
})
onLoad(() => {
  hospital_info.value = uni.getStorageSync('vaccine_hospital_info');
  order_info.value = uni.getStorageSync('vaccine_order_info');
  child_info.value = uni.getStorageSync('vaccine_child_info');
  vaccine_info.value = uni.getStorageSync('vaccine_info');
})
function submit() {
  uni.showLoading();
  request({
    url: '/api/healthservices/vaccine/yuyue',
    method: "post",
    data: {
      hospital_id: hospital_info.value.id,
      bm_student_id: child_info.value.id,
      order_time: order_info.value.order_time,
      order_period_id: order_info.value.id,
      vaccine_id: vaccine_info.value.id,
    }
  }).then(res => {
    uni.hideLoading();
    uni.showToast({
      title: "预约成功！",
      icon: "none",
      mask: true,
      duration: 1500
    })
    setTimeout(() => {
      uni.redirectTo({
        url: `./history?id=${res.id}&type=2`,
      })
    }, 1500)
  })
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .section {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 30rpx;

    .s2 {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 24rpx;

      .icon {
        width: 32rpx;
        height: 32rpx;
      }

      .t1 {
        font-size: 38rpx;
        font-weight: bold;
        color: #000000;
        word-break: break-all;
        margin-left: 8rpx;
      }

      .t2 {
        font-size: 32rpx;
        font-weight: 400;
        color: #7A7E8F;
        margin-left: 8rpx;
      }

      .t3 {
        font-size: 32rpx;
        font-weight: 400;
        color: #3D414E;
        margin-left: 8rpx;
      }
    }

    .s1 {
      border-bottom: 1rpx solid #EEE;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-bottom: 40rpx;

      .icon {
        width: 80rpx;
        height: 80rpx;
      }

      .info {
        padding-left: 38rpx;

        .t1 {
          font-size: 32rpx;
          font-weight: 500;
          color: #262937;
        }

        .t2 {
          font-size: 26rpx;
          font-weight: 400;
          color: #7A7E8F;
        }
      }
    }

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #2A2D3C;
      line-height: 44rpx;
    }

    .row1 {
      margin-bottom: 20rpx;
    }

    .row2 {
      font-size: 26rpx;
      font-weight: 400;
      color: #7A7E8E;
      line-height: 36rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .label {
        font-size: 32rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 32rpx;
        font-weight: 500;
        color: #000000;
      }

      .value1 {
        font-size: 32rpx;
        font-weight: 500;
        color: #FFBA34;
      }
    }
  }
}

.footer-zhanwei {
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
}

.footer {
  background: #FFFFFF;
  width: 750rpx;
  height: calc(constant(safe-area-inset-bottom) + 120rpx);
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  display: block;
  position: fixed;
  left: 0;
  bottom: 0;

  .footer-content {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 0 30rpx;

    .btn {
      width: 690rpx;
      height: 80rpx;
      border-radius: 16rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      background: #617EFB;
      line-height: 80rpx;
    }
  }
}
</style>