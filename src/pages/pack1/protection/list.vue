<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="检查报告">
    </u-navbar>
    <template v-if="list.length">
      <view class="item">
        <view class="name">小宝爱波</view>
        <view class="row">
          <view class="label">
            宝宝月龄：
          </view>
          <view class="value">
            医院现场支付
          </view>
        </view>
        <view class="row">
          <view class="label">
            体检时间：
          </view>
          <view class="value">
            医院现场支付
          </view>
        </view>
        <view class="row">
          <view class="label">
            预约门诊：
          </view>
          <view class="value">
            莲湖区青年路社区卫生服务中心
          </view>
        </view>
      </view>
    </template>
    <u-empty v-else margin-top="400" text="暂无婴幼儿体检记录"
      src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png"></u-empty>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onReachBottom, onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
onLoad(() => {
  getData();
})
let page = 1;
const list = ref([])
let hasMore = true;
function getData() {
  uni.showLoading();
  request({
    url: "/api/healthservices/protection/list",
    data: {
      per_page: 20,
      page: page,
    }
  }).then(res => {
    uni.hideLoading();
    if (res.length < 20) {
      hasMore = false;
    } else {
      hasMore = true;
    }
    list.value = res;
  })
}
onReachBottom(() => {
  if (hasMore) {
    page++;
    request({
      goLogin: true,
      url: "/api/healthservices/protection/list",
      data: {
        per_page: 20,
        page: page,
        status: 0
      }
    }).then(res => {
      uni.hideLoading();
      if (res.length < 20) {
        hasMore = false;
      } else {
        hasMore = true;
      }
      list.value.push(...res);
    })
  }
})
function goDeatil(item) {

}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .item {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;

    .name {
      font-size: 34rpx;
      font-weight: 500;
      color: #3D414E;
      margin-bottom: 40rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }
    }
  }
}
</style>