const boy_h = [
  [
    44.2,
    46.1,
    48,
    49.9,
    51.8,
    53.7,
    55.6
  ],
  [
    48.9,
    50.8,
    52.8,
    54.7,
    56.7,
    58.6,
    60.6
  ],
  [
    52.4,
    54.4,
    56.4,
    58.4,
    60.4,
    62.4,
    64.4
  ],
  [
    55.3,
    57.3,
    59.4,
    61.4,
    63.5,
    65.5,
    67.6
  ],
  [
    57.6,
    59.7,
    61.8,
    63.9,
    66,
    68,
    70.1
  ],
  [
    59.6,
    61.7,
    63.8,
    65.9,
    68,
    70.1,
    72.2
  ],
  [
    61.2,
    63.3,
    65.5,
    67.6,
    69.8,
    71.9,
    74
  ],
  [
    62.7,
    64.8,
    67,
    69.2,
    71.3,
    73.5,
    75.7
  ],
  [
    64,
    66.2,
    68.4,
    70.6,
    72.8,
    75,
    77.2
  ],
  [
    65.2,
    67.5,
    69.7,
    72,
    74.2,
    76.5,
    78.7
  ],
  [
    66.4,
    68.7,
    71,
    73.3,
    75.6,
    77.9,
    80.1
  ],
  [
    67.6,
    69.9,
    72.2,
    74.5,
    76.9,
    79.2,
    81.5
  ],
  [
    68.6,
    71,
    73.4,
    75.7,
    78.1,
    80.5,
    82.9
  ],
  [
    69.6,
    72.1,
    74.5,
    76.9,
    79.3,
    81.8,
    84.2
  ],
  [
    70.6,
    73.1,
    75.6,
    78,
    80.5,
    83,
    85.5
  ],
  [
    71.6,
    74.1,
    76.6,
    79.1,
    81.7,
    84.2,
    86.7
  ],
  [
    72.5,
    75,
    77.6,
    80.2,
    82.8,
    85.4,
    88
  ],
  [
    73.3,
    76,
    78.6,
    81.2,
    83.9,
    86.5,
    89.2
  ],
  [
    74.2,
    76.9,
    79.6,
    82.3,
    85,
    87.7,
    90.4
  ],
  [
    75,
    77.7,
    80.5,
    83.2,
    86,
    88.8,
    91.5
  ],
  [
    75.8,
    78.6,
    81.4,
    84.2,
    87,
    89.8,
    92.6
  ],
  [
    76.5,
    79.4,
    82.3,
    85.1,
    88,
    90.9,
    93.8
  ],
  [
    77.2,
    80.2,
    83.1,
    86,
    89,
    91.9,
    94.9
  ],
  [
    78,
    81,
    83.9,
    86.9,
    89.9,
    92.9,
    95.9
  ],
  [
    78,
    81,
    84.1,
    87.1,
    90.2,
    93.2,
    96.3
  ],
  [
    78.6,
    81.7,
    84.9,
    88,
    91.1,
    94.2,
    97.3
  ],
  [
    79.3,
    82.5,
    85.6,
    88.8,
    92,
    95.2,
    98.3
  ],
  [
    79.9,
    83.1,
    86.4,
    89.6,
    92.9,
    96.1,
    99.3
  ],
  [
    80.5,
    83.8,
    87.1,
    90.4,
    93.7,
    97,
    100.3
  ],
  [
    81.1,
    84.5,
    87.8,
    91.2,
    94.5,
    97.9,
    101.2
  ],
  [
    81.7,
    85.1,
    88.5,
    91.9,
    95.3,
    98.7,
    102.1
  ],
  [
    82.3,
    85.7,
    89.2,
    92.7,
    96.1,
    99.6,
    103
  ],
  [
    82.8,
    86.4,
    89.9,
    93.4,
    96.9,
    100.4,
    103.9
  ],
  [
    83.4,
    86.9,
    90.5,
    94.1,
    97.6,
    101.2,
    104.8
  ],
  [
    83.9,
    87.5,
    91.1,
    94.8,
    98.4,
    102,
    105.6
  ],
  [
    84.4,
    88.1,
    91.8,
    95.4,
    99.1,
    102.7,
    106.4
  ],
  [
    85,
    88.7,
    92.4,
    96.1,
    99.8,
    103.5,
    107.2
  ],
  [
    85.5,
    89.2,
    93,
    96.7,
    100.5,
    104.2,
    108
  ],
  [
    86,
    89.8,
    93.6,
    97.4,
    101.2,
    105,
    108.8
  ],
  [
    86.5,
    90.3,
    94.2,
    98,
    101.8,
    105.7,
    109.5
  ],
  [
    87,
    90.9,
    94.7,
    98.6,
    102.5,
    106.4,
    110.3
  ],
  [
    87.5,
    91.4,
    95.3,
    99.2,
    103.2,
    107.1,
    111
  ],
  [
    88,
    91.9,
    95.9,
    99.9,
    103.8,
    107.8,
    111.7
  ],
  [
    88.4,
    92.4,
    96.4,
    100.4,
    104.5,
    108.5,
    112.5
  ],
  [
    88.9,
    93,
    97,
    101,
    105.1,
    109.1,
    113.2
  ],
  [
    89.4,
    93.5,
    97.5,
    101.6,
    105.7,
    109.8,
    113.9
  ],
  [
    89.8,
    94,
    98.1,
    102.2,
    106.3,
    110.4,
    114.6
  ],
  [
    90.3,
    94.4,
    98.6,
    102.8,
    106.9,
    111.1,
    115.2
  ],
  [
    90.7,
    94.9,
    99.1,
    103.3,
    107.5,
    111.7,
    115.9
  ],
  [
    91.2,
    95.4,
    99.7,
    103.9,
    108.1,
    112.4,
    116.6
  ],
  [
    91.6,
    95.9,
    100.2,
    104.4,
    108.7,
    113,
    117.3
  ],
  [
    92.1,
    96.4,
    100.7,
    105,
    109.3,
    113.6,
    117.9
  ],
  [
    92.5,
    96.9,
    101.2,
    105.6,
    109.9,
    114.2,
    118.6
  ],
  [
    93,
    97.4,
    101.7,
    106.1,
    110.5,
    114.9,
    119.2
  ],
  [
    93.4,
    97.8,
    102.3,
    106.7,
    111.1,
    115.5,
    119.9
  ],
  [
    93.9,
    98.3,
    102.8,
    107.2,
    111.7,
    116.1,
    120.6
  ],
  [
    94.3,
    98.8,
    103.3,
    107.8,
    112.3,
    116.7,
    121.2
  ],
  [
    94.7,
    99.3,
    103.8,
    108.3,
    112.8,
    117.4,
    121.9
  ],
  [
    95.2,
    99.7,
    104.3,
    108.9,
    113.4,
    118,
    122.6
  ],
  [
    95.6,
    100.2,
    104.8,
    109.4,
    114,
    118.6,
    123.2
  ],
  [
    96.1,
    100.7,
    105.3,
    110,
    114.6,
    119.2,
    123.9
  ],
  [
    96.5,
    101.1,
    105.7,
    110.3,
    114.9,
    119.4,
    124
  ],
  [
    96.9,
    101.6,
    106.2,
    110.8,
    115.4,
    120,
    124.7
  ],
  [
    97.4,
    102,
    106.7,
    111.3,
    116,
    120.6,
    125.3
  ],
  [
    97.8,
    102.5,
    107.2,
    111.9,
    116.5,
    121.2,
    125.9
  ],
  [
    98.2,
    103,
    107.7,
    112.4,
    117.1,
    121.8,
    126.5
  ],
  [
    98.7,
    103.4,
    108.2,
    112.9,
    117.7,
    122.4,
    127.1
  ],
  [
    99.1,
    103.9,
    108.7,
    113.4,
    118.2,
    123,
    127.8
  ],
  [
    99.5,
    104.3,
    109.1,
    113.9,
    118.7,
    123.6,
    128.4
  ],
  [
    99.9,
    104.8,
    109.6,
    114.5,
    119.3,
    124.1,
    129
  ],
  [
    100.4,
    105.2,
    110.1,
    115,
    119.8,
    124.7,
    129.6
  ],
  [
    100.8,
    105.7,
    110.6,
    115.5,
    120.4,
    125.2,
    130.1
  ],
  [
    101.2,
    106.1,
    1110,
    116,
    120.9,
    125.8,
    130.7
  ],
  [
    102.4,
    107.4,
    112.4,
    117.4,
    122.4,
    127.5,
    132.5
  ],
  [
    103.6,
    108.7,
    113.8,
    118.9,
    124,
    129.1,
    134.2
  ]]
const boy_w = [
  [
    2.1,
    2.5,
    2.9,
    3.3,
    3.9,
    4.4,
    5
  ],
  [
    2.9,
    3.4,
    3.9,
    4.5,
    5.1,
    5.8,
    6.6
  ],
  [
    3.8,
    4.3,
    4.9,
    5.6,
    6.3,
    7.1,
    8
  ],
  [
    4.4,
    5,
    5.7,
    6.4,
    7.2,
    8,
    9
  ],
  [
    4.9,
    5.6,
    6.2,
    7,
    7.8,
    8.7,
    9.7
  ],
  [
    5.3,
    6,
    6.7,
    7.5,
    8.4,
    9.3,
    10.4
  ],
  [
    5.7,
    6.4,
    7.1,
    7.9,
    8.8,
    9.8,
    10.9
  ],
  [
    5.9,
    6.7,
    7.4,
    8.3,
    9.2,
    10.3,
    11.4
  ],
  [
    6.2,
    6.9,
    7.7,
    8.6,
    9.6,
    10.7,
    11.9
  ],
  [
    6.4,
    7.1,
    8,
    8.9,
    9.9,
    11,
    12.3
  ],
  [
    6.6,
    7.4,
    8.2,
    9.2,
    10.2,
    11.4,
    12.7
  ],
  [
    6.8,
    7.6,
    8.4,
    9.4,
    10.5,
    11.7,
    13
  ],
  [
    6.9,
    7.7,
    8.6,
    9.6,
    10.8,
    12,
    13.3
  ],
  [
    7.1,
    7.9,
    8.8,
    9.9,
    11,
    12.3,
    13.7
  ],
  [
    7.2,
    8.1,
    9,
    10.1,
    11.3,
    12.6,
    14
  ],
  [
    7.4,
    8.3,
    9.2,
    10.3,
    11.5,
    12.8,
    14.3
  ],
  [
    7.5,
    8.4,
    9.4,
    10.5,
    11.7,
    13.1,
    14.6
  ],
  [
    7.7,
    8.6,
    9.6,
    10.7,
    12,
    13.4,
    14.9
  ],
  [
    7.8,
    8.8,
    9.8,
    10.9,
    12.2,
    13.7,
    15.3
  ],
  [
    8,
    8.9,
    10,
    11.1,
    12.5,
    13.9,
    15.6
  ],
  [
    8.1,
    9.1,
    10.1,
    11.3,
    12.7,
    14.2,
    15.9
  ],
  [
    8.2,
    9.2,
    10.3,
    11.5,
    12.9,
    14.5,
    16.2
  ],
  [
    8.4,
    9.4,
    10.5,
    11.8,
    13.2,
    14.7,
    16.5
  ],
  [
    8.5,
    9.5,
    10.7,
    12,
    13.4,
    15,
    16.8
  ],
  [
    8.6,
    9.7,
    10.8,
    12.2,
    13.6,
    15.3,
    17.1
  ],
  [
    8.8,
    9.8,
    11,
    12.4,
    13.9,
    15.5,
    17.5
  ],
  [
    8.9,
    10,
    11.2,
    12.5,
    14.1,
    15.8,
    17.8
  ],
  [
    9,
    10.1,
    11.3,
    12.7,
    14.3,
    16.1,
    18.1
  ],
  [
    9.1,
    10.2,
    11.5,
    12.9,
    14.5,
    16.3,
    18.4
  ],
  [
    9.2,
    10.4,
    11.7,
    13.1,
    14.8,
    16.6,
    18.7
  ],
  [
    9.4,
    10.5,
    11.8,
    13.3,
    15,
    16.9,
    19
  ],
  [
    9.5,
    10.7,
    12,
    13.5,
    15.2,
    17.1,
    19.3
  ],
  [
    9.6,
    10.8,
    12.1,
    13.7,
    15.4,
    17.4,
    19.6
  ],
  [
    9.7,
    10.9,
    12.3,
    13.8,
    15.6,
    17.6,
    19.9
  ],
  [
    9.8,
    11,
    12.4,
    14,
    15.8,
    17.8,
    20.2
  ],
  [
    9.9,
    11.2,
    12.6,
    14.2,
    16,
    18.1,
    20.4
  ],
  [
    10,
    11.3,
    12.7,
    14.3,
    16.2,
    18.3,
    20.7
  ],
  [
    10.1,
    11.4,
    12.9,
    14.5,
    16.4,
    18.6,
    21
  ],
  [
    10.2,
    11.5,
    13,
    14.7,
    16.6,
    18.8,
    21.3
  ],
  [
    10.3,
    11.6,
    13.1,
    14.8,
    16.8,
    19,
    21.6
  ],
  [
    10.4,
    11.8,
    13.3,
    15,
    17,
    19.3,
    21.9
  ],
  [
    10.5,
    11.9,
    13.4,
    15.2,
    17.2,
    19.5,
    22.1
  ],
  [
    10.6,
    12,
    13.6,
    15.3,
    17.4,
    19.7,
    22.4
  ],
  [
    10.7,
    12.1,
    13.7,
    15.5,
    17.6,
    20,
    22.7
  ],
  [
    10.8,
    12.2,
    13.8,
    15.7,
    17.8,
    20.2,
    23
  ],
  [
    10.9,
    12.4,
    14,
    15.8,
    18,
    20.5,
    23.3
  ],
  [
    11,
    12.5,
    14.1,
    16,
    18.2,
    20.7,
    23.6
  ],
  [
    11.1,
    12.6,
    14.3,
    16.2,
    18.4,
    20.9,
    23.9
  ],
  [
    11.2,
    12.7,
    14.4,
    16.3,
    18.6,
    21.2,
    24.2
  ],
  [
    11.3,
    12.8,
    14.5,
    16.5,
    18.8,
    21.4,
    24.5
  ],
  [
    11.4,
    12.9,
    14.7,
    16.7,
    19,
    21.7,
    24.8
  ],
  [
    11.5,
    13.1,
    14.8,
    16.8,
    19.2,
    21.9,
    25.1
  ],
  [
    11.6,
    13.2,
    15,
    17,
    19.4,
    22.2,
    25.4
  ],
  [
    11.7,
    13.3,
    15.1,
    17.2,
    19.6,
    22.4,
    25.7
  ],
  [
    11.8,
    13.4,
    15.2,
    17.3,
    19.8,
    22.7,
    26
  ],
  [
    11.9,
    13.5,
    15.4,
    17.5,
    20,
    22.9,
    26.3
  ],
  [
    12,
    13.6,
    15.5,
    17.7,
    20.2,
    23.2,
    26.6
  ],
  [
    12.1,
    13.7,
    15.6,
    17.8,
    20.4,
    23.4,
    26.9
  ],
  [
    12.2,
    13.8,
    15.8,
    18,
    20.6,
    23.7,
    27.2
  ],
  [
    12.3,
    14,
    15.9,
    18.2,
    20.8,
    23.9,
    27.6
  ],
  [
    12.4,
    14.1,
    16,
    18.3,
    21,
    24.2,
    27.9
  ],
  [
    12.7,
    14.4,
    16.3,
    18.5,
    21.1,
    24.2,
    27.8
  ],
  [
    12.8,
    14.5,
    16.4,
    18.7,
    21.3,
    24.4,
    28.1
  ],
  [
    13,
    14.6,
    16.6,
    18.9,
    21.5,
    24.7,
    28.4
  ],
  [
    13.1,
    14.8,
    16.7,
    19,
    21.7,
    24.9,
    28.8
  ],
  [
    13.2,
    14.9,
    16.9,
    19.2,
    22,
    25.2,
    29.1
  ],
  [
    13.3,
    15,
    17,
    19.4,
    22.2,
    25.5,
    29.4
  ],
  [
    13.4,
    15.2,
    17.2,
    19.6,
    22.4,
    25.7,
    29.8
  ],
  [
    13.6,
    15.3,
    17.4,
    19.8,
    22.6,
    26,
    30.1
  ],
  [
    13.7,
    15.4,
    17.5,
    19.9,
    22.8,
    26.3,
    30.4
  ],
  [
    13.8,
    15.6,
    17.7,
    20.1,
    23.1,
    26.6,
    30.8
  ],
  [
    13.9,
    15.7,
    17.8,
    20.3,
    23.3,
    26.8,
    31.2
  ],
  [
    14.1,
    15.9,
    18,
    20.5,
    23.5,
    27.1,
    31.5
  ],
  [
    14.5,
    16.3,
    18.5,
    21.1,
    24.2,
    28,
    32.6
  ],
  [
    14.9,
    16.8,
    19,
    21.7,
    24.9,
    28.9,
    33.7
  ]
]
const girl_h = [
  [
    43.6,
    45.4,
    47.3,
    49.1,
    51,
    52.9,
    54.7
  ],
  [
    47.8,
    49.8,
    51.7,
    53.7,
    55.6,
    57.6,
    59.5
  ],
  [
    51,
    53,
    55,
    57.1,
    59.1,
    61.1,
    63.2
  ],
  [
    53.5,
    55.6,
    57.7,
    59.8,
    61.9,
    64,
    66.1
  ],
  [
    55.6,
    57.8,
    59.9,
    62.1,
    64.3,
    66.4,
    68.6
  ],
  [
    57.4,
    59.6,
    61.8,
    64,
    66.2,
    68.5,
    70.7
  ],
  [
    58.9,
    61.2,
    63.5,
    65.7,
    68,
    70.3,
    72.5
  ],
  [
    60.3,
    62.7,
    65,
    67.3,
    69.6,
    71.9,
    74.2
  ],
  [
    61.7,
    64,
    66.4,
    68.7,
    71.1,
    73.5,
    75.8
  ],
  [
    62.9,
    65.3,
    67.7,
    70.1,
    72.6,
    75,
    77.4
  ],
  [
    64.1,
    66.5,
    69,
    71.5,
    73.9,
    76.4,
    78.9
  ],
  [
    65.2,
    67.7,
    70.3,
    72.8,
    75.3,
    77.8,
    80.3
  ],
  [
    66.3,
    68.9,
    71.4,
    74,
    76.6,
    79.2,
    81.7
  ],
  [
    67.3,
    70,
    72.6,
    75.2,
    77.8,
    80.5,
    83.1
  ],
  [
    68.3,
    71,
    73.7,
    76.4,
    79.1,
    81.7,
    84.4
  ],
  [
    69.3,
    72,
    74.8,
    77.5,
    80.2,
    83,
    85.7
  ],
  [
    70.2,
    73,
    75.8,
    78.6,
    81.4,
    84.2,
    87
  ],
  [
    71.1,
    74,
    76.8,
    79.7,
    82.5,
    85.4,
    88.2
  ],
  [
    72,
    74.9,
    77.8,
    80.7,
    83.6,
    86.5,
    89.4
  ],
  [
    72.8,
    75.8,
    78.8,
    81.7,
    84.7,
    87.6,
    90.6
  ],
  [
    73.7,
    76.7,
    79.7,
    82.7,
    85.7,
    88.7,
    91.7
  ],
  [
    74.5,
    77.5,
    80.6,
    83.7,
    86.7,
    89.8,
    92.9
  ],
  [
    75.2,
    78.4,
    81.5,
    84.6,
    87.7,
    90.8,
    94
  ],
  [
    76,
    79.2,
    82.3,
    85.5,
    88.7,
    91.9,
    95
  ],
  [
    76.7,
    80,
    83.2,
    86.4,
    89.6,
    92.9,
    96.1
  ],
  [
    76.8,
    80,
    83.3,
    86.6,
    89.9,
    93.1,
    96.4
  ],
  [
    77.5,
    80.8,
    84.1,
    87.4,
    90.8,
    94.1,
    97.4
  ],
  [
    78.1,
    81.5,
    84.9,
    88.3,
    91.7,
    95,
    98.4
  ],
  [
    78.8,
    82.2,
    85.7,
    89.1,
    92.5,
    96,
    99.4
  ],
  [
    79.5,
    82.9,
    86.4,
    89.9,
    93.4,
    96.9,
    100.3
  ],
  [
    80.1,
    83.6,
    87.1,
    90.7,
    94.2,
    97.7,
    101.3
  ],
  [
    80.7,
    84.3,
    87.9,
    91.4,
    95,
    98.6,
    102.2
  ],
  [
    81.3,
    84.9,
    88.6,
    92.2,
    95.8,
    99.4,
    103.1
  ],
  [
    81.9,
    85.6,
    89.3,
    92.9,
    96.6,
    100.3,
    103.9
  ],
  [
    82.5,
    86.2,
    89.9,
    93.6,
    97.4,
    101.1,
    104.8
  ],
  [
    83.1,
    86.8,
    90.6,
    94.4,
    98.1,
    101.9,
    105.6
  ],
  [
    83.6,
    87.4,
    91.2,
    95.1,
    98.9,
    102.7,
    106.5
  ],
  [
    84.2,
    88,
    91.9,
    95.7,
    99.6,
    103.4,
    107.3
  ],
  [
    84.7,
    88.6,
    92.5,
    96.4,
    100.3,
    104.2,
    108.1
  ],
  [
    85.3,
    89.2,
    93.1,
    97.1,
    101,
    105,
    108.9
  ],
  [
    85.8,
    89.8,
    93.8,
    97.7,
    101.7,
    105.7,
    109.7
  ],
  [
    86.3,
    90.4,
    94.4,
    98.4,
    102.4,
    106.4,
    110.5
  ],
  [
    86.8,
    90.9,
    95,
    99,
    103.1,
    107.2,
    111.2
  ],
  [
    87.4,
    91.5,
    95.6,
    99.7,
    103.8,
    107.9,
    112
  ],
  [
    87.9,
    92,
    96.2,
    100.3,
    104.5,
    108.6,
    112.7
  ],
  [
    88.4,
    92.5,
    96.7,
    100.9,
    105.1,
    109.3,
    113.5
  ],
  [
    88.9,
    93.1,
    97.3,
    101.5,
    105.8,
    110,
    114.2
  ],
  [
    89.3,
    93.6,
    97.9,
    102.1,
    106.4,
    110.7,
    114.9
  ],
  [
    89.8,
    94.1,
    98.4,
    102.7,
    107,
    111.3,
    115.7
  ],
  [
    90.3,
    94.6,
    99,
    103.3,
    107.7,
    112,
    116.4
  ],
  [
    90.7,
    95.1,
    99.5,
    103.9,
    108.3,
    112.7,
    117.1
  ],
  [
    91.2,
    95.6,
    100.1,
    104.5,
    108.9,
    113.3,
    117.7
  ],
  [
    91.7,
    96.1,
    100.6,
    105,
    109.5,
    114,
    118.4
  ],
  [
    92.1,
    96.6,
    101.1,
    105.6,
    110.1,
    114.6,
    119.1
  ],
  [
    92.6,
    97.1,
    101.6,
    106.2,
    110.7,
    115.2,
    119.8
  ],
  [
    93,
    97.6,
    102.2,
    106.7,
    111.3,
    115.9,
    120.4
  ],
  [
    93.4,
    98.1,
    102.7,
    107.3,
    111.9,
    116.5,
    121.1
  ],
  [
    93.9,
    98.5,
    103.2,
    107.8,
    112.5,
    117.1,
    121.8
  ],
  [
    94.3,
    99,
    103.7,
    108.4,
    113,
    117.7,
    122.4
  ],
  [
    94.7,
    99.5,
    104.2,
    108.9,
    113.6,
    118.3,
    123.1
  ],
  [
    95.2,
    99.9,
    104.7,
    109.4,
    114.2,
    118.9,
    123.7
  ],
  [
    95.3,
    100.1,
    104.8,
    109.6,
    114.4,
    119.1,
    123.9
  ],
  [
    95.7,
    100.5,
    105.3,
    110.1,
    114.9,
    119.7,
    124.5
  ],
  [
    96.1,
    101,
    105.8,
    110.6,
    115.5,
    120.3,
    125.2
  ],
  [
    96.5,
    101.4,
    106.3,
    111.2,
    116,
    120.9,
    125.8
  ],
  [
    97,
    101.9,
    106.8,
    111.7,
    116.6,
    121.5,
    126.4
  ],
  [
    97.4,
    102.3,
    107.2,
    112.2,
    117.1,
    122,
    127
  ],
  [
    97.8,
    102.7,
    107.7,
    112.7,
    117.6,
    122.6,
    127.6
  ],
  [
    98.2,
    103.2,
    108.2,
    113.2,
    118.2,
    123.2,
    128.2
  ],
  [
    98.6,
    103.6,
    108.6,
    113.7,
    118.7,
    123.7,
    128.8
  ],
  [
    99,
    104,
    109.1,
    114.2,
    119.2,
    124.3,
    129.3
  ],
  [
    99.4,
    104.5,
    109.6,
    114.6,
    119.7,
    124.8,
    129.9
  ],
  [
    99.8,
    104.9,
    110,
    115.1,
    120.2,
    125.4,
    130.5
  ],
  [
    100.9,
    106.1,
    111.3,
    116.6,
    121.8,
    127,
    132.2
  ],
  [
    102.1,
    107.4,
    112.7,
    118,
    123.3,
    128.6,
    133.9
  ]
]
const girl_w = [
  [
    2,
    2.4,
    2.8,
    3.2,
    3.7,
    4.2,
    4.8
  ],
  [
    2.7,
    3.2,
    3.6,
    4.2,
    4.8,
    5.5,
    6.2
  ],
  [
    3.4,
    3.9,
    4.5,
    5.1,
    5.8,
    6.6,
    7.5
  ],
  [
    4,
    4.5,
    5.2,
    5.8,
    6.6,
    7.5,
    8.5
  ],
  [
    4.4,
    5,
    5.7,
    6.4,
    7.3,
    8.2,
    9.3
  ],
  [
    4.8,
    5.4,
    6.1,
    6.9,
    7.8,
    8.8,
    10
  ],
  [
    5.1,
    5.7,
    6.5,
    7.3,
    8.2,
    9.3,
    10.6
  ],
  [
    5.3,
    6,
    6.8,
    7.6,
    8.6,
    9.8,
    11.1
  ],
  [
    5.6,
    6.3,
    7,
    7.9,
    9,
    10.2,
    11.6
  ],
  [
    5.8,
    6.5,
    7.3,
    8.2,
    9.3,
    10.5,
    12
  ],
  [
    5.9,
    6.7,
    7.5,
    8.5,
    9.6,
    10.9,
    12.4
  ],
  [
    6.1,
    6.9,
    7.7,
    8.7,
    9.9,
    11.2,
    12.8
  ],
  [
    6.3,
    7,
    7.9,
    8.9,
    10.1,
    11.5,
    13.1
  ],
  [
    6.4,
    7.2,
    8.1,
    9.2,
    10.4,
    11.8,
    13.5
  ],
  [
    6.6,
    7.4,
    8.3,
    9.4,
    10.6,
    12.1,
    13.8
  ],
  [
    6.7,
    7.6,
    8.5,
    9.6,
    10.9,
    12.4,
    14.1
  ],
  [
    6.9,
    7.7,
    8.7,
    9.8,
    11.1,
    12.6,
    14.5
  ],
  [
    7,
    7.9,
    8.9,
    10,
    11.4,
    12.9,
    14.8
  ],
  [
    7.2,
    8.1,
    9.1,
    10.2,
    11.6,
    13.2,
    15.1
  ],
  [
    7.3,
    8.2,
    9.2,
    10.4,
    11.8,
    13.5,
    15.4
  ],
  [
    7.5,
    8.4,
    9.4,
    10.6,
    12.1,
    13.7,
    15.7
  ],
  [
    7.6,
    8.6,
    9.6,
    10.9,
    12.3,
    14,
    16
  ],
  [
    7.8,
    8.7,
    9.8,
    11.1,
    12.5,
    14.3,
    16.4
  ],
  [
    7.9,
    8.9,
    10,
    11.3,
    12.8,
    14.6,
    16.7
  ],
  [
    8.1,
    9,
    10.2,
    11.5,
    13,
    14.8,
    17
  ],
  [
    8.2,
    9.2,
    10.3,
    11.7,
    13.3,
    15.1,
    17.3
  ],
  [
    8.4,
    9.4,
    10.5,
    11.9,
    13.5,
    15.4,
    17.7
  ],
  [
    8.5,
    9.5,
    10.7,
    12.1,
    13.7,
    15.7,
    18
  ],
  [
    8.6,
    9.7,
    10.9,
    12.3,
    14,
    16,
    18.3
  ],
  [
    8.8,
    9.8,
    11.1,
    12.5,
    14.2,
    16.2,
    18.7
  ],
  [
    8.9,
    10,
    11.2,
    12.7,
    14.4,
    16.5,
    19
  ],
  [
    9,
    10.1,
    11.4,
    12.9,
    14.7,
    16.8,
    19.3
  ],
  [
    9.1,
    10.3,
    11.6,
    13.1,
    14.9,
    17.1,
    19.6
  ],
  [
    9.3,
    10.4,
    11.7,
    13.3,
    15.1,
    17.3,
    20
  ],
  [
    9.4,
    10.5,
    11.9,
    13.5,
    15.4,
    17.6,
    20.3
  ],
  [
    9.5,
    10.7,
    12,
    13.7,
    15.6,
    17.9,
    20.6
  ],
  [
    9.6,
    10.8,
    12.2,
    13.9,
    15.8,
    18.1,
    20.9
  ],
  [
    9.7,
    10.9,
    12.4,
    14,
    16,
    18.4,
    21.3
  ],
  [
    9.8,
    11.1,
    12.5,
    14.2,
    16.3,
    18.7,
    21.6
  ],
  [
    9.9,
    11.2,
    12.7,
    14.4,
    16.5,
    19,
    22
  ],
  [
    10.1,
    11.3,
    12.8,
    14.6,
    16.7,
    19.2,
    22.3
  ],
  [
    10.2,
    11.5,
    13,
    14.8,
    16.9,
    19.5,
    22.7
  ],
  [
    10.3,
    11.6,
    13.1,
    15,
    17.2,
    19.8,
    23
  ],
  [
    10.4,
    11.7,
    13.3,
    15.2,
    17.4,
    20.1,
    23.4
  ],
  [
    10.5,
    11.8,
    13.4,
    15.3,
    17.6,
    20.4,
    23.7
  ],
  [
    10.6,
    12,
    13.6,
    15.5,
    17.8,
    20.7,
    24.1
  ],
  [
    10.7,
    12.1,
    13.7,
    15.7,
    18.1,
    20.9,
    24.5
  ],
  [
    10.8,
    12.2,
    13.9,
    15.9,
    18.3,
    21.2,
    24.8
  ],
  [
    10.9,
    12.3,
    14,
    16.1,
    18.5,
    21.5,
    25.2
  ],
  [
    11,
    12.4,
    14.2,
    16.3,
    18.8,
    21.8,
    25.5
  ],
  [
    11.1,
    12.6,
    14.3,
    16.4,
    19,
    22.1,
    25.9
  ],
  [
    11.2,
    12.7,
    14.5,
    16.6,
    19.2,
    22.4,
    26.3
  ],
  [
    11.3,
    12.8,
    14.6,
    16.8,
    19.4,
    22.6,
    26.6
  ],
  [
    11.4,
    12.9,
    14.8,
    17,
    19.7,
    22.9,
    27
  ],
  [
    11.5,
    13,
    14.9,
    17.2,
    19.9,
    23.2,
    27.4
  ],
  [
    11.6,
    13.2,
    15.1,
    17.3,
    20.1,
    23.5,
    27.7
  ],
  [
    11.7,
    13.3,
    15.2,
    17.5,
    20.3,
    23.8,
    28.1
  ],
  [
    11.8,
    13.4,
    15.3,
    17.7,
    20.6,
    24.1,
    28.5
  ],
  [
    11.9,
    13.5,
    15.5,
    17.9,
    20.8,
    24.4,
    28.8
  ],
  [
    12,
    13.6,
    15.6,
    18,
    21,
    24.6,
    29.2
  ],
  [
    12.1,
    13.7,
    15.8,
    18.2,
    21.2,
    24.9,
    29.5
  ],
  [
    12.4,
    14,
    15.9,
    18.3,
    21.2,
    24.8,
    29.5
  ],
  [
    12.5,
    14.1,
    16,
    18.4,
    21.4,
    25.1,
    29.8
  ],
  [
    12.6,
    14.2,
    16.2,
    18.6,
    21.6,
    25.4,
    30.2
  ],
  [
    12.7,
    14.3,
    16.3,
    18.8,
    21.8,
    25.6,
    30.5
  ],
  [
    12.8,
    14.4,
    16.5,
    19,
    22,
    25.9,
    30.9
  ],
  [
    12.9,
    14.6,
    16.6,
    19.1,
    22.2,
    26.2,
    31.3
  ],
  [
    13,
    14.7,
    16.8,
    19.3,
    22.5,
    26.5,
    31.6
  ],
  [
    13.1,
    14.8,
    16.9,
    19.5,
    22.7,
    26.7,
    32
  ],
  [
    13.2,
    14.9,
    17,
    19.6,
    22.9,
    27,
    32.3
  ],
  [
    13.3,
    15,
    17.2,
    19.8,
    23.1,
    27.3,
    32.7
  ],
  [
    13.4,
    15.2,
    17.3,
    20,
    23.3,
    27.6,
    33.1
  ],
  [
    13.5,
    15.3,
    17.5,
    20.2,
    23.5,
    27.8,
    33.4
  ],
  [
    13.8,
    15.6,
    17.9,
    20.7,
    24.2,
    28.7,
    34.6
  ],
  [
    14.1,
    16,
    18.3,
    21.2,
    24.9,
    29.6,
    35.8
  ]
]
export { boy_h, boy_w, girl_h, girl_w }