<template>
  <view class="pages">
    <u-navbar :border-bottom="false" immersive :background="{ backgroundColor: 'rgba(255,255,255,0))' }" title="">
    </u-navbar>
    <swiper class="swiper-wrap" :indicator-dots="swiperOption.indicatorDots" :autoplay="swiperOption.autoplay"
      :interval="swiperOption.interval" :duration="swiperOption.duration">
      <swiper-item v-for="(item, index) in swiperList" :key="index">
        <view class="swiper-item">
          <image :src="item" mode="aspectFill" />
        </view>
      </swiper-item>
    </swiper>
    <view class="body">
      <view class="info-top">
        <view class="left" @click="intoDetail(info.id)">
          <view class="name">
            <view class="namess">{{ info.name }} </view>
          </view>
          <view class="tag">
            <text class="text">公立</text>
          </view>
          <view class="time">
            {{ info.servicetime }}
          </view>
        </view>
        <image class="phone" @click="callPhone" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/phone.png">
        </image>
      </view>
      <view class="address" @click="goDaohang">
        <image class="back" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/mapp.png">
        </image>
        <view class="add-content">
          <view class="lefec">
            <view class="t1">{{ info.address }}</view>
            <template v-if="!currentLocal.noLocation">
              <text class="distance" v-if="info.distance >= 1">距你{{ (info.distance * 1).toFixed(2)
                }}km</text>
              <text v-else class="distance">距你{{ info.distance * 1000 }}m</text>
            </template>
          </view>
          <view class="rsec">
            <image class="right" src="https://obs.tuoyupt.com/yjy_mini/front_img/school/right.png">
            </image>
          </view>
        </view>
      </view>
      <view class="titiii">
        服务类型
      </view>
      <view class="infoss" @click="goBook">
        <view class="row">
          <view class="t1">
            0-6岁儿童健康管理项目
          </view>
          <view class="t3">
            免费
          </view>
        </view>
        <view class="t2">可预约</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { useLocalStore } from "@/stores/useLocalStore.js";
import { onLoad } from "@dcloudio/uni-app";
import request from "@/request.js";
const currentLocal = useLocalStore();
let id = '';
const info = ref({
  name: "西安市莲湖区卫生服务中心",
  distance: 100,
  address: "莲湖区丰镐路2号民航小区",
  lng: '',
  lat: '',
  servicetime: "",
  contact: ""
})
const swiperList = ref([])
let swiperOption = ref({
  indicatorDots: false,
  autoplay: false,
  interval: 2000,
  duration: 500,
});
onLoad((option) => {
  id = option.id;
  getData();
})
function getData() {
  uni.showLoading()
  request({
    url: "/api/healthservices/hospitalDetail",
    data: {
      id,
      lng: currentLocal.longitude,
      lat: currentLocal.latitude
    }
  }).then(res => {
    info.value = res;
    try {
      let imgs = JSON.parse(res.images);
      if (imgs.length) {
        swiperList.value = imgs;
      } else {
        swiperList.value.push("https://obs.tuoyupt.com/yjy_mini/front_img/physical/yiyuan.png")
      }
    } catch (error) {
      swiperList.value.push("https://obs.tuoyupt.com/yjy_mini/front_img/physical/yiyuan.png")
    }
    uni.hideLoading();
  })
}
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: info.value.contact,
  });
}
function goDaohang() {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng),
    name: info.value.name,
    address: info.value.address,
    success: function () {
      console.log('success');
    }
  });
}
function goBook() {
  uni.showLoading();
  request({
    url: "/api/healthservices/protection/getTimeList",
    data: {
      hospital_id: info.value.id,
      week: 0
    }
  }).then(res => {
    uni.hideLoading();
    uni.setStorageSync("protection_hospital_info", info.value);
    uni.navigateTo({
      url: `./book`,
    });
  }).catch(() => {
    uni.hideLoading();
  })
}
</script>
<style lang="scss" scoped>
.pages {
  width: 100vw;

  .swiper-wrap {
    width: 750rpx;
    height: 420rpx;

    .swiper-item {
      display: block;
      height: 420rpx;
      text-align: center;

      image {
        height: 100%;
        display: block;
        width: 100%;
      }
    }
  }

  .body {
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    margin-top: -40rpx;
    padding: 40rpx 30rpx;
    position: relative;
    z-index: 2;

    .info-top {
      display: flex;
      flex-direction: row;
      padding-top: 40rpx;

      .left {
        flex: 1;

        .name {
          width: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: row;

          .namess {
            font-size: 40rpx;
            font-weight: 500;
            color: #262937;
            max-width: 540rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }

        .rate {
          display: flex;
          flex-direction: row;

          .rtt {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .tag {
          display: flex;
          flex-wrap: wrap;
          overflow: hidden;
          margin-top: 14rpx;
          margin-bottom: 14rpx;
          min-height: 40rpx;

          .text {
            font-size: 20rpx;
            color: #262937;
            padding: 0 16rpx;
            margin-right: 8rpx;
            margin-bottom: 8rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            height: 40rpx;
            display: flex;
            align-items: center;
          }
        }

        .time {
          font-size: 28rpx;
          font-weight: 400;
          color: #7A7E8F;
        }
      }

      .phone {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
    }

    .address {
      width: 690rpx;
      height: 132rpx;
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      margin-top: 40rpx;

      .back {
        position: absolute;
        width: 690rpx;
        height: 132rpx;
        left: 0;
        top: 0;
        z-index: 1;
      }

      .add-content {
        width: 690rpx;
        height: 132rpx;
        display: flex;
        flex-direction: row;
        position: relative;
        z-index: 2;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;

        .lefec {
          .t1 {
            font-size: 28rpx;
            font-weight: 500;
            color: #262937;
            line-height: 40rpx;
          }

          .distance {
            font-size: 24rpx;
            font-weight: 400;
            color: #787C8D;
          }
        }

        .rsec {
          width: 40rpx;
          height: 40rpx;

          .right {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .titiii {
      font-size: 32rpx;
      font-weight: 500;
      color: #292D3B;
      margin: 40rpx 0;
      font-weight: 600;
    }

    .infoss {
      width: 690rpx;
      height: 160rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      border: 1rpx solid #EEEEEE;
      padding: 30rpx;

      .row {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .t1 {
          font-size: 32rpx;
          font-weight: 600;
          color: #262937;
          line-height: 44rpx;
        }

        .t3 {
          font-size: 32rpx;
          font-weight: 500;
          color: #00CB46;
        }
      }


      .t2 {
        width: fit-content;
        padding: 0 12rpx;
        height: 40rpx;
        background: #E2F3FF;
        border-radius: 8rpx;
        font-size: 20rpx;
        font-weight: 400;
        color: #1975FF;
        line-height: 40rpx;
        text-align: center;
        margin-top: 16rpx;
      }
    }
  }
}
</style>