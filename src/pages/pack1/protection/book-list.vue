<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F1F5F8' }" title="历史预约">
    </u-navbar>
    <template v-if="list.length">
      <view class="item" v-for="item in list" :key="item.id" @click="goDeatil(item)">

        <view class="row">
          <view class="name">{{ item.bm_student.name }}</view>
          <view class="value" :class="['color' + item.status]">
            {{ item.status == 0 ? '待检查' : item.status == 1 ? '已检查' : item.status == 2 ? '已过期' : item.status == 3 ?
      '已取消' : '已签到'
            }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            预约门诊：
          </view>
          <view class="value">
            {{ item.hospital.name }}
          </view>
        </view>
        <view class="row">
          <view class="label">
            预约时间：
          </view>
          <view class="value">
            {{ item.order_time }} {{ item.order_period.order_period }}
          </view>
        </view>
      </view>
    </template>

    <u-empty v-else margin-top="400" text="暂无历史预约"
      src="https://obs.tuoyupt.com/yjy_mini/front_img/school/emttt.png"></u-empty>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onReachBottom, onShow } from '@dcloudio/uni-app';
import request from "@/request.js";
onShow(() => {
  getData();
})
let page = 1;
const list = ref([])
let hasMore = true;
function getData() {
  page = 1;
  uni.showLoading();
  request({
    goLogin: true,
    url: "/api/healthservices/protection/list",
    data: {
      per_page: 20,
      page: page,
    }
  }).then(res => {
    uni.hideLoading();
    if (res.length < 20) {
      hasMore = false;
    } else {
      hasMore = true;
    }
    list.value = res;
  })
}
onReachBottom(() => {
  if (hasMore) {
    page++;
    request({
      url: "/api/healthservices/protection/list",
      data: {
        per_page: 20,
        page: page,
      }
    }).then(res => {
      uni.hideLoading();
      if (res.length < 20) {
        hasMore = false;
      } else {
        hasMore = true;
      }
      list.value.push(...res);
    })
  }
})
function goDeatil(item) {
  uni.navigateTo({
    url: `./history?id=${item.id}`,
  });
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;

  .item {
    width: 690rpx;
    margin: 20rpx auto;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;


    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 10rpx;
      }

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .color0 {
        color: #1975FF;
      }

      .color1 {
        color: #00CB46;
      }

      .color2 {
        color: #ccc;
      }

      .color3 {
        color: ccc;
      }

      .color4 {
        color: #00CB46;
      }
    }
  }
}
</style>