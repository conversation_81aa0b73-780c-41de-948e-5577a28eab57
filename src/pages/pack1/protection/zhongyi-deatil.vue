<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ background: 'linear-gradient(180deg, #D5EDFF 0%, #F1F5F8 100%)' }"
      title="中医指导记录详情">
    </u-navbar>
    <view class="section">
      <view class="tops">
        <view class="ttt">
          {{ child_info.name }}
        </view>
        <view class="age">
          {{ calculateAge(info.visit_current, child_info.birth) }}
        </view>
      </view>
      <view class="hr"></view>
      <view class="row">
        <view class="label">
          随访日期：
        </view>
        <view class="value">
          {{ info.visit_current }}
        </view>
      </view>
      <view class="row">
        <view class="label">
          体检机构：
        </view>
        <view class="value">
          {{ info.hospital.name }}
        </view>
      </view>
    </view>
    <view class="section" id="section3">
      <view class="tops">
        <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/qta.png" class="icon">
        </image>
        <view class="tt">
          中医药健康服务管理
        </view>
      </view>
      <view class="item">
        <view class="r1">
          中医饮食调养指导：
        </view>
        <view class="r2 zhidao_zy">
          <div>1.养成良好的哺乳习惯，尽量延长夜间喂奶的间隔时间。</div>
          <div>2.养成良好的饮食习惯，避免偏食；节制零食，按时进食，提倡“三分饥”，防止乳食无度。</div>
          <div>3.食物宜细、软、烂、碎，而且应品种多样。</div>
          <div>4.严格控制冷饮，寒凉食物要适度</div>
        </view>
        <view class="r1">
          中医起居调摄指导：
        </view>
        <view class="r2 zhidao_zy">
          <div>1.保证充足的睡眠时间，逐渐养成夜间睡眠、白天活动的作息习惯。</div>
          <div>2.养成良好的小便习惯，适时把尿；培养每日定时大便的习惯。</div>
          <div>3.衣着要宽松，不可紧束而妨碍气血流通，影响骨骼生长发育。</div>
          <div>4.春季注意保暖，正确理解“春捂”；夏季纳凉要适度，避免直吹电风扇，空调温度不宜过低；秋季避过度，提倡“三分寒”，正确理解“秋凉”；冬季室内不宜过度密闭保暖，应适当通风，保持空气新鲜。</div>
          <div>5.经常到户外活动，多见风日，以增强体质。</div>
        </view>
        <view class="r1">
          传授摩腹、捏脊方法：
        </view>
        <view class="r2 zhidao_zy">
          <div v-if="info.type == 1 || info.type == 2">传授摩腹、捏脊方法</div>
          <div v-else-if="info.type == 3 || info.type == 4">传授按揉迎香穴、足三里穴方法</div>
          <div v-else>传授按揉四神聪穴方法</div>
        </view>
        <view class="r1">
          其他
        </view>
        <view class="r2 zhidao_zy">
          {{ info.qita }}
        </view>
      </view>
    </view>
    <view class="section">
      <view class="yisheng">
        <view class="yleft">
          <view class="yt1">
            随访医生签字
          </view>
          <view class="yt2">
            下次随访时间：{{ info.visit_next }}
          </view>
        </view>
        <view class="right">
          {{ info.visit_doctor }}
          <!-- <image class="rimg" src="https://obs.tuoyupt.com/miniprogram/changzhi/duihao.png"></image> -->
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, nextTick, computed } from "vue";
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
import dayjs from "dayjs";
let id = "";
let type = ref(1);
const child_info = ref({
  id: 0,
  name: '',
  birth: '',
  id_card: '',
});
function calculateAge(current, birth) {
  let birthday = dayjs(birth);
  let today = dayjs(current);
  let months = today.diff(birthday, 'month');
  if (months < 1) {
    return `${today.diff(birthday, 'day')}天`
  }
  if (months < 2) {
    return `满月`
  } else {
    birthday = birthday.year(today.year());
    birthday = birthday.month(today.month());
    let days = today.diff(birthday, 'day');
    if (days > 0) {
      return `${months}月${days}天`
    } else {
      return `${months - 1}月${Math.abs(days)}天`
    }
  }
}
onLoad((option) => {
  id = option.id;
  type.value = option.type;
  child_info.value = uni.getStorageSync("child_info")
  getData();
})
let month_arr = [
  {
    id: 1,
    name: '满月'
  },
  {
    id: 2,
    name: '3月龄'
  },
  {
    id: 3,
    name: '6月龄'
  },
  {
    id: 4,
    name: '8月龄'
  },
  {
    id: 5,
    name: '12月龄'
  },
  {
    id: 6,
    name: '18月龄'
  },
  {
    id: 7,
    name: '24月龄'
  },
  {
    id: 8,
    name: '30月龄'
  },
  {
    id: 9,
    name: '36月龄'
  }
]

function id2name(id, arr, key = 'name') {
  if (id === '' || (typeof id) == 'undefined') {
    return "-";
  }
  if (arr.length == 0) {
    return "-";
  }
  let newarr = arr.filter((e) => {
    return e.id == id;
  });
  if (newarr.length) {
    return newarr[0][key];
  } else {
    return "-"
  }
}
const info = ref({
  hospital: {
    name: ""
  },
  type: 1,
  qita: '',
  visit_current: '',
  visit_next: '',
  visit_next_address: '',
  visit_doctor: '',
});
function getData() {
  uni.showLoading();
  request({
    url: "/api/parent/protectionuser/zhongyi",
    data: {
      id: id,
    }
  }).then(async res => {
    uni.hideLoading();
    info.value = res;
  })
}

</script>
<style lang="scss" scoped>
.zhidao_zy {
  line-height: 44rpx;
  padding-top: 10rpx;
}

.tizhi {
  display: flex;
  padding-top: 20rpx;

  p {
    width: 50%;
    text-align: center;
    line-height: 40rpx;
  }
}

.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;
  padding-bottom: 30rpx;

  .top {
    width: 100vw;
    height: 206rpx;
    padding: 24rpx 30rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .t1 {
      font-weight: 600;
      font-size: 52rpx;
      color: #000000;
      line-height: 74rpx;
      margin-bottom: 20rpx;
    }

    .t2 {
      font-weight: 400;
      font-size: 26rpx;
      color: #7A7E8E;
      line-height: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn {
      width: 200rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #1975FF;
      line-height: 36rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }

  .tabs {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10rpx;
    padding: 0 30rpx;

    .tab {
      width: 210rpx;
      height: 64rpx;
      background: rgba(216, 216, 216, 0);
      border-radius: 8rpx;
      border: 2rpx solid #D9D9D9;
      font-weight: 500;
      font-size: 28rpx;
      color: #63676F;
      line-height: 64rpx;
      text-align: center;
      font-style: normal;
    }

    .active {
      background: #FFFFFF;
      color: #63676F;
    }
  }

  .sectionopen {
    transition: height 0.3s ease-in-out;
    will-change: height;
    overflow: hidden;
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;
    padding: 10rpx 30rpx 30rpx 30rpx;
    transition: height 0.3s ease-in-out;
    will-change: height;

    .t0 {
      height: 50rpx;
      font-weight: 600;
      font-size: 36rpx;
      color: #292D3B;
      margin-bottom: 20rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 10rpx;
      }

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .color0 {
        color: #1975FF;
      }

      .color1 {
        color: #00CB46;
      }
    }

    .hr {
      width: 100%;
      background: #eee;
      height: 1rpx;
      margin: 30rpx 0;
    }

    .infos {
      .row {
        display: flex;
        flex-direction: row;
        margin-top: 24rpx;

        .label {
          width: 130rpx;
          height: 36rpx;
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8E;
          line-height: 36rpx;
        }

        .value {
          flex: 1;
          font-size: 26rpx;
          text-align: right;
          font-weight: 400;
          color: #3D414E;
          line-height: 36rpx;
        }
      }
    }

    .yisheng {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-top: 20rpx;

      .yleft {
        .yt1 {
          font-weight: 600;
          font-size: 32rpx;
          color: #3D414E;
          line-height: 44rpx;
        }

        .yt2 {
          font-weight: 400;
          font-size: 26rpx;
          color: #797D8E;
          line-height: 36rpx;
          margin-top: 4rpx;
        }
      }

      .right {
        width: 167rpx;
        height: 88rpx;

        .rimg {
          width: 167rpx;
          height: 88rpx;
        }
      }
    }

    .zhidao {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #797D8E;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }

    .tops1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 10rpx;

      .tt {
        font-weight: 500;
        font-size: 36rpx;
        color: #3D414E;
        line-height: 50rpx;
      }

      .arrow {
        font-weight: 400;
        font-size: 24rpx;
        color: #ADAFBC;
        line-height: 36rpx;
      }
    }

    .tops {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 20rpx;
      margin-bottom: 30rpx;

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 12rpx;
      }

      .ttt {
        font-weight: 600;
        font-size: 40rpx;
        color: #000000;
        line-height: 56rpx;
      }

      .age {
        font-weight: 400;
        font-size: 26rpx;
        color: #797D8E;
        line-height: 36rpx;
        margin-left: 12rpx;
      }

      .tt {
        font-size: 32rpx;
        font-weight: 600;
        color: #282B39;
        flex: 1;
      }

      .arrow {
        font-weight: 400;
        font-size: 24rpx;
        color: #ADAFBC;
        line-height: 36rpx;
      }
    }

    .item {
      width: 100%;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #EEE;
      margin-top: 20rpx;

      .r1 {
        font-size: 32rpx;
        font-weight: 500;
        color: #282B39;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 12rpx 0;
      }

      .r2 {
        font-size: 28rpx;
        font-weight: 400;
        color: #797D8D;
      }
    }

    .tts {
      font-size: 32rpx;
      font-weight: 600;
      color: #3D414E;
    }

  }
}
</style>