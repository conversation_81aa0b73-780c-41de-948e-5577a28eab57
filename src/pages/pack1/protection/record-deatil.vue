<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ background: 'linear-gradient(180deg, #D5EDFF 0%, #F1F5F8 100%)' }"
      title="检查记录详情">
    </u-navbar>
    <view class="section">
      <view class="tops">
        <view class="ttt">
          {{ child_info.name }}
        </view>
        <view class="age">
          {{ calculateAge(info.visit_current, child_info.birth) }}
        </view>
      </view>
      <view class="hr"></view>
      <view class="row">
        <view class="label">
          随访日期：
        </view>
        <view class="value">
          {{ info.visit_current }}
        </view>
      </view>
      <view class="row">
        <view class="label">
          体检机构：
        </view>
        <view class="value">
          {{ info.hospital.name }}
        </view>
      </view>
    </view>
    <view class="section">
      <view class="tops1">
        <view class="tt">
          全部检查
        </view>
        <view class="arrow" @click="onClickShowAll">
          {{ showAll ? "收起全部" : "展开全部" }} <u-icon name="arrow-up"
            :style="{ transform: `rotate(${showAll ? 0 : 180}deg)` }"></u-icon>
        </view>
      </view>
      <view class="sectionopen" id="section1"
        :style="{ height: open1 ? height1 == 0 ? 'auto' : `${height1}px` : `80rpx` }">
        <view class="tops">
          <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/jca.png" class="icon">
          </image>
          <view class="tt">
            基础检查
          </view>
          <view class="arrow" @click="open1 = !open1" :style="{ transform: `rotate(${open1 ? 0 : 180}deg)` }">
            <u-icon name="arrow-up"></u-icon>
          </view>
        </view>
        <view class="item">
          <view class="r1">
            <view class="r1l">
              身高/cm
            </view>
            <view class="r1r">
              <view class="r1r1">评价：</view>
              <view class="r1r2">
                <view class="r1r22" :class="{ ac: info.height_level == 1 }">
                  上
                </view>
                <view class="r1r22" :class="{ ac: info.height_level == 2 }">
                  中
                </view>
                <view class="r1r22" :class="{ ac: info.height_level == 3 }">
                  下
                </view>
              </view>
            </view>
          </view>
          <view class="r2">
            {{ info.height ?? '-' }}
          </view>
        </view>
        <view class="item">
          <view class="r1">
            <view class="r1l">
              体重/kg
            </view>
            <view class="r1r">
              <view class="r1r1">评价：</view>
              <view class="r1r2">
                <view class="r1r22" :class="{ ac: info.weight_level == 1 }">
                  上
                </view>
                <view class="r1r22" :class="{ ac: info.weight_level == 2 }">
                  中
                </view>
                <view class="r1r22" :class="{ ac: info.weight_level == 3 }">
                  下
                </view>
              </view>
            </view>
          </view>
          <view class="r2">
            {{ info.weight ?? '-' }}
          </view>
        </view>
        <view class="item" v-if="type == 9">
          <view class="r1">
            <view class="r1l">
              身高/体重
            </view>
            <view class="r1r">
              <view class="r1r1">评价：</view>
              <view class="r1r2">
                <view class="r1r22" :class="{ ac: info.heightweight_level == 1 }">
                  上
                </view>
                <view class="r1r22" :class="{ ac: info.heightweight_level == 2 }">
                  中
                </view>
                <view class="r1r22" :class="{ ac: info.heightweight_level == 3 }">
                  下
                </view>
              </view>
            </view>
          </view>
          <view class="r2">
            {{ info.heightweight ?? '-' }}
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            头围
          </view>
          <view class="r2">
            {{ info.head ?? '-' }}cm
          </view>
        </view>
      </view>
      <view class="sectionopen" id="section2"
        :style="{ height: open2 ? height2 == 0 ? 'auto' : `${height2}px` : `80rpx` }">
        <view class="tops">
          <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/erkea.png" class="icon">
          </image>
          <view class="tt">
            体格检查
          </view>
          <view class="arrow" :style="{ transform: `rotate(${open2 ? 0 : 180}deg)` }" @click="open2 = !open2">
            <u-icon name="arrow-up"></u-icon>
          </view>
        </view>
        <view class="item" v-if="type == 9">
          <view class="r1">
            体格
          </view>
          <view class="r2">
            {{ id2name(info.tige, tige) }}
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            面色
          </view>
          <view class="r2">
            {{ id2name(info.face_color, face_color) }}
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            皮肤
          </view>
          <view class="r2">
            {{ id2name(info.skin, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type < 8">
          <view class="r1">
            前囟
          </view>
          <view class="r2">
            长{{ info.qianxin_chang }}cm * 宽{{ info.qianxin_kuan }}cm 状态：{{ id2name(info.qianxin, qianxin)
            }}
          </view>
        </view>
        <view class="item" v-if="type < 4">
          <view class="r1">
            颈部包块
          </view>
          <view class="r2">
            {{ id2name(info.jingbubaokuai, have_arr) }}
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            眼睛
          </view>
          <view class="r2">
            {{ id2name(info.eyes, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            耳外观
          </view>
          <view class="r2">
            {{ id2name(info.ear, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type == 3 || type == 9">
          <view class="r1">
            听力
          </view>
          <view class="r2">
            {{ id2name(info.hearing, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type < 5">
          <view class="r1">
            口腔
          </view>
          <view class="r2" v-if="type < 3">{{ id2name(info.kouqiang, normal_arr) }}</view>
          <view class="r2" v-else>
            出牙数：{{ info.tooth }}颗
          </view>
        </view>
        <view class="item">
          <view class="r1">
            胸部
          </view>
          <view class="r2">
            {{ id2name(info.xiongbu, normal_arr) }}
          </view>
        </view>
        <view class="item">
          <view class="r1">
            腹部
          </view>
          <view class="r2">
            {{ id2name(info.fubu, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type < 3">
          <view class="r1">
            脐部
          </view>
          <view class="r2" v-if="type == 1">
            {{ id2name(info.qibu, qibu) }}
          </view>
          <view class="r2" v-if="type == 2">
            {{ id2name(info.qibu, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type > 4 && type < 9">
          <view class="r1">
            步态
          </view>
          <view class="r2">
            {{ id2name(info.butai, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            四肢
          </view>
          <view class="r2">
            {{ id2name(info.sizhi, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type > 1 && type < 5">
          <view class="r1">
            可疑佝偻病症状
          </view>
          <view class="r2">
            {{ info.goulou_zhengzhuang.split(",").map(e => { return id2name(e, goulou_zhengzhuang) }).join(',') }}
          </view>
        </view>
        <view class="item" v-if="type > 1 && type < 8">
          <view class="r1">
            可疑佝偻病体征
          </view>
          <view class="r2">
            <text v-if="type == 2">{{ info.goulou_tizheng.split(",").map(e => {
              return id2name(e, goulou_tizheng1)
            }).join('，')
              }}</text>
            <text v-if="type > 2 && type < 5">{{ info.goulou_tizheng.split(",").map(e => {
              return id2name(e,
                goulou_tizheng2)
            }).join('，') }}</text>
            <text v-if="type > 4">{{ info.goulou_tizheng.split(",").map(e => {
              return id2name(e, goulou_tizheng3)
            }).join('，')
              }}</text>
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            肛门/外生殖器
          </view>
          <view class="r2">
            {{ id2name(info.gang_wai, normal_arr) }}
          </view>
        </view>
        <view class="item" v-if="type > 2">
          <view class="r1">
            血红蛋白值
          </view>
          <view class="r2">
            {{ info.xuehongdaibai ? `${info.xuehongdaibai}g/L` : "-" }}
          </view>
        </view>
      </view>
      <view id="section3" class="sectionopen"
        :style="{ height: open3 ? height3 == 0 ? 'auto' : `${height3}px` : `80rpx` }">
        <view class="tops">
          <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/qta.png" class="icon">
          </image>
          <view class="tt">
            其他检查
          </view>
          <view class="arrow" :style="{ transform: `rotate(${open3 ? 0 : 180}deg)` }" @click="open3 = !open3">
            <u-icon name="arrow-up"></u-icon>
          </view>
        </view>
        <view class="item" v-if="type < 9">
          <view class="r1">
            户外活动
          </view>
          <view class="r2">
            {{ info.huwai ? info.huwai : '-' }}小时/日
          </view>
        </view>
        <view class="item" v-if="type < 8">
          <view class="r1">
            服用维生素D
          </view>
          <view class="r2">
            {{ info.vd ? info.vd : '-' }}IU/日
          </view>
        </view>
        <view class="item" v-if="type > 1">
          <view class="r1">
            发育评估
          </view>
          <view class="r2">
            <text v-if="type == 2">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu2)
            }).join('，') }}</text>
            <text v-if="type == 3">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu3)
            }).join('，') }}</text>
            <text v-if="type == 4">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu4)
            }).join('，') }}</text>
            <text v-if="type == 5">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu5)
            }).join('，') }}</text>
            <text v-if="type == 6">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu6)
            }).join('，') }}</text>
            <text v-if="type == 7">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu7)
            }).join('，') }}</text>
            <text v-if="type == 8">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu8)
            }).join('，') }}</text>
            <text v-if="type == 9">{{ info.yundongfayu.split(",").map(e => {
              return id2name(e,
                yundongfayu9)
            }).join('，') }}</text>
          </view>
        </view>
        <view class="item" v-if="type < 8">
          <view class="r1">
            两次随访间患病情况
          </view>
          <view class="r2">
            {{ info.suifang ? id2name(info.suifang, have_arr) : '-' }}
            <view v-if="info.suifang == 2">
              <view>肺炎：{{ info.suifang_text[0] }}次</view>
              <view>腹泻：{{ info.suifang_text[1] }}次</view>
              <view>外伤：{{ info.suifang_text[2] }}次</view>
              <view>其他：{{ info.suifang_text[3] }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="section" id="section4" :style="{ height: open4 ? height4 == 0 ? 'auto' : `${height4}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          转诊建议
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open4 ? 0 : 180}deg)` }" @click="open4 = !open4">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <view class="item">
        <view class="r2">
          <view>{{ id2name(info.zhuanzhen, have_arr) }}</view>
          <view v-if="info.zhuanzhen == 2">原因：{{ info.zhuanzhen_text }}
          </view>
          <view v-if="info.zhuanzhen == 2">机构及科室：{{ info.jigoukeshi }}
          </view>
        </view>
      </view>
    </view>
    <view class="section" id="section5" :style="{ height: open5 ? height5 == 0 ? 'auto' : `${height5}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          指导
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open5 ? 0 : 180}deg)` }" @click="open5 = !open5">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <view class="zhidao" v-for="item in info.zhidao?.split(',')" :key="item">
        <image class="sw" src="https://obs.tuoyupt.com/miniprogram/changzhi/duihao.png"></image>
        {{ id2name(item, zhidao) }}
      </view>
    </view>
    <view class="section">
      <view class="yisheng">
        <view class="yleft">
          <view class="yt1">
            随访医生签字
          </view>
          <view class="yt2">
            本次随访时间：{{ info.visit_current }}
          </view>
        </view>
        <view class="right">
          {{ info.visit_doctor }}
          <!-- <image class="rimg" src="https://obs.tuoyupt.com/miniprogram/changzhi/duihao.png"></image> -->
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, nextTick, computed } from "vue";
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
import dayjs from "dayjs";
let id = "";
let type = ref(1);
const child_info = ref({
  id: 0,
  name: '',
  birth: '',
  id_card: '',
});
function calculateAge(current, birth) {
  let birthday = dayjs(birth);
  let today = dayjs(current);
  let months = today.diff(birthday, 'month');
  if (months < 1) {
    return `${today.diff(birthday, 'day')}天`
  }
  if (months < 2) {
    return `满月`
  } else {
    birthday = birthday.year(today.year());
    birthday = birthday.month(today.month());
    let days = today.diff(birthday, 'day');
    if (days > 0) {
      return `${months}月${days}天`
    } else {
      return `${months - 1}月${Math.abs(days)}天`
    }
  }
}
onLoad((option) => {
  id = option.id;
  type.value = option.type;
  child_info.value = uni.getStorageSync("child_info")
  getData();
})
let month_arr = [
  {
    id: 1,
    name: '满月'
  },
  {
    id: 2,
    name: '3月龄'
  },
  {
    id: 3,
    name: '6月龄'
  },
  {
    id: 4,
    name: '8月龄'
  },
  {
    id: 5,
    name: '12月龄'
  },
  {
    id: 6,
    name: '18月龄'
  },
  {
    id: 7,
    name: '24月龄'
  },
  {
    id: 8,
    name: '30月龄'
  },
  {
    id: 9,
    name: '36月龄'
  }
]

let mother_ill_history = [
  {
    id: '1',
    name: '无'
  },
  {
    id: '2',
    name: '糖尿病'
  },
  {
    id: '3',
    name: '妊娠期高血压'
  },
  {
    id: '4',
    name: '其他'
  }
]

let child_ill = [
  {
    id: '1',
    name: '未进行'
  },
  {
    id: '2',
    name: '检查均阴性'
  },
  {
    id: '3',
    name: '甲低'
  },
  {
    id: '4',
    name: '苯丙酮尿证'
  },
  {
    id: '5',
    name: '其他遗传代谢病'
  }
]

let yundongfayu2 = [
  {
    id: '1',
    name: '对很大声音没有反应'
  },
  {
    id: '2',
    name: '逗引时不发音或不会微笑'
  },
  {
    id: '3',
    name: '不注视人脸，不追视移动人和物品'
  },
  {
    id: '4',
    name: '俯卧时不会抬头'
  }
]

let yundongfayu3 = [
  {
    id: '1',
    name: '发音少，不会笑出声'
  },
  {
    id: '2',
    name: '不会伸手抓物'
  },
  {
    id: '3',
    name: '紧握拳分不开'
  },
  {
    id: '4',
    name: '不会扶坐'
  }
]


let yundongfayu4 = [
  {
    id: '1',
    name: '听到声音无应答'
  },
  {
    id: '2',
    name: '不会区分生人和熟人'
  },
  {
    id: '3',
    name: '双手间不会传递玩具'
  },
  {
    id: '4',
    name: '不会独坐'
  }
]

let yundongfayu5 = [
  {
    id: '1',
    name: '呼唤名字无反应'
  },
  {
    id: '2',
    name: '不会模仿“再见”或“欢迎”动作'
  },
  {
    id: '3',
    name: '不会用拇食指对捏小物品'
  },
  {
    id: '4',
    name: '不会扶物站立'
  }
]

let yundongfayu6 = [
  {
    id: '1',
    name: '不会有意识叫“爸爸”或“妈妈”'
  },
  {
    id: '2',
    name: '不会按要求指人或物'
  },
  {
    id: '3',
    name: '与人无目光交流'
  },
  {
    id: '4',
    name: '不会独走'
  }
]

let yundongfayu7 = [
  {
    id: '1',
    name: '不会说3个物品的名称'
  },
  {
    id: '2',
    name: '不会按吩咐做简单事情'
  },
  {
    id: '3',
    name: '不会用勺吃饭'
  },
  {
    id: '4',
    name: '不会扶栏上楼梯/台阶'
  }
]

let yundongfayu8 = [
  {
    id: '1',
    name: '不会说2-3个字的短语'
  },
  {
    id: '2',
    name: '兴趣单一，刻板'
  },
  {
    id: '3',
    name: '不会示意大小便'
  },
  {
    id: '4',
    name: '不会跑'
  }
]

let yundongfayu9 = [
  {
    id: '1',
    name: '不会说自己的名字'
  },
  {
    id: '2',
    name: '不会玩“拿木棍当马骑”等联想游戏'
  },
  {
    id: '3',
    name: '不会模仿画圈'
  },
  {
    id: '4',
    name: '不会双脚跳'
  }
]
let child_hear = [
  {
    id: '1',
    name: '通过'
  },
  {
    id: '2',
    name: '未通过'
  },
  {
    id: '3',
    name: '未筛查'
  },
  {
    id: '4',
    name: '不详'
  }
]

let qianxin = [
  {
    id: '1',
    name: '闭合',
  },
  {
    id: '2',
    name: '未闭'
  }
]

let qibu = [
  {
    id: '1',
    name: '未脱',
  },
  {
    id: '2',
    name: '脱落'
  },
  {
    id: '3',
    name: '脐部有渗出'
  },
  {
    id: '4',
    name: '其他'
  }
]

let zhidao = [
  {
    id: '1',
    name: '科学喂养',
  },
  {
    id: '2',
    name: '生长发育'
  },
  {
    id: '3',
    name: '疾病预防'
  },
  {
    id: '4',
    name: '预防伤害'
  },
  {
    id: '5',
    name: '口腔保健'
  },
  {
    id: '6',
    name: '其他'
  }
]

let goulou_zhengzhuang = [
  {
    id: '1',
    name: '无',
  },
  {
    id: '2',
    name: '夜惊'
  },
  {
    id: '3',
    name: '多汗'
  },
  {
    id: '4',
    name: '烦躁'
  },
]

let goulou_tizheng1 = [
  {
    id: '1',
    name: '无',
  },
  {
    id: '2',
    name: '颅骨软化'
  },
]

let goulou_tizheng2 = [
  {
    id: '1',
    name: '无',
  },
  {
    id: '2',
    name: '肋串珠'
  },
  {
    id: '3',
    name: '肋软骨沟'
  },
  {
    id: '4',
    name: '鸡胸'
  },
  {
    id: '5',
    name: '手足镯'
  },
  {
    id: '6',
    name: '颅骨软化'
  },
  {
    id: '7',
    name: '方颅'
  },
]

let goulou_tizheng3 = [
  {
    id: '1',
    name: '无',
  },
  {
    id: '2',
    name: '肋串珠'
  },
  {
    id: '3',
    name: '肋软骨沟'
  },
  {
    id: '4',
    name: '鸡胸'
  },
  {
    id: '5',
    name: '手足镯'
  },
  {
    id: '6',
    name: '"O"型腿'
  },
  {
    id: '7',
    name: '"X s"型腿'
  },
]

let have_arr = [
  {
    id: '1',
    name: '无'
  },
  {
    id: '2',
    name: '有'
  },
]

let normal_arr = [
  {
    id: '1',
    name: '未见异常'
  },
  {
    id: '2',
    name: '异常'
  },
]

let feed = [
  {
    id: '1',
    name: '纯母乳'
  },
  {
    id: '2',
    name: '混合'
  },
  {
    id: '3',
    name: '人工'
  },
]

let face_color = [
  {
    id: '1',
    name: '红润'
  },
  {
    id: '2',
    name: '黄染'
  },
  {
    id: '3',
    name: '其他'
  },
]

let tige = [
  {
    id: '1',
    name: '正常'
  },
  {
    id: '2',
    name: '低体重'
  },
  {
    id: '3',
    name: '消瘦'
  },
  {
    id: '4',
    name: '生长迟缓'
  },
  {
    id: '5',
    name: '超重'
  },
]

let huangdan = [
  {
    id: '1',
    name: '无'
  },
  {
    id: '2',
    name: '面部'
  },
  {
    id: '3',
    name: '躯干'
  },
  {
    id: '4',
    name: '四肢'
  },
  {
    id: '5',
    name: '手足'
  },
]

let dabian = [
  {
    id: '1',
    name: '糊状'
  },
  {
    id: '2',
    name: '稀'
  },
  {
    id: '3',
    name: '其他'
  },
]

let apgar_score = [
  {
    id: '1',
    name: '1min'
  },
  {
    id: '2',
    name: '5min'
  },
  {
    id: '3',
    name: '不详'
  },
]

let birth_history = [
  {
    id: '1',
    name: '顺产'
  },
  {
    id: '2',
    name: '胎头吸引'
  },
  {
    id: '3',
    name: '产钳'
  },
  {
    id: '4',
    name: '剖喜'
  },
  {
    id: '5',
    name: '双多胎'
  },
  {
    id: '6',
    name: '臀位'
  },
  {
    id: '7',
    name: '其他'
  }
]
function id2name(id, arr, key = 'name') {
  if (id === '' || (typeof id) == 'undefined') {
    return "-";
  }
  if (arr.length == 0) {
    return "-";
  }
  let newarr = arr.filter((e) => {
    return e.id == id;
  });
  if (newarr.length) {
    return newarr[0][key];
  } else {
    return "-"
  }
}
const info = ref({
  hospital: {
    name: ""
  },
  weight: '',
  height: '',
  height_level: '',
  weight_level: '',
  head: '',
  tige: '',
  face_color: '',
  skin: '',
  qianxin_chang: '',
  qianxin_kuan: '',
  qianxin: '',
  eyes: '',
  sizhi: '',
  ear: '',
  jingbubaokuai: '',
  huwai: '',
  vd: '',
  hearing: '',
  kouqiang: '',
  tooth: '',
  xiongbu: '',
  yundongfayu: "",
  suifang: '',
  suifang_text: ['', '', '', ''],
  fubu: '',
  butai: '',
  qibu: '',
  goulou_zhengzhuang: "",
  goulou_tizheng: "",
  gang_wai: '',
  zhuanzhen: '',
  zhuanzhen_text: '',
  jigoukeshi: '',
  zhidao: "",
  qita: '',
  visit_current: '',
  visit_next: '',
  visit_next_address: '',
  visit_doctor: '',
});
const open1 = ref(true);
const height1 = ref(0);
const open2 = ref(true);
const height2 = ref(0);
const open3 = ref(true);
const height3 = ref(0);
const open4 = ref(true);
const height4 = ref(0);
const open5 = ref(true);
const height5 = ref(0);
const showAll = computed(() => {
  if (open1.value && open2.value && open3.value) {
    return true
  }
  return false;
});
function onClickShowAll() {
  if (showAll.value) {
    open1.value = false;
    open2.value = false;
    open3.value = false;
  } else {
    open1.value = true;
    open2.value = true;
    open3.value = true;
  }
}
function getData() {
  uni.showLoading();
  request({
    url: "/api/parent/protectionuser/health",
    data: {
      student_id: id,
      type: type.value
    }
  }).then(async res => {
    let infos = {};
    for (const key in res) {
      if (Object.prototype.hasOwnProperty.call(res, key)) {
        const element = res[key];
        if (key == 'zhuanzhen' && element != 1) {
          infos[key] = '2';
          infos[key + '_text'] = element;
        } else if (key == 'suifang' && (Number.isNaN(element * 1))) {
          infos[key] = '2';
          infos[key + '_text'] = element.replace('2:', '').split(',');
        } else {
          if (element == 'null') {
            infos[key] = '';
          } else {
            infos[key] = element;
          }
        }
      }
    }
    uni.hideLoading();
    info.value = infos;
    await nextTick();
    uni.createSelectorQuery().select('#section1').boundingClientRect((rect) => {
      height1.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section2').boundingClientRect((rect) => {
      height2.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section3').boundingClientRect((rect) => {
      height3.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section4').boundingClientRect((rect) => {
      height4.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section5').boundingClientRect((rect) => {
      height5.value = rect.height;
    }).exec();
  })
}

</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;
  padding-bottom: 30rpx;

  .top {
    width: 100vw;
    height: 206rpx;
    padding: 24rpx 30rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .t1 {
      font-weight: 600;
      font-size: 52rpx;
      color: #000000;
      line-height: 74rpx;
      margin-bottom: 20rpx;
    }

    .t2 {
      font-weight: 400;
      font-size: 26rpx;
      color: #7A7E8E;
      line-height: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn {
      width: 200rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #1975FF;
      line-height: 36rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }

  .tabs {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10rpx;
    padding: 0 30rpx;

    .tab {
      width: 210rpx;
      height: 64rpx;
      background: rgba(216, 216, 216, 0);
      border-radius: 8rpx;
      border: 2rpx solid #D9D9D9;
      font-weight: 500;
      font-size: 28rpx;
      color: #63676F;
      line-height: 64rpx;
      text-align: center;
      font-style: normal;
    }

    .active {
      background: #FFFFFF;
      color: #63676F;
    }
  }

  .sectionopen {
    transition: height 0.3s ease-in-out;
    will-change: height;
    overflow: hidden;
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;
    padding: 10rpx 30rpx 30rpx 30rpx;
    transition: height 0.3s ease-in-out;
    will-change: height;

    .t0 {
      height: 50rpx;
      font-weight: 600;
      font-size: 36rpx;
      color: #292D3B;
      margin-bottom: 20rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 10rpx;
      }

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .color0 {
        color: #1975FF;
      }

      .color1 {
        color: #00CB46;
      }
    }

    .hr {
      width: 100%;
      background: #eee;
      height: 1rpx;
      margin: 30rpx 0;
    }

    .infos {
      .row {
        display: flex;
        flex-direction: row;
        margin-top: 24rpx;

        .label {
          width: 130rpx;
          height: 36rpx;
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8E;
          line-height: 36rpx;
        }

        .value {
          flex: 1;
          font-size: 26rpx;
          text-align: right;
          font-weight: 400;
          color: #3D414E;
          line-height: 36rpx;
        }
      }
    }

    .yisheng {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-top: 20rpx;

      .yleft {
        .yt1 {
          font-weight: 600;
          font-size: 32rpx;
          color: #3D414E;
          line-height: 44rpx;
        }

        .yt2 {
          font-weight: 400;
          font-size: 26rpx;
          color: #797D8E;
          line-height: 36rpx;
          margin-top: 4rpx;
        }
      }

      .right {
        width: 167rpx;
        height: 88rpx;

        .rimg {
          width: 167rpx;
          height: 88rpx;
        }
      }
    }

    .zhidao {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #797D8E;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }

    .tops1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 10rpx;

      .tt {
        font-weight: 500;
        font-size: 36rpx;
        color: #3D414E;
        line-height: 50rpx;
      }

      .arrow {
        font-weight: 400;
        font-size: 24rpx;
        color: #ADAFBC;
        line-height: 36rpx;
      }
    }

    .tops {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 20rpx;
      margin-bottom: 30rpx;

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 12rpx;
      }

      .ttt {
        font-weight: 600;
        font-size: 40rpx;
        color: #000000;
        line-height: 56rpx;
      }

      .age {
        font-weight: 400;
        font-size: 26rpx;
        color: #797D8E;
        line-height: 36rpx;
        margin-left: 12rpx;
      }

      .tt {
        font-size: 32rpx;
        font-weight: 600;
        color: #282B39;
        flex: 1;
      }

      .arrow {
        font-weight: 400;
        font-size: 24rpx;
        color: #ADAFBC;
        line-height: 36rpx;
      }
    }

    .item {
      width: 100%;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #EEE;
      margin-top: 20rpx;

      .r1 {
        font-size: 28rpx;
        font-weight: 500;
        color: #282B39;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .r1r {
          display: flex;
          flex-direction: row;
          align-items: center;

          .r1r1 {
            font-weight: 400;
            font-size: 24rpx;
            color: #797D8D;
            line-height: 34rpx;
          }

          .r1r2 {
            display: flex;
            flex-direction: row;
            align-items: center;

            .r1r22 {
              width: 48rpx;
              height: 48rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #282B39;
              line-height: 48rpx;
              text-align: center;
              margin-left: 12rpx;
            }

            .ac {
              background: rgba(25, 117, 255, 0);
              border-radius: 8rpx;
              border: 1rpx solid #1975FF;
              color: #1975FF;
              display: inline-block;
            }
          }
        }
      }

      .r2 {
        font-size: 28rpx;
        font-weight: 400;
        color: #797D8D;
      }
    }

    .tts {
      font-size: 32rpx;
      font-weight: 600;
      color: #3D414E;
    }

  }
}
</style>