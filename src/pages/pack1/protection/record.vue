<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ background: 'linear-gradient(180deg, #D5EDFF 0%, #F1F5F8 100%)' }"
      title="检查记录">
    </u-navbar>
    <template v-if="child_list.length">
      <view class="top">
        <view class="left">
          <view class="t1">
            {{ child_info.name }}
          </view>
          <view class="t2">
            出生日期： {{ child_info.birth }}
          </view>
        </view>
        <view class="btn" @click="selectShow = true">
          <image class="sw" src="https://obs.tuoyupt.com/miniprogram/changzhi/sw.svg"></image>
          <text>切换</text>
        </view>
      </view>
      <u-select @confirm="confirmSelect" value-name="id" label-name="name" v-model="selectShow"
        :list="child_list"></u-select>
      <u-tabs active-color="#FF7373" :list="tabList" :is-scroll="true" v-model="tab" @change="changTab"></u-tabs>
      <!-- <view class="tabs">
        <view class="tab" @click="changTab(0)" :class="{ active: tab == 0 }">
          新生儿访视记录
        </view>
        <view class="tab" @click="changTab(1)" :class="{ active: tab == 1 }">
          健康检查记录
        </view>
        <view class="tab" @click="changTab(2)" :class="{ active: tab == 2 }">
          生长发育曲线
        </view>
      </view> -->
      <template v-if="tab == 0">
        <template v-if="showPro">
          <view class="section" id="section1"
            :style="{ height: open1 ? height1 == 0 ? 'auto' : `${height1}px` : `100rpx` }">
            <view class="tops">
              <view class="tt">
                基础信息
              </view>
              <view class="arrow" :style="{ transform: `rotate(${open1 ? 0 : 180}deg)` }" @click="open1 = !open1">
                <u-icon name="arrow-up"></u-icon>
              </view>
            </view>
            <view class="row">
              <view class="label">
                身份证号：
              </view>
              <view class="value">
                {{ child_info.id_card }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                家庭住址：
              </view>
              <view class="value">
                {{ info.address }}
              </view>
            </view>
            <view class="hr"></view>
            <view class="tops">
              <view class="tt">
                父亲
              </view>
            </view>
            <view class="row">
              <view class="label">
                姓名：
              </view>
              <view class="value">
                {{ info.father_name }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                职业：
              </view>
              <view class="value">
                {{ info.father_zhiye ? info.father_zhiye : '-' }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                联系电话：
              </view>
              <view class="value">
                {{ info.father_phone ? info.father_phone : '-' }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                出生年月：
              </view>
              <view class="value">
                {{ info.father_birth ? info.father_birth : '-' }}
              </view>
            </view>
            <view class="hr"></view>
            <view class="tops">
              <view class="tt">
                母亲
              </view>
            </view>
            <view class="row">
              <view class="label">
                姓名：
              </view>
              <view class="value">
                {{ info.mother_name }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                职业：
              </view>
              <view class="value">
                {{ info.mother_zhiye ? info.mother_zhiye : '-' }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                联系电话：
              </view>
              <view class="value">
                {{ info.mother_phone }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                出生年月：
              </view>
              <view class="value">
                {{ info.mother_birth ? info.mother_birth : '-' }}
              </view>
            </view>
          </view>
          <view class="section" id="section2"
            :style="{ height: open2 ? height2 == 0 ? 'auto' : `${height2}px` : `100rpx` }">
            <view class="tops">
              <view class="tt">
                出生情况
              </view>
              <view class="arrow" :style="{ transform: `rotate(${open2 ? 0 : 180}deg)` }" @click="open2 = !open2">
                <u-icon name="arrow-up"></u-icon>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  出生孕周
                </view>
              </view>
              <view class="r2">
                {{ info.week ?? '-' }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  母亲妊娠期患病情况
                </view>
              </view>
              <view class="r2">
                {{info.mother_ill_history?.split(",").map(e => { return id2name(e, mother_ill_history) }).join('，')}}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  助产机构名称
                </view>
              </view>
              <view class="r2">
                {{ info.zhuchanjigou ?? '-' }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  出生情况
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.birth_history, birth_history) }}
                <text style="padding-left: 8px;" v-if="info.birth_history == 7">{{ info.birth_history_text
                }}</text>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  新生儿窒息
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.child_zhixi, have_arr) }}
                <text style="margin-left: 10px;">Apgar评分：{{ id2name(info.apgar_score, apgar_score) }}</text>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  是否有畸形
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.jixing, have_arr) }}
                <text style="padding-left: 8px;" v-if="info.jixing == 2">{{ info.jixing_text }}</text>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  新生儿听力筛查
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.child_hear, child_hear) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  新生儿疾病筛查
                </view>
              </view>
              <view class="r2">
                {{info.child_ill?.split(",").map(e => { return id2name(e, child_ill) }).join('，')}}
              </view>
            </view>

          </view>
          <view class="section" id="section3"
            :style="{ height: open3 ? height3 == 0 ? 'auto' : `${height3}px` : `100rpx` }">
            <view class="tops">
              <view class="tt">
                体格检查
              </view>
              <view class="arrow" :style="{ transform: `rotate(${open3 ? 0 : 180}deg)` }" @click="open3 = !open3">
                <u-icon name="arrow-up"></u-icon>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  新生儿出生体重
                </view>
              </view>
              <view class="r2">
                {{ info.birth_weight }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  目前体重
                </view>
              </view>
              <view class="r2">
                {{ info.current_weight }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  出生身长
                </view>
              </view>
              <view class="r2">
                {{ info.birth_height }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  喂养方式
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.feed, feed) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  吃奶量
                </view>
              </view>
              <view class="r2">
                {{ info.milk }}mL/次
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  吃奶次数
                </view>
              </view>
              <view class="r2">
                {{ info.milk_count }}次/日
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  呕吐
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.outu, have_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  大便
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.dabian, dabian) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  大便次数
                </view>
              </view>
              <view class="r2">
                {{ info.dabian_count }}次/日
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  体温
                </view>
              </view>
              <view class="r2">
                {{ info.temperature }}℃
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  心率
                </view>
              </view>
              <view class="r2">
                {{ info.xinlv }}次/分钟
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  呼吸频率
                </view>
              </view>
              <view class="r2">
                {{ info.huxipinlv }}次/分钟
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  面色
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.face_color, face_color) }}
                <text style="padding-left: 8px;" v-if="info.face_color == 3">{{ info.face_color_text }}</text>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  黄疸部位
                </view>
              </view>
              <view class="r2">
                {{info.huangdan?.split(",").map(e => { return id2name(e, huangdan) }).join('，')}}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  前囟
                </view>
              </view>
              <view class="r2">
                长：{{ info.qianxin_chang }}cm * 宽：{{ info.qianxin_kuan }}cm
                <text style="margin-left: 18px;">{{ id2name(info.qianxin, qianxin) }}</text>
                <text style="padding-left: 12px;" v-if="info.qianxin == '4'">{{ info.qianxin_text }}</text>
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  眼睛
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.eyes, normal_arr) }}</view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  四肢活动度
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.sizhi, normal_arr) }}</view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  耳外观
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.ear, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  颈部包块
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.jingbubaokuai, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  鼻
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.nose, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  皮肤
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.skin, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  口腔
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.kouqiang, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  肛门
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.gangmen, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  心肺听诊
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.xinfei, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  胸部
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.xiongbu, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  腹部触诊
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.fubu, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  脊柱
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.jizhu, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  外生殖器
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.waishengzhiqi, normal_arr) }}
              </view>
            </view>
            <view class="item">
              <view class="r1">
                <view class="r1l">
                  脐带
                </view>
              </view>
              <view class="r2">
                {{ id2name(info.qibu, qibu) }}
                <text style="padding-left: 8px;" v-if="info.qibu == 4">{{ info.qibu_text }}</text>
              </view>
            </view>
          </view>
          <view class="section" id="section5"
            :style="{ height: open5 ? height5 == 0 ? 'auto' : `${height5}px` : `100rpx` }">
            <view class="tops">
              <view class="tt">
                转诊
              </view>
              <view class="arrow" :style="{ transform: `rotate(${open5 ? 0 : 180}deg)` }" @click="open5 = !open5">
                <u-icon name="arrow-up"></u-icon>
              </view>
            </view>
            <view class="item">
              {{ id2name(info.zhuanzhen, have_arr) }}
              <text style="margin-left: 50px;" v-if="info.zhuanzhen == 2">机构及科室：{{ info.jigoukeshi }}</text>
            </view>
          </view>
          <view class="section" id="section6"
            :style="{ height: open6 ? height6 == 0 ? 'auto' : `${height6}px` : `100rpx` }">
            <view class="tops">
              <view class="tt">
                指导
              </view>
              <view class="arrow" :style="{ transform: `rotate(${open6 ? 0 : 180}deg)` }" @click="open6 = !open6">
                <u-icon name="arrow-up"></u-icon>
              </view>
            </view>
            <view class="zhidao" v-for="item in info.zhidao?.split(',')" :key="item">
              <image class="sw" src="https://obs.tuoyupt.com/miniprogram/changzhi/duihao.png"></image>
              {{ id2name(item, zhidao) }}
            </view>
          </view>
          <view class="section">
            <view class="yisheng">
              <view class="yleft">
                <view class="yt1">
                  随访医生签字
                </view>
                <view class="yt2">
                  本次随访时间：{{ info.visit_current }}
                </view>
              </view>
              <view class="right">
                {{ info.visit_doctor }}
                <!-- <image class="rimg" src="https://obs.tuoyupt.com/miniprogram/changzhi/duihao.png"></image> -->
              </view>
            </view>
            <view class="hr"></view>
            <view class="row">
              <view class="label">
                下次随访时间：
              </view>
              <view class="value">
                {{ info.visit_next }}
              </view>
            </view>
            <view class="row">
              <view class="label">
                下次随访地点：
              </view>
              <view class="value">
                {{ info.visit_next_address }}
              </view>
            </view>
          </view>
        </template>
        <u-empty v-else text="暂无访视记录"></u-empty>
      </template>
      <template v-if="tab == 1">
        <view class="section">
          <view class="t0" style="padding-top: 12rpx;">
            儿童健康检查记录
          </view>
          <template v-if="recordList.length">
            <view v-for="(item, index) in recordList" :key="item.type" @click="goDetail(item)">
              <view class="tops">
                <view class="tt">
                  {{ month_arr[item.type - 1].name }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  随访时月龄：
                </view>
                <view class="value">
                  {{ calculateAge(item.visit_current, child_info.birth) }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  随访时间：
                </view>
                <view class="value">
                  {{ item.visit_current }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  体检机构：
                </view>
                <view class="value">
                  {{ item.protection_user.hospital.name }}
                </view>
              </view>
              <view class="hr" v-if="index != recordList.length - 1"></view>
            </view>
          </template>
          <u-empty v-else text="暂无检查记录"></u-empty>
        </view>
      </template>
      <template v-if="tab == 2">
        <view class="section">
          <view class="t0" style="padding-top: 12rpx;">
            儿童视力检查记录
          </view>
          <template v-if="eyeList.length">
            <view v-for="(item, index) in eyeList" :key="item.type" @click="goDetailEye(item)">
              <view class="tops">
                <view class="tt">
                  {{ month_arr[item.type - 1].name }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  随访时月龄：
                </view>
                <view class="value">
                  {{ calculateAge(item.visit_current, child_info.birth) }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  随访时间：
                </view>
                <view class="value">
                  {{ item.visit_current }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  体检机构：
                </view>
                <view class="value">
                  {{ item.hospital.name }}
                </view>
              </view>
              <view class="hr" v-if="index != eyeList.length - 1"></view>
            </view>
          </template>
          <u-empty v-else text="暂无检查记录"></u-empty>
        </view>
      </template>
      <template v-if="tab == 3">
        <view class="section">
          <view class="t0" style="padding-top: 12rpx;">
            中医指导记录
          </view>
          <template v-if="zhongyiList.length">
            <view v-for="(item, index) in zhongyiList" :key="item.type" @click="goDetailZY(item)">
              <view class="tops">
                <view class="tt">
                  {{ month_arr2[item.type - 1].name }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  随访时月龄：
                </view>
                <view class="value">
                  <!-- {{ item.age_visit }} -->
                  {{ calculateAge(item.visit_current, child_info.birth) }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  随访时间：
                </view>
                <view class="value">
                  {{ item.visit_current }}
                </view>
              </view>
              <view class="row">
                <view class="label">
                  体检机构：
                </view>
                <view class="value">
                  {{ item.hospital.name }}
                </view>
              </view>
              <view class="hr" v-if="index != zhongyiList.length - 1"></view>
            </view>
          </template>
          <u-empty v-else text="暂无检查记录"></u-empty>
        </view>
      </template>
      <template v-if="tab == 4">
        <view class="section">
          <view class="tops">
            <view class="t0">
              0-3岁发育曲线
            </view>
            <view class="sex">
              <image class="sxi" v-if="child_info.sex == 1"
                src="https://obs.tuoyupt.com/miniprogram/changzhi/nan.png">
              </image>
              <image class="sxi" v-else src="https://obs.tuoyupt.com/miniprogram/changzhi/nv.png"></image>
              {{ child_info.sex == 2 ? '女孩' : '男孩' }}
            </view>
          </view>
          <Chart :data="chartData"></Chart>
          <view class="hr"></view>
          <Chart :data="chartData2"></Chart>
        </view>
      </template>
    </template>
    <u-empty v-else marginTop="180" text="暂未添加孩子"></u-empty>
  </view>
</template>
<script setup>
import { nextTick, ref } from "vue";
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
import Chart from "../components/chart";
import { boy_h, boy_w, girl_h, girl_w } from "./score.js";
import dayjs from "dayjs";

const current = ref(0);
const tabList = ref([{
  name: '新生儿访视记录',
  id: 0,
}, {
  name: '健康检查记录',
  id: 1,
}, {
  name: '视力检查记录',
  id: 'eye',
}, {
  name: '中医指导记录',
  id: 'zhongyi',
}, {
  name: '生长发育曲线',
  id: 2,
}])

function setBase(sex, type) {
  let data = "";
  let title = "";
  if (sex == 1) {
    if (type == 1) {
      data = boy_h;
      title = "身长（身高）/cm";
    } else {
      data = boy_w;
      title = "体重/kg";
    }
  } else {

    if (type == 1) {
      data = girl_h;
      title = "身长（身高）/cm";
    } else {
      data = girl_w;
      title = "体重/kg";
    }
  }
  let baseDate = [[], [], [], [], [], [], []];
  for (let index = 0; index < 36; index++) {
    for (let j = 0; j < 7; j++) {
      baseDate[j].push(data[index][j]);
    }
  }
  let min = Math.floor(data[0][0])
  let max = Math.round(data[36][5])
  return {
    baseDate,
    min,
    max,
    title
  }
}
let x = [];
for (let index = 0; index < 36; index++) {
  x.push(index)
}
const chartData = ref({
  xData: x,
  yData: [],
  min: 0,
  max: 99,
  interval: 5,
  title: "",
  baseDate: []
})
const chartData2 = ref({
  xData: x,
  yData: [],
  min: 0,
  max: 99,
  title: "",
  interval: 1,
  baseDate: []
})
onLoad(() => {
  getChild();
})
const tab = ref(0);
function changTab(index, value) {
  console.log(index)
  tab.value = index;
  if (index == 0) {
    if (info.value.id == 0) {
      getData();
    }
  }
  if (index == 1) {
    getRecord()
  }
  if (index == 2) {
    getEye()
  }
  if (index == 3) {
    getZhongyi()
  }
  if (index == 4) {
    getChart()
  }
}
const child_list = ref([]);
const child_info = ref({
  id: 0,
  name: '',
  birth: '',
  id_card: '',
});
const showPro = ref(false);
const info = ref({
  "id": 0,
  "hospital_id": 11,
  "protection_user_id": 1,
  "address": "家庭住址",
  "father_name": "家庭住址",
  "father_zhiye": "家庭住址",
  "father_phone": "14010120222",
  "father_birth": "2025-01-16",
  "mother_name": "家庭住址",
  "mother_zhiye": "家庭住址",
  "mother_phone": "14010120222",
  "mother_birth": "2025-01-16",
  "week": 40,
  "zhuchanjigou": "助产机构名称",
  "mother_ill_history": "2,3",
  "birth_history": "2",
  "child_zhixi": "2",
  "apgar_score": "1",
  "child_ill": "2,4",
  "child_hear": "2",
  "jixing": "畸形",
  "birth_weight": "5",
  "current_weight": "6",
  "birth_height": "40",
  "feed": "1",
  "milk": "40",
  "milk_count": "4",
  "outu": "2",
  "dabian": "1",
  "dabian_count": 10,
  "temperature": "36.0",
  "xinlv": 100,
  "huxipinlv": 100,
  "face_color": "1",
  "huangdan": "2,3,4,5",
  "qianxin_chang": "2",
  "qianxin_kuan": "2",
  "qianxin": "1",
  "eyes": "1",
  "sizhi": "1",
  "ear": "1",
  "jingbubaokuai": "1",
  "nose": "1",
  "skin": "1",
  "kouqiang": "1",
  "gangmen": "1",
  "xinfei": "1",
  "xiongbu": "1",
  "fubu": "1",
  "jizhu": "1",
  "waishengzhiqi": "1",
  "qibu": "1",
  "zhuanzhen": "2",
  "jigoukeshi": "机构及科室",
  "zhidao": "1,2,3,4,5",
  "visit_current": "2025-01-17",
  "visit_next": "2025-01-31",
  "visit_next_address": "下次随访地点",
  "visit_doctor": "顺义中心",
  "status": 0,
  "created_at": "2025-01-16 09:15:38",
  "updated_at": "2025-01-16 09:15:38",
  "deleted_at": null
});
let month_arr2 = [
  {
    id: 1,
    name: '6月龄'
  },
  {
    id: 2,
    name: '12月龄'
  },
  {
    id: 3,
    name: '18月龄'
  },
  {
    id: 4,
    name: '24月龄'
  },
  {
    id: 5,
    name: '30月龄'
  },
  {
    id: 6,
    name: '36月龄'
  }
]
let month_arr = [
  {
    id: 1,
    name: '满月'
  },
  {
    id: 2,
    name: '3月龄'
  },
  {
    id: 3,
    name: '6月龄'
  },
  {
    id: 4,
    name: '8月龄'
  },
  {
    id: 5,
    name: '12月龄'
  },
  {
    id: 6,
    name: '18月龄'
  },
  {
    id: 7,
    name: '24月龄'
  },
  {
    id: 8,
    name: '30月龄'
  },
  {
    id: 9,
    name: '36月龄'
  }
]

let mother_ill_history = [
  {
    id: '1',
    name: '无'
  },
  {
    id: '2',
    name: '糖尿病'
  },
  {
    id: '3',
    name: '妊娠期高血压'
  },
  {
    id: '4',
    name: '其他'
  }
]

let child_ill = [
  {
    id: '1',
    name: '未进行'
  },
  {
    id: '2',
    name: '检查均阴性'
  },
  {
    id: '3',
    name: '甲低'
  },
  {
    id: '4',
    name: '苯丙酮尿证'
  },
  {
    id: '5',
    name: '其他遗传代谢病'
  }
]

let child_hear = [
  {
    id: '1',
    name: '通过'
  },
  {
    id: '2',
    name: '未通过'
  },
  {
    id: '3',
    name: '未筛查'
  },
  {
    id: '4',
    name: '不详'
  }
]

let qianxin = [
  {
    id: '1',
    name: '闭合',
  },
  {
    id: '2',
    name: '未闭'
  }
]

let qibu = [
  {
    id: '1',
    name: '未脱',
  },
  {
    id: '2',
    name: '脱落'
  },
  {
    id: '3',
    name: '脐部有渗出'
  },
  {
    id: '4',
    name: '其他'
  }
]

let zhidao = [
  {
    id: '1',
    name: '科学喂养',
  },
  {
    id: '2',
    name: '生长发育'
  },
  {
    id: '3',
    name: '疾病预防'
  },
  {
    id: '4',
    name: '预防伤害'
  },
  {
    id: '5',
    name: '口腔保健'
  },
  {
    id: '6',
    name: '其他'
  }
]

let have_arr = [
  {
    id: '1',
    name: '无'
  },
  {
    id: '2',
    name: '有'
  },
]

let normal_arr = [
  {
    id: '1',
    name: '未见异常'
  },
  {
    id: '2',
    name: '异常'
  },
]

let feed = [
  {
    id: '1',
    name: '纯母乳'
  },
  {
    id: '2',
    name: '混合'
  },
  {
    id: '3',
    name: '人工'
  },
]

let face_color = [
  {
    id: '1',
    name: '红润'
  },
  {
    id: '2',
    name: '黄染'
  },
  {
    id: '3',
    name: '其他'
  },
]



let huangdan = [
  {
    id: '1',
    name: '无'
  },
  {
    id: '2',
    name: '面部'
  },
  {
    id: '3',
    name: '躯干'
  },
  {
    id: '4',
    name: '四肢'
  },
  {
    id: '5',
    name: '手足'
  },
]

let dabian = [
  {
    id: '1',
    name: '糊状'
  },
  {
    id: '2',
    name: '稀'
  },
  {
    id: '3',
    name: '其他'
  },
]

let apgar_score = [
  {
    id: '1',
    name: '1min'
  },
  {
    id: '2',
    name: '5min'
  },
  {
    id: '3',
    name: '不详'
  },
]

let birth_history = [
  {
    id: '1',
    name: '顺产'
  },
  {
    id: '2',
    name: '胎头吸引'
  },
  {
    id: '3',
    name: '产钳'
  },
  {
    id: '4',
    name: '剖喜'
  },
  {
    id: '5',
    name: '双多胎'
  },
  {
    id: '6',
    name: '臀位'
  },
  {
    id: '7',
    name: '其他'
  }
]
function id2name(id, arr, key = 'name') {
  if (id === '' || (typeof id) == 'undefined') {
    return "-";
  }
  if (arr.length == 0) {
    return "-";
  }
  let newarr = arr.filter((e) => {
    return e.id == id;
  });
  if (newarr.length) {
    return newarr[0][key];
  } else {
    return "-"
  }
}
function getChild() {
  request({
    url: "/api/healthservices/user/list",
  }).then(res => {
    child_list.value = res;
    if (res.length) {
      child_info.value = res[0];
      getData();
    }
  })
}
const recordList = ref([]);
function getRecord() {
  request({
    url: "/api/parent/protectionuser/health/list",
    data: {
      student_id: child_info.value.id
    }
  }).then(res => {
    uni.hideLoading();
    recordList.value = res.sort((a, b) => {
      return a.type - b.type;
    });
  })
}
let eyeList = ref([])
function getEye() {
  request({
    url: "/api/parent/protectionuser/eyesight/list",
    data: {
      student_id: child_info.value.id
    }
  }).then(res => {
    uni.hideLoading();
    eyeList.value = res.sort((a, b) => {
      return a.type - b.type;
    });
  })
}
let zhongyiList = ref([])
function getZhongyi() {
  request({
    url: "/api/parent/protectionuser/zhongyi/list",
    data: {
      student_id: child_info.value.id
    }
  }).then(res => {
    uni.hideLoading();
    zhongyiList.value = res.sort((a, b) => {
      return a.type - b.type;
    });
  })
}
function calculateAge(current, birth) {
  let birthday = dayjs(birth);
  let today = dayjs(current);
  let months = today.diff(birthday, 'month');
  if (months < 1) {
    return `${today.diff(birthday, 'day')}天`
  }
  if (months < 2) {
    return `满月`
  } else {
    birthday = birthday.year(today.year());
    birthday = birthday.month(today.month());
    let days = today.diff(birthday, 'day');
    if (days > 0) {
      return `${months}月${days}天`
    } else {
      return `${months - 1}月${Math.abs(days)}天`
    }
  }
}

function goDetail(it) {
  uni.setStorageSync("child_info", child_info.value)
  uni.navigateTo({
    url: `./record-deatil?type=${it.type}&id=${child_info.value.id}`,
  });
}
function goDetailZY(it) {
  uni.setStorageSync("child_info", child_info.value)
  uni.navigateTo({
    url: `./zhongyi-deatil?id=${it.id}`,
  });
}
function goDetailEye(it) {
  uni.setStorageSync("child_info", child_info.value)
  uni.navigateTo({
    url: `./eye-deatil?id=${it.id}`,
  });
}
const open1 = ref(true);
const height1 = ref(0);
const open2 = ref(true);
const height2 = ref(0);
const open3 = ref(true);
const height3 = ref(0);
const open5 = ref(true);
const height5 = ref(0);
const open6 = ref(true);
const height6 = ref(0);
function getData() {
  uni.showLoading();
  request({
    url: "/api/parent/protectionuser/visit",
    data: {
      student_id: child_info.value.id
    }
  }).then(async res => {
    if (res) {
      let infos = {};
      for (const key in res) {
        if (Object.prototype.hasOwnProperty.call(res, key)) {
          const element = res[key];
          if (key == 'birth_history' && (Number.isNaN(element * 1))) {
            infos[key] = '7';
            infos[key + '_text'] = element;
          } else if (key == 'jixing' && (Number.isNaN(element * 1))) {
            infos[key] = '2';
            infos[key + '_text'] = element;
          } else if (key == 'face_color' && (Number.isNaN(element * 1))) {
            infos[key] = '3';
            infos[key + '_text'] = element;
          } else if (key == 'qianxin' && (Number.isNaN(element * 1))) {
            infos[key] = '4';
            infos[key + '_text'] = element;
          } else if (key == 'qibu' && (Number.isNaN(element * 1))) {
            infos[key] = '4';
            infos[key + '_text'] = element;
          } else {
            if (element == 'null') {
              infos[key] = '';
            } else {
              infos[key] = element;
            }
          }
        }
      }
      info.value = infos;
      showPro.value = true;
      await nextTick();
      uni.createSelectorQuery().select('#section1').boundingClientRect((rect) => {
        height1.value = rect.height;
      }).exec();
      uni.createSelectorQuery().select('#section2').boundingClientRect((rect) => {
        height2.value = rect.height;
      }).exec();
      uni.createSelectorQuery().select('#section3').boundingClientRect((rect) => {
        height3.value = rect.height;
      }).exec();
      uni.createSelectorQuery().select('#section5').boundingClientRect((rect) => {
        height5.value = rect.height;
      }).exec();
      uni.createSelectorQuery().select('#section6').boundingClientRect((rect) => {
        height6.value = rect.height;
      }).exec();
    }
    uni.hideLoading();
  })
}
const type = ref(1);
function getChart() {
  uni.showLoading();
  request({
    url: "/api/parent/protectionuser/wh",
    data: {
      student_id: child_info.value.id
    }
  }).then(res => {
    uni.hideLoading();
    let heightData = new Array(36).fill(null);
    let weightDate = new Array(36).fill(null);
    let key = [1, 4, 7, 9, 13, 19, 25, 31, 36]
    if (res && res.length) {
      res.forEach(element => {
        heightData[key[element.type - 1]] = Number(element.height);
        weightDate[key[element.type - 1]] = Number(element.weight);
      });
    }
    chartData.value = {
      interval: 5,
      yData: heightData,
      ...setBase(child_info.value.sex, 1),
    };
    chartData2.value = {
      interval: 1,
      yData: weightDate,
      ...setBase(child_info.value.sex, 2),
    };
  })
}
const selectShow = ref(false);
function confirmSelect(e) {
  let info = child_list.value.find(item => {
    return item.id == e[0].value;
  })
  child_info.value = info;
  if (tab.value == 0) {
    getData();
  }
  if (tab.value == 1) {
    getRecord()
  }
  if (tab.value == 2) {
    getChart()
  }
}
</script>
<style lang="scss" scoped>
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;
  padding-bottom: 30rpx;

  .top {
    width: 100vw;
    height: 206rpx;
    padding: 24rpx 30rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .t1 {
      font-weight: 600;
      font-size: 52rpx;
      color: #000000;
      line-height: 74rpx;
      margin-bottom: 20rpx;
    }

    .t2 {
      font-weight: 400;
      font-size: 26rpx;
      color: #7A7E8E;
      line-height: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn {
      width: 200rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #1975FF;
      line-height: 36rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }

  .tabs {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10rpx;
    padding: 0 30rpx;

    .tab {
      width: 210rpx;
      height: 64rpx;
      background: rgba(216, 216, 216, 0);
      border-radius: 8rpx;
      border: 2rpx solid #D9D9D9;
      font-weight: 500;
      font-size: 28rpx;
      color: #63676F;
      line-height: 64rpx;
      text-align: center;
      font-style: normal;
    }

    .active {
      background: #FFFFFF;
      color: #63676F;
    }
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;
    padding: 10rpx 30rpx 20rpx 30rpx;
    transition: height 0.3s ease-in-out;
    will-change: height;

    .t0 {
      height: 50rpx;
      font-weight: 600;
      font-size: 36rpx;
      color: #292D3B;
      margin-bottom: 20rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 10rpx;
      }

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .color0 {
        color: #1975FF;
      }

      .color1 {
        color: #00CB46;
      }
    }

    .hr {
      width: 100%;
      background: #eee;
      height: 1rpx;
      margin: 30rpx 0;
    }

    .infos {
      .row {
        display: flex;
        flex-direction: row;
        margin-top: 24rpx;

        .label {
          width: 130rpx;
          height: 36rpx;
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8E;
          line-height: 36rpx;
        }

        .value {
          flex: 1;
          font-size: 26rpx;
          text-align: right;
          font-weight: 400;
          color: #3D414E;
          line-height: 36rpx;
        }
      }
    }

    .yisheng {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-top: 16rpx;

      .yleft {
        .yt1 {
          font-weight: 600;
          font-size: 32rpx;
          color: #3D414E;
          line-height: 44rpx;
        }

        .yt2 {
          font-weight: 400;
          font-size: 26rpx;
          color: #797D8E;
          line-height: 36rpx;
          margin-top: 4rpx;
        }
      }

      .right {
        width: 167rpx;
        height: 88rpx;
        padding-top: 20rpx;

        .rimg {
          width: 167rpx;
          height: 88rpx;
        }
      }
    }

    .zhidao {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #797D8E;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }

    .tops {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 20rpx;
      margin-bottom: 30rpx;

      .arrow {
        transform: rotate(0deg);
        transition: transform 0.3s ease-in-out;
      }

      .sex {
        text-align: right;
        font-weight: 500;
        font-size: 26rpx;
        color: #ADAFBC;
        line-height: 36rpx;
        flex: 1;

        .sxi {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
      }

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 12rpx;
      }

      .tt {
        font-size: 32rpx;
        font-weight: 600;
        color: #282B39;
        flex: 1;
      }
    }

    .item {
      width: 100%;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #EEE;
      margin-top: 30rpx;

      .r1 {
        font-size: 28rpx;
        font-weight: 500;
        color: #282B39;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .r1r {
          display: flex;
          flex-direction: row;
          align-items: center;

          .r1r1 {
            font-weight: 400;
            font-size: 24rpx;
            color: #797D8D;
            line-height: 34rpx;
          }

          .r1r2 {
            display: flex;
            flex-direction: row;
            align-items: center;

            .r1r22 {
              width: 48rpx;
              height: 48rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #282B39;
              line-height: 48rpx;
              text-align: center;
              margin-left: 12rpx;
            }

            .ac {
              background: rgba(25, 117, 255, 0);
              border-radius: 8rpx;
              border: 1rpx solid #1975FF;
              color: #1975FF;
              display: inline-block;
            }
          }
        }
      }

      .r2 {
        margin-top: 16rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #797D8D;
      }
    }

    .tts {
      font-size: 32rpx;
      font-weight: 600;
      color: #3D414E;
    }
  }
}
</style>