<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ background: 'linear-gradient(180deg, #D5EDFF 0%, #F1F5F8 100%)' }"
      title="检查记录详情">
    </u-navbar>
    <view class="section">
      <view class="tops">
        <view class="ttt">
          {{ child_info.name }}
        </view>
        <view class="age">
          {{ calculateAge(info.visit_current, child_info.birth) }}
        </view>
      </view>
      <view class="hr"></view>
      <view class="row">
        <view class="label">
          随访日期：
        </view>
        <view class="value">
          {{ info.visit_current }}
        </view>
      </view>
      <view class="row">
        <view class="label">
          体检机构：
        </view>
        <view class="value">
          {{ info.hospital.name }}
        </view>
      </view>
    </view>
    <view class="section">
      <view class="tops1">
        <view class="tt">
          视力检查记录
        </view>
        <view class="arrow" @click="onClickShowAll">
          {{ showAll ? "收起全部" : "展开全部" }} <u-icon name="arrow-up"
            :style="{ transform: `rotate(${showAll ? 0 : 180}deg)` }"></u-icon>
        </view>
      </view>
      <view class="sectionopen" id="section1"
        :style="{ height: open1 ? height1 == 0 ? 'auto' : `${height1}px` : `80rpx` }">
        <view class="tops">
          <image src="https://obs.tuoyupt.com/yjy_mini/front_img/physical/jca.png" class="icon">
          </image>
          <view class="tt">
            总体情况
          </view>
          <view class="arrow" @click="open1 = !open1" :style="{ transform: `rotate(${open1 ? 0 : 180}deg)` }">
            <u-icon name="arrow-up"></u-icon>
          </view>
        </view>
        <view class="item">
          <view class="r2">
            {{ info.zongti == 1 ?  '异常' : '未见异常' }}
          </view>
        </view>
        
      </view>
      
    </view>
    <view class="section" id="section2" 
     v-if="info.yanjian.right.arr.length || info.yanjian.left.arr.length ||
     info.jiemo.right.arr.length || info.jiemo.left.arr.length ||
     info.yanqiu.right.arr.length || info.yanqiu.left.arr.length || 
     info.jiaomo.right.arr.length || info.jiaomo.left.arr.length ||
     info.tongkong.right.arr.length || info.tongkong.left.arr.length ||
     info.gongmo.right.arr.length || info.gongmo.left.arr.length
     "
    :style="{ height: open2 ? height2 == 0 ? 'auto' : `${height2}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          眼外观
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open2 ? 0 : 180}deg)` }" @click="open2 = !open2">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <view class="item" v-if="info.yanjian.right.arr.length || info.yanjian.left.arr.length">
        <view class="r1">
          <view class="r1l">
            眼睑
          </view>
        </view>
        <template  v-if="info.type == 1">
          <view class="r2" v-if="info.yanjian.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.yanjian.right.arr.sort().map(e=>{return id2name(e, yanjian1)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.yanjian.right.mark" class="other">{{ info.yanjian.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.yanjian.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.yanjian.left.arr.sort().map(e=>{return id2name(e, yanjian1)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.yanjian.left.mark" class="other">{{ info.yanjian.left.mark }}</span>
              </p>
             
            </view>
          </view>
        </template>
        <template  v-if="info.type > 5">
          <view class="r2" v-if="info.yanjian.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.yanjian.right.arr.sort().map(e=>{return id2name(e, yanjian2)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.yanjian.right.mark" class="other">{{ info.yanjian.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.yanjian.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.yanjian.left.arr.sort().map(e=>{return id2name(e, yanjian1)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.yanjian.left.mark" class="other">{{ info.yanjian.left.mark }}</span>
              </p>
            </view>
          </view>
        </template>
        </view>
        <view class="item"  v-if="info.type == 1 && (info.jiemo.right.arr.length || info.jiemo.left.arr.length)">
          <view class="r1">
            <view class="r1l">
              结膜
            </view>
          </view>
          <view class="r2" v-if="info.jiemo.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.jiemo.right.arr.sort().map(e=>{return id2name(e, jiemo)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.jiemo.right.mark" class="other">{{ info.jiemo.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.jiemo.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.jiemo.left.arr.sort().map(e=>{return id2name(e, jiemo)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.jiemo.left.mark" class="other">{{ info.jiemo.left.mark }}</span>
              </p>
            </view>
          </view>
        </view>
        <view class="item" v-if="info.yanqiu.right.arr.length || info.yanqiu.left.arr.length">
          <view class="r1">
            <view class="r1l">
              眼球
            </view>
          </view>
          <view class="r2" v-if="info.yanqiu.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.yanqiu.right.arr.sort().map(e=>{return id2name(e, yanqiu)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.yanqiu.right.mark" class="other">{{ info.yanqiu.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.yanqiu.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.yanqiu.left.arr.sort().map(e=>{return id2name(e, yanqiu)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.yanqiu.left.mark" class="other">{{ info.yanqiu.left.mark }}</span>
              </p>
            </view>
          </view>
        </view>
        <view class="item" v-if="info.type !== 1 && (info.jiemo.right.arr.length || info.jiemo.left.arr.length)">
          <view class="r1">
            <view class="r1l">
              结膜
            </view>
          </view>
          <view class="r2" v-if="info.jiemo.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.jiemo.right.arr.sort().map(e=>{return id2name(e, jiemo2)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.jiemo.right.mark" class="other">{{ info.jiemo.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.jiemo.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.jiemo.left.arr.sort().map(e=>{return id2name(e, jiemo2)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.jiemo.left.mark" class="other">{{ info.jiemo.left.mark }}</span>
              </p>
            </view>
          </view>
        </view>
        <view class="item" v-if="info.jiaomo.right.arr.length || info.jiaomo.left.arr.length">
          <view class="r1">
            <view class="r1l">
              角膜
            </view>
          </view>
          <view class="r2" v-if="info.jiaomo.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.jiaomo.right.arr.sort().map(e=>{return id2name(e, jiaomo)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.jiaomo.right.mark" class="other">{{ info.jiaomo.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.jiaomo.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.jiaomo.left.arr.sort().map(e=>{return id2name(e, jiaomo)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.jiaomo.left.mark" class="other">{{ info.jiaomo.left.mark }}</span>
              </p>
            </view>
          </view>
        </view>
        <view class="item" v-if="info.tongkong.right.arr.length || info.tongkong.left.arr.length">
          <view class="r1">
            <view class="r1l">
              瞳孔
            </view>
          </view>
          <view class="r2" v-if="info.tongkong.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.tongkong.right.arr.sort().map(e=>{return id2name(e, tongkong)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.tongkong.right.mark" class="other">{{ info.tongkong.right.mark }}</span>
              </p>
            </view>
          </view>
          <view class="r2" v-if="info.tongkong.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.tongkong.left.arr.sort().map(e=>{return id2name(e, tongkong)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.tongkong.left.mark" class="other">{{ info.tongkong.left.mark }}</span>
              </p>
            </view>
          </view>
        </view>
        <view class="item" v-if="info.type == 1 && (info.gongmo.right.arr.length || info.gongmo.left.arr.length)">
          <view class="r1">
            <view class="r1l">
              巩膜
            </view>
          </view>
          <view class="r2"  v-if="info.gongmo.right.arr.length">
            <p class="r2l">右眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.gongmo.right.arr.sort().map(e=>{return id2name(e, gongmo)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.gongmo.right.mark" class="other">{{ info.gongmo.right.mark }}</span>
              </p>
              
            </view>
          </view>
          <view class="r2" v-if="info.gongmo.left.arr.length">
            <p class="r2l">左眼</p>
            <view class="r2r">
              <p v-for="(item,index) in info.gongmo.left.arr.sort().map(e=>{return id2name(e, gongmo)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.gongmo.left.mark" class="other">{{ info.gongmo.left.mark }}</span>
              </p>
            </view>
          </view>
      </view>
    </view>
    <view class="section" v-if="info.type == 1 && (
      info.zaochan.arr.length || info.zhongzeng.arr.length || info.yichuan.arr.length || info.muqinchuanran.arr.length
      || info.lumian1.arr.length || info.lumian2.arr.length
    )" id="section3" :style="{ height: open3 ? height3 == 0 ? 'auto' : `${height3}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          主要眼病高危因素
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open3 ? 0 : 180}deg)` }" @click="open3 = !open3">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <view class="item" v-if="info.zaochan.arr.length">
        <view class="r1">
          <view class="r1l">
            出生时早产或低出生体重
          </view>
        </view>
        <view class="r2">
          <view class="r2r">
            <p v-for="(item,index) in info.zaochan.arr.sort().map(e=>{return id2name(e, main_tizhong)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.zaochan.mark" class="other">{{ info.zaochan.mark }}</span>
            </p>
           
          </view>
        </view>
      </view>
      <view class="item" v-if="info.zhongzeng.arr.length">
        <view class="r1">
          <view class="r1l">
            曾入住新生儿重症监护病房
          </view>
        </view>
        <view class="r2">
          <view class="r2r">
            <p v-for="(item,index) in info.zhongzeng.arr.sort().map(e=>{return id2name(e, main_zhongzheng)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.zhongzeng.mark" class="other">{{ info.zhongzeng.mark }}</span>
            </p>
            
          </view>
        </view>
      </view>
      <view class="item" v-if="info.yichuan.arr.length">
        <view class="r1">
          <view class="r1l">
            遗传性眼病家族史
          </view>
        </view>
        <view class="r2">
          <view class="r2r">
            <p v-for="(item,index) in info.yichuan.arr.sort().map(e=>{return id2name(e, main_yichang)})" :key="index">
              {{ item.name }}
              <span class="other" v-if=" info.yichuan.mark[index]">{{ info.yichuan.mark[index] }}</span>
            </p>
          </view>
        </view>
      </view>
      <view class="item" v-if="info.muqinchuanran.arr.length">
        <view class="r1">
          <view class="r1l">
            母亲孕期宫内传染
          </view>
        </view>
        <view class="r2">
          <view class="r2r">
            <p v-for="(item,index) in info.muqinchuanran.arr.sort().map(e=>{return id2name(e, main_yunqi)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.muqinchuanran.mark" class="other">{{ info.muqinchuanran.mark }}</span>
            </p>
           
          </view>
        </view>
      </view>
      <view class="item" v-if="info.lumian1.arr.length">
        <view class="r1">
          <view class="r1l">
            颅面及颜面畸形
          </view>
        </view>
        <view class="r2">
          <view class="r2r">
            <p v-for="(item,index) in info.lumian1.arr.sort().map(e=>{return id2name(e, main_jixing)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.lumian1.mark" class="other">{{ info.lumian1.mark }}</span>
            </p>
            
          </view>
        </view>
      </view>
      <view class="item" v-if="info.lumian2.arr.length">
        <view class="r1">
          <view class="r1l">
            眼部情况
          </view>
        </view>
        <view class="r2">
          <view class="r2r">
            <p v-for="(item,index) in info.lumian2.arr.sort().map(e=>{return id2name(e, main_eye)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.lumian2.mark" class="other">{{ info.lumian2.mark }}</span>
            </p>
           
          </view>
        </view>
      </view>
    </view>
    <view class="section" v-if="info.type !== 1" id="section3" :style="{ height: open3 ? height3 == 0 ? 'auto' : `${height3}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          其他检查
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open3 ? 0 : 180}deg)` }" @click="open3 = !open3">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <view class="item" v-if="info.type < 6">
        <view class="r1">
          <view class="r1l">
            瞬目反射
          </view>
        </view>
        <view class="r2">
          {{ info.shunmu == 1 ? '异常' : '未见异常' }}
        </view>
      </view>
      <view class="item" v-if="info.type < 6">
        <view class="r1">
          <view class="r1l">
            红球试验
          </view>
        </view>
        <view class="r2">
          {{ info.hongqiu == 1 ? '异常' : '未见异常'  }}
        </view>
      </view>
      <view class="item">
        <view class="r1">
          <view class="r1l">
            视物行为观察
          </view>
        </view>
        <view class="r2">
          {{ info.xingwei == 1 ? '异常' : '未见异常'  }}
        </view>
      </view>
      <view class="item" v-if="info.type < 6">
        <view class="r1">
          <view class="r1l">
            红光反射
          </view>
        </view>
        <view class="r2">
          <p class="r2l">右眼</p>
          <view class="r2r">
            {{ info.hongguang.right == 1 ? '异常' : '未见异常'  }}
          </view>
        </view>
        <view class="r2">
          <p class="r2l">左眼</p>
          <view class="r2r">
            {{ info.hongguang.left == 1 ? '异常' : '未见异常'  }}
          </view>
        </view>
      </view>
      <view class="item" v-if="info.yanwei.right.arr.length || info.yanwei.left.arr.length">
        <view class="r1">
          <view class="r1l">
            眼位检查
          </view>
        </view>
        <view class="r2" v-if="info.yanwei.right.arr.length">
          <p class="r2l">右眼</p>
          <view class="r2r">
            <p v-for="(item,index) in info.yanwei.right.arr.sort().map(e=>{return id2name(e, other_eye)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.yanwei.right.mark" class="other">{{ info.yanwei.right.mark }}</span>
            </p>
          </view>
        </view>
        <view class="r2" v-if="info.yanwei.left.arr.length">
          <p class="r2l">左眼</p>
          <view class="r2r">
            <p v-for="(item,index) in info.yanwei.left.arr.sort().map(e=>{return id2name(e, other_eye)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.yanwei.left.mark" class="other">{{ info.yanwei.left.mark }}</span>
            </p>
          </view>
        </view>
      </view>
      <view class="item">
        <view class="r1">
          <view class="r1l">
            单眼遮盖厌恶试验
          </view>
        </view>
        <view class="r2">
          <p class="r2l">右眼</p>
          <view class="r2r">
            {{ info.danyan.right == 1 ? '异常' : '未见异常'  }}
          </view>
        </view>
        <view class="r2">
          <p class="r2l">左眼</p>
          <view class="r2r">
            {{ info.danyan.left == 1 ? '异常' : '未见异常'  }}
          </view>
        </view>
      </view>
      <view class="item" v-if="info.type > 5">
        <view class="r1">
          <view class="r1l">
            屈光筛查
          </view>
        </view>
        <view class="r2" v-if="info.quguang.right.S || info.quguang.right.C || info.quguang.right.A || info.quguang.right.arr.length">
          <p class="r2l">右眼</p>
          <view class="r2r">
            <p v-if="info.quguang.right.S || info.quguang.right.C || info.quguang.right.A"> 
              S<span class="other">{{ info.quguang.right.S }}</span>,
                C<span class="other">{{ info.quguang.right.C }}</span>,
                A<span class="other">{{ info.quguang.right.A }}</span>
            </p>
            <!-- <p>（非散瞳验光结果仅供参考）</p> -->
            <p v-for="(item,index) in info.quguang.right.arr.sort().map(e=>{return id2name(e, quguang)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.quguang.right.mark" class="other">{{ info.quguang.right.mark }}</span>
            </p>
          </view>
        </view>
        <view class="r2" v-if="info.quguang.left.S || info.quguang.left.C || info.quguang.left.A || info.quguang.left.arr.length">
          <p class="r2l">左眼</p>
          <view class="r2r">
            <p v-if="info.quguang.left.S || info.quguang.left.C || info.quguang.left.A"> S<span class="other">{{ info.quguang.left.S }}</span>,
                C<span class="other">{{ info.quguang.left.C }}</span>,
                A<span class="other">{{ info.quguang.left.A }}</span>
            </p>
            <!-- <p>（非散瞳验光结果仅供参考）</p> -->
            <p v-for="(item,index) in info.quguang.left.arr.sort().map(e=>{return id2name(e, quguang)})" :key="index">
              {{ item.name }}
              <span v-if="item.id == 'other' && info.quguang.left.mark" class="other">{{ info.quguang.left.mark }}</span>
            </p>
          </view>
        </view>
      </view>
    </view>
    <view class="section" id="section4" :style="{ height: open4 ? height4 == 0 ? 'auto' : `${height4}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          转诊建议
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open4 ? 0 : 180}deg)` }" @click="open4 = !open4">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <view class="item">
        <view class="r2">
          <view>{{ info.zhuanzhen.isHave == 1 ? '有' : '无' }}</view>
        </view>
        <view class="r2" v-if="info.zhuanzhen.isHave == 1">
          <view class="r2l">原因</view>
          <view class="r2r" >
            <template v-if="info.type == 1">
              <p v-for="(item,index) in info.zhuanzhen.arr.sort().map(e=>{return id2name(e, zhuanzhen)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.zhuanzhen.mark" class="other">{{ info.zhuanzhen.mark }}</span>
              </p>
            </template>
            <template v-if="info.type>1 && info.type < 6">
              <p v-for="(item,index) in info.zhuanzhen.arr.sort().map(e=>{return id2name(e, zhuanzhen1)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.zhuanzhen.mark" class="other">{{ info.zhuanzhen.mark }}</span>
              </p>
            </template>
            <template v-if="info.type > 5">
              <p v-for="(item,index) in info.zhuanzhen.arr.sort().map(e=>{return id2name(e, zhuanzhen2)})" :key="index">
                {{ item.name }}
                <span v-if="item.id == 'other' && info.zhuanzhen.mark" class="other">{{ info.zhuanzhen.mark }}</span>
              </p>
            </template>
           
            <p v-if="info.zhuanzhen.school">机构：{{ info.zhuanzhen.school }}</p>
          </view>
        </view>
      </view>
    </view>
    <view class="section" id="section5" :style="{ height: open5 ? height5 == 0 ? 'auto' : `${height5}px` : `100rpx` }">
      <view class="tops">
        <view class="tt">
          健康指导
        </view>
        <view class="arrow" :style="{ transform: `rotate(${open5 ? 0 : 180}deg)` }" @click="open5 = !open5">
          <u-icon name="arrow-up"></u-icon>
        </view>
      </view>
      <template v-if="info.type == 1">
        <b>普遍性指导</b>
        <p class="zhidao" v-for="(item, index) in zhidao.pubian" :key="index">{{index+1}}.{{item}}</p>
        <b>针对性指导</b>
        <p class="zhidao" v-for="(item, index) in zhidao.zhendui" :key="index">{{index+1}}.{{item}}</p>
      </template>
      <template v-if="info.type > 1 && info.type < 6">
        <b>普遍性指导</b>
        <p  class="zhidao" v-for="(item, index) in zhidao1.pubian" :key="index">{{index+1}}.{{item}}</p>
        <b>针对性指导</b>
        <p class="zhidao" v-for="(item, index) in zhidao1.zhendui" :key="index">{{index+1}}.{{item}}</p>
      </template>
      <template v-if="info.type > 5">
        <b>普遍性指导</b>
        <p class="zhidao" v-for="(item, index) in zhidao2.pubian" :key="index">{{index+1}}.{{item}}</p>
        <b>针对性指导</b>
        <p class="zhidao" v-for="(item, index) in zhidao2.zhendui" :key="index">{{index+1}}.{{item}}</p>
      </template>
      {{ info.zhidao }}
    </view>
    <view class="section">
      <view class="yisheng">
        <view class="yleft">
          医师签名
        </view>
        <view class="right">
          {{ info.visit_doctor }}
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, nextTick, computed } from "vue";
import { onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
import dayjs from "dayjs";
import { yanjian1,yanjian2, jiemo, jiemo2, yanqiu, jiaomo, tongkong, gongmo, 
  main_tizhong,main_zhongzheng, main_yichang ,main_yunqi, main_jixing, main_eye,
  zhuanzhen, zhuanzhen1,zhuanzhen2, zhidao,zhidao1,zhidao2, other_eye, quguang 
} from "./baby_eye_data.js";
let id = "";
let type = ref(1);
const child_info = ref({
  id: 0,
  name: '',
  birth: '',
  id_card: '',
});
function calculateAge(current, birth) {
  let birthday = dayjs(birth);
  let today = dayjs(current);
  let months = today.diff(birthday, 'month');
  if (months < 1) {
    return `${today.diff(birthday, 'day')}天`
  }
  if (months < 2) {
    return `满月`
  } else {
    birthday = birthday.year(today.year());
    birthday = birthday.month(today.month());
    let days = today.diff(birthday, 'day');
    if (days > 0) {
      return `${months}月${days}天`
    } else {
      return `${months - 1}月${Math.abs(days)}天`
    }
  }
}
onLoad((option) => {
  id = option.id;
  type.value = option.type;
  child_info.value = uni.getStorageSync("child_info")
  getData();
})


function id2name(id, arr, key = 'name') {
  if (id === '' || (typeof id) == 'undefined') {
    return "-";
  }
  if (arr.length == 0) {
    return "-";
  }
  let newarr = arr.filter((e) => {
    return e.id == id;
  });
  if (newarr.length) {
    return newarr[0];
  } else {
    return "-"
  }
}
const info = ref({
  hospital:{},
  zongti: '0',//总体情况
  type: '',
  visit_current:'',//本次访视日期
  visit_next:'',//下次访视日期
  visit_doctor:'',//随访医生签名
  status:'',//状态：1=已生成报告
  yanjian:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//眼睑
  jiemo:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//结膜
  yanqiu:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//眼球
  jiaomo:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//角膜
  tongkong:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//瞳孔
  gongmo:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//巩膜
  shunmu:[],//瞬目反射
  hongqiu:[],//红球试验
  xingwei:[],//视物行为观察
  hongguang:{
    right:[],
    left:[],
  },//红光反射
  yanwei:{
    right:{
      arr:[],
      mark: '',
    },
    left:{
      arr:[],
      mark: '',
    },
  },//眼位检查
  danyan:{
    right:[],
    left:[],
  },//单眼遮盖厌恶实验
  quguang:{
    right:{
      S:'',
      A: '',
      C:'',
      arr:[],
      mark:''
    },
    left:{
      S:'',
      A: '',
      C:'',
      arr:[],
      mark:''
    },
  },//屈光检查
  zaochan:{
      arr:[],
      mark: '',
    },//出生时早产或低出生体重
  zhongzeng:{
      arr:[],
      mark: '',
    },//曾入住新生儿重症监护病房
  yichuan:{
      arr:[],
      mark: ['','',''],
    },//遗传性眼病家族史
  muqinchuanran:{
      arr:[],
      mark:'',
    },//母孕期宫内传染
  lumian1:{
      arr:[],
      mark: '',
    },//颅面及颜面畸形1
  lumian2:{
      arr:[],
      mark: '',
    },//眼部情况
  zhuanzhen:{
    isHave:0,
      arr:[],
      mark: '',
      school: '',
    },//转诊建议
  zhidao:'',//健康指导
});
const open1 = ref(true);
const height1 = ref(0);
const open2 = ref(true);
const height2 = ref(0);
const open3 = ref(true);
const height3 = ref(0);
const open4 = ref(true);
const height4 = ref(0);
const open5 = ref(true);
const height5 = ref(0);
const showAll = computed(() => {
  if (open1.value && open2.value && open3.value) {
    return true
  }
  return false;
});
function onClickShowAll() {
  if (showAll.value) {
    open1.value = false;
    open2.value = false;
    open3.value = false;
  } else {
    open1.value = true;
    open2.value = true;
    open3.value = true;
  }
}
function getData() {
  uni.showLoading();
  request({
    url: "/api/parent/protectionuser/eyesight",
    data: {
      id: id,
    }
  }).then(async res => {
    let infos = {};
    for (const key in res) {
        if (Object.prototype.hasOwnProperty.call(info.value, key)) {
          const element = res[key];
          if (key == 'visit_next' || key == 'visit_current' || key == 'zongti' || key == 'zhidao' || key == 'visit_doctor' ||  key == 'hospital') {
            infos[key] = element
          } else {
            if (element !== 'null' && element) {
              infos[key] = JSON.parse(element);
            }
          }
        }
      }
    uni.hideLoading();
    info.value = infos;
    await nextTick();
    uni.createSelectorQuery().select('#section1').boundingClientRect((rect) => {
      height1.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section2').boundingClientRect((rect) => {
      height2.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section3').boundingClientRect((rect) => {
      height3.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section4').boundingClientRect((rect) => {
      height4.value = rect.height;
    }).exec();
    uni.createSelectorQuery().select('#section5').boundingClientRect((rect) => {
      height5.value = rect.height;
    }).exec();
  })
}

</script>
<style lang="scss" scoped>
#section5 b{
  display: block;
  margin-top: 20rpx;
}
.other{
  text-decoration: underline;
}
.pages {
  background: #F1F5F8;
  min-height: 100vh;
  width: 100vw;
  padding-bottom: 30rpx;

  .top {
    width: 100vw;
    height: 206rpx;
    padding: 24rpx 30rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .t1 {
      font-weight: 600;
      font-size: 52rpx;
      color: #000000;
      line-height: 74rpx;
      margin-bottom: 20rpx;
    }

    .t2 {
      font-weight: 400;
      font-size: 26rpx;
      color: #7A7E8E;
      line-height: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn {
      width: 200rpx;
      height: 40rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #1975FF;
      line-height: 36rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }

  .tabs {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10rpx;
    padding: 0 30rpx;

    .tab {
      width: 210rpx;
      height: 64rpx;
      background: rgba(216, 216, 216, 0);
      border-radius: 8rpx;
      border: 2rpx solid #D9D9D9;
      font-weight: 500;
      font-size: 28rpx;
      color: #63676F;
      line-height: 64rpx;
      text-align: center;
      font-style: normal;
    }

    .active {
      background: #FFFFFF;
      color: #63676F;
    }
  }

  .sectionopen {
    transition: height 0.3s ease-in-out;
    will-change: height;
    overflow: hidden;
  }

  .section {
    width: 690rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx auto;
    position: relative;
    overflow: hidden;
    padding: 10rpx 30rpx 30rpx 30rpx;
    transition: height 0.3s ease-in-out;
    will-change: height;

    .t0 {
      height: 50rpx;
      font-weight: 600;
      font-size: 36rpx;
      color: #292D3B;
      margin-bottom: 20rpx;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .name {
        font-size: 34rpx;
        font-weight: 600;
        color: #3D414E;
        margin-bottom: 10rpx;
      }

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #797D8D;
      }

      .value {
        font-size: 26rpx;
        font-weight: 500;
        color: #3D414E;
      }

      .color0 {
        color: #1975FF;
      }

      .color1 {
        color: #00CB46;
      }
    }

    .hr {
      width: 100%;
      background: #eee;
      height: 1rpx;
      margin: 30rpx 0;
    }

    .infos {
      .row {
        display: flex;
        flex-direction: row;
        margin-top: 24rpx;

        .label {
          width: 130rpx;
          height: 36rpx;
          font-size: 26rpx;
          font-weight: 400;
          color: #797D8E;
          line-height: 36rpx;
        }

        .value {
          flex: 1;
          font-size: 26rpx;
          text-align: right;
          font-weight: 400;
          color: #3D414E;
          line-height: 36rpx;
        }
      }
    }

    .yisheng {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-top: 20rpx;
      align-items: center;

      .yleft {
        font-weight: 600;
          font-size: 32rpx;
          color: #3D414E;
      }

      .right {
       
        flex: 1;
        text-align: right;
      }
    }

    .zhidao {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-weight: 400;
      font-size: 26rpx;
      color: #797D8E;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      padding: 10rpx 0;
      .sw {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }

    .tops1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 10rpx;

      .tt {
        font-weight: 500;
        font-size: 36rpx;
        color: #3D414E;
        line-height: 50rpx;
      }

      .arrow {
        font-weight: 400;
        font-size: 24rpx;
        color: #ADAFBC;
        line-height: 36rpx;
      }
    }

    .tops {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 20rpx;
      margin-bottom: 30rpx;

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 12rpx;
      }

      .ttt {
        font-weight: 600;
        font-size: 40rpx;
        color: #000000;
        line-height: 56rpx;
      }

      .age {
        font-weight: 400;
        font-size: 26rpx;
        color: #797D8E;
        line-height: 36rpx;
        margin-left: 12rpx;
      }

      .tt {
        font-size: 32rpx;
        font-weight: 600;
        color: #282B39;
        flex: 1;
      }

      .arrow {
        font-weight: 400;
        font-size: 24rpx;
        color: #ADAFBC;
        line-height: 36rpx;
      }
    }

    .item {
      width: 100%;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #EEE;
      margin-top: 20rpx;

      .r1 {
        font-size: 28rpx;
        font-weight: 500;
        color: #282B39;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .r1r {
          display: flex;
          flex-direction: row;
          align-items: center;

          .r1r1 {
            font-weight: 400;
            font-size: 24rpx;
            color: #797D8D;
            line-height: 34rpx;
          }

          .r1r2 {
            display: flex;
            flex-direction: row;
            align-items: center;

            .r1r22 {
              width: 48rpx;
              height: 48rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #282B39;
              line-height: 48rpx;
              text-align: center;
              margin-left: 12rpx;
            }

            .ac {
              background: rgba(25, 117, 255, 0);
              border-radius: 8rpx;
              border: 1rpx solid #1975FF;
              color: #1975FF;
              display: inline-block;
            }
          }
        }
      }

      .r2 {
        font-size: 28rpx;
        font-weight: 400;
        color: #797D8D;
        display: flex;
        padding: 10rpx 0;
        .r2l{
          width: 100rpx;
          color: #333333;
        }
        .r2r{
          flex: 1;
          p{
            display: flex;
            align-items: center;
            padding: 5rpx 0;
            .sw {
              width: 32rpx;
              height: 32rpx;
              margin-right: 10rpx;
            }
          }
        }
      }
    }

    .tts {
      font-size: 32rpx;
      font-weight: 600;
      color: #3D414E;
    }

  }
}
</style>