<template>
	<view class="pages">
		<u-navbar :border-bottom="false" :background="{ backgroundColor: '#FFFFFF' }" title="报名预约"></u-navbar>
		<u-form :model="form" ref="formRef" label-width="200rpx">
			<view style="height: 20rpx;"></view>
			<u-form-item label="预约人姓名" required prop="parents_name">
				<u-input v-model="form.parents_name" placeholder="请输入姓名" />
			</u-form-item>
			<u-form-item label="预约人手机号" required prop="phone">
				<u-input v-model="form.phone" placeholder="请输入手机号" />
			</u-form-item>
			<view style="height: 20rpx;"></view>
			<u-form-item label="宝宝姓名"><u-input v-model="form.baby_name" placeholder="请输入姓名" /></u-form-item>
			<u-form-item label="宝宝关系">
				<u-input v-model="form.relationship_name" disabled type="select" @click="showRela" placeholder="请选择" />
			</u-form-item>
		</u-form>
	</view>
	<u-select :list="selectEumn" v-model="rshow" @confirm="reconfirm"></u-select>
	<view class="footer">
		<view class="footer-content">
			<u-button class="btn" type="primary" @click="back">取消</u-button>
			<u-button class="btn" type="primary" @click="submit">报名</u-button>
		</view>
	</view>
</template>
<script setup>
import { onReady, onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import request from '@/request';
import { useUserStore } from '@/stores/userInfo.js';
const userInfo = useUserStore();
userInfo.refrsh(true);
const selectEumn = ref([]);
const rshow = ref(false);
function showRela() {
	rshow.value = true;
}
function reconfirm(e) {
	form.value.relationship = e[0].value;
	form.value.relationship_name = e[0].label;
}
function getRelationship() {
	request({
		url: `/api/parent/school/relation`,
	}).then((res) => {
		let arr = [];
		for (const key in res) {
			if (Object.hasOwnProperty.call(res, key)) {
				const element = res[key];
				arr.push({
					value: key,
					label: element,
				});
			}
		}
		relation.value = arr;
	});
}
const form = ref({
	parents_name: '',
	phone: '',
	baby_name: '',
	relationship: '',
	relationship_name: ''
});
function back() {
	uni.navigateBack();
}
const activity_id = ref('');
onLoad((option) => {
	if (option.id) {
		activity_id.value = option.id;
	}
	getRelationship();
});
function submit() {
	let info = uni.getStorageSync('enentinfo');
	formRef.value.validate((valid) => {
		if (valid) {
			uni.showLoading();
			let data = {
				event_id: info.id,
				join_name: form.value.parents_name,
				phone: form.value.phone,
				relationship: form.value.relationship,
				student_name: form.value.baby_name
			};
			if (info.act_from == 2) {
				data.school_id = info.school_id;
			} else {
				data.school_id = 0;
			}
			if (userInfo.student_id) {
				data.student_id = userInfo.student_id;
			} else {
				data.student_id = 0;
			}
			request({
				url: '/api/parent/schoolevent/add',
				method: 'post',
				data: data
			}).then((res) => {
				uni.hideLoading();
				if (res) {
					uni.showToast({
						title: '预约成功',
						icon: 'none',
						duration: 2000
					});
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages/pack2/activety/detail?id=' + info.id
						});
					}, 2000)
					uni.$emit('activeBack');
				}
			});
		}
	});
}
const formRef = ref(null);
const rules = {
	parents_name: [
		{
			required: true,
			message: '请输入姓名',
			trigger: 'blur'
		}
	],
	phone: [
		{
			required: true,
			message: '请输入手机号',
			trigger: 'blur'
		},
		{
			pattern: /^1[3-9]\d{9}$/,
			message: '请输入正确的手机号',
			trigger: 'blur'
		}
	]
};
onReady(() => {
	formRef.value.setRules(rules);
});
</script>
<style lang="scss" scoped>
.footer {
	background: #ffffff;
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
	position: fixed;
	left: 0;
	bottom: 0;

	.footer-content {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		background: #f6f6f6;
		box-sizing: border-box;
		padding: 0 30rpx;

		.btn {
			width: 45%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			background: linear-gradient(126deg, #09E3D9 0%, #1FAFEE 51%, #448EFF 100%);
		}
	}
}


.pages {
	height: 100vh;
	width: 100vw;
	display: flex;
	flex-direction: column;
	background: #f6f6f6;

	::v-deep .u-form-item {
		background: #ffffff;
		padding: 30rpx;
	}

	.footer-zhanwei {
		width: 750rpx;
		height: calc(constant(safe-area-inset-bottom) + 120rpx);
		height: calc(env(safe-area-inset-bottom) + 120rpx);
	}
}
</style>
