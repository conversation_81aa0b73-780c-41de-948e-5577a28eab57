<template>
	<view class="page">
		<u-navbar :custom-back="backIndex" :is-back="!isC" :border-bottom="true" :title="title"></u-navbar>
		<image class="ic" v-if="type == 1" src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/succ1.png">
		</image>
		<image class="ic" v-else src="https://obs.tuoyupt.com/miniprogram/childcare-subsidies/succ2.png"></image>
		<view class="t1" v-if="type == 1" @click="backIndex">预约成功！</view>
		<view class="t1" v-else-if="type == 2" @click="backIndex">您已签到成功！</view>
		<view class="t1" v-else-if="type == 3" @click="backIndex">反馈成功</view>
		<view class="bt2" v-if="!isC" @click="backIndex">返回</view>
		<view v-if="type == 2" class="info">
			<view class="to">签到信息</view>
			<view class="row">
				<view class="lt">姓名</view>
				<view class="lr">{{ signinfo.join_name }}</view>
			</view>
			<view class="row">
				<view class="lt">手机号</view>
				<view class="lr">{{ signinfo.phone }}</view>
			</view>
			<view class="row">
				<view class="lt">活动开始时间</view>
				<view class="lr">{{ enentinfo.start_time }}</view>
			</view>
		</view>
	</view>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
const type = ref(1);
const title = ref('预约成功');
const isC = ref(false);
const signinfo = ref({});
const enentinfo = ref({});
onLoad((option) => {
	if (option.isC == 1) {
		isC.value = true;
	}
	if (option.type) {
		type.value = option.type;
	}
	if (type.value == 2) {
		title.value = '签到成功';
		signinfo.value = uni.getStorageSync('signinfo');
		enentinfo.value = uni.getStorageSync('enentinfo');
	}
	if (type.value == 3) {
		title.value = '反馈成功';
	}
});

function backIndex() {
	uni.$emit('activeBack');
	if (type.value == 2 || type.value == 1) {
		uni.navigateBack();
	}
	if (type.value == 3) {
		uni.navigateBack();
	}
}
</script>
<style lang="scss" scoped>
.page {
	display: flex;
	flex-direction: column;
	width: 100vw;
	min-height: 100vh;
	align-items: center;

	.ic {
		width: 238rpx;
		height: 220rpx;
		position: relative;
		left: 12rpx;
		margin-top: 122rpx;
	}

	.t1 {
		width: 600rpx;
		font-weight: 600;
		font-size: 36rpx;
		color: #262937;
		line-height: 50rpx;
		text-align: center;
		margin-top: 58rpx;
	}

	.bt1 {
		width: 600rpx;
		height: 92rpx;
		background: linear-gradient(126deg, #09E3D9 0%, #1FAFEE 51%, #448EFF 100%);
		border-radius: 8rpx;
		font-weight: 500;
		font-size: 32rpx;
		color: #ffffff;
		line-height: 92rpx;
		text-align: center;
		font-style: normal;
		margin-top: 142rpx;
	}

	.bt2 {
		width: 600rpx;
		height: 92rpx;
		background: linear-gradient(126deg, #09E3D9 0%, #1FAFEE 51%, #448EFF 100%);
		border-radius: 8rpx;
		font-weight: 500;
		font-size: 32rpx;
		color: #ffffff;
		line-height: 92rpx;
		text-align: center;
		font-style: normal;
		margin-top: 48rpx;
	}

	.t2 {
		width: 600rpx;
		height: 40rpx;
		font-weight: 600;
		font-size: 28rpx;
		color: #080808;
		line-height: 40rpx;
		text-align: left;
		margin-top: 116rpx;
	}

	.t3 {
		width: 600rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #787c8d;
		line-height: 40rpx;
		margin-top: 32rpx;
	}

	.info {
		width: 690rpx;
		margin-top: 40rpx;
		border-top: 1rpx solid #ccc;
		padding-top: 20rpx;

		.to {
			font-family: 600;
			font-size: 32rpx;
		}

		.row {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			font-size: 24rpx;
			margin-top: 24rpx;
			color: #787c8d;
		}
	}
}
</style>
