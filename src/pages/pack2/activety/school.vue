<template>
  <view class="pages">
    <u-navbar :border-bottom="false" :background="{ backgroundColor: '#F6F6F6' }" title="搜索">
    </u-navbar>
    <view class="section">
      <view class="pop-top">
        <u-search :showLeft="false" focus @focus="onFocus" height="80" @clear="onClear" @search="search" shape="square"
          :show-action="false" placeholder="机构名称" v-model="keyword"></u-search>
        <u-button style="margin-left: 20rpx;" type="primary" @click="search">搜索</u-button>
      </view>
      <template v-if="showRes">
        <template v-if="list.length">
          <view class="item" @click="selectItem(item)" v-for="(item, index) in list" :key="index">
            <view class="name">{{ item.city_name }}-{{ item.area_name }}</view>
            <view class="name">{{ item.name }}</view>
          </view>
          <u-loadmore margin-top="40" :status="status" :load-text="loadText" />
        </template>
        <u-empty v-else margin-top="400" text="暂无相关机构"></u-empty>
      </template>
    </view>
    <view class="zhanwei"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onReachBottom, onLoad } from '@dcloudio/uni-app';
import request from "@/request.js";
let loadText = {
  loadmore: '轻轻上拉',
  loading: '努力加载中',
  nomore: '已经加载全部'
}
onLoad(() => {
  getList();
})
function onFocus() {
  showRes.value = false;
}
function onClear() {
  showRes.value = false;
  list.value = []
}
function search() {
  getList();
}
function selectItem(item) {
  uni.$emit("selectSchool", item)
  uni.navigateBack();
}
const showRes = ref(false);
let page = 1;
const keyword = ref("");
let list = ref([{}])
let status = ref('loadmore');
let hasMore = true;
function getList() {
  page = 1;
  uni.showLoading()
  request({
    url: `/api/parent/school/list`,
    method: 'get',
    data: {
      page,
      per_page: 10,
      order: "distance",
      name: keyword.value,
    }
  }).then(res => {
    list.value = res;
    if (res.length < 10) {
      hasMore = false;
      status.value = 'nomore';
    } else {
      hasMore = true;
      status.value = 'loadmore';
    }
    showRes.value = true;
    uni.hideLoading();
  })
}
onReachBottom(() => {
  if (hasMore) {
    status.value = 'loading';
    page++;
    request({
      url: `/api/parent/school/list`,
      method: 'get',
      data: {
        page,
        per_page: 10,
        order: "distance",
        name: keyword.value,
      }
    }).then(res => {
      list.value.push(...res);
      if (res.length < 10) {
        hasMore = false;
        status.value = 'nomore';
      } else {
        hasMore = true;
        status.value = 'loadmore';
      }
    })
  }
})

</script>

<style lang="scss" scoped>
.pages {
  background-color: #FFFFFF;

  .top-zhanwei {
    width: 750rpx;
    height: 190rpx;
  }

  .section {
    position: relative;
    z-index: 2;
    padding: 40rpx 30rpx 0;
    background: #FFFFFF;
    border-radius: 40rpx 40rpx 0 0;
    min-height: 60vh;

    .pop-top {
      width: 690rpx;
      height: 80rpx;
      border-radius: 24rpx;
      display: flex;
      flex-direction: row;
      align-items: center;

      .hr {
        width: 2rpx;
        height: 32rpx;
        background: #AEB0BC;
      }

      .warp {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        width: 188rpx;
        height: 80rpx;

        .icon1 {
          width: 24rpx;
          height: 24rpx;
          margin-right: 12rpx;
        }

        .text {
          width: 78rpx;
          font-size: 26rpx;
          font-weight: 500;
          color: #262937;
        }

        .icon2 {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }


  .item {
    width: 100%;
    display: flex;
    background: #fff;
    margin-top: 30rpx;
    flex-direction: row;
    border-bottom: 1rpx solid #eee;
    padding-bottom: 20rpx;

    .name {
      font-size: 32rpx;
      font-weight: 500;
      color: #262937;
      line-height: 44rpx;
      width: 50%;
    }
  }
}

.zhanwei {
  height: calc(env(safe-area-inset-bottom));
}

.mno-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20%;
  flex-direction: column;
  background: #fff;

  image {
    width: 160rpx;
    height: 160rpx;
    margin: 196rpx 0 24rpx;
  }

  .t1 {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #262937;
    margin-bottom: 20rpx;
  }

  .t2 {
    font-size: 28rpx;
    font-weight: 400;
    color: #aeb0bc;
    line-height: 44rpx;
  }

  .btn {
    width: 240rpx;
    height: 80rpx;
    background: #1975FF;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    margin-top: 48rpx;
    margin-bottom: 100rpx;
  }
}

.top {
  width: 750rpx;
  height: 400rpx;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 1;

  .backimg {
    width: 750rpx;
    height: 420rpx;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.warp {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 24rpx;

  .icon1 {
    width: 40rpx;
    height: 40rpx;
    margin-right: 14rpx;
  }

  .text {
    font-size: 34rpx;
    font-weight: 600;
    color: #000000;
  }

  .icon2 {
    width: 32rpx;
    height: 32rpx;
  }
}
</style>