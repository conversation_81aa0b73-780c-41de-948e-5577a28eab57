<template>
	<u-navbar :border-bottom="false" title-color="#FFFFFF" back-icon-color="#FFFFFF" :is-back="isback" immersive
		:background="{ background: 'rgba(255, 255,255, 0)' }" title="活动签到"></u-navbar>
	<image src="/static/img/signtop.png" class="signtop"></image>
	<view class="body">
		<view class="pages">
			<view class="content">
				<view class="t1">{{ info.title }}</view>
				<view class="t2">请根据您预约时填写的信息完成签到提交</view>
				<view class="form">
					<u-form :model="formData" ref="formRef" label-position="top">
						<u-form-item label="您预约时填报的姓名" required prop="join_name">
							<u-input v-model="formData.join_name" placeholder="请输入姓名" />
						</u-form-item>
						<u-form-item label="您预约时填报的手机号" required prop="phone">
							<u-input v-model="formData.phone" placeholder="请输入手机号" />
						</u-form-item>
					</u-form>
				</view>
				<u-button class="btn" type="primary" @click="submit">签到</u-button>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
import request from '@/request.js';
const formData = ref({
	phone: '',
	join_name: ''
});

const info = ref({
	title: ""
});
const formRef = ref(null);
const rules = {
	phone: [
		{
			required: true,
			message: '请输入联系方式',
			trigger: 'blur'
		}
	],
	join_name: [
		{
			required: true,
			message: '请输入预约人姓名',
			trigger: 'blur'
		}
	]
};
onReady(() => {
	formRef.value.setRules(rules);
});
const isback = ref(false);
onLoad((option) => {
	info.value = uni.getStorageSync('enentinfo');
	isback.value = true;
});
function submit() {
	formRef.value.validate((valid) => {
		if (valid) {
			uni.showLoading();
			request({
				url: '/api/parent/schoolevent/sign',
				method: 'post',
				data: {
					event_id: info.value.id,
					...formData.value
				}
			}).then((res) => {
				uni.hideLoading();
				if (res) {
					uni.setStorageSync('signinfo', formData.value);
					uni.redirectTo({
						url: './success?isC=1&type=2'
					});
				}
			});
		}
	});
}
</script>
<style lang="scss" scoped>
.signtop {
	width: 750rpx;
	height: 574rpx;
	position: fixed;
	left: 0;
	top: 0;
}

.footer {
	background: #ffffff;
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
	position: fixed;
	left: 0;
	bottom: 0;

	.footer-content {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		background: #ffffff;
		box-sizing: border-box;
		padding: 0 30rpx;

		.btn {
			width: 45%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
		}
	}
}

.body {
	height: 100vh;
	width: 100vw;
	background: #f6f6f6;
}

.pages {
	height: 100vh;
	width: 100vw;
	padding: 0 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	position: fixed;
	left: 0;
	top: 0;

	.t1 {
		text-align: center;
		font-size: 32rpx;
		font-weight: 600;
		line-height: 60rpx;
		color: #FFFFFF;
		margin-top: 180rpx;
	}

	.t2 {
		text-align: center;
		font-size: 28rpx;
		line-height: 60rpx;
		color: #FFFFFF;
	}

	.form {
		width: 690rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
	}

	.btn {
		margin-top: 40rpx;
		background: linear-gradient(126deg, #09E3D9 0%, #1FAFEE 51%, #448EFF 100%);
	}
}
</style>
