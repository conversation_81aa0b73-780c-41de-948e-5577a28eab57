<template>
	<view class="pages">
		<u-navbar :border-bottom="false" :background="{ backgroundColor: '#FFFFFF' }" title="活动详情"></u-navbar>
		<view class="top">
			<image v-if="info.act_from == 2" class="form" src="/static/img/actype1.png">
			</image>
			<image v-else class="form" src="/static/img/actype2.png">
			</image>
			{{ info.title }}
		</view>
		<view class="hr"></view>
		<view class="row">
			<view class="t2">
				<u-icon name="clock" size="32" color="#AEB0BC"></u-icon>
				活动时间
			</view>
		</view>
		<view class="row">
			<view class="t1">
				<text>{{ info.start_time }}~{{ info.end_time }}</text>
			</view>
		</view>
		<view class="row">
			<view class="t2"> <u-icon name="map" size="32" color="#AEB0BC"></u-icon>活动地点</view>
		</view>
		<view class="row">
			<view class="t1">
				{{ info.address }}
			</view>
		</view>
		<view class="row">
			<view class="t2"><u-icon name="account-fill" size="32" color="#AEB0BC"></u-icon>讲师信息</view>
		</view>
		<view class="row">
			<text class="t1">{{ info.instructor ? info.instructor : '暂无' }}</text>
		</view>
		<view class="row" v-if="info.instructor_desc">
			<text class="t1"> <u-icon name="integral" size="32" color="#1975FF"></u-icon>{{ info.instructor_desc ?
				info.instructor_desc : '暂无' }}</text>
		</view>
		<view class="row">
			<view class="t2"><u-icon name="plus-people-fill" size="32" color="#AEB0BC"></u-icon>发布方</view>
		</view>
		<view class="row">
			<text class="t1"> {{ info.act_from == 2 ? info.abbreviation : info.organizations_name }}</text>
		</view>
		<view class="hr"></view>
		<view class="row">
			<view class="t3">活动介绍</view>
		</view>
		<view class="content">
			<u-parse :tag-style="richStyle" :html="info.content" :selectable="true"
				:show-with-animation="true"></u-parse>
		</view>

		<template v-if="info?.act_imgs?.length">
			<view class="row">
				<view class="t2">活动动态</view>
			</view>
			<view>
				<u-waterfall class="media-list" v-model="info.act_imgs">
					<template v-slot:left="{ leftList }">
						<view v-for="(item, index) in leftList" :key="index">
							<view class="media" @click="preview(item)">
								<video class="cover"
									v-if="item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.ogg')"></video>
								<image class="cover" v-else :src="item" />
							</view>
						</view>
					</template>
					<template v-slot:right="{ rightList }">
						<view v-for="(item, index) in rightList" :key="index">
							<view class="media" @click="preview(item)">
								<video class="cover" :src="item"
									v-if="item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.ogg')"></video>
								<image class="cover" v-else :src="item" />
							</view>
						</view>
					</template>
				</u-waterfall>
			</view>
		</template>
		<Preview :previewData="previewData" />
		<view class="footer-zhanwei"></view>
	</view>
	<view class="footer">
		<view class="footer-content">
			<template v-if="info.status == 0 || info.status == 1">
				<u-button class="btn" v-if="info.join_type == 0" type="primary" @click="goyuyue">预约参加活动</u-button>
				<u-button class="btn" v-if="info.join_type == 1" type="primary" @click="quxiao">取消预约</u-button>
				<u-button class="btn" v-if="info.join_type == 1 && info.status == 1" type="primary" @click="qiandao">签到</u-button>
				<u-button class="btn btn2" type="primary" v-if="info.join_type == 2">已签到</u-button>
			</template>
			<template v-if="info.status == 2">
				<template v-if="info.join_type == 2">
					<u-button class="btn" type="primary" v-if="info.is_remark == 0" @click="gofeed">发布活动反馈</u-button>
					<template v-else>
						<u-button class="btn" type="primary" @click="editfeed">修改反馈</u-button>
						<u-button class="btn" type="primary" @click="delfeed">删除反馈</u-button>
					</template>
				</template>
			</template>
		</view>
	</view>
</template>
<script setup>
import { ref, reactive } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import request from '@/request.js';
import { useUserStore } from '@/stores/userInfo.js';
import Preview from '@/components/preview.vue';
import { str2arr } from '@/utils/dataHandle.js';
let previewData = reactive({
	show: false,
	type: 'image',
	video_url: '',
	image_list: []
});
function preview(item) {
	if (item.type == '0') {
		previewData.type = 'image';
		previewData.image_list = str2arr(item.image);
	} else if ((item.type = '1')) {
		previewData.type = 'video';
		previewData.video_url = item.video_url;
	}
	previewData.show = true;
}
const userInfo = useUserStore();
let id = '';
const info = ref({
	id: 0,
	title: '',
	range: 1,
	type: '',
	content: ``,
	instructor: '',
	instructor_desc: '',
	imgs: '',
	status: -1,
	abbreviation: '',
	publish_time: '',
	areas: '',
	address: '',
	start_time: '',
	end_time: '',
	register_start: '',
	register_end: '',
	sign_start: '',
	sign_end: '',
	province: '',
	publish: false,
	city: '',
	area: '',
	street: ''
});
const mediaList = ref([]);
onLoad((option) => {
	id = option.id;
	getInfo();

	uni.$on('activeBack', getInfo);
});
onUnload(() => {
	uni.$off('activeBack', getInfo);
});
function getInfo() {
	uni.showLoading();
	request({
		url: '/api/parent/schoolevent/eventform',
		data: {
			id
		}
	}).then((res) => {
		uni.hideLoading();
		res.act_imgs = res.act_imgs ? JSON.parse(res.act_imgs) : [];
		info.value = res;
	});
}

function delfeed() {
	uni.showModal({
		title: '提示？',
		content: '确认要删除反馈吗？',
		success(res) {
			if (res.confirm) {
				uni.showLoading();
				request({
					url: '/api/parent/schoolevent/delfeedback',
					method: 'post',
					data: {
						join_id: info.value.join_id
					}
				}).then((res) => {
					uni.hideLoading();
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
					getInfo();
				});
			}
		}
	});
}
function goyuyue() {
	uni.setStorageSync('enentinfo', info.value);
	uni.navigateTo({
		url: `./signup?id=${id}`
	});
}
function editfeed() {
	uni.setStorageSync('enentinfo', info.value);
	uni.navigateTo({
		url: `./feed?id=${id}&type=edit`
	});
}
function gofeed() {
	uni.setStorageSync('enentinfo', info.value);
	uni.navigateTo({
		url: `./feed?id=${id}`
	});
}
function qiandao() {
	uni.setStorageSync('enentinfo', info.value);
	uni.navigateTo({
		url: `./signin?type=1`
	});
}
function quxiao() {
	uni.showModal({
		title: '提示？',
		content: '确认要取消预约吗？',
		success(res) {
			if (res.confirm) {
				uni.showLoading();
				request({
					url: '/api/parent/schoolevent/delete',
					method: 'post',
					data: {
						id: info.value.join_id
					}
				}).then((res) => {
					uni.hideLoading();
					uni.showToast({
						title: '取消成功',
						icon: 'none',
						duration: 1500
					});
					setTimeout(getInfo, 2000)
				});
			}
		}
	});
}
</script>
<style lang="scss" scoped>
.footer {
	background: #ffffff;
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
	position: fixed;
	left: 0;
	bottom: 0;

	.footer-content {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		background: #ffffff;
		box-sizing: border-box;
		padding: 0 30rpx;
		justify-content: center;

		.btn {
			width: 50%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			background: linear-gradient(126deg, #09E3D9 0%, #1FAFEE 51%, #448EFF 100%);
			font-size: 32rpx;
		}

		.btn2 {
			opacity: 0.75;
			color: #333333;
		}
	}
}

.media {
	flex: 0 1 336rpx;
	width: 336rpx;
	margin-top: 40rpx;
	overflow: hidden;
	position: relative;

	.cover {
		width: 336rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #f6f6f6;
		max-height: 392rpx;
		text-align: center;

		image {
			width: 336rpx;
			display: block;
		}
	}

	.name {
		margin-top: 16rpx;
		font-size: 28rpx;
		color: #262937;
		line-height: 36rpx;
		letter-spacing: 1px;
	}
}

.pages {
	height: 100vh;
	width: 100vw;
	padding-bottom: 20rpx;
	overflow-y: scroll;

	.top {
		padding: 0 30rpx;
		font-weight: 600;
		font-size: 32rpx;
		color: #262937;
		line-height: 40rpx;
		margin-top: 20rpx;

		.form {
			width: 124rpx;
			height: 40rpx;
			margin-right: 8rpx;
		}
	}

	.hr {
		width: 690rpx;
		background: #999999;
		height: 1rpx;
		margin: 20rpx auto;
	}

	.title {
		padding: 20rpx 30rpx;
		font-size: 48rpx;
		text-align: center;
	}

	.row {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		padding: 0 30rpx;
		margin-bottom: 20rpx;

		.t2 {
			color: #AEB0BC;
		}

		.t1 {
			color: #333333;
		}

		.t3 {
			color: #333333;
			font-size: 32rpx;
		}
	}

	.content {
		padding: 0rpx 30rpx;
		font-size: 32rpx;
		margin-bottom: 20rpx;
	}

	.footer-zhanwei {
		width: 750rpx;
		height: calc(constant(safe-area-inset-bottom) + 120rpx);
		height: calc(env(safe-area-inset-bottom) + 120rpx);
	}

	.value {
		padding: 10rpx 30rpx 20rpx 50rpx;
		font-size: 32rpx;

		.instructor {
			margin-left: 20rpx;
			margin-right: 20rpx;
		}
	}

	.swiper-wrap {
		height: 420rpx;
	}

	.swiper-item {
		display: block;
		height: 420rpx;
		text-align: center;

		.video,
		.image {
			height: 100%;
			display: block;
			width: 100%;
		}
	}
}
</style>
