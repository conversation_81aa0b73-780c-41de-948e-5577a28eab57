<template>
	<view class="pages">
		<u-navbar :border-bottom="false" immersive :background="{ background: 'rgba(255, 255,255, 0)' }"
			title="活动反馈"></u-navbar>
		<view class="topback">

		</view>
		<view class="content">
			<view class="stat">评价内容
				<image class="i" src="/static/img/satr.png"></image>
			</view>
			<u-form :model="form" ref="form1" label-position="top">
				<u-form-item label="是否匿名" required>
					<u-radio-group v-model="form.is_name">
						<u-radio name="0">否</u-radio>
						<u-radio name="1">是</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="评价反馈内容" required>
					<u-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
				</u-form-item>
				<u-form-item label="上传图片">
					<u-upload :fileList="fileList" upload-text="上传实拍照片" width="600rpx" height="200rpx" max-count="10"
						@on-list-change="imgChange" multiple deletable :auto-upload="false"></u-upload>
				</u-form-item>
			</u-form>
		</view>

	</view>
	<view class="footer">
		<view class="footer-content">
			<u-button class="btn" type="primary" @click="back">取消</u-button>
			<u-button class="btn" type="primary" @click="submit">提交反馈</u-button>
		</view>
	</view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '@/request.js';
import { OBSupload } from '@/utils/obs/obs-upload.js';
let id = '';
let info = {};
const fileList = ref([])
onLoad((option) => {
	id = option.id;
	info = uni.getStorageSync('enentinfo');
	if (option.type == 'edit') {
		form.value.remark = info.remark;
		form.value.is_name = info.is_name.toString();
		let imgs = info.remark_act_imgs	 ? info.remark_act_imgs.split(',') : [];
		fileList.value = imgs.map(item => {
			return {
				url: decodeURIComponent(item)
			}
		})
	}
});
function back() {
	uni.navigateBack();
}
async function submit() {
	if (form.value.remark == '') {
		uni.showToast({
			title: '请输入反馈内容',
			icon: 'none'
		});
		return;
	}
	uni.showLoading();
	let imgs = [];
	if (imgList.length) {
		let requestArr = []
		imgList.filter(item => item.progress == 0).forEach(item => {
			let fileName = item.file.name;
			let fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
			requestArr.push(OBSupload(item.url, fileExtension, fileName))
		})
		let flag = false;
		try {
			imgs = await Promise.all(requestArr);
		} catch (error) {
			flag = true;
			uni.showToast({
				title: "图片上传失败",
				icon: "error"
			})
		}
		if (flag) {
			uni.hideLoading();
			return;
		}
	}
	request({
		url: '/api/parent/schoolevent/feedback',
		method: 'get',
		data: {
			event_id: info.id,
			join_id: info.join_id,
			is_name: form.value.is_name,
			remark: form.value.remark,
			act_imgs: imgs.join(',')
		}
	}).then((res) => {
		uni.hideLoading();
		if (res) {
			uni.showToast({
				title: '提交成功',
				icon: 'none'
			});
			setTimeout(() => {
				uni.redirectTo({
					url: './success?type=3'
				});
			}, 2000)
		}
	});
}
let imgList = [];
function imgChange(e) {
	imgList = e;
}
const form = ref({
	remark: '',
	is_name: '0'
});
</script>
<style lang="scss" scoped>
.footer {
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
	position: fixed;
	left: 0;
	bottom: 0;

	.footer-content {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		box-sizing: border-box;
		padding: 0 30rpx;

		.btn {
			width: 45%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			background: linear-gradient(126deg, #09E3D9 0%, #1FAFEE 51%, #448EFF 100%);
		}
	}
}

.pages {
	height: 100vh;
	width: 100vw;
	padding: 0 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	background: #F6F6F6;
	position: relative;

	.topback {
		width: 750rpx;
		left: 0;
		top: 0;
		position: absolute;
		height: 306rpx;
		background: linear-gradient(180deg, #CDDBFF 0%, rgba(255, 255, 255, 0) 100%);
	}

	.content {
		width: 690rpx;
		background: #FFFFFF;
		border-radius: 0rpx 32rpx 32rpx 32rpx;
		position: relative;
		z-index: 2;
		margin-top: 194rpx;
		padding: 30rpx;

		.stat {
			position: absolute;
			width: 180rpx;
			height: 72rpx;
			background: linear-gradient(270deg, rgba(196, 255, 217, 0.19) 0%, #A1F7FF 100%);
			border-radius: 16rpx 0rpx 0rpx 0rpx;
			left: 0;
			top: -72rpx;
			font-weight: 600;
			font-size: 32rpx;
			color: #262937;
			line-height: 72rpx;
			text-align: center;

			.i {
				width: 34rpx;
				height: 34rpx;
				position: absolute;
				top: -7rpx;
				right: -7rpx;
			}
		}
	}

	.footer-zhanwei {
		width: 750rpx;
		height: calc(constant(safe-area-inset-bottom) + 120rpx);
		height: calc(env(safe-area-inset-bottom) + 120rpx);
	}
}
</style>
