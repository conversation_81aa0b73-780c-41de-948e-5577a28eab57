<template>
	<view class="pages">
		<u-navbar :border-bottom="false" :background="{ backgroundColor: '#FFFFFF' }" title="托育活动"></u-navbar>
		<view class="tabs">
			<view class="tab" :class="{ ac: my == '' }" @click="setMy('')">全部</view>
			<view class="tab" :class="{ ac: my == 1 }" @click="setMy(1)">我预约的</view>
			<view class="tab" :class="{ ac: my == 2 }" @click="setMy(2)">参与记录</view>
		</view>
		<view class="filet">
			<view class="left">
				<view @click="selectTime" class="select">
					{{ date.month ? `${date.year}-${date.month}` : '活动时间' }}
					<u-icon name="arrow-down-fill" size="24" color="#262937"></u-icon>
				</view>
				<view @click="show = true" class="select">
					{{ type_name ? type_name : '活动类型' }}
					<u-icon name="arrow-down-fill" size="24" color="#262937"></u-icon>
				</view>
			</view>
			<view class="hr"></view>
			<view class="search">
				<view class="input">
					<u-search round clearabled shape="square" @clear="getList" @search="getList" @custom="getList"
						animation show-action placeholder="请输入活动名称" v-model="keyword"></u-search>
				</view>
			</view>
		</view>
		<scroll-view class="scoll" scroll-y v-if="list.length" @scrolltolower="scrolltolower">
			<view class="sitem" v-for="item in list" :key="item.id" @click="goDetail(item)">
				<view class="content">
					<view class="row">
						<image v-if="item.act_from == 2" class="form" src="/static/img/actype1.png">

						</image>
						<image v-else class="form" src="/static/img/actype2.png">

						</image>
						<view class="t1">
							{{ item.title }}
						</view>
					</view>
					<view class="t2">
						<u-icon name="map" size="32" color="#787C8D"></u-icon>
						{{ item.address }}
					</view>
					<view class="t2">
						<u-icon name="clock" size="32" color="#787C8D"></u-icon>
						{{ item.start_time }}-{{ item.end_time }}
					</view>
					<view class="t4">
						{{ id2name(item.status, status) }}
					</view>

				</view>
				<view class="footer">
					<view class="date">
						{{ dayjs(item.start_time).format('MM/DD') }}
					</view>
					<view>
						<text>查看详情</text>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
			<u-loadmore :status="more_status" />
			<br />
		</scroll-view>
		<u-empty text="暂无活动" v-else></u-empty>
	</view>
	<u-picker mode="time" v-model="timeShow" :params="params" @confirm="timeConfirm"></u-picker>
	<u-select v-model="show" :list="typeList" value-name="id" label-name="category_name" @confirm="confirm"></u-select>
</template>
<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '@/request.js';
import dayjs from 'dayjs';
import { useUserStore } from '@/stores/userInfo.js';
const userInfo = useUserStore();
onLoad(() => {
	getSelectList();
	getList();
});
function id2name(id, arr, key = 'name') {
	if (id === '' || typeof id == 'undefined') {
		return '';
	}
	if (arr.length == 0) {
		return '';
	}
	let newarr = arr.filter((e) => {
		return e.id == id;
	});
	if (newarr.length) {
		return newarr[0][key];
	} else {
		return '';
	}
}
const status = [
	{ id: 0, name: '未开始' },
	{ id: 1, name: '进行中' },
	{ id: 2, name: '已结束' },
	{ id: -1, name: '全部' }
];
let now = new Date();
const date = ref({
	year: now.getFullYear(),
	month: ''
});
let page = 1;
let total = 0;
const more_status = ref('loadmore');
function getList() {
	page = 1;
	uni.showLoading();
	request({
		url: '/api/parent/schoolevent/list',
		data: {
			title: keyword.value,
			per_page: 10,
			page,
			school_id: userInfo.school_id || 8,
			year: date.value.year,
			select_date: date.value.month ? `${date.value.year}-${date.value.month}` : "",
			join_type: my.value,
			category_id: typeid.value
		}
	}).then((res) => {
		uni.hideLoading();
		list.value = res.data;
		total = res.total;
		if (list.value.length < total) {
			more_status.value = 'loadmore';
		} else {
			more_status.value = 'nomore';
		}
	});
}
function scrolltolower() {
	if (more_status.value == 'loadmore') {
		page++;
		more_status.value = 'loading';
		uni.showLoading();
		request({
			url: '/api/parent/schoolevent/list',
			data: {
				title: keyword.value,
				per_page: 10,
				page: page,
				school_id: userInfo.school_id || 8,
				year: date.value.year,
				select_date: date.value.month ? `${date.value.year}-${date.value.month}` : "",
				join_type: my.value,
				category_id: typeid.value
			}
		}).then((res) => {
			uni.hideLoading();
			list.value.push(...res.data);
			total = res.total;
			if (list.value.length < total) {
				more_status.value = 'loadmore';
			} else {
				more_status.value = 'nomore';
			}
		});
	}
}
const type_name = ref('');
const typeid = ref('');
const typeList = ref([]);
function getSelectList() {
	request({
		url: '/api/parent/schoolevent/select'
	}).then((res) => {
		typeList.value = [{ id: "", category_name: "全部" }, ...res.category];
	});
}

function setMy(t) {
	if (my.value !== t) {
		my.value = t;
		getList();
	}
	if (my.value !== '') {
		if (!uni.getStorageSync('token')) {
			uni.redirectTo({
				url: '/pages_work/login/main'
			});
		}
	}
}

const my = ref(0);

const keyword = ref('');
const list = ref([]);
const show = ref(false);
function selectTime() {
	timeShow.value = true;
}
const timeShow = ref(false);
const params = {
	year: true,
	month: true,
	day: false,
	hour: false,
	minute: false,
	second: false
};
function timeConfirm(e) {
	timeShow.value = false;
	date.value.year = e.year;
	date.value.month = e.month;
	getList();
}
function confirm(e) {
	show.value = false;

	typeid.value = e[0].value;
	if (typeid.value == "") {
		type_name.value = "";
	} else {
		type_name.value = e[0].label;
	}
	getList();
}
function goDetail(item) {
	uni.navigateTo({
		url: `./detail?id=${item.id}`
	});
}
</script>
<style lang="scss" scoped>
.pop {
	padding: 30rpx;

	.t1 {
		font-size: 32rpx;
		font-weight: 600;
	}

	.btn {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		height: 60rpx;
		margin-top: 40rpx;
		align-items: center;
		border-radius: 30rpx;
		padding: 0 30rpx;

		.b {
			width: 200rpx;
		}
	}

	.time {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		border: 1rpx solid #ccc;
		height: 60rpx;
		margin-top: 20rpx;
		align-items: center;
		border-radius: 30rpx;
		padding: 0 60rpx;

		.timep {
			width: 40%;
			height: 60rpx;
			text-align: center;
			line-height: 60rpx;
			font-size: 24rpx;
		}
	}

	.types {
		margin: 20rpx 0;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		flex-wrap: wrap;

		.type {
			width: 30%;
			text-align: center;
			border: 1rpx solid #ccc;
			height: 45rpx;
			line-height: 45rpx;
			border-radius: 22rpx;
			margin-bottom: 12rpx;
		}

		.actype {
			background: #39ade2;
			color: #ffffff;
		}
	}
}

.pages {
	height: 100vh;
	width: 100vw;
	display: flex;
	flex-direction: column;

	.scoll {
		flex: 1;
		min-height: 50vh;
		width: 100%;
		background: #f6f6f6;

		.sitem {
			width: 690rpx;
			margin: 12rpx auto;
			position: relative;
			background: #ffffff;
			border-radius: 24rpx;

			.content {
				padding: 30rpx 30rpx 10rpx 30rpx;

				.row {
					display: flex;
					flex-direction: row;
					align-items: center;
				}

				.form {
					width: 124rpx;
					height: 40rpx;
					margin-right: 8rpx;
				}





				.t1 {
					font-size: 32rpx;
					font-weight: 600;
					margin-top: 4rpx;
					max-width: 380rpx;
				}

				.t2 {
					font-size: 25rpx;
					margin-top: 8rpx;
				}

				.t4 {
					width: 104rpx;
					height: 40rpx;
					border-radius: 6rpx;
					border: 2rpx solid #AECEFF;
					text-align: center;
					line-height: 40rpx;
					font-size: 24rpx;
					margin-top: 12rpx;
				}
			}

			.footer {
				border-top: 1rpx solid #ccc;
				width: 630rpx;
				margin: 12rpx auto 0;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				padding: 12rpx 0;
				font-size: 25rpx;

				.date {
					font-weight: 600;
				}
			}
		}
	}



	.filet {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 100rpx;
		width: 100%;
		align-items: center;
		padding: 0rpx 30rpx;

		.hr {
			background: #c6c6c6;
			width: 1rpx;
			height: 50%;
			margin: 0 12rpx;
		}

		.left {
			width: 60%;
			display: flex;
			flex-direction: row;

			.select {
				width: 184rpx;
				height: 60rpx;
				background: #F6F6F6;
				border-radius: 30rpx;
				flex-shrink: 0;
				line-height: 60rpx;
				text-align: center;
				color: #262937;
			}
		}

		.search {
			display: flex;
			flex-direction: row;


			.input {
				flex: 1;
			}

			.icon {
				width: 32rpx;
				height: 32rpx;
				margin-top: 16rpx;
				margin-left: 24rpx;
			}
		}
	}

	.tabs {
		display: flex;
		flex-direction: row;
		width: 100%;
		height: 60rpx;
		justify-content: space-around;
		padding: 0 60rpx;

		.tab {
			color: #AEB0BC;
			width: 160rpx;
			height: 60rpx;
			text-align: center;
			line-height: 60rpx;
			font-size: 32rpx;
		}

		.ac {
			color: #111111;
		}
	}
}

.picker {
	padding: 16rpx 30rpx;
}
</style>
