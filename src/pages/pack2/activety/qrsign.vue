<template>
	<view class="pages">
		<u-navbar :border-bottom="false" :is-back="false" :background="{ backgroundColor: '#FFFFFF' }"
			title="活动详情"></u-navbar>
		<view class="title">{{ info.title }}</view>
		<view class="row">
			<view class="t2">活动时间</view>
		</view>
		<view class="row">
			<view class="t1">
				<u-icon name="clock" size="32" color="#39ade2"></u-icon>
				<text>{{ info.start_time }}~{{ info.end_time }}</text>
			</view>
		</view>
		<view class="row">
			<view class="t2">活动介绍</view>
		</view>
		<view class="content">
			<u-parse :tag-style="richStyle" :html="info.content" :selectable="true"
				:show-with-animation="true"></u-parse>
		</view>
		<view class="row">
			<view class="t2">讲师信息</view>
		</view>
		<view class="value">
			<u-icon name="account-fill" size="32" color="#39ade2"></u-icon>
			<text class="instructor">{{ info.instructor ? info.instructor : '暂无' }}</text>
			<u-icon name="integral" size="32" color="#39ade2"></u-icon>
			<text class="instructor">{{ info.instructor_desc ? info.instructor_desc : '暂无' }}</text>
		</view>
		<view class="row">
			<view class="t2">活动地点</view>
		</view>
		<view class="value">
			{{ info.address }}
		</view>
		<view class="row">
			<view class="t2">发布方</view>
		</view>
		<view class="value">
			{{ info.organizations_name }}
		</view>
		<template v-if="info?.act_imgs?.length">
			<view class="row">
				<view class="t2">活动动态</view>
			</view>
			<view>
				<u-waterfall class="media-list" v-model="info.act_imgs">
					<template v-slot:left="{ leftList }">
						<view v-for="(item, index) in leftList" :key="index">
							<view class="media" @click="preview(item)">
								<video class="cover"
									v-if="item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.ogg')"></video>
								<image class="cover" v-else :src="item" />
							</view>
						</view>
					</template>
					<template v-slot:right="{ rightList }">
						<view v-for="(item, index) in rightList" :key="index">
							<view class="media" @click="preview(item)">
								<video class="cover" :src="item"
									v-if="item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.ogg')"></video>
								<image class="cover" v-else :src="item" />
							</view>
						</view>
					</template>
				</u-waterfall>
			</view>
		</template>
		<Preview :previewData="previewData" />
		<view class="footer-zhanwei"></view>
	</view>
	<view class="footer">
		<view class="footer-content">
			<template v-if="info.status == 1">
				<u-button class="btn" type="primary" @click="goyuyue">签到</u-button>
			</template>
			<template v-else>
				<view class="btn">不在签到时间</view>
			</template>
		</view>
	</view>
</template>
<script setup>
import { ref, reactive } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import request from '@/request.js';
import Preview from '@/components/preview.vue';
import { str2arr } from '@/utils/dataHandle.js';
let previewData = reactive({
	show: false,
	type: 'image',
	video_url: '',
	image_list: []
});
function preview(item) {
	if (item.type == '0') {
		previewData.type = 'image';
		previewData.image_list = str2arr(item.image);
	} else if ((item.type = '1')) {
		previewData.type = 'video';
		previewData.video_url = item.video_url;
	}
	previewData.show = true;
}
let id = '';
const info = ref({
	id: 0,
	title: '',
	range: 1,
	type: '',
	content: ``,
	instructor: '',
	instructor_desc: '',
	imgs: '',
	status: -1,
	abbreviation: '',
	publish_time: '',
	areas: '',
	address: '',
	start_time: '',
	end_time: '',
	register_start: '',
	register_end: '',
	sign_start: '',
	sign_end: '',
	province: '',
	publish: false,
	city: '',
	area: '',
	street: ''
});
const mediaList = ref([]);
onLoad((option) => {
	if (option.id) {
		id = option.id;
		getInfo();
	} else {
		alert('二维码错误');
	}
	uni.$on('activeBack', getInfo);
});
onUnload(() => {
	uni.$off('activeBack', getInfo);
});
function getInfo() {
	uni.showLoading();
	request({
		url: '/api/auth/eventform',
		data: {
			id
		}
	}).then((res) => {
		uni.hideLoading();
		if (res) {
			res.act_imgs = res.act_imgs ? JSON.parse(res.act_imgs) : [];
			info.value = res;
		} else {
			uni.showToast({
				title: '二维码过期',
				icon: 'none'
			});
		}
	});
}
const feedInfo = ref({
	img_url: '',
	message: ''
});
const showImg = (index) => {
	uni.previewImage({
		current: index,
		urls: feedInfo.value.img_url.split(','),
		indicator: 'number',
		longPressActions: {
			itemList: ['发送给朋友', '保存图片', '收藏']
		}
	});
};
function goyuyue() {
	let baseurl = location.href.split('/#/')[0] + '/#';
	uni.setStorageSync('enentinfo', info.value);
	let stype = 0;
	if (info.value.act_to_type_arr.includes('0')) {
		stype = 0;
	} else if (info.value.act_to_type_arr.length == 1 && info.value.act_to_type_arr.includes('1')) {
		stype = 1;
	} else {
		stype = 2;
	}
	if (stype == 0) {
		uni.showActionSheet({
			itemList: ['机构签到', '家长签到'],
			success: function (res) {
				if (res.tapIndex == 0) {
					location.href = `/teacher/#/pages_work/login/main?redirect=${encodeURIComponent(baseurl + '/pages/pack/activety/detail?id=' + id)}`;
				} else if (res.tapIndex == 1) {
					if (uni.getStorageSync('token')) {
						uni.navigateTo({
							url: `./signin?id=${id}`
						});
					} else {
						uni.redirectTo({
							url: `/pages_work/login/main?redirect=${encodeURIComponent('/pages/pack2/activety/detail?id=' + id)}&isuni=1`
						});
					}
				}
			},
			fail: function (res) {
				console.log(res.errMsg);
			}
		});
	} else if (stype == 1) {
		location.href = `/teacher/#/pages_work/login/main?redirect=${encodeURIComponent(baseurl + '/pages/pack/activety/detail?id=' + id)}`;
	} else {
		if (uni.getStorageSync('token')) {
			uni.navigateTo({
				url: `./signin?id=${id}`
			});
		} else {
			uni.redirectTo({
				url: `/pages_work/login/main?redirect=${encodeURIComponent('/pages/pack2/activety/detail?id=' + id)}&isuni=1`
			});
		}
	}
}
</script>
<style lang="scss" scoped>
.footer {
	background: #ffffff;
	width: 750rpx;
	height: calc(constant(safe-area-inset-bottom) + 120rpx);
	height: calc(env(safe-area-inset-bottom) + 120rpx);
	position: fixed;
	left: 0;
	bottom: 0;

	.footer-content {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		background: #ffffff;
		box-sizing: border-box;
		padding: 0 30rpx;
		justify-content: center;

		.btn {
			width: 50%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
		}
	}
}

.media {
	flex: 0 1 336rpx;
	width: 336rpx;
	margin-top: 40rpx;
	overflow: hidden;
	position: relative;

	.cover {
		width: 336rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #f6f6f6;
		max-height: 392rpx;
		text-align: center;

		image {
			width: 336rpx;
			display: block;
		}
	}

	.name {
		margin-top: 16rpx;
		font-size: 28rpx;
		color: #262937;
		line-height: 36rpx;
		letter-spacing: 1px;
	}
}

.pages {
	height: 100vh;
	width: 100vw;
	padding-bottom: 20rpx;
	overflow-y: scroll;

	.title {
		padding: 20rpx 30rpx;
		font-size: 48rpx;
		text-align: center;
	}

	.row {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		padding: 0 30rpx;
		margin-bottom: 20rpx;

		.t2 {
			color: #333333;
			font-weight: 600;
		}

		.t1 {
			color: #999999;
		}
	}

	.content {
		padding: 0rpx 30rpx;
		font-size: 32rpx;
		margin-bottom: 20rpx;
	}

	.footer-zhanwei {
		width: 750rpx;
		height: calc(constant(safe-area-inset-bottom) + 120rpx);
		height: calc(env(safe-area-inset-bottom) + 120rpx);
	}

	.value {
		padding: 10rpx 30rpx 20rpx 50rpx;
		font-size: 32rpx;

		.instructor {
			margin-left: 20rpx;
			margin-right: 20rpx;
		}
	}

	.swiper-wrap {
		height: 420rpx;
	}

	.swiper-item {
		display: block;
		height: 420rpx;
		text-align: center;

		.video,
		.image {
			height: 100%;
			display: block;
			width: 100%;
		}
	}
}
</style>
