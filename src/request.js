// import { baseUrl } from "@/config/index.js";
const request = (option) => {
  // uni.showLoading({
  //   mask: true,
  //   title: "加载中",
  // });
  let header = option.header || {
    "content-type": "application/x-www-form-urlencoded",
    "minifrom": "jingxue"
  };

  if (uni.getStorageSync("token")) {
    header["Authorization"] = "Bearer " + uni.getStorageSync("token");
  }
  return new Promise((resolve, reject) => {
    uni
      .request({
        method: option.method || "GET",
        url: option.url,
        data: option.data,
        header,
        dataType: "json",
      })
      .then((response) => {
        let { code, data, message } = response.data;
        if (code) {
          if (code == 203 && option.url == '/api/parent/indexinfo/datainfo') {
            return reject(response.data);
          }
          if (code == 200) {
            resolve(data);
          } else if (code == 220) {
            resolve(response.data);
          } else {
            if (code == 401) {
              uni.hideLoading()
              if (option.goLogin) {
                uni.redirectTo({
                  url: "/pages_work/login/main"
                })
                reject(response.data);
              } else {
                uni.clearStorageSync();
                reject(response.data);
              }
            } else {
              uni.showToast({ icon: "none", title: message });
              reject('');
            }
          }
        } else {
          uni.hideLoading()
          reject(response);
        }
      })
      .catch((error) => {
        uni.hideLoading()
        reject(error);
      });
  });
};

const showError = (error) => {
  let errorMsg = "";
  switch (error.statusCode) {
    case 400:
      errorMsg = "请求参数错误";
      break;
    case 401:
      errorMsg = "未授权，请登录";
      break;
    case 403:
      errorMsg = "跨域拒绝访问";
      break;
    case 404:
      errorMsg = "请求地址出错";
      break;
    case 408:
      errorMsg = "请求超时";
      break;
    case 500:
      errorMsg = "服务器内部错误";
      break;
    case 501:
      errorMsg = "服务未实现";
      break;
    case 502:
      errorMsg = "网关错误";
      break;
    case 503:
      errorMsg = "服务不可用";
      break;
    case 504:
      errorMsg = "网关超时";
      break;
    case 505:
      errorMsg = "HTTP版本不受支持";
      break;
    default:
      errorMsg = error.msg;
      break;
  }

  uni.showToast({
    title: errorMsg,
    icon: "none",
    duration: 2000,
    complete: function () {
      setTimeout(function () {
        uni.hideToast();
      }, 1000);
    },
  });
};

export default request;
