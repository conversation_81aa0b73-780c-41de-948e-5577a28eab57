.fixed-btn {
    position: fixed;
    bottom: 0;
    height: 160rpx;
    left: 0;
    z-index: 999;
    width: 100vw;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;

    .btn1 {
        width: 40%
    }
}

.page-nodata {
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #666;
}

.modal-conent {
    padding: 0 48rpx;
    line-height: 1.5;

    .title {
        font-weight: bold;
        margin-bottom: 24rpx;
        display: block;
        text-align: center;
    }

    .p {
        margin-bottom: 12rpx;
        display: block;
        font-size: 28rpx;
        color: #666;
    }

    .p:last-of-type {
        margin-bottom: 24rpx;
    }
}

.u-noborder-bottom:after {
    border-bottom-width: 0px;
}

.flex {
    display: flex;
}

.flex-ct {
    display: flex;
    align-items: center;
    justify-content: center;
}