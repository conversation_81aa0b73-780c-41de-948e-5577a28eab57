// stores/counter.js
import { defineStore } from 'pinia';
import request from "@/request"
export const useUserStore = defineStore('userInfo', {
  state: () => {
    return {
      class_id: 0,
      id: 0,
      master_hospital_id: 0,
      identity: -1,
      img_url: "",
      class_name: "",
      circle_type: 0,
      msg_count: 0,
      nickname: "",
      parent_id: 0,
      phone: "",
      relationship_name: "",
      school_id: 0,
      service_id: 1,
      status: 1,
      student_arr: [],
      student_id: 0,
      student_name: "",
      username: "",
      wechat: {
        avatar_url: ""
      }
    };
  },
  getters: {
    currentStudent() {
      if (this.student_id == 0 || this.student_arr.length == 0) {
        return {}
      } else {
        return this.student_arr.find(item => item.id == this.student_id)
      }
    }
  },
  actions: {
    async refrsh(force = false) {
      if (force) {
        try {
          let res = await request({
            method: "POST",
            url: `/api/auth/refreshlogin`,
          })
          this.$patch(res.user);
        } catch (error) {
          uni.reLaunch({
            url: "/pages_work/login/main"
          })
        }
      } else {
        if (this.id == 0) {
          try {
            let res = await request({
              method: "POST",
              url: `/api/auth/refreshlogin`,
            })
            this.$patch(res.user);
            return 1
          } catch (error) {
            return 0
          }
        }
      }
      return 1
    }
  },
  persist: true
});
