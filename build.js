const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const fs = require('fs-extra');
const path = require('path');

// 定义源目录和目标目录
const sourceDir = path.join(__dirname, './dist/build/h5');
const targetDir = path.join(__dirname, '../zhongtuoyun-luzhou-service'); // 修改为你的目标文件夹路径
const type = "parents";

async function build() {
    try {
        console.log("开始打包")
        await execPromise('npm run build:h5')
        if (fs.existsSync(sourceDir)) {
            console.log("打包成功")
            let { stdout } = await execPromise('git rev-parse --abbrev-ref HEAD', { cwd: targetDir })
            const currentBranch = stdout.trim();
            if (currentBranch !== 'master') {
                console.log('不在master分支');
                let { stderr } = await execPromise('git checkout master', { cwd: targetDir })
                if (stderr) {
                    console.error(`切换分支失败`);
                }
            } else {
                console.log('已经在 master 分支');
            }
            let { stderr } = await execPromise('git pull', { cwd: targetDir })
            if (stderr) {
                console.error(`拉取代码失败`);
            }
            await fs.rm(targetDir + `/public/${type}`, { recursive: true, force: true });
            fs.copy(sourceDir, targetDir + `/public/${type}`,async (err) => {
                if (err) {
                    console.error('复制失败:', err);
                } else {
                    console.log('复制成功！');
                    await execPromise('git add .', { cwd: targetDir })
                    await execPromise('git commit -m "build"', { cwd: targetDir })
                    console.log('commit');
                    await execPromise('git push', { cwd: targetDir })
                    console.log('推送成功');
                }
            });
        } else {
            console.log('dist 文件夹不存在，可能是 build:h5 执行失败');
        }
    } catch (err) {
        console.error(err);
    }
}
build()
